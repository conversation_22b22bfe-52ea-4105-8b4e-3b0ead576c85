filebeat.config:
  modules:
    path: ${path.config}/modules.d/*.yml
    reload.enabled: false

filebeat.autodiscover:
  providers:
    - type: docker
      hints.enabled: true
      templates:
        - condition:
            contains:
              container.labels.app: "bahtbrowse"
          config:
            - type: container
              paths:
                - /var/lib/docker/containers/${data.docker.container.id}/*.log
              json.keys_under_root: true
              json.add_error_key: true
              json.message_key: log

# Custom prospectors for specific log files
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /var/log/bahtbrowse/*.log
    fields:
      app: bahtbrowse
      log_type: application
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    json.message_key: message

# Module configurations
filebeat.modules:
  - module: system
    syslog:
      enabled: true
    auth:
      enabled: true

# Processors for enriching data
processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"
  - add_host_metadata: ~
  - add_cloud_metadata: ~
  - add_kubernetes_metadata: ~
  - add_fields:
      target: ''
      fields:
        service.name: 'bahtbrowse'
        environment: 'production'

# Output configuration
output.logstash:
  hosts: ["logstash:5044"]
  ssl.enabled: false

# Logging
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# Monitoring settings
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]

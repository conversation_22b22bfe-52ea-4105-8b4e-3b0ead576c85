input {
  beats {
    port => 5044
  }
  
  tcp {
    port => 5000
    codec => json
  }
  
  udp {
    port => 5000
    codec => json
  }
}

filter {
  if [container][labels][app] == "bahtbrowse" {
    mutate {
      add_field => { "[@metadata][app]" => "bahtbrowse" }
    }
    
    # Extract browser container information
    if [container][labels][component] == "browser" {
      mutate {
        add_field => { "[@metadata][component]" => "browser" }
      }
      
      # Extract browser type
      if [container][labels][browser_type] {
        mutate {
          add_field => { "browser_type" => "%{[container][labels][browser_type]}" }
        }
      }
      
      # Extract container ID
      if [container][labels][container_id] {
        mutate {
          add_field => { "container_id" => "%{[container][labels][container_id]}" }
        }
      }
    }
    
    # Extract API server information
    if [container][labels][component] == "api" {
      mutate {
        add_field => { "[@metadata][component]" => "api" }
      }
    }
    
    # Extract container manager information
    if [container][labels][component] == "worker" {
      mutate {
        add_field => { "[@metadata][component]" => "worker" }
      }
    }
    
    # Parse JSON messages
    if [message] =~ /^\{.*\}$/ {
      json {
        source => "message"
        target => "parsed_json"
      }
      
      # Extract fields from parsed JSON
      if [parsed_json][session_id] {
        mutate {
          add_field => { "session_id" => "%{[parsed_json][session_id]}" }
        }
      }
      
      if [parsed_json][user_id] {
        mutate {
          add_field => { "user_id" => "%{[parsed_json][user_id]}" }
        }
      }
      
      if [parsed_json][event_type] {
        mutate {
          add_field => { "event_type" => "%{[parsed_json][event_type]}" }
        }
      }
      
      if [parsed_json][duration] {
        mutate {
          add_field => { "duration" => "%{[parsed_json][duration]}" }
        }
      }
    }
    
    # Categorize log levels
    if [message] =~ /\bERROR\b/ or [message] =~ /\bException\b/ or [message] =~ /\bFailed\b/ {
      mutate {
        add_field => { "log_level" => "error" }
      }
    } else if [message] =~ /\bWARN\b/ or [message] =~ /\bWARNING\b/ {
      mutate {
        add_field => { "log_level" => "warning" }
      }
    } else if [message] =~ /\bINFO\b/ {
      mutate {
        add_field => { "log_level" => "info" }
      }
    } else if [message] =~ /\bDEBUG\b/ {
      mutate {
        add_field => { "log_level" => "debug" }
      }
    } else {
      mutate {
        add_field => { "log_level" => "unknown" }
      }
    }
    
    # Add timestamp
    date {
      match => [ "timestamp", "ISO8601", "yyyy-MM-dd'T'HH:mm:ss.SSSZ" ]
      target => "@timestamp"
      remove_field => [ "timestamp" ]
    }
  }
  
  # Process metrics data
  if [metricset] {
    mutate {
      add_field => { "[@metadata][type]" => "metrics" }
    }
  }
  
  # Process APM data
  if [processor][event] == "transaction" or [processor][event] == "error" or [processor][event] == "span" {
    mutate {
      add_field => { "[@metadata][type]" => "apm" }
    }
  }
}

output {
  # Output to Elasticsearch with different indices based on data type
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "bahtbrowse-%{[@metadata][type]}-%{+YYYY.MM.dd}"
    
    # Set default type if not specified
    if [@metadata][type] == "" {
      index => "bahtbrowse-logs-%{+YYYY.MM.dd}"
    }
  }
  
  # Debug output (uncomment for debugging)
  # stdout { codec => rubydebug }
}

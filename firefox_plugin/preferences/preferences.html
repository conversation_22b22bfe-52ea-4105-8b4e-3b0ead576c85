
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>BahtBrowse Preferences</title>
          <link rel="stylesheet" href="preferences.css">
          <!-- Font Awesome CDN -->
          <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        </head>
        <body>
          <div id="preferences-content">
            <img id="bahtbrowse-logo" src="../icons/logo.png" alt="BahtBrowse Logo">
            <h1 id="preferences-title"><i class="fas fa-sliders-h"></i> BahtBrowse Preferences</h1>
            <form id="preferences-form">
              <div id="browser-preferences-section" class="section">
                <h2><i class="fas fa-globe"></i> Browser Preferences</h2>
                <div class="form-group">
                  <label for="allow-downloads-toggle"><i class="fas fa-download"></i> Allow Downloads</label>
                  <input type="checkbox" id="allow-downloads-toggle">
                </div>
                <div class="form-group">
                  <label for="allow-clipboard-toggle"><i class="fas fa-clipboard"></i> Allow Clipboard Access</label>
                  <input type="checkbox" id="allow-clipboard-toggle">
                </div>
                <div class="form-group">
                  <label for="allow-microphone-toggle"><i class="fas fa-microphone"></i> Allow Microphone Access</label>
                  <input type="checkbox" id="allow-microphone-toggle">
                </div>
                <div class="form-group">
                  <label for="allow-camera-toggle"><i class="fas fa-camera"></i> Allow Camera Access</label>
                  <input type="checkbox" id="allow-camera-toggle">
                </div>
                <div class="form-group">
                  <label for="allow-notifications-toggle"><i class="fas fa-bell"></i> Allow Notifications</label>
                  <input type="checkbox" id="allow-notifications-toggle">
                </div>
                <div class="form-group">
                  <label for="allow-location-toggle"><i class="fas fa-map-marker-alt"></i> Allow Location Access</label>
                  <input type="checkbox" id="allow-location-toggle">
                </div>
              </div>
              <div id="security-preferences-section" class="section">
                <h2><i class="fas fa-shield-alt"></i> Security Preferences</h2>
                <div class="form-group">
                  <label for="clear-cookies-on-exit-toggle"><i class="fas fa-cookie-bite"></i> Clear Cookies on Exit</label>
                  <input type="checkbox" id="clear-cookies-on-exit-toggle" checked>
                </div>
                <div class="form-group">
                  <label for="clear-history-on-exit-toggle"><i class="fas fa-history"></i> Clear History on Exit</label>
                  <input type="checkbox" id="clear-history-on-exit-toggle" checked>
                </div>
                <div class="form-group">
                  <label for="block-trackers-toggle"><i class="fas fa-user-secret"></i> Block Trackers</label>
                  <input type="checkbox" id="block-trackers-toggle" checked>
                </div>
                <div class="form-group">
                  <label for="block-ads-toggle"><i class="fas fa-ad"></i> Block Ads</label>
                  <input type="checkbox" id="block-ads-toggle" checked>
                </div>
                <div class="form-group">
                  <label for="block-popups-toggle"><i class="fas fa-window-restore"></i> Block Popups</label>
                  <input type="checkbox" id="block-popups-toggle" checked>
                </div>
              </div>
              <div id="appearance-preferences-section" class="section">
                <h2><i class="fas fa-paint-brush"></i> Appearance Preferences</h2>
                <div class="form-group">
                  <label for="dark-theme-toggle"><i class="fas fa-moon"></i> Dark Theme</label>
                  <input type="checkbox" id="dark-theme-toggle">
                </div>
                <div class="form-group">
                  <label for="font-size-select"><i class="fas fa-text-height"></i> Font Size</label>
                  <select id="font-size-select">
                    <option value="small">Small</option>
                    <option value="medium" selected>Medium</option>
                    <option value="large">Large</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="language-select"><i class="fas fa-language"></i> Language</label>
                  <select id="language-select">
                    <option value="en" selected>English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                    <option value="ja">Japanese</option>
                  </select>
                </div>
              </div>
              <div id="advanced-preferences-section" class="section">
                <h2><i class="fas fa-cogs"></i> Advanced Preferences</h2>
                <div class="form-group">
                  <label for="proxy-enabled-toggle"><i class="fas fa-network-wired"></i> Use Proxy</label>
                  <input type="checkbox" id="proxy-enabled-toggle">
                </div>
                <div class="form-group">
                  <label for="proxy-host-input"><i class="fas fa-server"></i> Proxy Host</label>
                  <input type="text" id="proxy-host-input">
                  <div id="proxy-host-error" class="error-message"><i class="fas fa-exclamation-triangle"></i> Proxy host is required</div>
                </div>
                <div class="form-group">
                  <label for="proxy-port-input"><i class="fas fa-plug"></i> Proxy Port</label>
                  <input type="text" id="proxy-port-input">
                  <div id="proxy-port-error" class="error-message"><i class="fas fa-exclamation-triangle"></i> Proxy port must be a number</div>
                </div>
                <div class="form-group">
                  <label for="custom-user-agent"><i class="fas fa-user-astronaut"></i> Custom User Agent</label>
                  <input type="text" id="custom-user-agent">
                </div>
                <div class="form-group">
                  <label for="custom-js"><i class="fas fa-code"></i> Custom JavaScript</label>
                  <textarea id="custom-js"></textarea>
                </div>
              </div>
              <div class="form-actions">
                <button type="button" id="save-preferences-button"><i class="fas fa-save"></i> Save Preferences</button>
                <button type="button" id="reset-preferences-button"><i class="fas fa-undo"></i> Reset to Default</button>
              </div>
            </form>
            <div id="save-success-message" class="message success"><i class="fas fa-check-circle"></i> Preferences saved successfully!</div>
            <div id="reset-success-message" class="message success"><i class="fas fa-check-circle"></i> Preferences reset to default!</div>
            <div id="save-error-message" class="message error"><i class="fas fa-exclamation-circle"></i> Failed to save preferences!</div>
            <div id="reset-dialog" class="dialog">
              <div class="dialog-content">
                <h2><i class="fas fa-exclamation-triangle"></i> Reset Preferences</h2>
                <p>Are you sure you want to reset all preferences to default?</p>
                <div class="dialog-actions">
                  <button id="confirm-reset-button"><i class="fas fa-check"></i> Reset</button>
                  <button id="cancel-reset-button"><i class="fas fa-times"></i> Cancel</button>
                </div>
              </div>
            </div>
          </div>
          <script src="preferences.js"></script>
        </body>
      </html>

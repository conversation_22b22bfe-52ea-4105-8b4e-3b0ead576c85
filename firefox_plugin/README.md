# BahtBrowse Firefox Plugin

A Firefox extension that allows users to quickly redirect their current browsing session to the BahtBrowse containerized browser environment for enhanced security.

## Features

- One-click redirection to BahtBrowse
- Configurable BahtBrowse host and port
- Option to open in new tab or current tab
- Whitelist and blacklist functionality for URL filtering
- Connection testing to verify BahtBrowse service availability
- Improved error handling with user-friendly notifications

## Development

### Prerequisites

- Firefox Developer Edition or Firefox Browser
- Basic knowledge of JavaScript and browser extensions

### Installation

#### Temporary Installation (for testing)

1. Open Firefox
2. Navigate to `about:debugging`
3. Click on "This Firefox"
4. Click on "Load Temporary Add-on..."
5. Navigate to the `build` directory
6. Select the `bahtbrowse_bouncer.xpi` or `bahtbrowse_bouncer.min.xpi` file

#### Permanent Installation

1. Open Firefox
2. Navigate to `about:addons`
3. Click the gear icon and select "Install Add-on From File..."
4. Navigate to the `build` directory
5. Select the `bahtbrowse_bouncer.xpi` or `bahtbrowse_bouncer.min.xpi` file

#### Development Installation

1. Clone this repository
2. Open Firefox and navigate to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on"
5. Select the `manifest.json` file from the cloned repository

### Building the Extension

#### Standard Build

To build the extension without minification:

```bash
./build.sh
```

This will create `build/bahtbrowse_bouncer.xpi`.

#### Minified Build (Recommended)

To build the extension with JavaScript minification:

```bash
./build_minified_only.sh
```

This will create `build/bahtbrowse_bouncer.min.xpi` with minified JavaScript files for improved performance.

## Usage

1. Click the BahtBrowse icon in the Firefox toolbar
2. Confirm that you want to redirect the current page to BahtBrowse
3. The page will open in the BahtBrowse containerized environment

## Configuration

Access the extension settings by:
1. Right-clicking the toolbar icon and selecting "Manage Extension"
2. Going to Add-ons Manager (`about:addons`) and finding BahtBrowse Redirector
3. Clicking on "Options" or "Preferences"

Settings include:
- BahtBrowse host and port (with connection testing)
- Opening behavior (new tab or current tab)
- URL filtering with whitelist and blacklist
- Notification preferences

## Troubleshooting

### Common Issues

1. **Connection Failed**: If you see a "Connection Failed" message, ensure that:
   - The BahtBrowse service is running
   - The host and port settings are correct
   - There are no network issues preventing the connection
   - Use the "Test Connection" button in settings to verify connectivity

2. **Cross-Origin Errors**: Some websites may block redirections due to security policies. In these cases:
   - Try using the context menu option instead of the toolbar button
   - Consider adding the site to the blacklist if it consistently fails

3. **Plugin Not Working**: If the plugin seems unresponsive:
   - Check the browser console for error messages
   - Restart Firefox
   - Reinstall the plugin

## License

[MIT License](LICENSE)

## Directory Structure

```
firefox_plugin/
├── background.js         # Main background script
├── background.min.js     # Minified background script
├── build/                # Build output directory
│   └── bahtbrowse_bouncer.xpi  # Packaged extension
├── build_minified_only.sh  # Script to build with minification
├── build.sh              # Script to build without minification
├── icons/                # Extension icons
├── INSTALLATION.md       # Installation instructions
├── manifest.json         # Extension manifest
├── options/              # Settings page files
│   ├── options.css
│   ├── options.html
│   ├── options.js
│   └── options.min.js    # Minified options script
├── package.json          # Package information
├── package.sh            # Packaging script
├── popup/                # Popup UI files
│   ├── redirect.css
│   ├── redirect.html
│   ├── redirect.js
│   └── redirect.min.js   # Minified popup script
├── README.md             # This file
└── simple_minify.sh      # JavaScript minification script
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

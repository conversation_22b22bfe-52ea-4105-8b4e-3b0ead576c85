   const logsContainer = document.getElementById('logs'); const refreshButton = document.getElementById('refresh'); const clearButton = document.getElementById('clear'); const filterInput = document.getElementById('filter');   let allLogs = [];   function fetchLogs(){  browser.runtime.sendMessage({ type: 'getLogs' })  .then(response =>{  allLogs = response.logs || [];  renderLogs();  })  .catch(error =>{  showError('Error fetching logs: ' + error.message);  }); }   function clearLogs(){  logsContainer.innerHTML = '<div class="no-logs">No logs available</div>'; }   function showError(message){  logsContainer.innerHTML = `<div class="log-entry" style="color: red">${message}</div>`; }   function renderLogs(){    logsContainer.innerHTML = '';     const filterValue = filterInput.value.toLowerCase();     const filteredLogs = filterValue  ? allLogs.filter(log =>  log.category.toLowerCase().includes(filterValue) ||  log.message.toLowerCase().includes(filterValue) ||  (log.data && JSON.stringify(log.data).toLowerCase().includes(filterValue)))  : allLogs;     if (filteredLogs.length === 0){  logsContainer.innerHTML = '<div class="no-logs">No logs available</div>';  return;  }     filteredLogs.forEach(log =>{  const logEntry = document.createElement('div');  logEntry.className = 'log-entry';     const timestamp = new Date(log.timestamp);  const formattedTime = timestamp.toLocaleTimeString();     logEntry.innerHTML = `  <span class="timestamp">[${formattedTime}]</span>  <span class="category ${log.category}">${log.category}</span>  <span class="message">${log.message}</span>  ${log.data ? `<div class="data">${JSON.stringify(log.data,null,2)}</div>` : ''}  `;   logsContainer.appendChild(logEntry);  }); }   refreshButton.addEventListener('click',fetchLogs); clearButton.addEventListener('click',clearLogs); filterInput.addEventListener('input',renderLogs);   fetchLogs();   setInterval(fetchLogs,5000); 
{"browser_specific_settings": {"gecko": {"id": "<EMAIL>"}}, "manifest_version": 2, "name": "10<PERSON><PERSON> <PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "Bounce URLs to the 10baht Browse containerized browser for enhanced security", "icons": {"48": "icons/bahtbrowse-48.png", "96": "icons/bahtbrowse-96.png"}, "permissions": ["activeTab", "storage", "notifications", "contextMenus", "tabs", "<all_urls>"], "browser_action": {"default_icon": {"19": "icons/bahtbrowse-19.png", "38": "icons/bahtbrowse-38.png"}, "default_title": "10B<PERSON><PERSON> <PERSON>rowse", "default_popup": "popup/redirect.html"}, "page_action": {"default_icon": {"19": "icons/bahtbrowse-19.png", "38": "icons/bahtbrowse-38.png"}, "default_title": "Browse with 10Baht Browse"}, "options_ui": {"page": "options/options.html", "browser_style": true}, "background": {"scripts": ["background.js"]}, "commands": {"_execute_browser_action": {"suggested_key": {"default": "Alt+B"}, "description": "Open in 10Baht Browse"}}, "web_accessible_resources": ["popup/debug-logs.html", "icons/*.png"]}
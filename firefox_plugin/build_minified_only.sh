#!/bin/bash

# <PERSON>ript to create a minified Firefox extension package
# This script ensures only minified JavaScript files are included

echo "========================================================"
echo "BahtBrowse Firefox Extension - Minified Build"
echo "========================================================"

# Step 1: Minify JavaScript files
echo "Step 1: Minifying JavaScript files..."
./simple_minify.sh

# Step 2: Create a temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo "Step 2: Created temporary directory: $TEMP_DIR"

# Step 3: Create directory structure
echo "Step 3: Creating directory structure..."
mkdir -p $TEMP_DIR/icons $TEMP_DIR/options $TEMP_DIR/popup

# Step 4: Copy non-JavaScript files
echo "Step 4: Copying non-JavaScript files..."
cp manifest.json README.md INSTALLATION.md package.json $TEMP_DIR/
cp -r icons/* $TEMP_DIR/icons/
cp options/options.css $TEMP_DIR/options/
cp options/options.html $TEMP_DIR/options/
cp popup/redirect.css $TEMP_DIR/popup/
cp popup/redirect.html $TEMP_DIR/popup/

# Step 5: Copy both regular and minified JavaScript files
echo "Step 5: Copying both regular and minified JavaScript files..."
cp background.js $TEMP_DIR/
cp background.min.js $TEMP_DIR/
cp options/options.js $TEMP_DIR/options/
cp options/options.min.js $TEMP_DIR/options/
cp popup/redirect.js $TEMP_DIR/popup/
cp popup/redirect.min.js $TEMP_DIR/popup/

# Step 6: Do NOT update HTML files to use minified JavaScript
echo "Step 6: Keeping original JavaScript references..."

# Step 7: Do NOT update manifest.json to use minified background.js
echo "Step 7: Keeping manifest.json as is..."

# Step 8: Create build directory if it doesn't exist
mkdir -p build
XPI_FILE="build/bahtbrowse_bouncer.debug.xpi"
echo "Step 8: Creating XPI file: $XPI_FILE"

# Step 9: Create the XPI file
cd $TEMP_DIR
echo "Step 9: Creating ZIP archive..."
zip -r "$OLDPWD/$XPI_FILE" *
ZIP_RESULT=$?
cd - > /dev/null

# Step 10: Check if the XPI file was created successfully
if [ $ZIP_RESULT -eq 0 ] && [ -f "$XPI_FILE" ]; then
    echo "Step 10: XPI file created successfully: $XPI_FILE"

    echo "You can install it in Firefox by going to:"
    echo "  - Temporary installation: about:debugging > This Firefox > Load Temporary Add-on"
    echo "  - Permanent installation: about:addons > gear icon > Install Add-on From File..."
else
    echo "Step 10: Failed to create XPI file. Error code: $ZIP_RESULT"
fi

# Step 11: Clean up
rm -rf "$TEMP_DIR"
echo "Step 11: Cleaned up temporary directory"

echo "========================================================"
echo "Debug build complete!"
echo "========================================================"

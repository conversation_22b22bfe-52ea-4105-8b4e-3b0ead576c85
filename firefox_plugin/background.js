// Background script for 10baht Browse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true,
  connectionStatusCheck: true,
  connectionCheckInterval: 30 // seconds
};

// Connection status variable
let serverConnectionStatus = {
  isConnected: false,
  lastChecked: null,
  checkInProgress: false
};

// Initialize settings
console.log('10baht Browse Bouncer initializing...');

browser.runtime.onInstalled.addListener(() => {
  console.log('Extension installed or updated');
  browser.storage.sync.get('settings').then((result) => {
    if (!result.settings) {
      console.log('No settings found, using defaults');
      browser.storage.sync.set({ settings: DEFAULT_SETTINGS })
        .then(() => console.log('Default settings saved'))
        .catch(error => console.error('Error saving default settings:', error));
    } else {
      console.log('Existing settings found:', result.settings);
    }

    // Start the connection status checker
    startConnectionStatusChecker();

    // Show welcome notification
    showNotification('10baht Browse Installed', 'Click the toolbar button to securely browse pages');
  }).catch(error => {
    console.error('Error getting settings during initialization:', error);
  });
});

// Log when extension is loaded
console.log('10baht Browse loaded, version:', browser.runtime.getManifest().version);

// Add error logging
window.addEventListener('error', function(event) {
  console.error('Global error caught:', event.error);
});

// Add unhandled promise rejection logging
window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
});

// Enhanced context menu setup
function setupContextMenus() {
  // Remove any existing menus to prevent duplicates
  browser.contextMenus.removeAll().then(() => {
    // Parent menu item
    browser.contextMenus.create({
      id: 'open-in-10baht',
      title: 'Open in 10baht Browse',
      contexts: ['link', 'page']
    });

    // For regular links and pages
    browser.contextMenus.create({
      id: 'open-in-10baht-new',
      title: 'Open in New Session',
      contexts: ['link', 'page'],
      parentId: 'open-in-10baht'
    });

    // For links only - open in current session
    browser.contextMenus.create({
      id: 'open-in-10baht-link',
      title: 'Open Link in 10baht Browse',
      contexts: ['link']
    });

    // For pages only
    browser.contextMenus.create({
      id: 'open-in-10baht-page',
      title: 'Browse This Page Securely',
      contexts: ['page']
    });
  });
}

// Set up context menu items
setupContextMenus();

// Handle context menu clicks
browser.contextMenus.onClicked.addListener((info, tab) => {
  console.log('Context menu item clicked:', info.menuItemId);
  logDebug('CONTEXT_MENU', 'Context menu item clicked', {
    menuItemId: info.menuItemId,
    linkUrl: info.linkUrl,
    pageUrl: tab.url,
    frameUrl: info.frameUrl,
    srcUrl: info.srcUrl,
    selectionText: info.selectionText
  });

  // Get the URL to open based on context
  let urlToOpen;
  
  switch (info.menuItemId) {
    case 'open-in-10baht':
    case 'open-in-10baht-new':
      // Open in a new session
      urlToOpen = info.linkUrl || info.srcUrl || tab.url;
      break;

    case 'open-in-10baht-link':
      // Special handling for links
      urlToOpen = info.linkUrl;
      break;

    case 'open-in-10baht-page':
      // Open current page
      urlToOpen = tab.url;
      break;
  }

  // Only proceed if we have a URL to open
  if (urlToOpen) {
    logDebug('CONTEXT_MENU', 'Redirecting to BahtBrowse', { urlToOpen });
    redirectToBahtBrowse(tab, urlToOpen);
  } else {
    logDebug('CONTEXT_MENU', 'No URL to open', { info, tab });
    showNotification('Error', 'No URL found to open in 10baht Browse');
  }
});

// Handle toolbar button click
browser.browserAction.onClicked.addListener((tab) => {
  console.log('Toolbar button clicked for tab:', tab);
  console.log('Tab URL:', tab.url);
  redirectToBahtBrowse(tab, tab.url);
});

// Enhanced logging function
function logDebug(category, message, data) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    category,
    message,
    data: data || null
  };

  console.log(`[${timestamp}][${category}] ${message}`, data || '');

  // Store in logs array for potential retrieval by popup
  recentLogs.push(logEntry);
  if (recentLogs.length > 100) {
    recentLogs.shift(); // Keep logs from growing too large
  }
}

// Storage for recent logs
const recentLogs = [];

// Function to check if 10baht Browse service is available
function checkBahtBrowseAvailability(host, port) {
  return new Promise((resolve, reject) => {
    logDebug('CONNECTION', `Checking if 10baht Browse is available at ${host}:${port}`);

    // Create a test URL to the 10baht Browse service
    // Test the /browse/ endpoint directly since that's where we'll redirect to
    const testUrl = `http://${host}:${port}/browse/`;
    logDebug('CONNECTION', `Using test URL: ${testUrl}`);

    // Get plugin version
    const pluginVersion = browser.runtime.getManifest().version;
    logDebug('CONNECTION', 'Plugin version:', pluginVersion);

    // Use fetch with a timeout to check if the service is available
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), 5000);
    });

    Promise.race([
      fetch(testUrl, {
        method: 'GET', // Try GET instead of POST for availability check
        headers: {
          'X-Test-Connection': 'true',
          'X-Client-Info': '10Baht-Browse-Firefox-Plugin',
          'X-Request-Source': 'background-script',
          'X-Plugin-Version': pluginVersion
        }
      }),
      timeoutPromise
    ])
      .then(response => {
        logDebug('CONNECTION', '10baht Browse service response:', {
          ok: response.ok,
          status: response.status,
          url: response.url,
          redirected: response.redirected,
          type: response.type,
          headers: Array.from(response.headers.entries())
        });

        if (response.ok || response.status === 405) { // 405 Method Not Allowed is ok - it means endpoint exists but doesn't like GET
          return response.text().catch(() => ({ status: 'success' }));
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      })
      .then(data => {
        logDebug('CONNECTION', '10baht Browse service is available:', data);
        resolve(true);
      })
      .catch(error => {
        logDebug('CONNECTION', '10baht Browse service is not available:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
        resolve(false);
      });
  });
}

// Function to start periodic connection status checker
function startConnectionStatusChecker() {
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    if (settings.connectionStatusCheck) {
      // Check immediately
      checkServerConnectionStatus();

      // Set up periodic checking
      setInterval(() => {
        checkServerConnectionStatus();
      }, settings.connectionCheckInterval * 1000);

      console.log(`Connection status checker started, interval: ${settings.connectionCheckInterval} seconds`);
    } else {
      console.log('Connection status checker disabled in settings');
    }
  }).catch(error => {
    console.error('Error starting connection checker:', error);
  });
}

// Function to check server connection status and update UI
function checkServerConnectionStatus() {
  // Don't run multiple checks simultaneously
  if (serverConnectionStatus.checkInProgress) {
    return;
  }

  serverConnectionStatus.checkInProgress = true;

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort)
      .then(isAvailable => {
        // Update status
        serverConnectionStatus.isConnected = isAvailable;
        serverConnectionStatus.lastChecked = new Date();
        serverConnectionStatus.checkInProgress = false;

        // Update browser action icon based on connection status
        const iconPath = isAvailable ?
          'icons/bahtbrowse-48.png' :
          'icons/bahtbrowse-48-disconnected.png';

        browser.browserAction.setIcon({ path: iconPath });

        // Send message to any open options pages to update status
        browser.runtime.sendMessage({
          type: 'connectionStatusUpdate',
          status: serverConnectionStatus
        }).catch(() => {
          // Silently ignore errors when no receivers
        });

        console.log(`Connection status updated: ${isAvailable ? 'Connected' : 'Disconnected'}`);
      })
      .catch(error => {
        console.error('Error checking server connection:', error);
        serverConnectionStatus.checkInProgress = false;
        serverConnectionStatus.isConnected = false;

        // Set disconnected icon
        browser.browserAction.setIcon({ path: 'icons/bahtbrowse-48-disconnected.png' });
      });
  }).catch(error => {
    console.error('Error getting settings for connection check:', error);
    serverConnectionStatus.checkInProgress = false;
  });
}

// Function to get current connection status
function getConnectionStatus() {
  return serverConnectionStatus;
}

// Function to redirect URL to 10baht Browse
function redirectToBahtBrowse(tab, url) {
  logDebug('REDIRECT', 'Redirecting to 10baht Browse:', { url, tabId: tab.id });

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    logDebug('REDIRECT', 'Using settings:', settings);

    // Check if server is available using cached status if recent
    const shouldUseCache = serverConnectionStatus.lastChecked &&
      (new Date() - serverConnectionStatus.lastChecked < 10000); // 10 seconds

    logDebug('REDIRECT', `Using cached status: ${shouldUseCache}`,
      shouldUseCache ? { isConnected: serverConnectionStatus.isConnected, lastChecked: serverConnectionStatus.lastChecked } : null);

    const checkPromise = shouldUseCache ?
      Promise.resolve(serverConnectionStatus.isConnected) :
      checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort);

    checkPromise
      .then(isAvailable => {
        logDebug('REDIRECT', `Service availability check result: ${isAvailable}`);

        if (!isAvailable) {
          const errorMsg = `10baht Browse service is not available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}. Please make sure the service is running.`;
          logDebug('REDIRECT', 'Error: Service not available', { errorMsg });
          alert(errorMsg);
          return;
        }

        try {
          // Create form data for POST request
          const formData = new FormData();
          formData.append('url', url);

          // Add a unique timestamp to ensure a fresh session
          const timestamp = Date.now().toString();
          formData.append('timestamp', timestamp);

          // Create the 10baht Browse base URL (without query parameters)
          const bahtbrowseBaseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/`;
          logDebug('REDIRECT', '10baht Browse Base URL:', {
            baseUrl: bahtbrowseBaseUrl,
            fullInfo: {
              protocol: 'http',
              host: settings.bahtbrowseHost,
              port: settings.bahtbrowsePort,
              path: '/browse/',
              timestamp
            }
          });

          // Log URL parsing to check for any issues
          try {
            const parsedUrl = new URL(bahtbrowseBaseUrl);
            logDebug('REDIRECT', 'Parsed URL details:', {
              href: parsedUrl.href,
              protocol: parsedUrl.protocol,
              host: parsedUrl.host,
              hostname: parsedUrl.hostname,
              port: parsedUrl.port,
              pathname: parsedUrl.pathname
            });
          } catch(parseError) {
            logDebug('REDIRECT', 'URL parsing error:', {
              error: parseError.message,
              url: bahtbrowseBaseUrl
            });
          }

          // Create a new tab first
          browser.tabs.create({ url: 'about:blank' })
            .then(newTab => {
              logDebug('REDIRECT', 'New blank tab created:', {
                tabId: newTab.id,
                windowId: newTab.windowId
              });

              // Execute a content script to perform the POST and handle the redirect
              const contentScript = `
                // Detailed logging in content script
                function contentLog(type, message, data) {
                  const timestamp = new Date().toISOString();
                  console.log(\`[CONTENT-SCRIPT][\${timestamp}][\${type}] \${message}\`, data || '');

                  // Create log element if not exists
                  if (!document.getElementById('bahtbrowse-log')) {
                    const logDiv = document.createElement('div');
                    logDiv.id = 'bahtbrowse-log';
                    logDiv.style.position = 'fixed';
                    logDiv.style.bottom = '10px';
                    logDiv.style.right = '10px';
                    logDiv.style.width = '400px';
                    logDiv.style.height = '200px';
                    logDiv.style.backgroundColor = 'rgba(0,0,0,0.8)';
                    logDiv.style.color = '#fff';
                    logDiv.style.padding = '10px';
                    logDiv.style.overflow = 'auto';
                    logDiv.style.fontSize = '12px';
                    logDiv.style.fontFamily = 'monospace';
                    logDiv.style.zIndex = '10000';
                    document.body.appendChild(logDiv);
                  }

                  // Add to log element
                  const logElement = document.getElementById('bahtbrowse-log');
                  const entry = document.createElement('div');
                  entry.innerHTML = \`[\${timestamp.substring(11, 19)}][\${type}] \${message}\`;
                  logElement.appendChild(entry);
                  logElement.scrollTop = logElement.scrollHeight;
                }

                // Show loading indicator
                document.body.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;"><div style="text-align: center;"><h2>Redirecting to 10baht Browse...</h2><p>Secure browsing session is being prepared for: ${url.replace(/'/g, "\\'")}</p><div style="border: 8px solid #f3f3f3; border-top: 8px solid #a520c6; border-radius: 50%; width: 60px; height: 60px; margin: 20px auto; animation: spin 2s linear infinite;"></div></div></div><style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>';

                const targetUrl = '${bahtbrowseBaseUrl.replace(/'/g, "\\'")}';
                contentLog('INFO', 'Starting redirect process');
                contentLog('INFO', 'Target URL', targetUrl);

                const formData = new FormData();
                formData.append('url', '${url.replace(/'/g, "\\'")}');
                formData.append('timestamp', '${Date.now()}');
                formData.append('browser_source', 'firefox_plugin');

                contentLog('INFO', 'FormData created', {
                  url: '${url.replace(/'/g, "\\'")}',
                  timestamp: '${Date.now()}',
                  browser_source: 'firefox_plugin'
                });

                contentLog('INFO', 'Starting fetch request');
                fetch(targetUrl, {
                  method: 'POST',
                  body: formData,
                  headers: {
                    'X-Requested-With': '10BahtBrowsePlugin',
                    'Cache-Control': 'no-cache, no-store, must-revalidate'
                  },
                  redirect: 'follow',
                  cache: 'no-store'
                })
                .then(response => {
                  contentLog('INFO', 'Got response', {
                    ok: response.ok,
                    status: response.status,
                    url: response.url,
                    redirected: response.redirected,
                    type: response.type
                  });

                  if (response.redirected) {
                    // Add cache-busting parameter to the redirect URL
                    const redirectUrl = new URL(response.url);
                    redirectUrl.searchParams.set('_nocache', Date.now());

                    contentLog('INFO', 'Redirecting to', redirectUrl.toString());

                    // Add a short delay to ensure logs are visible
                    setTimeout(() => {
                      window.location.href = redirectUrl.toString();
                    }, 500);
                  } else {
                    return response.text().then(text => {
                      contentLog('ERROR', 'Response not redirected and not OK', text);
                      throw new Error('10baht Browse error: ' + text);
                    });
                  }
                })
                .catch(error => {
                  contentLog('ERROR', 'Error in POST request', {
                    name: error.name,
                    message: error.message
                  });
                  document.body.innerHTML = '<div style="padding: 20px; font-family: Arial, sans-serif;"><h1 style="color: #e74c3c;">Error connecting to 10baht Browse</h1><p>' + error.message + '</p><button onclick="window.location.reload()" style="padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button></div>';
                });
              `;

              // Log content script execution
              logDebug('REDIRECT', 'Executing content script', {
                tabId: newTab.id,
                scriptLength: contentScript.length
              });

              browser.tabs.executeScript(newTab.id, {
                code: contentScript
              })
              .then(() => {
                logDebug('REDIRECT', 'Content script executed successfully');
                // Show notification
                showNotification('10baht Browse', `Opening ${url} in 10baht Browse`);
              })
              .catch(error => {
                logDebug('REDIRECT', 'Error executing content script:', {
                  name: error.name,
                  message: error.message,
                  stack: error.stack
                });
                alert(`Error executing POST request: ${error.message}`);
              });
            })
            .catch(error => {
              logDebug('REDIRECT', 'Error creating new tab:', {
                name: error.name,
                message: error.message,
                stack: error.stack
              });
              alert(`Error opening in new tab: ${error.message}`);
            });
        } catch (error) {
          logDebug('REDIRECT', 'Error during redirection:', {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
          alert(`Error during redirection: ${error.message}`);
        }
      })
      .catch(error => {
        logDebug('REDIRECT', 'Error checking 10baht Browse availability:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
        alert(`Error checking 10baht Browse availability: ${error.message}`);
      });
  }).catch(error => {
    logDebug('REDIRECT', 'Error getting settings:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    alert(`Error getting settings: ${error.message}`);
  });
}

// Function to show browser notifications
function showNotification(title, message) {
  try {
    console.log('Showing notification:', title, message);
    browser.notifications.create({
      type: 'basic',
      iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
      title: title,
      message: message
    }).then(notificationId => {
      console.log('Notification shown, ID:', notificationId);
    }).catch(error => {
      console.error('Error showing notification:', error);
      // Fallback to console if notifications fail
      console.warn(`Notification (fallback): ${title} - ${message}`);
    });
  } catch (error) {
    console.error('Error in showNotification:', error);
    // Fallback to console if notifications fail
    console.warn(`Notification (fallback): ${title} - ${message}`);
  }
}

// Function to check if URL should be redirected based on whitelist/blacklist
function shouldRedirect(url, settings) {
  console.log('Checking if URL should be redirected:', url);
  console.log('Whitelist enabled:', settings.enableWhitelist, 'Whitelist:', settings.whitelist);
  console.log('Blacklist enabled:', settings.enableBlacklist, 'Blacklist:', settings.blacklist);

  // If whitelist is enabled, only redirect if URL is in whitelist
  if (settings.enableWhitelist && settings.whitelist.length > 0) {
    const shouldRedirect = settings.whitelist.some(pattern => url.includes(pattern));
    console.log('Whitelist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If blacklist is enabled, don't redirect if URL is in blacklist
  if (settings.enableBlacklist && settings.blacklist.length > 0) {
    const shouldRedirect = !settings.blacklist.some(pattern => url.includes(pattern));
    console.log('Blacklist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If neither whitelist nor blacklist is enabled, always redirect
  console.log('No whitelist/blacklist enabled, should redirect');
  return true;
}

// Listen for messages from options page
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'getConnectionStatus') {
    sendResponse(serverConnectionStatus);
  } else if (message.type === 'checkConnectionNow') {
    checkServerConnectionStatus();
    sendResponse({ message: 'Connection check initiated' });
  } else if (message.type === 'getLogs') {
    sendResponse({ logs: recentLogs });
  }
  return true;
});

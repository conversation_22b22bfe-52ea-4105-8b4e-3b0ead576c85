/* Dark mode detection */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #1f1d1d;
    --container-bg: #212529;
    --section-bg: #331f38;
    --text-color: #f9f9fa;
    --border-color: #5c5c66;
    --input-bg: #1a1c20;
    --input-text: #f9f9fa;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #212529;
    --secondary-hover: #331f38;
    --success-bg: #054d2e;
    --success-color: #87efb5;
    --error-bg: #5e292b;
    --error-color: #ff9aa2;
    --testing-bg: #2a4887;
    --testing-color: #a7c6ff;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --checkbox-bg: #212529;
    --primary-color-rgb: 165, 32, 198;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
    --status-indicator-bg: rgba(26, 28, 32, 0.8);
    --last-checked-color: #8a8d91;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --background-color: #1f1d1d;
    --container-bg: #212529;
    --section-bg: #331f38;
    --text-color: #f9f9fa;
    --border-color: #5c5c66;
    --input-bg: #1a1c20;
    --input-text: #f9f9fa;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #212529;
    --secondary-hover: #331f38;
    --success-bg: #054d2e;
    --success-color: #87efb5;
    --error-bg: #5e292b;
    --error-color: #ff9aa2;
    --testing-bg: #2a4887;
    --testing-color: #a7c6ff;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --checkbox-bg: #212529;
    --primary-color-rgb: 165, 32, 198;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
    --status-indicator-bg: rgba(26, 28, 32, 0.8);
    --last-checked-color: #8a8d91;
  }
}

/* 10baht Browse theme styles */
body {
  font-family: 'Vibur', sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue';
  margin: 0;
  padding: 20px;
  background: linear-gradient(300deg,#1f1d1d,#331f38,#a520c6);
  background-size: 180% 180%;
  animation: gradient-animation 18s ease infinite;
  color: var(--text-color);
  line-height: 1.5;
  transition: background-color 0.3s ease, color 0.3s ease;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.container {
  max-width: 600px;
  margin: 0 auto;
  background-color: rgba(33, 37, 41, 0.8);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.5);
  padding: 24px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.header-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.header-logo img {
  filter: drop-shadow(0 0 10px rgba(165, 32, 198, 0.5));
}

h1 {
  font-family: 'Vibur', sans-serif;
  font-size: 28px;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--primary-color);
  color: #fff;
  text-align: center;
  transition: color 0.3s ease, border-color 0.3s ease;
  text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px var(--glow-color), 0 0 40px var(--glow-color);
  animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px var(--glow-color), 0 0 40px var(--glow-color);
  }
  to {
    text-shadow: 0 0 20px #fff, 0 0 30px #ff4da6, 0 0 40px #ff4da6, 0 0 50px #ff4da6;
  }
}

h2 {
  font-family: 'Vibur', sans-serif;
  font-size: 20px;
  margin-top: 24px;
  margin-bottom: 12px;
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
  transition: color 0.3s ease, border-color 0.3s ease;
  text-shadow: 0 0 5px var(--primary-color);
}

.section {
  margin-bottom: 32px;
  background-color: rgba(51, 31, 56, 0.7);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
  background-color: rgba(33, 37, 41, 0.8);
  padding: 8px 12px;
  border-radius: 50px;
  border: 1px solid var(--border-color);
  transition: border-color 0.15s ease-in-out, background-color 0.3s ease;
}

.form-group.checkbox:hover {
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(165, 32, 198, 0.3);
}

.form-group.checkbox label {
  margin-bottom: 0;
  margin-left: 10px;
  cursor: pointer;
}

input[type="text"], input[type="number"] {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 50px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-color 0.3s ease, color 0.3s ease;
  background-color: var(--input-bg);
  color: var(--input-text);
}

input[type="text"]:focus, input[type="number"]:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 10px rgba(165, 32, 198, 0.5);
}

input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

textarea {
  width: 100%;
  height: 80px;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, background-color 0.3s ease, color 0.3s ease;
  background-color: var(--input-bg);
  color: var(--input-text);
}

textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 10px rgba(165, 32, 198, 0.5);
}

.buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

button {
  background: linear-gradient(to right, #331f38, #a520c6);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 50px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0,0,0,0.4);
}

button:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

#resetButton {
  background: transparent;
  color: #fff;
  border: 1px solid var(--primary-color);
  box-shadow: none;
  transition: all 0.3s ease;
}

#resetButton:hover {
  background-color: rgba(165, 32, 198, 0.2);
  box-shadow: 0 0 10px rgba(165, 32, 198, 0.3);
}

.secondary-button {
  background: transparent;
  color: #fff;
  border: 1px solid var(--primary-color);
  padding: 8px 16px;
  margin-top: 8px;
  box-shadow: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background-color: rgba(165, 32, 198, 0.2);
  box-shadow: 0 0 10px rgba(165, 32, 198, 0.3);
  transform: translateY(-2px);
}

.test-connection-group {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.test-connection-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 300px;
}

.connection-status {
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 50px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  min-height: 20px;
  transition: all 0.3s ease;
  opacity: 0;
}

.connection-status.success {
  background-color: rgba(5, 77, 46, 0.7);
  color: var(--success-color);
  opacity: 1;
  text-shadow: 0 0 5px var(--success-color);
}

.connection-status.error {
  background-color: rgba(94, 41, 43, 0.7);
  color: var(--error-color);
  opacity: 1;
  text-shadow: 0 0 5px var(--error-color);
}

.connection-status.testing {
  background-color: rgba(42, 72, 135, 0.7);
  color: var(--testing-color);
  opacity: 1;
  text-shadow: 0 0 5px var(--testing-color);
}

#testConnectionButton {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 50px;
  transition: all 0.3s ease;
  background: linear-gradient(to right, #331f38, #a520c6);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

#testConnectionButton.connected {
  background: linear-gradient(to right, #1b5e20, #2e7d32);
  box-shadow: 0 0 15px rgba(40, 167, 69, 0.5);
}

#testConnectionButton:hover {
  transform: scale(1.05);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  display: inline-block;
  transition: all 0.3s ease;
}

.status-dot.connected {
  background-color: #28a745;
  box-shadow: 0 0 5px #28a745;
}

.status-dot.disconnected {
  background-color: #dc3545;
  box-shadow: 0 0 5px #dc3545;
}

.last-checked {
  font-size: 11px;
  color: var(--last-checked-color);
  text-align: center;
  margin-top: 5px;
  opacity: 0.8;
}

.icon {
  margin-right: 8px;
  font-size: 16px;
}

.status {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 50px;
  text-align: center;
  display: none;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(0,0,0,0.3);
  animation: fadeIn 0.3s ease-in-out;
  transition: all 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.status.success {
  background-color: rgba(5, 77, 46, 0.7);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
  display: block;
}

.status.error {
  background-color: rgba(94, 41, 43, 0.7);
  color: var(--error-color);
  border-left: 4px solid var(--error-color);
  display: block;
}

.status.testing {
  background-color: rgba(42, 72, 135, 0.7);
  color: var(--testing-color);
  border-left: 4px solid var(--testing-color);
  display: block;
}

/* Connection status styles */
.connection-status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 50px;
  background-color: var(--status-indicator-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.status-text {
  margin-right: 10px;
  flex-grow: 1;
}

/* Add glow button style for the Check Now button */
.glow-button {
  color: #fff !important;
  background: linear-gradient(to right, #331f38, #a520c6) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  text-shadow: 0 0 5px #fff;
  transition: all 0.3s ease;
}

.glow-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
  background-size: 400%;
  z-index: -1;
  filter: blur(5px);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50px;
  animation: glowing 20s linear infinite;
}

.glow-button:hover::before {
  opacity: 1;
}

.glow-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.3);
}

@keyframes glowing {
  0% { background-position: 0 0; }
  50% { background-position: 400% 0; }
  100% { background-position: 0 0; }
}

// Options script for 10Baht Browse

// Add console log at start of script
console.log('Options script starting execution');

// Debug information function
function updateDebugInfo(message) {
  const debugContent = document.getElementById('debugContent');
  if (debugContent) {
    const timestamp = new Date().toISOString();
    debugContent.innerHTML += `[${timestamp}] ${message}<br>`;
  }
}

// Clear debug information
function clearDebugInfo() {
  const debugContent = document.getElementById('debugContent');
  if (debugContent) {
    debugContent.innerHTML = '';
  }
}

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true,
  connectionStatusCheck: true,
  connectionCheckInterval: 30 // seconds
};

// Log the default settings
console.log('Default settings:', DEFAULT_SETTINGS);

// Add error logging
window.addEventListener('error', function(event) {
  console.error('Options page error caught:', event.error);
  updateDebugInfo(`ERROR: ${event.error.message}`);
});

// Add unhandled promise rejection logging
window.addEventListener('unhandledrejection', function(event) {
  console.error('Options page unhandled promise rejection:', event.reason);
  updateDebugInfo(`PROMISE REJECTION: ${event.reason}`);
});

// Update connection status indicator
function updateConnectionStatus(status) {
  const statusDot = document.getElementById('statusDot');
  const lastChecked = document.getElementById('lastChecked');
  const testConnectionButton = document.getElementById('testConnectionButton');

  if (!statusDot || !lastChecked || !testConnectionButton) {
    console.error('Connection status elements not found in DOM');
    return;
  }

  if (status.isConnected) {
    statusDot.className = 'status-dot connected';
    testConnectionButton.classList.add('connected');
  } else {
    statusDot.className = 'status-dot disconnected';
    testConnectionButton.classList.remove('connected');
  }

  if (status.lastChecked) {
    const date = new Date(status.lastChecked);
    lastChecked.textContent = `Last checked: ${date.toLocaleTimeString()}`;
  } else {
    lastChecked.textContent = '';
  }
}

// Function to request current connection status
function requestConnectionStatus() {
  browser.runtime.sendMessage({ type: 'getConnectionStatus' })
    .then(status => {
      console.log('Connection status received:', status);
      updateConnectionStatus(status);
    })
    .catch(error => {
      console.error('Error getting connection status:', error);
    });
}

// Function to request a connection check
function requestConnectionCheck() {
  browser.runtime.sendMessage({ type: 'checkConnectionNow' })
    .then(response => {
      console.log('Connection check initiated:', response);
      // Status will be updated via message listener
    })
    .catch(error => {
      console.error('Error requesting connection check:', error);
    });
}

// Check if the browser global is defined
console.log('Browser API available:', typeof browser !== 'undefined');
console.log('Browser storage API available:', typeof browser !== 'undefined' && browser.storage ? true : false);

// Load settings when options page is opened
document.addEventListener('DOMContentLoaded', function() {
  console.log('Options page DOM loaded');
  updateDebugInfo('Options page DOM loaded');

  console.log('Document ready state:', document.readyState);
  updateDebugInfo(`Document ready state: ${document.readyState}`);

  // Request initial connection status
  requestConnectionStatus();

  // Set up connection status check - now directly using the test connection button
  const testConnectionButton = document.getElementById('testConnectionButton');
  if (testConnectionButton) {
    testConnectionButton.addEventListener('click', function() {
      console.log('Test connection button clicked');
      updateDebugInfo('Test connection button clicked');
      testConnection();
      // Also request a connection check from the background script
      requestConnectionCheck();
    });
  }

  // Listen for connection status updates from background script
  browser.runtime.onMessage.addListener((message) => {
    if (message.type === 'connectionStatusUpdate') {
      console.log('Received connection status update:', message.status);
      updateConnectionStatus(message.status);
    }
  });

  try {
    updateDebugInfo('Checking DOM elements:');

    // Check if critical elements exist
    const elements = ['saveButton', 'resetButton', 'testConnectionButton', 'bahtbrowseHost', 'bahtbrowsePort', 'connectionStatusCheck', 'connectionCheckInterval'];
    elements.forEach(id => {
      const exists = !!document.getElementById(id);
      console.log(`${id} exists:`, exists);
      updateDebugInfo(`${id} exists: ${exists}`);
    });

    // Display additional browser API information
    updateDebugInfo(`Browser API available: ${typeof browser !== 'undefined'}`);
    updateDebugInfo(`Storage API available: ${typeof browser !== 'undefined' && browser.storage ? true : false}`);

    // Get storage API status
    if (typeof browser !== 'undefined' && browser.storage) {
      browser.storage.sync.get('testValue').then(() => {
        updateDebugInfo('Storage API working correctly');
      }).catch(error => {
        updateDebugInfo(`Storage API error: ${error.message}`);
      });
    }

    // Attempt to load settings
    updateDebugInfo('Attempting to load settings...');
    loadSettings();

    // Add event listeners to buttons
    console.log('Setting up button event listeners');
    updateDebugInfo('Setting up button event listeners');

    const saveButton = document.getElementById('saveButton');
    const resetButton = document.getElementById('resetButton');
    const testConnectionButton = document.getElementById('testConnectionButton');

    console.log('Save button found:', !!saveButton);
    console.log('Reset button found:', !!resetButton);
    console.log('Test connection button found:', !!testConnectionButton);

    if (saveButton) {
      saveButton.addEventListener('click', function() {
        console.log('Save button clicked');
        updateDebugInfo('Save button clicked');
        saveSettings();
      });
    } else {
      console.error('Save button not found in the DOM');
      updateDebugInfo('ERROR: Save button not found in the DOM');
    }

    if (resetButton) {
      resetButton.addEventListener('click', function() {
        console.log('Reset button clicked');
        updateDebugInfo('Reset button clicked');
        resetSettings();
      });
    } else {
      console.error('Reset button not found in the DOM');
      updateDebugInfo('ERROR: Reset button not found in the DOM');
    }

    if (testConnectionButton) {
      testConnectionButton.addEventListener('click', function() {
        console.log('Test connection button clicked');
        updateDebugInfo('Test connection button clicked');
        testConnection();
      });
    } else {
      console.error('Test connection button not found in the DOM');
      updateDebugInfo('ERROR: Test connection button not found in the DOM');
    }
  } catch (error) {
    console.error('Error in DOMContentLoaded event:', error);
    updateDebugInfo(`Error in initialization: ${error.message}`);
  }
});

// Function to load settings from storage
function loadSettings() {
  console.log('loadSettings function called');
  updateDebugInfo('loadSettings function called');

  try {
    updateDebugInfo('Attempting to get settings from storage...');
    browser.storage.sync.get('settings').then((result) => {
      console.log('Settings loaded from storage:', result);
      updateDebugInfo(`Settings loaded from storage: ${JSON.stringify(result)}`);

      const settings = result.settings || DEFAULT_SETTINGS;
      console.log('Using settings:', settings);
      updateDebugInfo(`Using settings: ${JSON.stringify(settings)}`);

      try {
        // Populate form fields with current settings
        console.log('Populating form fields');
        updateDebugInfo('Populating form fields');

        document.getElementById('bahtbrowseHost').value = settings.bahtbrowseHost;
        document.getElementById('bahtbrowsePort').value = settings.bahtbrowsePort;
        document.getElementById('openInNewTab').checked = settings.openInNewTab;
        document.getElementById('showNotifications').checked = settings.showNotifications;
        document.getElementById('enableWhitelist').checked = settings.enableWhitelist;
        document.getElementById('whitelist').value = settings.whitelist.join('\n');
        document.getElementById('enableBlacklist').checked = settings.enableBlacklist;
        document.getElementById('blacklist').value = settings.blacklist.join('\n');

        // New connection status settings
        document.getElementById('connectionStatusCheck').checked =
          settings.connectionStatusCheck !== undefined ? settings.connectionStatusCheck : DEFAULT_SETTINGS.connectionStatusCheck;

        document.getElementById('connectionCheckInterval').value =
          settings.connectionCheckInterval || DEFAULT_SETTINGS.connectionCheckInterval;

        console.log('Form fields populated successfully');
        updateDebugInfo('Form fields populated successfully');
      } catch (error) {
        console.error('Error populating form fields:', error);
        updateDebugInfo(`Error populating form fields: ${error.message}`);
      }
    }).catch(error => {
      console.error('Error getting settings from storage:', error);
      updateDebugInfo(`Error getting settings from storage: ${error.message}`);
    });
  } catch (error) {
    console.error('Exception in loadSettings function:', error);
    updateDebugInfo(`Exception in loadSettings function: ${error.message}`);
  }
}

// Function to save settings to storage
function saveSettings() {
  const settings = {
    bahtbrowseHost: document.getElementById('bahtbrowseHost').value.trim() || DEFAULT_SETTINGS.bahtbrowseHost,
    bahtbrowsePort: document.getElementById('bahtbrowsePort').value.trim() || DEFAULT_SETTINGS.bahtbrowsePort,
    openInNewTab: document.getElementById('openInNewTab').checked,
    showNotifications: document.getElementById('showNotifications').checked,
    enableWhitelist: document.getElementById('enableWhitelist').checked,
    whitelist: document.getElementById('whitelist').value.split('\n').map(line => line.trim()).filter(line => line),
    enableBlacklist: document.getElementById('enableBlacklist').checked,
    blacklist: document.getElementById('blacklist').value.split('\n').map(line => line.trim()).filter(line => line),
    connectionStatusCheck: document.getElementById('connectionStatusCheck').checked,
    connectionCheckInterval: parseInt(document.getElementById('connectionCheckInterval').value) || DEFAULT_SETTINGS.connectionCheckInterval
  };

  // Ensure the interval is within reasonable bounds
  if (settings.connectionCheckInterval < 5) settings.connectionCheckInterval = 5;
  if (settings.connectionCheckInterval > 300) settings.connectionCheckInterval = 300;

  browser.storage.sync.set({ settings }).then(() => {
    // Show success message
    const statusElement = document.getElementById('status');
    statusElement.textContent = 'Settings saved successfully!';
    statusElement.className = 'status success';

    // Trigger a connection check to update status with new settings
    requestConnectionCheck();

    // Hide message after 3 seconds
    setTimeout(() => {
      statusElement.className = 'status';
    }, 3000);
  });
}

// Function to reset settings to defaults
function resetSettings() {
  browser.storage.sync.set({ settings: DEFAULT_SETTINGS }).then(() => {
    loadSettings();

    // Show success message
    const statusElement = document.getElementById('status');
    statusElement.textContent = 'Settings reset to defaults!';
    statusElement.className = 'status success';

    // Hide message after 3 seconds
    setTimeout(() => {
      statusElement.className = 'status';
    }, 3000);
  });
}

// Function to check if BahtBrowse service is available
function checkBahtBrowseAvailability(host, port) {
  return new Promise((resolve, reject) => {
    console.log(`Checking if 10Baht Browse is available at ${host}:${port}`);

    // Create a test URL to the BahtBrowse service
    // Use the dedicated test-connection endpoint
    const testUrl = `http://${host}:${port}/browse/test-connection`;
    console.log(`Using test URL: ${testUrl}`);

    // Use fetch with a timeout to check if the service is available
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), 5000);
    });

    Promise.race([
      fetch(testUrl, {
        method: 'GET',
        headers: {
          'X-Test-Connection': 'true',
          'X-Client-Info': '10Baht-Browse-Firefox-Plugin'
        }
      }),
      timeoutPromise
    ])
      .then(response => {
        console.log('10Baht Browse service response:', response);
        if (response.ok) {
          return response.json().catch(() => ({ status: 'success' }));
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      })
      .then(data => {
        console.log('10Baht Browse service is available:', data);
        resolve(true);
      })
      .catch(error => {
        console.error('10Baht Browse service is not available:', error);
        resolve(false);
      });
  });
}

// Function to test connection to BahtBrowse service
function testConnection() {
  const host = document.getElementById('bahtbrowseHost').value.trim() || DEFAULT_SETTINGS.bahtbrowseHost;
  const port = document.getElementById('bahtbrowsePort').value.trim() || DEFAULT_SETTINGS.bahtbrowsePort;
  const serverSettingsSection = document.getElementById('serverSettingsSection'); // Get the section

  console.log(`Test connection button clicked for ${host}:${port}`);

  // Use the connection status element instead of the global status
  const connectionStatusElement = document.getElementById('connectionStatus');
  connectionStatusElement.textContent = `Testing connection to ${host}:${port}...`;
  connectionStatusElement.className = 'connection-status testing';

  // Test connection
  console.log('Calling checkBahtBrowseAvailability...');
  checkBahtBrowseAvailability(host, port)
    .then(isAvailable => {
      console.log(`Connection test result: ${isAvailable ? 'success' : 'failure'}`);
      if (isAvailable) {
        connectionStatusElement.textContent = `Connection successful! 10Baht Browse service is available at ${host}:${port}`;
        connectionStatusElement.className = 'connection-status success';
        if (serverSettingsSection) {
          serverSettingsSection.style.display = 'none'; // Hide section on success
        }
      } else {
        connectionStatusElement.textContent = `Connection failed! 10Baht Browse service is not available at ${host}:${port}. Please make sure the service is running.`;
        connectionStatusElement.className = 'connection-status error';
        if (serverSettingsSection) {
          serverSettingsSection.style.display = 'block'; // Show section on failure
        }
      }

      // Keep the status visible (don't hide it after a timeout)
    })
    .catch(error => {
      console.error('Error during connection test:', error);
      connectionStatusElement.textContent = `Error testing connection: ${error.message}`;
      connectionStatusElement.className = 'connection-status error';
      if (serverSettingsSection) {
        serverSettingsSection.style.display = 'block'; // Show section on error
      }

      // Keep the status visible (don't hide it after a timeout)
    });
}

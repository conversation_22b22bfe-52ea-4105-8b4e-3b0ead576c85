#!/usr/bin/env python3
"""
Initialize the browser pool with dummy containers for development/testing.
"""

import os
import sys
import uuid
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from docker_queue.redis_client import (
    add_container_to_pool,
    get_browser_pool_status
)
from docker_queue.config.browser_pool_config import SUPPORTED_BROWSER_TYPES

def create_dummy_container(browser_type, os_type):
    """Create a dummy container for development/testing."""
    container_id = str(uuid.uuid4())
    container_name = f"bahtbrowse-browser-{browser_type}-{os_type}-{container_id[:8]}"
    
    # Create container data
    container_data = {
        'container_id': container_id,
        'container_name': container_name,
        'browser_type': browser_type,
        'os_type': os_type,
        'status': 'available',
        'created_at': time.time(),
        'ip_address': '127.0.0.1',
        'port': 6080 if browser_type == 'firefox' and os_type == 'ubuntu' else 6081,
        'usage_count': 0,
        'health_status': 'healthy',
        'last_health_check': time.time()
    }
    
    # Add container to pool
    add_container_to_pool(container_id, container_data)
    
    return container_id, container_data

def init_browser_pool(num_containers_per_type=2):
    """Initialize the browser pool with dummy containers."""
    print(f"Initializing browser pool with {num_containers_per_type} containers per browser type...")
    
    # Create containers for each browser type
    for browser_type in SUPPORTED_BROWSER_TYPES:
        for os_type in ['ubuntu', 'alpine']:
            for i in range(num_containers_per_type):
                container_id, container_data = create_dummy_container(browser_type, os_type)
                print(f"Created dummy container: {container_data['container_name']}")
    
    # Print pool status
    pool_status = get_browser_pool_status()
    print("\nBrowser pool status:")
    for browser_type, status in pool_status.items():
        print(f"  {browser_type}: {status}")

if __name__ == '__main__':
    # Get number of containers per type from command line
    num_containers = 2
    if len(sys.argv) > 1:
        try:
            num_containers = int(sys.argv[1])
        except ValueError:
            print(f"Invalid number of containers: {sys.argv[1]}")
            sys.exit(1)
    
    # Initialize browser pool
    init_browser_pool(num_containers)

#!/bin/bash

# This script prepares the project for production deployment

echo "Preparing BahtBrowse for production deployment..."

# Check if we're on the right branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "refactor/cw17-pre-integration" ]; then
  echo "Warning: You are not on the refactor/cw17-pre-integration branch."
  echo "Current branch: $CURRENT_BRANCH"
  read -p "Do you want to continue? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# Run tests to ensure everything is working
echo "Running tests..."
./scripts/utils/deploy_test_environment.sh
./scripts/utils/verify_test_environment.sh

# Build the Firefox plugin
echo "Building Firefox plugin..."
cd browser_plugins/firefox && ./rebuild_all.sh
cd ../..

# Skip building Docker images for now
echo "Skipping Docker image build..."
# ./scripts/build/build-benchmark-containers.sh

# Use the existing production configuration file
echo "Using existing production configuration..."

echo "Production configuration created at docker/compose/docker-compose.production.yml"

# Create a deployment script
echo "Creating deployment script..."
cat > scripts/utils/deploy_production.sh << EOL
#!/bin/bash

# This script deploys BahtBrowse to production

# Set environment variables
export PROD_ENV_NAME="bahtbrowse-prod"

echo "Deploying BahtBrowse to production..."

# Start the services
echo "Starting services..."
cd docker/compose && docker compose -f docker-compose.production.yml -p \$PROD_ENV_NAME up -d && cd ../..

# Wait for services to start
echo "Waiting for services to start..."
sleep 10

# Test the connection
echo "Testing connection..."
curl -s http://localhost/browse/test-connection

# Check if the test was successful
if [ \$? -eq 0 ]; then
  echo "Production environment deployed successfully!"
  echo "Access the production environment at http://localhost"
else
  echo "Failed to deploy production environment. Check the logs for more information."
  cd docker/compose && docker compose -f docker-compose.production.yml -p \$PROD_ENV_NAME logs && cd ../..
fi

# Display running containers
echo "Running containers:"
docker ps --filter "name=\$PROD_ENV_NAME"
EOL

chmod +x scripts/utils/deploy_production.sh

echo "Deployment script created at scripts/utils/deploy_production.sh"
echo "Production preparation complete!"

#!/bin/bash

# <PERSON>ript to prepare the Firefox extension for distribution

# Create a build directory
mkdir -p build/bahtbrowse_firefox_plugin

# Copy the extension files
echo "Preparing BahtBrowse Firefox extension..."
cp -r firefox_plugin/* build/bahtbrowse_firefox_plugin/

# Check if copy was successful
if [ $? -eq 0 ]; then
    echo "Extension prepared successfully!"
    echo "Files copied to: build/bahtbrowse_firefox_plugin/"
else
    echo "Error: Failed to prepare extension."
    exit 1
fi

# Try to create a zip file if zip is available
if command -v zip &> /dev/null; then
    echo "Creating XPI package..."
    cd build
    zip -r bahtbrowse_firefox_plugin.xpi bahtbrowse_firefox_plugin/* -x "*.git*" -x "*.DS_Store" -x "*__MACOSX*"
    cd ..

    if [ $? -eq 0 ]; then
        echo "XPI file created at: build/bahtbrowse_firefox_plugin.xpi"
    else
        echo "Warning: Failed to create XPI file, but files are still available in the build directory."
    fi
else
    echo "Note: zip command not found. XPI file not created."
    echo "To create an XPI file manually, zip the contents of the build/bahtbrowse_firefox_plugin directory."
fi

echo ""
echo "Installation instructions:"
echo "1. Open Firefox"
echo "2. Go to about:addons"
echo "3. Click the gear icon and select 'Install Add-on From File...'"
echo "4. Navigate to the build/bahtbrowse_firefox_plugin.xpi file and select it"
echo "5. Follow the prompts to complete installation"

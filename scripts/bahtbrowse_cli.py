#!/usr/bin/env python
"""
BahtBrowse CLI - A Rich CLI for monitoring BahtBrowse services.

This tool provides a visually appealing interface for monitoring BahtBrowse services,
including their status, ports, and running browser containers.
"""

import os
import sys
import socket
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any

import typer
import requests
from rich.console import Console
from rich.table import Table
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.box import ROUNDED

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from docker_queue.redis_client import (
        get_containers_by_browser_type,
        get_active_sessions
    )
    from docker_queue.config.browser_pool_config import SUPPORTED_BROWSER_TYPES
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

# Initialize Typer app and Rich console
app = typer.Typer(help="BahtBrowse CLI - Monitor and manage BahtBrowse services")
console = Console()

# Default ports
DEFAULT_PORTS = {
    "Central API": 5002,
    "Docker Queue API": 5001,
    "Redis": 6379,
    "Celery Flower": 5555,
}

# Service endpoints for health checks
SERVICE_ENDPOINTS = {
    "Central API": "/",
    "Docker Queue API": "/api/health",
}

def check_port_in_use(port: int) -> bool:
    """Check if a port is in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def get_service_status(service: str, port: int) -> Tuple[bool, Dict[str, Any]]:
    """Get the status of a service."""
    if service in SERVICE_ENDPOINTS:
        try:
            response = requests.get(f"http://localhost:{port}{SERVICE_ENDPOINTS[service]}", timeout=2)
            return response.status_code == 200, response.json()
        except requests.RequestException:
            return False, {}
    elif service == "Redis":
        # Check Redis using socket connection
        return check_port_in_use(port), {}
    elif service == "Celery Flower":
        # Check Flower using socket connection
        return check_port_in_use(port), {}
    return False, {}

def get_docker_containers() -> List[Dict[str, Any]]:
    """Get running Docker containers related to BahtBrowse."""
    try:
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.ID}}|{{.Names}}|{{.Ports}}|{{.Status}}|{{.Image}}"],
            capture_output=True,
            text=True,
            check=True
        )
        containers = []
        for line in result.stdout.strip().split('\n'):
            if not line:
                continue
            parts = line.split('|')
            if len(parts) >= 5:
                container_id, name, ports, status, image = parts[:5]
                if "bahtbrowse" in name.lower() or "bahtbrowse" in image.lower():
                    # Determine container type based on naming convention
                    container_type = "unknown"
                    if "bahtbrowse-services-" in name.lower():
                        container_type = "service"
                    elif "bahtbrowse-browsers-" in name.lower() or "bahtbrowse-browser-" in name.lower():
                        container_type = "browser"
                    # For containers not following the convention yet
                    elif any(s in name.lower() for s in ["api", "worker", "beat", "flower", "redis", "postgres"]):
                        container_type = "service"
                    elif any(b in name.lower() for b in ["firefox", "chromium"]):
                        container_type = "browser"

                    containers.append({
                        "id": container_id,
                        "name": name,
                        "ports": ports,
                        "status": status,
                        "image": image,
                        "type": container_type
                    })
        return containers
    except (subprocess.SubprocessError, FileNotFoundError):
        return []

def get_browser_containers() -> Dict[str, List[Dict[str, Any]]]:
    """Get browser containers grouped by browser type."""
    if not REDIS_AVAILABLE:
        return {}

    result = {}
    for browser_type in SUPPORTED_BROWSER_TYPES:
        containers = get_containers_by_browser_type(browser_type)
        if containers:
            result[browser_type] = [
                {
                    "id": container_id,
                    "status": container_data.get("status", "unknown"),
                    "created_at": datetime.fromtimestamp(container_data.get("created_at", 0)).strftime("%Y-%m-%d %H:%M:%S"),
                    "last_used_at": datetime.fromtimestamp(container_data.get("last_used_at", 0)).strftime("%Y-%m-%d %H:%M:%S") if container_data.get("last_used_at", 0) > 0 else "Never",
                    "usage_count": container_data.get("usage_count", 0),
                    "os_type": container_data.get("os_type", "unknown"),
                }
                for container_id, container_data in containers.items()
            ]
    return result

def get_active_browser_sessions() -> List[Dict[str, Any]]:
    """Get active browser sessions."""
    if not REDIS_AVAILABLE:
        return []

    try:
        sessions = get_active_sessions()
        return [
            {
                "session_id": session_id,
                "user_id": session_data.get("user_id", "unknown"),
                "container_id": session_data.get("container_id", "unknown"),
                "browser_type": session_data.get("browser_type", "unknown"),
                "os_type": session_data.get("os_type", "unknown"),
                "created_at": datetime.fromtimestamp(session_data.get("created_at", 0)).strftime("%Y-%m-%d %H:%M:%S"),
                "url": session_data.get("url", ""),
            }
            for session_id, session_data in sessions.items()
        ]
    except Exception:
        return []

def display_services_table() -> Table:
    """Display a table of services and their status."""
    table = Table(title="BahtBrowse Services", box=ROUNDED)

    table.add_column("Service", style="cyan")
    table.add_column("Port", style="magenta")
    table.add_column("Status", style="bold")
    table.add_column("Details", style="green")

    for service, default_port in DEFAULT_PORTS.items():
        # Check if service is running
        is_running, details = get_service_status(service, default_port)

        # Determine status style
        status_style = "green" if is_running else "red"
        status_text = "Running" if is_running else "Stopped"

        # Format details
        details_text = ""
        if details and isinstance(details, dict):
            if "version" in details:
                details_text += f"Version: {details['version']} "
            if "status" in details:
                details_text += f"Status: {details['status']} "

        table.add_row(
            service,
            str(default_port),
            Text(status_text, style=status_style),
            details_text
        )

    return table

def display_docker_containers_table() -> Table:
    """Display a table of Docker containers."""
    table = Table(title="Docker Containers", box=ROUNDED)

    table.add_column("ID", style="cyan")
    table.add_column("Name", style="magenta")
    table.add_column("Type", style="bright_blue")
    table.add_column("Ports", style="yellow")
    table.add_column("Status", style="green")
    table.add_column("Image", style="blue")

    containers = get_docker_containers()

    if not containers:
        table.add_row("No containers found", "", "", "", "", "")
    else:
        # Group containers by type
        services = [c for c in containers if c["type"] == "service"]
        browsers = [c for c in containers if c["type"] == "browser"]
        others = [c for c in containers if c["type"] not in ["service", "browser"]]

        # Sort each group by name
        services.sort(key=lambda c: c["name"])
        browsers.sort(key=lambda c: c["name"])
        others.sort(key=lambda c: c["name"])

        # Add services first
        for container in services:
            table.add_row(
                container["id"][:12],
                container["name"],
                "Service",
                container["ports"],
                container["status"],
                container["image"]
            )

        # Add browsers next
        for container in browsers:
            table.add_row(
                container["id"][:12],
                container["name"],
                "Browser",
                container["ports"],
                container["status"],
                container["image"]
            )

        # Add others last
        for container in others:
            table.add_row(
                container["id"][:12],
                container["name"],
                "Other",
                container["ports"],
                container["status"],
                container["image"]
            )

    return table

def display_browser_containers_table() -> Table:
    """Display a table of browser containers."""
    table = Table(title="Browser Containers", box=ROUNDED)

    table.add_column("Browser Type", style="cyan")
    table.add_column("OS Type", style="magenta")
    table.add_column("Container ID", style="yellow")
    table.add_column("Status", style="green")
    table.add_column("Created At", style="blue")
    table.add_column("Last Used", style="purple")
    table.add_column("Usage Count", style="orange3")

    if not REDIS_AVAILABLE:
        table.add_row("Redis not available", "", "", "", "", "", "")
        return table

    browser_containers = get_browser_containers()

    if not browser_containers:
        table.add_row("No browser containers found", "", "", "", "", "", "")
    else:
        for browser_type, containers in browser_containers.items():
            for container in containers:
                table.add_row(
                    browser_type,
                    container["os_type"],
                    container["id"][:12],
                    container["status"],
                    container["created_at"],
                    container["last_used_at"],
                    str(container["usage_count"])
                )

    return table

def display_active_sessions_table() -> Table:
    """Display a table of active browser sessions."""
    table = Table(title="Active Browser Sessions", box=ROUNDED)

    table.add_column("Session ID", style="cyan")
    table.add_column("User ID", style="magenta")
    table.add_column("Browser", style="yellow")
    table.add_column("OS", style="green")
    table.add_column("Container ID", style="blue")
    table.add_column("Created At", style="purple")

    if not REDIS_AVAILABLE:
        table.add_row("Redis not available", "", "", "", "", "")
        return table

    sessions = get_active_browser_sessions()

    if not sessions:
        table.add_row("No active sessions found", "", "", "", "", "")
    else:
        for session in sessions:
            table.add_row(
                session["session_id"][:8],
                session["user_id"],
                session["browser_type"],
                session["os_type"],
                session["container_id"][:12],
                session["created_at"]
            )

    return table

@app.command()
def status(
    refresh: bool = typer.Option(False, "--refresh", "-r", help="Continuously refresh the display"),
    interval: int = typer.Option(5, "--interval", "-i", help="Refresh interval in seconds")
):
    """Display the status of BahtBrowse services, containers, and sessions."""
    if refresh:
        try:
            with Live(refresh=True) as live:
                while True:
                    layout = Layout()
                    layout.split_column(
                        Layout(display_services_table(), name="services"),
                        Layout(display_docker_containers_table(), name="docker"),
                        Layout(display_browser_containers_table(), name="browsers"),
                        Layout(display_active_sessions_table(), name="sessions")
                    )
                    live.update(layout)
                    time.sleep(interval)
        except KeyboardInterrupt:
            console.print("\n[bold green]Exiting BahtBrowse CLI[/bold green]")
    else:
        console.print(display_services_table())
        console.print(display_docker_containers_table())
        console.print(display_browser_containers_table())
        console.print(display_active_sessions_table())

@app.command()
def services():
    """Display the status of BahtBrowse services."""
    console.print(display_services_table())

@app.command()
def containers():
    """Display Docker containers related to BahtBrowse."""
    console.print(display_docker_containers_table())

@app.command()
def browsers():
    """Display browser containers."""
    console.print(display_browser_containers_table())

@app.command()
def sessions():
    """Display active browser sessions."""
    console.print(display_active_sessions_table())

if __name__ == "__main__":
    app()

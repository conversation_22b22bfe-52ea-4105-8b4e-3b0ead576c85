# BahtBrowse CLI

A Rich CLI tool for monitoring BahtBrowse services, containers, and browser sessions.

## Container Naming Convention

BahtBrowse follows a standardized naming convention for containers:

- **Service containers**: All service containers should be prefixed with `bahtbrowse-services-`
  - Example: `bahtbrowse-services-api`, `bahtbrowse-services-worker-container`

- **Browser containers**: All browser containers should be prefixed with `bahtbrowse-browsers-`
  - Example: `bahtbrowse-browsers-firefox-ubuntu`, `bahtbrowse-browsers-chromium-alpine`

The CLI tool recognizes this naming convention and categorizes containers accordingly.

## Features

- Display status of all BahtBrowse services with their ports
- Show running Docker containers related to BahtBrowse
- List browser containers with detailed information
- View active browser sessions
- Live refresh mode for real-time monitoring

## Requirements

The CLI tool requires the following Python packages:
- rich
- typer
- requests

These dependencies are included in the `shell.nix` file.

## Usage

### Basic Usage

```bash
# Display all information
./scripts/bahtbrowse_cli.py status

# Display only services information
./scripts/bahtbrowse_cli.py services

# Display only Docker containers
./scripts/bahtbrowse_cli.py containers

# Display only browser containers
./scripts/bahtbrowse_cli.py browsers

# Display only active sessions
./scripts/bahtbrowse_cli.py sessions
```

### Live Refresh Mode

To continuously monitor the status with automatic refresh:

```bash
# Refresh every 5 seconds (default)
./scripts/bahtbrowse_cli.py status --refresh

# Refresh with custom interval (e.g., every 2 seconds)
./scripts/bahtbrowse_cli.py status --refresh --interval 2
```

## Help

For help and to see all available options:

```bash
./scripts/bahtbrowse_cli.py --help
```

## Integration with NixOS

The CLI tool is designed to work with the NixOS environment. The required dependencies are included in the `shell.nix` file.

To use the CLI tool in a Nix shell:

```bash
nix-shell
./scripts/bahtbrowse_cli.py status
```

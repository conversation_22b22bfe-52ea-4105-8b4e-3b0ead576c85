# bahtBrowse Scripts

This directory contains scripts for managing bahtBrowse services.

## CLI Tool

The `bahtbrowse_cli.py` script provides a Rich CLI interface for monitoring bahtBrowse services, containers, and browser sessions:

```bash
# Display all information
./scripts/bahtbrowse_cli.py status

# Display only services information
./scripts/bahtbrowse_cli.py services

# Display only Docker containers
./scripts/bahtbrowse_cli.py containers

# Display only browser containers
./scripts/bahtbrowse_cli.py browsers

# Display only active sessions
./scripts/bahtbrowse_cli.py sessions

# Live refresh mode (updates every 5 seconds)
./scripts/bahtbrowse_cli.py status --refresh
```

For more information, see [CLI_README.md](CLI_README.md).

## Service Management Scripts

### Start Services

The `start_services.sh` script starts all bahtBrowse services with the correct port configurations:

```bash
./scripts/start_services.sh
```

This script will:
1. Check if the default ports are available
2. Start the services on the default ports if available
3. Find alternative ports if the default ports are in use
4. Display the actual ports being used

Default ports:
- Central API: 5002
- Docker Queue API: 5001
- Redis: 6379
- Celery Flower: 5555

You can override the default ports using environment variables:

```bash
API_PORT=5010 FLASK_RUN_PORT=5011 ./scripts/start_services.sh
```

### Stop Services

The `stop_services.sh` script stops all bahtBrowse services:

```bash
./scripts/stop_services.sh
```

This script will stop all running services, regardless of which ports they are using.

## Test Scripts

### Port Configuration Test

The `test/test_port_configuration.py` script verifies that the services are running on the correct ports:

```bash
python scripts/test/test_port_configuration.py
```

This script will:
1. Check if the services are running
2. Verify that they are accessible on the expected ports
3. Display a summary of the test results

You can specify custom ports to test:

```bash
python scripts/test/test_port_configuration.py --central-api-port=5010 --docker-queue-api-port=5011
```

## Other Scripts

### Build Scripts

The `build/` directory contains scripts for building Docker images:

```bash
./scripts/build/build_all.sh
```

### Test Scripts

The `test/` directory contains scripts for testing various components:

```bash
python scripts/test/test_port_configuration.py
```

## Environment Variables

The following environment variables can be used to configure the scripts:

- `API_PORT`: The port for the Central API (default: 5002)
- `FLASK_RUN_PORT`: The port for the Docker Queue API (default: 5001)
- `REDIS_PORT`: The port for Redis (default: 6379)
- `CELERY_FLOWER_PORT`: The port for Celery Flower (default: 5555)

## Troubleshooting

If you encounter issues with the scripts, try the following:

1. Make sure the scripts are executable:
   ```bash
   chmod +x scripts/*.sh
   ```

2. Check if the required dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

3. Check if the services are already running:
   ```bash
   ./scripts/stop_services.sh
   ./scripts/start_services.sh
   ```

4. Check the logs for errors:
   ```bash
   cat /tmp/logs/*.log
   ```

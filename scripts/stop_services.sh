#!/bin/bash
# Stop all BahtBrowse services

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to stop a process by port
stop_process_by_port() {
    local name=$1
    local port=$2
    
    echo -e "${YELLOW}Stopping $name on port $port...${NC}"
    
    # Find the process using the port
    local pid=$(lsof -t -i:$port)
    
    if [ -z "$pid" ]; then
        echo -e "${YELLOW}No process found using port $port.${NC}"
        return 0
    fi
    
    # Kill the process
    kill -15 $pid 2>/dev/null
    
    # Wait for the process to stop
    local max_attempts=5
    local attempt=0
    while [ $attempt -lt $max_attempts ]; do
        sleep 1
        if ! lsof -i:$port >/dev/null 2>&1; then
            echo -e "${GREEN}$name stopped successfully.${NC}"
            return 0
        fi
        attempt=$((attempt + 1))
    done
    
    # Force kill if necessary
    echo -e "${RED}Failed to stop $name gracefully. Forcing...${NC}"
    kill -9 $pid 2>/dev/null
    
    if ! lsof -i:$port >/dev/null 2>&1; then
        echo -e "${GREEN}$name stopped successfully (forced).${NC}"
        return 0
    else
        echo -e "${RED}Failed to stop $name.${NC}"
        return 1
    fi
}

# Function to stop Celery processes
stop_celery_processes() {
    echo -e "${YELLOW}Stopping Celery processes...${NC}"
    
    # Find Celery worker and beat processes
    local celery_pids=$(ps aux | grep "celery" | grep -v grep | awk '{print $2}')
    
    if [ -z "$celery_pids" ]; then
        echo -e "${YELLOW}No Celery processes found.${NC}"
        return 0
    fi
    
    # Kill the processes
    for pid in $celery_pids; do
        echo -e "${YELLOW}Stopping Celery process $pid...${NC}"
        kill -15 $pid 2>/dev/null
    done
    
    # Wait for the processes to stop
    sleep 2
    
    # Check if any processes are still running
    celery_pids=$(ps aux | grep "celery" | grep -v grep | awk '{print $2}')
    
    if [ -z "$celery_pids" ]; then
        echo -e "${GREEN}All Celery processes stopped successfully.${NC}"
        return 0
    else
        # Force kill if necessary
        echo -e "${RED}Failed to stop Celery processes gracefully. Forcing...${NC}"
        for pid in $celery_pids; do
            kill -9 $pid 2>/dev/null
        done
        
        sleep 1
        
        celery_pids=$(ps aux | grep "celery" | grep -v grep | awk '{print $2}')
        
        if [ -z "$celery_pids" ]; then
            echo -e "${GREEN}All Celery processes stopped successfully (forced).${NC}"
            return 0
        else
            echo -e "${RED}Failed to stop some Celery processes.${NC}"
            return 1
        fi
    fi
}

# Stop Central API
stop_process_by_port "Central API" 5002

# Stop Docker Queue API
stop_process_by_port "Docker Queue API" 5001

# Stop Celery processes
stop_celery_processes

# Don't stop Redis as it might be used by other applications
echo -e "${YELLOW}Not stopping Redis as it might be used by other applications.${NC}"
echo -e "${YELLOW}If you want to stop Redis, run: sudo service redis-server stop${NC}"

echo -e "\n${GREEN}All BahtBrowse services have been stopped.${NC}"

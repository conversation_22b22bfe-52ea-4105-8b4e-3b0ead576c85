#!/bin/bash
# Start all BahtBrowse services with the correct port configurations

# Set default ports
CENTRAL_API_PORT=5002
DOCKER_QUEUE_API_PORT=5001
REDIS_PORT=6379
CELERY_FLOWER_PORT=5555

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
    local port=$1
    nc -z localhost $port >/dev/null 2>&1
    return $?
}

# Function to start a service
start_service() {
    local name=$1
    local command=$2
    local port=$3
    
    echo -e "${YELLOW}Starting $name on port $port...${NC}"
    
    # Check if port is already in use
    if check_port $port; then
        echo -e "${RED}Port $port is already in use. $name may already be running.${NC}"
        return 1
    fi
    
    # Start the service
    eval "$command" &
    
    # Wait for the service to start
    local max_attempts=10
    local attempt=0
    while [ $attempt -lt $max_attempts ]; do
        sleep 1
        if check_port $port; then
            echo -e "${GREEN}$name started successfully on port $port.${NC}"
            return 0
        fi
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}Failed to start $name on port $port.${NC}"
    return 1
}

# Create logs directory
mkdir -p /tmp/logs

# Start Redis if not already running
if ! check_port $REDIS_PORT; then
    echo -e "${YELLOW}Starting Redis on port $REDIS_PORT...${NC}"
    sudo service redis-server start
    
    # Wait for Redis to start
    local max_attempts=10
    local attempt=0
    while [ $attempt -lt $max_attempts ]; do
        sleep 1
        if check_port $REDIS_PORT; then
            echo -e "${GREEN}Redis started successfully on port $REDIS_PORT.${NC}"
            break
        fi
        attempt=$((attempt + 1))
        if [ $attempt -eq $max_attempts ]; then
            echo -e "${RED}Failed to start Redis on port $REDIS_PORT.${NC}"
            exit 1
        fi
    done
else
    echo -e "${GREEN}Redis is already running on port $REDIS_PORT.${NC}"
fi

# Start Docker Queue API
start_service "Docker Queue API" "cd /workspaces/bahtbrowse && PYTHONPATH=/workspaces/bahtbrowse FLASK_RUN_PORT=$DOCKER_QUEUE_API_PORT python -m flask run --host=0.0.0.0 --port=$DOCKER_QUEUE_API_PORT" $DOCKER_QUEUE_API_PORT

# Start Celery worker
echo -e "${YELLOW}Starting Celery worker...${NC}"
cd /workspaces/bahtbrowse && PYTHONPATH=/workspaces/bahtbrowse celery -A docker_queue.celery_app worker -Q container_management,pool_management -l info > /tmp/logs/celery_worker.log 2>&1 &
CELERY_WORKER_PID=$!
echo -e "${GREEN}Celery worker started with PID $CELERY_WORKER_PID.${NC}"

# Start Celery beat
echo -e "${YELLOW}Starting Celery beat...${NC}"
cd /workspaces/bahtbrowse && PYTHONPATH=/workspaces/bahtbrowse celery -A docker_queue.celery_app beat -l info > /tmp/logs/celery_beat.log 2>&1 &
CELERY_BEAT_PID=$!
echo -e "${GREEN}Celery beat started with PID $CELERY_BEAT_PID.${NC}"

# Start Central API
start_service "Central API" "cd /workspaces/bahtbrowse && PYTHONPATH=/workspaces/bahtbrowse API_PORT=$CENTRAL_API_PORT python docker/api/app.py" $CENTRAL_API_PORT

# Run the port configuration test
echo -e "${YELLOW}Running port configuration test...${NC}"
python scripts/test/test_port_configuration.py --central-api-port=$CENTRAL_API_PORT --docker-queue-api-port=$DOCKER_QUEUE_API_PORT --redis-port=$REDIS_PORT

# Print service URLs
echo -e "\n${GREEN}=== BahtBrowse Services ====${NC}"
echo -e "${GREEN}Central API:${NC} http://localhost:$CENTRAL_API_PORT/"
echo -e "${GREEN}Docker Queue API:${NC} http://localhost:$DOCKER_QUEUE_API_PORT/"
echo -e "${GREEN}Redis:${NC} localhost:$REDIS_PORT"
echo -e "${GREEN}Celery Worker PID:${NC} $CELERY_WORKER_PID"
echo -e "${GREEN}Celery Beat PID:${NC} $CELERY_BEAT_PID"
echo -e "\n${GREEN}=== Test URLs ====${NC}"
echo -e "${GREEN}Firefox on Ubuntu:${NC} http://localhost:$CENTRAL_API_PORT/browse/?browser=firefox&os=ubuntu&url=https://example.com"
echo -e "${GREEN}Chromium on Ubuntu:${NC} http://localhost:$CENTRAL_API_PORT/browse/?browser=chromium&os=ubuntu&url=https://example.com"
echo -e "${GREEN}Firefox on Alpine:${NC} http://localhost:$CENTRAL_API_PORT/browse/?browser=firefox&os=alpine&url=https://example.com"
echo -e "${GREEN}Chromium on Alpine:${NC} http://localhost:$CENTRAL_API_PORT/browse/?browser=chromium&os=alpine&url=https://example.com"
echo -e "${GREEN}Direct VNC Access:${NC} http://localhost:$CENTRAL_API_PORT/direct/firefox/ubuntu?url=https://example.com"

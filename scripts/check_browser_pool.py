#!/usr/bin/env python3
"""
Check the browser pool status directly.
"""

import os
import sys
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from docker_queue.redis_client import (
    get_browser_pool_status,
    get_containers_by_browser_type
)
from docker_queue.config.browser_pool_config import SUPPORTED_BROWSER_TYPES

def check_browser_pool():
    """Check the browser pool status."""
    # Get pool status
    pool_status = get_browser_pool_status()
    print("\nBrowser pool status:")
    print(json.dumps(pool_status, indent=2))
    
    # Get containers by browser type
    for browser_type in SUPPORTED_BROWSER_TYPES:
        containers = get_containers_by_browser_type(browser_type)
        print(f"\nContainers for {browser_type}:")
        for container_id, container_data in containers.items():
            print(f"  {container_id}: {container_data}")

if __name__ == '__main__':
    # Check browser pool
    check_browser_pool()

#!/usr/bin/env python3
"""
Test script to verify that the services are running on the correct ports.
"""

import argparse
import json
import logging
import os
import socket
import sys
import time
from urllib.request import urlopen, Request
from urllib.error import URLError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Default port configuration
DEFAULT_PORTS = {
    "central_api": 5002,
    "docker_queue_api": 5001,
    "redis": 6379,
    "celery_flower": 5555,
}

def is_port_in_use(port, host="localhost"):
    """Check if a port is in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex((host, port)) == 0

def test_http_endpoint(url, expected_status=200, timeout=5):
    """Test an HTTP endpoint."""
    try:
        request = Request(url)
        response = urlopen(request, timeout=timeout)
        status = response.status
        content = response.read().decode('utf-8')
        
        if status != expected_status:
            logger.error(f"Unexpected status code: {status}, expected: {expected_status}")
            return False, f"Status code: {status}, expected: {expected_status}"
        
        try:
            data = json.loads(content)
            return True, data
        except json.JSONDecodeError:
            return True, content
    except URLError as e:
        logger.error(f"Failed to access {url}: {e}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Unexpected error accessing {url}: {e}")
        return False, str(e)

def test_central_api(port=DEFAULT_PORTS["central_api"]):
    """Test the central API."""
    logger.info(f"Testing Central API on port {port}...")
    
    # Check if port is in use
    if not is_port_in_use(port):
        logger.error(f"Central API port {port} is not in use")
        return False, f"Port {port} is not in use"
    
    # Test root endpoint
    success, data = test_http_endpoint(f"http://localhost:{port}/")
    if not success:
        return False, data
    
    # Check if response contains expected fields
    if "name" not in data or "version" not in data or "status" not in data:
        logger.error(f"Unexpected response format: {data}")
        return False, f"Unexpected response format: {data}"
    
    # Test status endpoint
    success, data = test_http_endpoint(f"http://localhost:{port}/status")
    if not success:
        return False, data
    
    logger.info(f"Central API test passed: {data.get('status', 'unknown')}")
    return True, data

def test_docker_queue_api(port=DEFAULT_PORTS["docker_queue_api"]):
    """Test the Docker Queue API."""
    logger.info(f"Testing Docker Queue API on port {port}...")
    
    # Check if port is in use
    if not is_port_in_use(port):
        logger.error(f"Docker Queue API port {port} is not in use")
        return False, f"Port {port} is not in use"
    
    # Test root endpoint
    success, data = test_http_endpoint(f"http://localhost:{port}/")
    if not success:
        return False, data
    
    # Check if response contains expected fields
    if "name" not in data or "version" not in data or "status" not in data:
        logger.error(f"Unexpected response format: {data}")
        return False, f"Unexpected response format: {data}"
    
    # Test pool status endpoint
    success, data = test_http_endpoint(f"http://localhost:{port}/api/pool/status")
    if not success:
        return False, data
    
    logger.info(f"Docker Queue API test passed: {data.get('status', 'unknown')}")
    return True, data

def test_redis(port=DEFAULT_PORTS["redis"]):
    """Test Redis."""
    logger.info(f"Testing Redis on port {port}...")
    
    # Check if port is in use
    if not is_port_in_use(port):
        logger.error(f"Redis port {port} is not in use")
        return False, f"Port {port} is not in use"
    
    # We can't easily test Redis without a client, so just check if the port is open
    logger.info(f"Redis port {port} is open")
    return True, f"Redis port {port} is open"

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test BahtBrowse service ports")
    parser.add_argument("--central-api-port", type=int, default=DEFAULT_PORTS["central_api"],
                        help=f"Central API port (default: {DEFAULT_PORTS['central_api']})")
    parser.add_argument("--docker-queue-api-port", type=int, default=DEFAULT_PORTS["docker_queue_api"],
                        help=f"Docker Queue API port (default: {DEFAULT_PORTS['docker_queue_api']})")
    parser.add_argument("--redis-port", type=int, default=DEFAULT_PORTS["redis"],
                        help=f"Redis port (default: {DEFAULT_PORTS['redis']})")
    args = parser.parse_args()
    
    # Test all services
    services = [
        ("Central API", test_central_api, args.central_api_port),
        ("Docker Queue API", test_docker_queue_api, args.docker_queue_api_port),
        ("Redis", test_redis, args.redis_port),
    ]
    
    all_passed = True
    results = {}
    
    for name, test_func, port in services:
        logger.info(f"Testing {name} on port {port}...")
        success, data = test_func(port)
        results[name] = {
            "success": success,
            "data": data,
            "port": port
        }
        if not success:
            all_passed = False
    
    # Print summary
    logger.info("\n=== Test Results ===")
    for name, result in results.items():
        status = "PASS" if result["success"] else "FAIL"
        logger.info(f"{name}: {status} (Port: {result['port']})")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())

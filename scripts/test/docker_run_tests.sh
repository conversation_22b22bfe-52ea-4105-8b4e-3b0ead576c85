#!/bin/bash
# Run Chromium tests using Docker

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Print header
echo -e "${BOLD}======================================================${NC}"
echo -e "${BOLD}       BahtBrowse Chromium Testing Framework          ${NC}"
echo -e "${BOLD}======================================================${NC}"

# Check if container is running
CONTAINER_NAME="bahtbrowse-scratch-local-browser_chromium-1"
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}Error: Container $CONTAINER_NAME is not running!${NC}"
    echo -e "${YELLOW}Starting the container...${NC}"

    # Try to start the container
    ./build-ungoogled-chromium-prebuilt.sh

    # Check if container started successfully
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}Failed to start container. Please start it manually.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Container started successfully.${NC}"
fi

# Copy test files to the container
echo -e "${YELLOW}Copying test files to the container...${NC}"
docker cp tests/container_test_chromium.py $CONTAINER_NAME:/tmp/container_test_chromium.py

# Install dependencies in the container
echo -e "${YELLOW}Installing dependencies in the container...${NC}"
docker exec $CONTAINER_NAME apt-get update
docker exec $CONTAINER_NAME apt-get install -y python3-rich python3-requests

# Run the tests
echo -e "${YELLOW}Running Chromium tests...${NC}"
docker exec $CONTAINER_NAME python3 /tmp/container_test_chromium.py
TEST_RESULT=$?

# Check the exit code
if [ $TEST_RESULT -eq 0 ]; then
    echo -e "\n${GREEN}All Chromium tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}Some Chromium tests failed. Please check the output above.${NC}"
    exit 1
fi

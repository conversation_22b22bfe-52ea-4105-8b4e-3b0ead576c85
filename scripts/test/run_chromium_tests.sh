#!/bin/bash
# Run comprehensive Chromium tests for BahtBrowse

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Print header
echo -e "${BOLD}======================================================${NC}"
echo -e "${BOLD}       BahtBrowse Chromium Testing Framework          ${NC}"
echo -e "${BOLD}======================================================${NC}"

# Check if container is running
CONTAINER_NAME="bahtbrowse-scratch-local-browser_chromium-1"
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}Error: Container $CONTAINER_NAME is not running!${NC}"
    echo -e "${YELLOW}Starting the container...${NC}"

    # Try to start the container
    ./build-ungoogled-chromium-prebuilt.sh

    # Check if container started successfully
    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}Failed to start container. Please start it manually.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Container started successfully.${NC}"
fi

# Check if Python 3 is installed
echo -e "${YELLOW}Checking Python installation...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Python 3 is not installed or not in PATH.${NC}"
    echo -e "${YELLOW}Please install Python 3 and try again.${NC}"
    exit 1
fi
echo -e "${GREEN}Python 3 is installed.${NC}"

# Check if pip is available
echo -e "${YELLOW}Checking pip installation...${NC}"
if ! python3 -m pip --version &> /dev/null; then
    echo -e "${RED}pip is not installed for Python 3.${NC}"
    echo -e "${YELLOW}Please install pip for Python 3 and try again.${NC}"
    echo -e "${YELLOW}You can install it with: sudo apt-get install python3-pip${NC}"
    exit 1
fi
echo -e "${GREEN}pip is installed.${NC}"

# Check if Python dependencies are installed
echo -e "${YELLOW}Checking Python dependencies...${NC}"
python3 -m pip install rich requests > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to install Python dependencies.${NC}"
    echo -e "${YELLOW}Installing Python dependencies...${NC}"
    python3 -m pip install rich requests
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install Python dependencies. Please install them manually:${NC}"
        echo -e "${YELLOW}python3 -m pip install rich requests${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}Python dependencies are installed.${NC}"

# Run the tests
echo -e "${YELLOW}Running Chromium tests...${NC}"
echo -e "${BOLD}------------------------------------------------------${NC}"

# Set environment variables for the tests
export CONTAINER_NAME="bahtbrowse-scratch-local-browser_chromium-1"
export HOST_PORT="8080"
export VNC_PORT="6080"

# Run the functional tests
echo -e "${YELLOW}Running Chromium functional tests...${NC}"
python3 tests/test_chromium.py
FUNCTIONAL_RESULT=$?

# Run the security tests
echo -e "\n${YELLOW}Running Chromium security tests...${NC}"
python3 tests/test_chromium_security.py
SECURITY_RESULT=$?

# Run the performance tests
echo -e "\n${YELLOW}Running Chromium performance tests...${NC}"
python3 tests/test_chromium_performance.py
PERFORMANCE_RESULT=$?

# Check the exit codes
if [ $FUNCTIONAL_RESULT -eq 0 ] && [ $SECURITY_RESULT -eq 0 ] && [ $PERFORMANCE_RESULT -eq 0 ]; then
    echo -e "\n${GREEN}All Chromium tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}Some Chromium tests failed. Please check the output above.${NC}"

    if [ $FUNCTIONAL_RESULT -ne 0 ]; then
        echo -e "${RED}Functional tests failed.${NC}"
    fi

    if [ $SECURITY_RESULT -ne 0 ]; then
        echo -e "${RED}Security tests failed.${NC}"
    fi

    if [ $PERFORMANCE_RESULT -ne 0 ]; then
        echo -e "${RED}Performance tests failed.${NC}"
    fi

    exit 1
fi

#!/bin/bash

# Create a temporary log directory
TEMP_LOG_DIR="/tmp/bahtbrowse_logs"
mkdir -p $TEMP_LOG_DIR
echo "Using temporary log directory: $TEMP_LOG_DIR"

# Run tests for Docker Queue Management
echo "Running tests for Docker Queue Management..."
BAHTBROWSE_LOG_DIR=$TEMP_LOG_DIR python -m pytest tests/docker_queue/ -v --cov=docker_queue --cov-report=term --cov-report=html

# Check exit code
if [ $? -eq 0 ]; then
    echo "All tests passed!"
    echo "Coverage report available in htmlcov/index.html"
else
    echo "Tests failed!"
    exit 1
fi

#!/bin/bash

echo "Testing connection to BahtBrowse service..."
echo "Testing localhost:8082..."
curl -s http://localhost:8082/browse/test-connection | jq .

echo "Testing 127.0.0.1:8082..."
curl -s http://127.0.0.1:8082/browse/test-connection | jq .

echo "Testing GitHub Codespace URL..."
curl -s https://glorious-barnacle-7r9wq9x7vfx74v.github.dev:8082/browse/test-connection || echo "Failed to connect to GitHub Codespace URL"

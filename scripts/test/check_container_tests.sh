#!/bin/bash

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Define container name
CONTAINER_NAME="docker_browser_chromium"

# Check if container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}Container $CONTAINER_NAME is not running.${NC}"
    exit 1
fi

# Wait for tests to complete
echo -e "${YELLOW}Waiting for startup tests to complete...${NC}"
sleep 10

# Check if test results exist
echo -e "${BLUE}Checking test results...${NC}"
if docker exec $CONTAINER_NAME test -f /tmp/test_results/startup_tests.txt; then
    # Copy test results from container
    echo -e "${BLUE}Copying test results from container...${NC}"
    docker cp $CONTAINER_NAME:/tmp/test_results/startup_tests.txt ./startup_tests.txt

    # Check if python and rich are available for pretty output
    if command -v python3 &> /dev/null && python3 -c "import rich" &> /dev/null; then
        # Use Python with Rich to display test results
        python3 - << EOF
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
import re

console = Console()

# Read the test results file
with open('./startup_tests.txt', 'r') as f:
    content = f.read()

# Extract header information
header_match = re.search(r'Date: (.*?)\nContainer: (.*?)\n', content, re.DOTALL)
if header_match:
    date = header_match.group(1)
    container = header_match.group(2)
    console.print(Panel.fit(f"[bold cyan]BahtBrowse Container Test Results[/bold cyan]\n[yellow]Date:[/yellow] {date}\n[yellow]Container:[/yellow] {container}", border_style="blue"))

# Extract test results
test_results = []
for line in content.split('\n'):
    if line.startswith('[PASS]') or line.startswith('[FAIL]'):
        status = line[1:5]
        rest = line[6:].strip()
        name_message = rest.split(':', 1)
        if len(name_message) == 2:
            name = name_message[0].strip()
            message_duration = name_message[1].strip()
            # Extract duration if present
            duration_match = re.search(r'\(([\d\.]+)s\)$', message_duration)
            if duration_match:
                duration = duration_match.group(1)
                message = message_duration[:-len(f"({duration}s)")].strip()
            else:
                duration = "N/A"
                message = message_duration
            test_results.append((status, name, message, duration))

# Create a table for test results
table = Table(title="Test Results")
table.add_column("Status", style="bold")
table.add_column("Test", style="cyan")
table.add_column("Message")
table.add_column("Duration", justify="right")

for status, name, message, duration in test_results:
    status_style = "[green]✓ PASS[/green]" if status == "PASS" else "[red]✗ FAIL[/red]"
    table.add_row(status_style, name, message, f"{duration}s")

console.print(table)

# Extract summary
summary_match = re.search(r'Summary:\nTotal Tests: (\d+)\nPassed: (\d+)\nFailed: (\d+)', content)
if summary_match:
    total = int(summary_match.group(1))
    passed = int(summary_match.group(2))
    failed = int(summary_match.group(3))

    summary_style = "green" if failed == 0 else "red"
    summary = f"[bold {summary_style}]Summary: {passed}/{total} tests passed"
    if failed > 0:
        summary += f", {failed} failed"
    summary += f"[/bold {summary_style}]"

    console.print(Panel(summary, border_style=summary_style))

    if failed > 0:
        failed_tests = [(name, message) for status, name, message, _ in test_results if status == "FAIL"]
        failed_table = Table(title="Failed Tests", border_style="red")
        failed_table.add_column("Test", style="cyan")
        failed_table.add_column("Message")

        for name, message in failed_tests:
            failed_table.add_row(name, message)

        console.print(failed_table)
        exit(1)
    else:
        exit(0)
else:
    console.print("[red]Could not parse summary information[/red]")
    exit(1)
EOF
    else
        # Display test results in plain text
        echo -e "${BLUE}Test Results:${NC}"
        cat ./startup_tests.txt

        # Check if any tests failed
        if grep -q "\[FAIL\]" ./startup_tests.txt; then
            echo -e "\n${RED}Some tests failed!${NC}"
            echo -e "${YELLOW}Failed tests:${NC}"
            grep "\[FAIL\]" ./startup_tests.txt
            exit 1
        else
            echo -e "\n${GREEN}All tests passed!${NC}"
            exit 0
        fi
    fi
else
    echo -e "${RED}Test results not found. Tests may not have completed yet.${NC}"
    echo -e "${YELLOW}Checking container logs...${NC}"
    docker exec $CONTAINER_NAME cat /tmp/logs/startup.log
    docker exec $CONTAINER_NAME cat /tmp/logs/startup_tests_output.log 2>/dev/null || echo -e "${RED}No test output log found.${NC}"
    exit 1
fi

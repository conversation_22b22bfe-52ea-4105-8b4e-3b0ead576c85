#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Start testing
section "TESTING CENTRAL API"

# Build and start the containers
info "Building and starting containers..."
docker compose -f docker/compose/docker-compose.central-api.yml up -d
result $? "Container startup"

# Wait for containers to initialize
info "Waiting for containers to initialize..."
sleep 20

# Test if central API is running
info "Testing if central API is running..."
docker ps | grep bahtbrowse-central-api > /dev/null
result $? "Central API is running"

# Test root endpoint
info "Testing root endpoint..."
curl -s http://localhost:5050/ | grep "name" > /dev/null
result $? "Root endpoint"

# Test status endpoint
info "Testing status endpoint..."
curl -s http://localhost:5050/status | grep "containers" > /dev/null
result $? "Status endpoint"

# Test Firefox on Ubuntu
info "Testing Firefox on Ubuntu..."
curl -s -I "http://localhost:5050/browse/?browser=firefox&os=ubuntu&url=https://example.com" | grep "302 Found" > /dev/null
result $? "Firefox on Ubuntu redirection"

# Test Firefox on Alpine
info "Testing Firefox on Alpine..."
curl -s -I "http://localhost:5050/browse/?browser=firefox&os=alpine&url=https://example.com" | grep "302 Found" > /dev/null
result $? "Firefox on Alpine redirection"

# Test Chromium on Ubuntu
info "Testing Chromium on Ubuntu..."
curl -s -I "http://localhost:5050/browse/?browser=chromium&os=ubuntu&url=https://example.com" | grep "302 Found" > /dev/null
result $? "Chromium on Ubuntu redirection"

# Test Chromium on Alpine
info "Testing Chromium on Alpine..."
curl -s -I "http://localhost:5050/browse/?browser=chromium&os=alpine&url=https://example.com" | grep "302 Found" > /dev/null
result $? "Chromium on Alpine redirection"

# Test direct access
info "Testing direct access..."
curl -s -I "http://localhost:5050/direct/firefox/ubuntu?url=https://example.com" | grep "302 Found" > /dev/null
result $? "Direct access"

# Test test-connection endpoint
info "Testing test-connection endpoint..."
curl -s "http://localhost:5050/browse/test-connection?browser=firefox&os=ubuntu" | grep "success" > /dev/null
result $? "Test connection endpoint"

# Clean up
section "CLEANUP"
info "Stopping containers..."
docker compose -f docker/compose/docker-compose.central-api.yml down
result $? "Container stop"

section "TEST SUMMARY"
echo -e "${GREEN}All tests completed.${NC}"

#!/bin/bash

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Running ungoogled-chromium profile for BahtBrowse...${NC}"

# Check if the container is already running
if docker ps | grep -q docker_browser_chromium; then
    echo -e "${YELLOW}Container is already running. Stopping it...${NC}"
    docker stop docker_browser_chromium
    docker rm docker_browser_chromium
fi

# Run the Docker container
echo -e "${GREEN}Starting container...${NC}"
docker run -it --name=docker_browser_chromium --privileged -p 6080:6080 -p80:80 -p8082:8082 -p 5901:5901 docker_browser_chromium

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Container started successfully!${NC}"
else
    echo -e "${RED}Failed to start container!${NC}"
    exit 1
fi

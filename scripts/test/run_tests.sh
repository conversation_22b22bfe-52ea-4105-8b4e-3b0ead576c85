#!/bin/bash
# Run tests with coverage for the BahtBrowse backend

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Installing test dependencies...${NC}"
pip install -r requirements-dev.txt

echo -e "${YELLOW}Running tests with coverage...${NC}"
pytest --cov=files tests/

# Check if coverage meets our 95% threshold
COVERAGE=$(pytest --cov=files tests/ | grep TOTAL | awk '{print $4}' | sed 's/%//')
if (( $(echo "$COVERAGE >= 95" | bc -l) )); then
    echo -e "${GREEN}Coverage is $COVERAGE%, which meets our 95% requirement!${NC}"
else
    echo -e "${RED}Coverage is only $COVERAGE%, which is below our 95% requirement.${NC}"
    echo -e "${YELLOW}Please add more tests to increase coverage.${NC}"
fi

echo -e "${YELLOW}Generating HTML coverage report...${NC}"
pytest --cov=files --cov-report=html tests/
echo -e "${GREEN}HTML coverage report generated in htmlcov/ directory${NC}"

# Run flake8 to check for code style issues
echo -e "${YELLOW}Running flake8 to check code style...${NC}"
flake8 files/ tests/

echo -e "${YELLOW}Running mypy for type checking...${NC}"
mypy files/

echo -e "${GREEN}Tests completed!${NC}"

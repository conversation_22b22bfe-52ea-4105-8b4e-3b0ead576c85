# bahtBrowse Test Scripts

This directory contains scripts for testing bahtBrowse components.

## Port Configuration Test

The `test_port_configuration.py` script verifies that the services are running on the correct ports:

```bash
python scripts/test/test_port_configuration.py
```

This script will:
1. Check if the services are running
2. Verify that they are accessible on the expected ports
3. Display a summary of the test results

Default ports to test:
- Central API: 5002
- Docker Queue API: 5001
- Redis: 6379

You can specify custom ports to test:

```bash
python scripts/test/test_port_configuration.py --central-api-port=5010 --docker-queue-api-port=5011 --redis-port=6380
```

### Output

The script will output a summary of the test results:

```
=== Test Results ===
Central API: PASS (Port: 5002)
Docker Queue API: PASS (Port: 5001)
Redis: PASS (Port: 6379)
```

If a service is not running or not accessible on the expected port, the test will fail:

```
=== Test Results ===
Central API: FAIL (Port: 5002)
Docker Queue API: PASS (Port: 5001)
Redis: PASS (Port: 6379)
```

### Return Code

The script will return:
- `0` if all tests pass
- `1` if any test fails

This allows you to use the script in CI/CD pipelines or other automated testing environments.

## Usage in CI/CD

You can use the port configuration test script in CI/CD pipelines to verify that the services are running correctly:

```yaml
# Example GitHub Actions workflow
name: Test Services

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Start services
        run: ./scripts/start_services.sh
      - name: Test port configuration
        run: python scripts/test/test_port_configuration.py
```

## Troubleshooting

If the port configuration test fails, try the following:

1. Make sure the services are running:
   ```bash
   ./scripts/start_services.sh
   ```

2. Check if the ports are already in use by other applications:
   ```bash
   sudo lsof -i :<port>
   ```

3. Check the logs for errors:
   ```bash
   cat /tmp/logs/*.log
   ```

4. Try specifying different ports:
   ```bash
   python scripts/test/test_port_configuration.py --central-api-port=5010 --docker-queue-api-port=5011
   ```

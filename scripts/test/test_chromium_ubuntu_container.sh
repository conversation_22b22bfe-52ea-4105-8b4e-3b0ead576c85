#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
section() {
    echo -e "\n${BLUE}========== $1 ==========${NC}\n"
}

# Function to print test results
result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}[PASS]${NC} $2"
    else
        echo -e "${RED}[FAIL]${NC} $2"
    fi
}

# Function to print info messages
info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Start testing
section "TESTING CHROMIUM ON UBUNTU CONTAINER"

# Build the container
info "Building the container..."
docker compose -f docker/compose/docker-compose.chromium-ubuntu-vnc.yml build
result $? "Container build"

# Start the container
info "Starting the container..."
docker compose -f docker/compose/docker-compose.chromium-ubuntu-vnc.yml up -d
result $? "Container start"

# Wait for container to initialize
info "Waiting for container to initialize..."
sleep 10

# Get container name
CONTAINER_NAME="bahtbrowse-browser-chromium-ubuntu"
info "Container name: $CONTAINER_NAME"

# Test if container is running
info "Testing if container is running..."
docker ps | grep $CONTAINER_NAME > /dev/null
result $? "Container is running"

# Test if Chromium is running
info "Testing if Chromium is running..."
docker exec $CONTAINER_NAME ps aux | grep chromium > /dev/null
result $? "Chromium is running"

# Test if VNC server is running
info "Testing if VNC server is running..."
docker exec $CONTAINER_NAME ps aux | grep Xtigervnc > /dev/null
result $? "VNC server is running"

# Test if noVNC proxy is running
info "Testing if noVNC proxy is running..."
docker exec $CONTAINER_NAME ps aux | grep novnc_proxy > /dev/null
result $? "noVNC proxy is running"

# Test if API server is running
info "Testing if API server is running..."
docker exec $CONTAINER_NAME ps aux | grep "python3 /tmp/app.py" > /dev/null
result $? "API server is running"

# Test VNC connection
info "Testing VNC connection..."
curl -s http://localhost:6093/vnc.html > /dev/null
result $? "VNC connection"

# Test API connection
info "Testing API connection..."
curl -s http://localhost:8094/ > /dev/null
result $? "API connection"

# Clean up
section "CLEANUP"
info "Stopping container..."
docker compose -f docker/compose/docker-compose.chromium-ubuntu-vnc.yml down
result $? "Container stop"

section "TEST SUMMARY"
echo -e "${GREEN}All tests completed.${NC}"

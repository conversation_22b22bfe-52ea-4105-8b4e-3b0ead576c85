#!/bin/bash

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create test results directory
mkdir -p test_results

# Define container name
CONTAINER_NAME="docker_browser_chromium"

# Check if container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}Container $CONTAINER_NAME is not running. Starting it...${NC}"
    ./run_chromium.sh &

    # Wait for container to start
    echo -e "${YELLOW}Waiting for container to start...${NC}"
    sleep 10

    if ! docker ps | grep -q $CONTAINER_NAME; then
        echo -e "${RED}Failed to start container. Cannot proceed with tests.${NC}"
        exit 1
    fi
fi

# Initialize combined report
COMBINED_REPORT="test_results/combined_test_report.md"
echo "# BahtBrowse Container Test Report" > $COMBINED_REPORT
echo "" >> $COMBINED_REPORT
echo "Generated: $(date)" >> $COMBINED_REPORT
echo "" >> $COMBINED_REPORT

# Run basic container tests
echo -e "${BLUE}Running basic container tests...${NC}"
chmod +x tests/test_container.sh
./tests/test_container.sh
BASIC_RESULT=$?

# Add basic test results to combined report
echo "## Basic Container Tests" >> $COMBINED_REPORT
if [ $BASIC_RESULT -eq 0 ]; then
    echo "✅ **PASSED**" >> $COMBINED_REPORT
else
    echo "❌ **FAILED**" >> $COMBINED_REPORT
fi
echo "" >> $COMBINED_REPORT
echo "See [detailed report](test_report.txt) for more information." >> $COMBINED_REPORT
echo "" >> $COMBINED_REPORT

# Run functional tests
echo -e "${BLUE}Running functional tests...${NC}"
python3 tests/test_functional.py
FUNCTIONAL_RESULT=$?

# Add functional test results to combined report
echo "## Functional Tests" >> $COMBINED_REPORT
if [ $FUNCTIONAL_RESULT -eq 0 ]; then
    echo "✅ **PASSED**" >> $COMBINED_REPORT
else
    echo "❌ **FAILED**" >> $COMBINED_REPORT
fi
echo "" >> $COMBINED_REPORT
echo "See [detailed report](functional_test_report.txt) for more information." >> $COMBINED_REPORT
echo "" >> $COMBINED_REPORT

# Run locale tests
echo -e "${BLUE}Running locale tests...${NC}"
python3 tests/test_locale.py
LOCALE_RESULT=$?

# Add locale test results to combined report
echo "## Locale Tests" >> $COMBINED_REPORT
if [ $LOCALE_RESULT -eq 0 ]; then
    echo "✅ **PASSED**" >> $COMBINED_REPORT
else
    echo "❌ **FAILED**" >> $COMBINED_REPORT
fi
echo "" >> $COMBINED_REPORT
echo "See [detailed report](locale_test_report.txt) for more information." >> $COMBINED_REPORT
echo "" >> $COMBINED_REPORT

# Run security tests
echo -e "${BLUE}Running security tests...${NC}"
python3 tests/test_security.py
SECURITY_RESULT=$?

# Add security test results to combined report
echo "## Security Tests" >> $COMBINED_REPORT
if [ $SECURITY_RESULT -eq 0 ]; then
    echo "✅ **PASSED**" >> $COMBINED_REPORT
else
    echo "❌ **FAILED**" >> $COMBINED_REPORT
fi
echo "" >> $COMBINED_REPORT
echo "See [detailed report](security_test_report.txt) for more information." >> $COMBINED_REPORT
echo "" >> $COMBINED_REPORT

# Calculate overall result
if [ $BASIC_RESULT -eq 0 ] && [ $FUNCTIONAL_RESULT -eq 0 ] && [ $LOCALE_RESULT -eq 0 ] && [ $SECURITY_RESULT -eq 0 ]; then
    OVERALL_RESULT=0
    echo "## Overall Result" >> $COMBINED_REPORT
    echo "✅ **ALL TESTS PASSED**" >> $COMBINED_REPORT
else
    OVERALL_RESULT=1
    echo "## Overall Result" >> $COMBINED_REPORT
    echo "❌ **SOME TESTS FAILED**" >> $COMBINED_REPORT

    # List failed test categories
    echo "" >> $COMBINED_REPORT
    echo "### Failed Test Categories:" >> $COMBINED_REPORT
    if [ $BASIC_RESULT -ne 0 ]; then
        echo "- Basic Container Tests" >> $COMBINED_REPORT
    fi
    if [ $FUNCTIONAL_RESULT -ne 0 ]; then
        echo "- Functional Tests" >> $COMBINED_REPORT
    fi
    if [ $LOCALE_RESULT -ne 0 ]; then
        echo "- Locale Tests" >> $COMBINED_REPORT
    fi
    if [ $SECURITY_RESULT -ne 0 ]; then
        echo "- Security Tests" >> $COMBINED_REPORT
    fi
fi

# Print summary
echo -e "\n${YELLOW}Test Summary:${NC}"
echo -e "Basic Container Tests: $([ $BASIC_RESULT -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "Functional Tests: $([ $FUNCTIONAL_RESULT -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "Locale Tests: $([ $LOCALE_RESULT -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "Security Tests: $([ $SECURITY_RESULT -eq 0 ] && echo -e "${GREEN}PASSED${NC}" || echo -e "${RED}FAILED${NC}")"
echo -e "\nOverall Result: $([ $OVERALL_RESULT -eq 0 ] && echo -e "${GREEN}ALL TESTS PASSED${NC}" || echo -e "${RED}SOME TESTS FAILED${NC}")"
echo -e "Combined report saved to ${COMBINED_REPORT}"

exit $OVERALL_RESULT

#!/bin/bash

# Check if ImageMagick is installed
if command -v convert &> /dev/null; then
    echo "Generating placeholder icons using ImageMagick..."

    # Create icons in different sizes
    convert -size 19x19 xc:none -fill "#0060df" -draw "circle 9.5,9.5 9.5,1" -fill white -draw "text 7,13 'B'" firefox_plugin/icons/bahtbrowse-19.png
    convert -size 38x38 xc:none -fill "#0060df" -draw "circle 19,19 19,2" -fill white -draw "text 14,26 'B'" firefox_plugin/icons/bahtbrowse-38.png
    convert -size 48x48 xc:none -fill "#0060df" -draw "circle 24,24 24,2" -fill white -draw "text 18,32 'B'" firefox_plugin/icons/bahtbrowse-48.png
    convert -size 96x96 xc:none -fill "#0060df" -draw "circle 48,48 48,4" -fill white -draw "text 36,64 'B'" firefox_plugin/icons/bahtbrowse-96.png

    echo "Icons generated successfully!"
else
    echo "ImageMagick not found. Please install it or create icons manually."
    echo "Required icon sizes: 19x19, 38x38, 48x48, 96x96"
    echo "Save them as firefox_plugin/icons/bahtbrowse-{size}.png"
fi

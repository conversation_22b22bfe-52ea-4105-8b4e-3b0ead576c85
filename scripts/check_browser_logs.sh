#!/bin/bash

# Script to check browser console logs from BahtBrowse

# Check if <PERSON><PERSON> is running
if ! docker ps &>/dev/null; then
    echo "Error: Docker is not running or you don't have permission to access it."
    exit 1
fi

# Check if the BahtBrowse container is running
if ! docker ps | grep -q "docker_browser"; then
    echo "Error: BahtBrowse container (docker_browser) is not running."
    exit 1
fi

# Function to display logs with colors
display_logs() {
    local log_file=$1
    local lines=${2:-50}

    # Check if the log file exists in the container
    if ! docker exec docker_browser test -f "$log_file"; then
        echo "Log file $log_file does not exist in the container."
        return 1
    fi

    # Get the logs with colors
    docker exec docker_browser cat "$log_file" | tail -n "$lines" | \
    sed -E 's/\[ERROR\]|\[BROWSER CONSOLE\] \[ERROR\]/\x1B[31m&\x1B[0m/g' | \
    sed -E 's/\[WARN\]|\[WARNING\]|\[BROWSER CONSOLE\] \[WARN\]|\[BROWSER CONSOLE\] \[WARNING\]/\x1B[33m&\x1B[0m/g' | \
    sed -E 's/\[INFO\]|\[BROWSER CONSOLE\] \[INFO\]/\x1B[32m&\x1B[0m/g' | \
    sed -E 's/\[DEBUG\]|\[BROWSER CONSOLE\] \[DEBUG\]/\x1B[36m&\x1B[0m/g'
}

# Parse command line arguments
LINES=50
LOG_FILE="/tmp/browser_console.log"
SERVER_LOG="/tmp/bahtbrowse_server.log"

while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -s|--server)
            LOG_FILE="$SERVER_LOG"
            shift
            ;;
        -b|--browser)
            LOG_FILE="/tmp/browser_console.log"
            shift
            ;;
        -a|--all)
            echo "=== BahtBrowse Server Log ==="
            display_logs "$SERVER_LOG" "$LINES"
            echo -e "\n=== Browser Console Log ==="
            display_logs "/tmp/browser_console.log" "$LINES"
            exit 0
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Display BahtBrowse browser console logs"
            echo ""
            echo "Options:"
            echo "  -n, --lines NUMBER    Display the last NUMBER lines (default: 50)"
            echo "  -s, --server          Display server logs instead of browser console logs"
            echo "  -b, --browser         Display browser console logs (default)"
            echo "  -a, --all             Display both server and browser console logs"
            echo "  -h, --help            Display this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Display the logs
echo "Displaying the last $LINES lines of $LOG_FILE:"
display_logs "$LOG_FILE" "$LINES"

exit 0

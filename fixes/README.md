# BahtBrowse Redirect Loop Fix

This directory contains fixes for the redirect loop issue in BahtBrowse.

## Issue Description

When a user clicks the BahtBrowse button in Firefox, it should copy the currently active URL and open it in a BahtBrowse session using the format `localhost:8081/browse/?url=URLGOESHERE`. However, there was a redirect loop issue where the browser would continuously redirect between different URLs.

## Root Cause

The root cause of the issue was that the app.py file was redirecting to a URL with a port number (8083), but the nginx configuration was not properly handling this redirect. This resulted in a redirect loop.

## Fix

The fix consists of the following components:

1. **novnc_proxy.html**: A custom HTML page that handles the redirect to the VNC session properly.
2. **ui_rfb_fix.js**: A JavaScript file that fixes issues with the UI.rfb object in noVNC.
3. **nginx/alpine_nginx.conf**: A custom nginx configuration for the Alpine container.
4. **apply_redirect_fix.sh**: A script to apply the fixes to the Alpine container.

## How to Apply the Fix

To apply the fix, run the following command:

```bash
./fixes/apply_redirect_fix.sh
```

This script will:
1. Check if the Alpine container is running
2. Copy the necessary files to the container
3. Fix the app.py file to use the correct redirect URL
4. Update the nginx configuration
5. Restart nginx and the API server
6. Test the fixes

## Testing

After applying the fix, you can test it by:

1. Starting the BahtBrowse service
2. Opening Firefox with the BahtBrowse extension installed
3. Navigating to a website
4. Clicking the BahtBrowse button in Firefox
5. Verifying that the website opens in the BahtBrowse session without any redirect loops

## Files

- **novnc_proxy.html**: Custom HTML page for handling redirects
- **ui_rfb_fix.js**: JavaScript fix for UI.rfb issues in noVNC
- **nginx/alpine_nginx.conf**: Custom nginx configuration for Alpine container
- **apply_redirect_fix.sh**: Script to apply the fixes
- **README.md**: This documentation file

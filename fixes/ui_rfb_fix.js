/**
 * Fix for UI.rfb undefined issue in noVNC
 * 
 * This script patches the UI object to ensure UI.rfb is properly initialized
 * and handles cases where it becomes undefined.
 */

(function() {
    console.log("UI.rfb fix script loaded");
    
    // Wait for the UI object to be defined
    function waitForUI(callback, maxAttempts = 20) {
        let attempts = 0;
        
        function checkUI() {
            attempts++;
            if (attempts > maxAttempts) {
                console.error("Gave up waiting for UI object");
                return;
            }
            
            if (window.UI) {
                callback();
            } else {
                setTimeout(checkUI, 500);
            }
        }
        
        checkUI();
    }
    
    // Fix UI.rfb undefined issue
    function fixUIRfb() {
        console.log("Checking UI.rfb status");
        
        // If UI.rfb is already defined, nothing to do
        if (window.UI && window.UI.rfb) {
            console.log("UI.rfb is already defined");
            return;
        }
        
        console.warn("UI.rfb is undefined, attempting to fix");
        
        // Try to reconnect
        if (window.UI && window.UI.connect) {
            console.log("Calling UI.connect()");
            try {
                window.UI.connect();
                
                // Check if it worked
                setTimeout(() => {
                    if (window.UI && window.UI.rfb) {
                        console.log("Successfully created UI.rfb");
                    } else {
                        console.error("Failed to create UI.rfb");
                    }
                }, 1000);
            } catch (err) {
                console.error("Error calling UI.connect():", err);
            }
        } else {
            console.error("UI.connect is not defined");
        }
    }
    
    // Patch UI methods to handle undefined rfb
    function patchUIMethods() {
        if (!window.UI) return;
        
        console.log("Patching UI methods");
        
        // Methods that use UI.rfb
        const methods = [
            'setDesktopSize',
            'clipboardPasteFrom',
            'toggleViewOnly',
            'sendCtrlAltDel',
            'setCredentials',
            'machineShutdown',
            'machineReboot',
            'machineReset',
            'toggleFullscreen'
        ];
        
        methods.forEach(method => {
            if (typeof window.UI[method] === 'function') {
                const original = window.UI[method];
                
                window.UI[method] = function() {
                    if (!window.UI.rfb) {
                        console.error(`UI.rfb is undefined when calling ${method}`);
                        fixUIRfb();
                        
                        // If rfb is still undefined, throw a more helpful error
                        if (!window.UI.rfb) {
                            throw new Error(`UI.rfb is undefined when calling ${method}. Try refreshing the page.`);
                        }
                    }
                    
                    return original.apply(this, arguments);
                };
                
                console.log(`Patched UI.${method}`);
            }
        });
    }
    
    // Patch the UI.rfb property to prevent it from becoming undefined
    function patchUIRfbProperty() {
        if (!window.UI) return;
        
        console.log("Patching UI.rfb property");
        
        let rfbValue = window.UI.rfb;
        
        // Define a getter and setter for UI.rfb
        Object.defineProperty(window.UI, 'rfb', {
            get: function() {
                return rfbValue;
            },
            set: function(newValue) {
                console.log("UI.rfb is being set to:", newValue ? "defined" : "undefined");
                rfbValue = newValue;
                
                // If rfb is being set to undefined, log a warning
                if (!newValue) {
                    console.warn("UI.rfb is being set to undefined");
                    console.trace();
                }
            },
            configurable: true
        });
        
        console.log("UI.rfb property patched");
    }
    
    // Initialize the fix
    waitForUI(() => {
        console.log("UI object found, applying fixes");
        fixUIRfb();
        patchUIMethods();
        patchUIRfbProperty();
        
        // Monitor for future issues
        setInterval(() => {
            if (window.UI && !window.UI.rfb) {
                console.warn("UI.rfb has become undefined");
                fixUIRfb();
            }
        }, 5000);
    });
    
    // Add a global function to manually fix UI.rfb
    window.fixUIRfb = fixUIRfb;
    
    console.log("UI.rfb fix initialized");
})();

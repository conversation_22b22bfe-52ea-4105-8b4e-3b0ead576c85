<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BahtBrowse - noVNC Proxy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #f0f0f0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        .logo span {
            color: #2ecc71;
        }
        main {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        .loading {
            text-align: center;
        }
        .spinner {
            border: 5px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 5px solid #2ecc71;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <script src="/ui_rfb_fix.js"></script>
    <script>
        // Extract session ID from URL
        function getSessionId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('session');
        }
        
        // Redirect to the VNC session
        function redirectToVNC() {
            const sessionId = getSessionId();
            if (sessionId) {
                // Add timestamp to prevent caching
                const timestamp = Date.now();
                window.location.href = `/vnc1/run?session=${sessionId}&_ts=${timestamp}`;
            } else {
                document.getElementById('message').textContent = 'Error: No session ID found in URL';
            }
        }
        
        // Wait for page to load then redirect
        window.onload = function() {
            // Short delay to ensure everything is loaded
            setTimeout(redirectToVNC, 1000);
        };
    </script>
</head>
<body>
    <header>
        <div class="logo">10Baht <span>bahtBrowse</span></div>
    </header>
    <main>
        <div class="loading">
            <h2>Connecting to your secure browsing session...</h2>
            <div class="spinner"></div>
            <p id="message">Please wait while we establish your connection.</p>
        </div>
    </main>
</body>
</html>

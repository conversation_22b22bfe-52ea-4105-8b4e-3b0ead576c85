#!/bin/bash
# Script to fix the redirect loop issue in BahtBrowse

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Fixing redirect loop issue in BahtBrowse${NC}"

# Check if the container is running
echo -e "${YELLOW}Checking if container is running...${NC}"
if ! docker ps | grep -q "compose-browser_firefox_alpine-1"; then
    echo -e "${RED}Error: Alpine container is not running${NC}"
    echo -e "${YELLOW}Starting Alpine container...${NC}"
    docker compose -f docker/compose/docker-compose.firefox-alpine.yml up -d
    sleep 5
fi

# Copy the files to the Alpine container
echo -e "${YELLOW}Copying files to Alpine container...${NC}"
docker exec compose-browser_firefox_alpine-1 mkdir -p /tmp/serve /var/www/html
docker cp fixes/novnc_proxy.html compose-browser_firefox_alpine-1:/tmp/serve/
docker cp fixes/ui_rfb_fix.js compose-browser_firefox_alpine-1:/var/www/html/

# Fix the app.py file in the Alpine container
echo -e "${YELLOW}Fixing app.py in Alpine container...${NC}"
docker exec compose-browser_firefox_alpine-1 sed -i 's|f"http://{host_without_port}:8083/novnc_proxy.html|f"http://{host_without_port}/novnc_proxy.html|g' /tmp/app.py

# Update the nginx configuration in the Alpine container
echo -e "${YELLOW}Updating nginx configuration in Alpine container...${NC}"
docker cp fixes/nginx/alpine_nginx.conf compose-browser_firefox_alpine-1:/etc/nginx/http.d/default.conf

# Restart nginx in the Alpine container
echo -e "${YELLOW}Restarting nginx in Alpine container...${NC}"
docker exec compose-browser_firefox_alpine-1 nginx -s reload

# Restart the API server in the Alpine container
echo -e "${YELLOW}Restarting API server in Alpine container...${NC}"
docker exec compose-browser_firefox_alpine-1 bash -c "pkill -f 'python3.*app.py' || true"
docker exec -d compose-browser_firefox_alpine-1 bash -c "python3 /tmp/app.py > /tmp/app.log 2>&1 &"

echo -e "${GREEN}Fixes applied successfully${NC}"
echo -e "${YELLOW}Testing the fixes...${NC}"

# Test the fixes
echo -e "${YELLOW}Testing Alpine container...${NC}"
curl -I http://localhost:8002/novnc_proxy.html
curl -I http://localhost:8002/ui_rfb_fix.js

echo -e "${GREEN}All fixes have been applied and tested${NC}"

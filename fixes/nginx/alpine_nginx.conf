server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /tmp/serve;
    index index.html;

    server_name _;

    # Proxy HTML page - direct access without redirects
    location = /novnc_proxy.html {
        root /tmp/serve;
        try_files $uri =404;
        
        # Cache control headers
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # UI.rfb fix script
    location = /ui_rfb_fix.js {
        root /var/www/html;
        try_files $uri =404;
        
        # Cache control headers
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    location / {
        try_files $uri $uri/ =404;
    }

    location /vnc {
        alias /tmp/noVNC;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    location /downloads {
        alias /tmp/downloads;
        autoindex on;
        try_files $uri $uri/ =404;
    }

    location /api {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

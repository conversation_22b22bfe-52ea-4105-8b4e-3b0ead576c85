BahtBrowse Container Startup Test Report
=======================================
Date: 2025-04-25 11:21:03
Container: d9a43b29de77

Test Results:
[PASS] VNC Server Running: VNC server is running (0.002s)
[PASS] noVNC Proxy Running: noVNC proxy is running (0.002s)
[PASS] Window Manager Running: Window manager is running (0.002s)
[PASS] API Server Running: API server is running (0.002s)
[PASS] VNC Port Open: VNC port 5901 is open (0.0s)
[PASS] noVNC Port Open: noVNC port 6080 is open (0.0s)
[PASS] HTTP Port Open: HTTP port 80 is open (0.0s)
[PASS] API Port Open: API port 8082 is open (0.0s)
[PASS] Landing Page Accessible: Landing page is accessible and contains 'BahtBrowse' (0.012s)
[PASS] noVNC Accessible: noVNC interface is accessible (0.003s)
[PASS] API Accessible: API is accessible and returns success (0.001s)
[PASS] Landing Page Redirect: Landing page contains meta refresh redirect to noVNC (0.0s)
[PASS] Chromium Installed: Chromium is installed at /usr/bin/chromium (0.001s)
[PASS] Downloads Directory: Downloads directory exists and is writable (0.0s)
[PASS] VNC Password Set: VNC password file exists in root home (0.0s)

Summary:
Total Tests: 15
Passed: 15
Failed: 0

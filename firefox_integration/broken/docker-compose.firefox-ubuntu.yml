version: '3'

services:
  firefox-ubuntu:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8001:80"      # HTTP port
      - "5901:5901"    # VNC port
      - "6081:6080"    # noVNC web interface
      - "8082:8082"    # API server
    environment:
      - DISPLAY=:1
      - LANG=en_US.UTF-8
      - LC_ALL=en_US.UTF-8
      - BROWSER_TYPE=firefox
      - BROWSER_ARGS=--no-sandbox
      - SESSION_TIMEOUT=3600
      - VNC_PASSWORD=123456
    volumes:
      - ./files/app.py:/tmp/app.py
      - ./files/nginx_default:/etc/nginx/sites-available/default
      - ./files/session_manager.py:/tmp/session_manager.py
      - ./files/session_validator.py:/tmp/session_validator.py
      - ./files/startup_tests.py:/tmp/startup_tests.py
      - ./files/fix_locales.sh:/tmp/fix_locales.sh
      - ./files/startwm.sh:/tmp/startwm.sh
      - ./files/vnc.html:/tmp/noVNC/vnc.html
      - ./files/user.js:/etc/firefox/user.js
      - ./files/chromium_preferences.json:/tmp/chromium_preferences.json
      - ./files/console_logger.js:/tmp/console_logger.js
      - ./files/custom.css:/tmp/custom.css
      - ./files/vibur.css:/tmp/vibur.css
      - ./files/xkb.conf:/tmp/xkb.conf
    restart: unless-stopped
    shm_size: 2gb
    cap_add:
      - SYS_ADMIN
    security_opt:
      - seccomp=unconfined

# BahtBrowse Firefox Plugin v1.0

## Release Information
- Version: 1.0
- Release Date: 2025-04-23

## Installation Instructions

### Method 1: Create and Install XPI File
1. Zip all the files in this directory (excluding this RELEASE_NOTES.md file)
2. Rename the zip file to have a .xpi extension (e.g., bahtbrowse_firefox_plugin.xpi)
3. Open Firefox
4. Go to `about:addons` (type this in the address bar)
5. Click the gear icon and select "Install Add-on From File..."
6. Navigate to the XPI file you created and select it
7. Follow the prompts to complete installation

### Method 2: Developer Mode (Temporary Installation)
1. Open Firefox
2. Go to `about:debugging` (type this in the address bar)
3. Click "This Firefox"
4. Click "Load Temporary Add-on..."
5. Navigate to this directory and select the `manifest.json` file
6. The extension will be loaded temporarily (until Firefox is restarted)

## Features
- One-click redirection from Firefox to BahtBrowse
- Context menu integration for links and pages
- Keyboard shortcut (Alt+B) for quick access
- Configurable settings for host, port, and behavior
- Whitelist/blacklist functionality for URL filtering
- Browser notifications for user feedback

## Configuration
After installation, configure the plugin:
1. Right-click on the BahtBrowse icon in the toolbar
2. Select "Manage Extension"
3. Click on "Options" or "Preferences"
4. Set the BahtBrowse host and port (default is localhost:8081)
5. Adjust other settings as needed
6. Click "Save Settings"

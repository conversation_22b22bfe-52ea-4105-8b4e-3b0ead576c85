# BahtBrowse Firefox Plugin Installation Guide

This guide will help you install and configure the BahtBrowse Firefox Plugin.

## Prerequisites

- Firefox browser (version 78.0 or later)
- A running BahtBrowse instance

## Installation Methods

### Method 1: Install from Firefox Add-ons (Recommended)

1. Open Firefox
2. Go to [Firefox Add-ons Marketplace](https://addons.mozilla.org/en-US/firefox/)
3. Search for "BahtBrowse Redirector"
4. Click "Add to Firefox"
5. Follow the prompts to complete installation

### Method 2: Install from XPI file

1. Download the latest `.xpi` file from the [Releases page](https://github.com/tenbahtsecurity/bahtbrowse/releases)
2. Open Firefox
3. Go to `about:addons` (type this in the address bar)
4. Click the gear icon and select "Install Add-on From File..."
5. Navigate to the downloaded `.xpi` file and select it
6. Follow the prompts to complete installation

### Method 3: Install for Development

1. Clone the repository or download the source code
2. Open Firefox
3. Go to `about:debugging` (type this in the address bar)
4. Click "This Firefox"
5. Click "Load Temporary Add-on..."
6. Navigate to the `firefox_plugin` directory and select the `manifest.json` file
7. The extension will be loaded temporarily (until Firefox is restarted)

## Configuration

After installation, you should configure the plugin to connect to your BahtBrowse instance:

1. Right-click on the BahtBrowse icon in the Firefox toolbar
2. Select "Manage Extension"
3. Click on "Options" or "Preferences"
4. Set the following:
   - BahtBrowse Host: The hostname or IP address of your BahtBrowse instance (default: localhost)
   - BahtBrowse Port: The port number of your BahtBrowse instance (default: 8081)
   - Other preferences as desired
5. Click "Save Settings"

## Usage

### Basic Usage

1. Navigate to any webpage in Firefox
2. Click the BahtBrowse icon in the toolbar
3. The current page will be opened in BahtBrowse

### Keyboard Shortcut

- Press `Alt+B` to open the current page in BahtBrowse

### Context Menu

- Right-click on any webpage or link
- Select "Open in BahtBrowse" from the context menu

## Troubleshooting

### Plugin Not Working

1. Check that your BahtBrowse instance is running
2. Verify the host and port settings in the plugin options
3. Try restarting Firefox

### Cannot Connect to BahtBrowse

1. Ensure BahtBrowse is running on the specified host and port
2. Check for any network restrictions or firewalls
3. Try using "localhost" instead of an IP address if running locally

### Other Issues

For other issues, please submit a bug report on the [GitHub repository](https://github.com/tenbahtsecurity/bahtbrowse/issues).

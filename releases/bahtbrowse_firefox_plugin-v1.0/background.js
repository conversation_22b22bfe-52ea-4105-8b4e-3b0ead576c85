// Background script for BahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true
};

// Initialize settings
console.log('BahtBrowse Redirector initializing...');

browser.runtime.onInstalled.addListener(() => {
  console.log('Extension installed or updated');
  browser.storage.sync.get('settings').then((result) => {
    if (!result.settings) {
      console.log('No settings found, using defaults');
      browser.storage.sync.set({ settings: DEFAULT_SETTINGS })
        .then(() => console.log('Default settings saved'))
        .catch(error => console.error('Error saving default settings:', error));
    } else {
      console.log('Existing settings found:', result.settings);
    }
    // Show welcome notification
    showNotification('BahtBrowse Redirector Installed', 'Click the toolbar button to redirect pages to BahtBrowse');
  }).catch(error => {
    console.error('Error getting settings during initialization:', error);
  });
});

// Log when extension is loaded
console.log('BahtBrowse Redirector loaded, version:', browser.runtime.getManifest().version);

// Add context menu item
browser.contextMenus.create({
  id: 'open-in-bahtbrowse',
  title: 'Open in BahtBrowse',
  contexts: ['link', 'page']
});

// Handle context menu clicks
browser.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'open-in-bahtbrowse') {
    // If it's a link context, use the link URL, otherwise use the page URL
    const url = info.linkUrl || tab.url;
    redirectToBahtBrowse(tab, url);
  }
});

// Handle toolbar button click
browser.browserAction.onClicked.addListener((tab) => {
  redirectToBahtBrowse(tab, tab.url);
});

// Function to redirect URL to BahtBrowse
function redirectToBahtBrowse(tab, url) {
  console.log('Redirecting to BahtBrowse:', url);

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    console.log('Using settings:', settings);

    // Check if URL should be redirected based on whitelist/blacklist
    if (shouldRedirect(url, settings)) {
      const bahtbrowseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(url)}`;
      console.log('Redirect URL:', bahtbrowseUrl);

      try {
        if (settings.openInNewTab) {
          console.log('Opening in new tab');
          browser.tabs.create({ url: bahtbrowseUrl })
            .then(newTab => {
              console.log('New tab created:', newTab);
            })
            .catch(error => {
              console.error('Error creating new tab:', error);
              alert(`Error opening in new tab: ${error.message}`);
            });
        } else {
          console.log('Updating current tab');
          browser.tabs.update(tab.id, { url: bahtbrowseUrl })
            .then(updatedTab => {
              console.log('Tab updated:', updatedTab);
            })
            .catch(error => {
              console.error('Error updating tab:', error);
              alert(`Error updating tab: ${error.message}`);
            });
        }

        // Show notification if enabled
        if (settings.showNotifications) {
          showNotification('Redirected to BahtBrowse', `Opening ${url} in BahtBrowse`);
        }
      } catch (error) {
        console.error('Error during redirection:', error);
        alert(`Error during redirection: ${error.message}`);
      }
    } else {
      console.log('URL should not be redirected due to whitelist/blacklist settings');
    }
  }).catch(error => {
    console.error('Error getting settings:', error);
    alert(`Error getting settings: ${error.message}`);
  });
}

// Function to show browser notifications
function showNotification(title, message) {
  try {
    console.log('Showing notification:', title, message);
    browser.notifications.create({
      type: 'basic',
      iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
      title: title,
      message: message
    }).then(notificationId => {
      console.log('Notification shown, ID:', notificationId);
    }).catch(error => {
      console.error('Error showing notification:', error);
      // Fallback to console if notifications fail
      console.warn(`Notification (fallback): ${title} - ${message}`);
    });
  } catch (error) {
    console.error('Error in showNotification:', error);
    // Fallback to console if notifications fail
    console.warn(`Notification (fallback): ${title} - ${message}`);
  }
}

// Function to check if URL should be redirected based on whitelist/blacklist
function shouldRedirect(url, settings) {
  console.log('Checking if URL should be redirected:', url);
  console.log('Whitelist enabled:', settings.enableWhitelist, 'Whitelist:', settings.whitelist);
  console.log('Blacklist enabled:', settings.enableBlacklist, 'Blacklist:', settings.blacklist);

  // If whitelist is enabled, only redirect if URL is in whitelist
  if (settings.enableWhitelist && settings.whitelist.length > 0) {
    const shouldRedirect = settings.whitelist.some(pattern => url.includes(pattern));
    console.log('Whitelist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If blacklist is enabled, don't redirect if URL is in blacklist
  if (settings.enableBlacklist && settings.blacklist.length > 0) {
    const shouldRedirect = !settings.blacklist.some(pattern => url.includes(pattern));
    console.log('Blacklist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If neither whitelist nor blacklist is enabled, always redirect
  console.log('No whitelist/blacklist enabled, should redirect');
  return true;
}

# BahtBrowse Firefox Plugin

A Firefox extension that allows users to quickly redirect their current browsing session to the BahtBrowse containerized browser environment for enhanced security.

## Features

- One-click redirection to BahtBrowse
- Configurable BahtBrowse host and port
- Option to open in new tab or current tab
- Whitelist and blacklist functionality for URL filtering

## Development

### Prerequisites

- Firefox Developer Edition or Firefox Browser
- Basic knowledge of JavaScript and browser extensions

### Installation for Development

1. Clone this repository
2. Open Firefox and navigate to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on"
5. Select the `manifest.json` file from the cloned repository

### Building the Extension

To build the extension for distribution:

1. Zip the contents of the extension directory (excluding any development files)
2. Rename the zip file to have a `.xpi` extension

## Usage

1. Click the BahtBrowse icon in the Firefox toolbar
2. Confirm that you want to redirect the current page to BahtBrowse
3. The page will open in the BahtBrowse containerized environment

## Configuration

Access the extension settings by:
1. Right-clicking the toolbar icon and selecting "Manage Extension"
2. Going to Add-ons Manager (`about:addons`) and finding BahtBrowse Redirector
3. Clicking on "Options" or "Preferences"

Settings include:
- BahtBrowse host and port
- Opening behavior (new tab or current tab)
- URL filtering with whitelist and blacklist

## License

[MIT License](LICENSE)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

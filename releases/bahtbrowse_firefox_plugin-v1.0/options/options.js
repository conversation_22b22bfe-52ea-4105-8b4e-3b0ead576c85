// Options script for BahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true
};

// Load settings when options page is opened
document.addEventListener('DOMContentLoaded', loadSettings);

// Add event listeners to buttons
document.getElementById('saveButton').addEventListener('click', saveSettings);
document.getElementById('resetButton').addEventListener('click', resetSettings);

// Function to load settings from storage
function loadSettings() {
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    // Populate form fields with current settings
    document.getElementById('bahtbrowseHost').value = settings.bahtbrowseHost;
    document.getElementById('bahtbrowsePort').value = settings.bahtbrowsePort;
    document.getElementById('openInNewTab').checked = settings.openInNewTab;
    document.getElementById('showNotifications').checked = settings.showNotifications;
    document.getElementById('enableWhitelist').checked = settings.enableWhitelist;
    document.getElementById('whitelist').value = settings.whitelist.join('\n');
    document.getElementById('enableBlacklist').checked = settings.enableBlacklist;
    document.getElementById('blacklist').value = settings.blacklist.join('\n');
  });
}

// Function to save settings to storage
function saveSettings() {
  const settings = {
    bahtbrowseHost: document.getElementById('bahtbrowseHost').value.trim() || DEFAULT_SETTINGS.bahtbrowseHost,
    bahtbrowsePort: document.getElementById('bahtbrowsePort').value.trim() || DEFAULT_SETTINGS.bahtbrowsePort,
    openInNewTab: document.getElementById('openInNewTab').checked,
    showNotifications: document.getElementById('showNotifications').checked,
    enableWhitelist: document.getElementById('enableWhitelist').checked,
    whitelist: document.getElementById('whitelist').value.split('\n').map(line => line.trim()).filter(line => line),
    enableBlacklist: document.getElementById('enableBlacklist').checked,
    blacklist: document.getElementById('blacklist').value.split('\n').map(line => line.trim()).filter(line => line)
  };

  browser.storage.sync.set({ settings }).then(() => {
    // Show success message
    const statusElement = document.getElementById('status');
    statusElement.textContent = 'Settings saved successfully!';
    statusElement.className = 'status success';

    // Hide message after 3 seconds
    setTimeout(() => {
      statusElement.className = 'status';
    }, 3000);
  });
}

// Function to reset settings to defaults
function resetSettings() {
  browser.storage.sync.set({ settings: DEFAULT_SETTINGS }).then(() => {
    loadSettings();

    // Show success message
    const statusElement = document.getElementById('status');
    statusElement.textContent = 'Settings reset to defaults!';
    statusElement.className = 'status success';

    // Hide message after 3 seconds
    setTimeout(() => {
      statusElement.className = 'status';
    }, 3000);
  });
}

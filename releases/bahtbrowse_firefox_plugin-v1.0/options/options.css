body {
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f9f9fa;
  color: #0c0c0d;
}

.container {
  max-width: 600px;
  margin: 0 auto;
}

h1 {
  font-size: 22px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #d7d7db;
}

h2 {
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
}

.section {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
}

.form-group.checkbox label {
  margin-bottom: 0;
  margin-left: 8px;
}

input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #d7d7db;
  border-radius: 2px;
  font-size: 14px;
}

textarea {
  width: 100%;
  height: 80px;
  padding: 8px;
  border: 1px solid #d7d7db;
  border-radius: 2px;
  font-size: 14px;
  resize: vertical;
}

.buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

button {
  background-color: #0060df;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 2px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #003eaa;
}

#resetButton {
  background-color: #f9f9fa;
  color: #0060df;
  border: 1px solid #0060df;
}

#resetButton:hover {
  background-color: #e7e7e7;
}

.status {
  margin-top: 15px;
  padding: 10px;
  border-radius: 2px;
  text-align: center;
  display: none;
}

.status.success {
  background-color: #e6f4ea;
  color: #137333;
  display: block;
}

.status.error {
  background-color: #fce8e6;
  color: #c5221f;
  display: block;
}

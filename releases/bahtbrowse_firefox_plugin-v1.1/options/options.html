<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css">
  <link href="https://fonts.googleapis.com/css2?family=Vibur&display=swap" rel="stylesheet">
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo-container">
        <img src="../icons/bahtbrowse-48.png" alt="BahtBrowse Logo" class="logo">
      </div>
      <h1 class="title">BahtBrowse Preferences</h1>
    </div>

    <form id="optionsForm">
      <div class="form-group">
        <label for="bahtbrowseUrl">
          <img src="../icons/feather/globe.svg" class="form-icon" alt="URL">
          BahtBrowse URL:
        </label>
        <input type="url" id="bahtbrowseUrl" name="bahtbrowseUrl" placeholder="http://localhost:8001">
        <p class="help-text">The URL of your BahtBrowse instance</p>
      </div>

      <div class="form-group">
        <label for="connectionTimeout">
          <img src="../icons/feather/clock.svg" class="form-icon" alt="Timeout">
          Connection Timeout (ms):
        </label>
        <input type="number" id="connectionTimeout" name="connectionTimeout" min="1000" max="30000" step="1000">
        <p class="help-text">Timeout for connection tests in milliseconds</p>
      </div>

      <div class="form-group checkbox">
        <input type="checkbox" id="showNotifications" name="showNotifications">
        <label for="showNotifications">
          <img src="../icons/feather/bell.svg" class="form-icon" alt="Notifications">
          Show notifications when redirecting
        </label>
      </div>

      <div class="form-group checkbox">
        <input type="checkbox" id="darkMode" name="darkMode">
        <label for="darkMode">
          <img src="../icons/feather/moon.svg" class="form-icon" alt="Dark Mode">
          Use dark mode (overrides system preference)
        </label>
      </div>

      <div class="button-group">
        <button type="button" id="testConnection" class="button secondary">
          <img src="../icons/feather/activity.svg" class="button-icon" alt="Test">
          Test Connection
        </button>
        <button type="submit" id="saveOptions" class="button primary">
          <img src="../icons/feather/save.svg" class="button-icon" alt="Save">
          Save
        </button>
      </div>
    </form>

    <div id="connectionStatus" class="connection-status hidden">
      <span id="statusIcon" class="status-icon"></span>
      <span id="statusText" class="status-text"></span>
    </div>

    <div id="saveStatus" class="save-status hidden">
      <img src="../icons/feather/check-circle.svg" class="status-icon-img success" alt="Success">
      <span class="status-text">Options saved!</span>
    </div>
  </div>
  <script src="options.js"></script>
</body>
</html>

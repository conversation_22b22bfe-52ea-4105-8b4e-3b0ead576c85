// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseUrl: 'http://localhost:8001',
  showNotifications: true,
  darkMode: false,
  connectionTimeout: 5000
};

// DOM elements
const optionsForm = document.getElementById('optionsForm');
const bahtbrowseUrlInput = document.getElementById('bahtbrowseUrl');
const connectionTimeoutInput = document.getElementById('connectionTimeout');
const showNotificationsCheckbox = document.getElementById('showNotifications');
const darkModeCheckbox = document.getElementById('darkMode');
const testConnectionButton = document.getElementById('testConnection');
const saveOptionsButton = document.getElementById('saveOptions');
const connectionStatus = document.getElementById('connectionStatus');
const statusIcon = document.getElementById('statusIcon');
const statusText = document.getElementById('statusText');
const saveStatus = document.getElementById('saveStatus');

// Load settings
function loadSettings() {
  browser.storage.local.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    // Populate form fields
    bahtbrowseUrlInput.value = settings.bahtbrowseUrl;
    connectionTimeoutInput.value = settings.connectionTimeout;
    showNotificationsCheckbox.checked = settings.showNotifications;
    darkModeCheckbox.checked = settings.darkMode;

    // Apply dark mode if enabled
    if (settings.darkMode || window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.body.classList.add('dark-mode');
    }
  });
}

// Save settings
function saveSettings(e) {
  e.preventDefault();

  const settings = {
    bahtbrowseUrl: bahtbrowseUrlInput.value,
    connectionTimeout: parseInt(connectionTimeoutInput.value, 10),
    showNotifications: showNotificationsCheckbox.checked,
    darkMode: darkModeCheckbox.checked
  };

  browser.storage.local.set({ settings }).then(() => {
    // Show save status
    saveStatus.classList.remove('hidden');

    // Apply dark mode if enabled
    if (settings.darkMode) {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }

    // Hide save status after 3 seconds
    setTimeout(() => {
      saveStatus.classList.add('hidden');
    }, 3000);
  });
}

// Test connection to BahtBrowse
function testConnection() {
  // Show loading state
  connectionStatus.classList.remove('hidden');
  statusIcon.innerHTML = `<img src="../icons/feather/loader.svg" class="status-icon-img" alt="Loading">`;
  statusText.textContent = 'Testing connection...';

  const bahtbrowseUrl = bahtbrowseUrlInput.value;
  const timeout = parseInt(connectionTimeoutInput.value, 10);

  // Test connection to the API endpoint
  fetch(`${bahtbrowseUrl}/api/test-connection`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (response.ok) {
      return response.json();
    }
    throw new Error(`Server responded with status: ${response.status}`);
  })
  .then(data => {
    // Connection successful
    statusIcon.innerHTML = `<img src="../icons/feather/check-circle.svg" class="status-icon-img success" alt="Success">`;
    statusText.textContent = 'Connection successful!';
  })
  .catch(error => {
    // Connection failed
    statusIcon.innerHTML = `<img src="../icons/feather/alert-circle.svg" class="status-icon-img error" alt="Error">`;
    statusText.textContent = `Connection failed: ${error.message}`;
  });
}

// Event listeners
document.addEventListener('DOMContentLoaded', loadSettings);
optionsForm.addEventListener('submit', saveSettings);
testConnectionButton.addEventListener('click', testConnection);

// Toggle dark mode when checkbox changes
darkModeCheckbox.addEventListener('change', () => {
  if (darkModeCheckbox.checked) {
    document.body.classList.add('dark-mode');
  } else {
    document.body.classList.remove('dark-mode');
  }
});

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseUrl: 'http://localhost:8001',
  showNotifications: true,
  darkMode: false,
  connectionTimeout: 5000
};

// DOM elements
const openCurrentUrlButton = document.getElementById('openCurrentUrl');
const openOptionsButton = document.getElementById('openOptions');
const testConnectionButton = document.getElementById('testConnection');
const connectionStatus = document.getElementById('connectionStatus');
const statusIcon = document.getElementById('statusIcon');
const statusText = document.getElementById('statusText');

// Load settings and apply theme
browser.storage.local.get('settings').then((result) => {
  const settings = result.settings || DEFAULT_SETTINGS;

  // Apply dark mode if enabled
  if (settings.darkMode || window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.body.classList.add('dark-mode');
  }
});

// Open current URL in BahtBrowse
openCurrentUrlButton.addEventListener('click', () => {
  browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
    const currentUrl = tabs[0].url;
    redirectToBahtBrowse(currentUrl);
  });
});

// Open options page
openOptionsButton.addEventListener('click', () => {
  browser.runtime.openOptionsPage();
});

// Test connection to BahtBrowse
testConnectionButton.addEventListener('click', () => {
  testConnection();
});

// Function to redirect to BahtBrowse
function redirectToBahtBrowse(url) {
  // Get settings
  browser.storage.local.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    const bahtbrowseUrl = settings.bahtbrowseUrl;

    // Construct the BahtBrowse URL
    const redirectUrl = `${bahtbrowseUrl}/api/browse?url=${encodeURIComponent(url)}`;

    // Open the URL in a new tab
    browser.tabs.create({ url: redirectUrl });

    // Show notification if enabled
    if (settings.showNotifications) {
      browser.notifications.create({
        type: 'basic',
        iconUrl: browser.runtime.getURL('../icons/bahtbrowse-48.png'),
        title: 'BahtBrowse Redirector',
        message: `Redirecting to BahtBrowse: ${url}`
      });
    }

    // Close the popup
    window.close();
  });
}

// Function to test connection to BahtBrowse
function testConnection() {
  // Show loading state
  connectionStatus.classList.remove('hidden');
  statusIcon.innerHTML = `<img src="../icons/feather/loader.svg" class="status-icon-img" alt="Loading">`;
  statusText.textContent = 'Testing connection...';

  // Get settings
  browser.storage.local.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    const bahtbrowseUrl = settings.bahtbrowseUrl;
    const timeout = settings.connectionTimeout || 5000;

    // Test connection to the API endpoint
    fetch(`${bahtbrowseUrl}/api/test-connection`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      timeout: timeout
    })
    .then(response => {
      if (response.ok) {
        return response.json();
      }
      throw new Error(`Server responded with status: ${response.status}`);
    })
    .then(data => {
      // Connection successful
      statusIcon.innerHTML = `<img src="../icons/feather/check-circle.svg" class="status-icon-img success" alt="Success">`;
      statusText.textContent = 'Connection successful!';
    })
    .catch(error => {
      // Connection failed
      statusIcon.innerHTML = `<img src="../icons/feather/alert-circle.svg" class="status-icon-img error" alt="Error">`;
      statusText.textContent = `Connection failed: ${error.message}`;
    });
  });
}

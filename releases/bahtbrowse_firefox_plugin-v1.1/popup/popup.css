:root {
  --primary-color: #a520c6;
  --primary-hover-color: #8a1dad;
  --secondary-color: #212529;
  --secondary-hover-color: #331f38;
  --success-color: #28a745;
  --error-color: #dc3545;
  --text-color: #f9f9fa;
  --background-color: #1f1d1d;
  --border-color: #5c5c66;
  --glow-color: #e60073;
  --secondary-glow: #0fa;
}

/* Light mode */
@media (prefers-color-scheme: light) {
  :root {
    --primary-color: #a520c6;
    --primary-hover-color: #8a1dad;
    --secondary-color: #331f38;
    --secondary-hover-color: #212529;
    --text-color: #212529;
    --background-color: #f8f9fa;
    --border-color: #dee2e6;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #a520c6;
    --primary-hover-color: #8a1dad;
    --secondary-color: #212529;
    --secondary-hover-color: #331f38;
    --text-color: #f9f9fa;
    --background-color: #1f1d1d;
    --border-color: #5c5c66;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
  }

  body.dark-mode {
    --text-color: #f9f9fa;
    --background-color: #1f1d1d;
    --border-color: #5c5c66;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background: linear-gradient(300deg, #1f1d1d, #331f38, #a520c6);
  background-size: 180% 180%;
  animation: gradient-animation 18s ease infinite;
  width: 320px;
  min-height: 300px;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.logo-container {
  position: relative;
  width: 48px;
  height: 48px;
  margin-right: 12px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 0 10px var(--glow-color);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.title {
  font-family: 'Vibur', cursive;
  font-size: 28px;
  margin: 0;
  color: white;
  text-shadow: 0 0 5px var(--glow-color), 0 0 10px var(--glow-color);
  animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px var(--glow-color), 0 0 10px var(--glow-color);
  }
  to {
    text-shadow: 0 0 10px var(--glow-color), 0 0 20px var(--glow-color), 0 0 30px var(--secondary-glow);
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.button {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.button:hover::before {
  left: 100%;
}

.button-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  filter: brightness(0) invert(1);
}

.button.primary {
  background-color: var(--primary-color);
  color: white;
}

.button.primary:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.button.secondary {
  background-color: var(--secondary-color);
  color: white;
}

.button.secondary:hover {
  background-color: var(--secondary-hover-color);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.connection-status {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  margin-top: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-icon-img {
  width: 100%;
  height: 100%;
  filter: brightness(0) invert(1);
  animation: pulse 1.5s infinite;
}

.status-icon-img.success {
  filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
  animation: none;
  box-shadow: 0 0 8px var(--success-color);
}

.status-icon-img.error {
  filter: invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
  animation: none;
  box-shadow: 0 0 8px var(--error-color);
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.status-text {
  font-size: 14px;
  color: white;
}

.hidden {
  display: none;
}

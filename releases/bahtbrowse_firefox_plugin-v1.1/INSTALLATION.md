# BahtBrowse Firefox Plugin Installation Guide

## Installation Methods

### Method 1: Install from XPI file (Recommended)

1. Download the `bahtbrowse_redirector.xpi` file from this directory
2. Open Firefox
3. Go to `about:addons` (or click the menu button and select "Add-ons and themes")
4. Click the gear icon and select "Install Add-on From File..."
5. Navigate to and select the downloaded `bahtbrowse_redirector.xpi` file
6. Click "Add" when prompted
7. The BahtBrowse plugin should now be installed and visible in your toolbar

### Method 2: Install temporarily for development

1. Open Firefox
2. Go to `about:debugging`
3. Click "This Firefox" in the left sidebar
4. Click "Load Temporary Add-on..."
5. Navigate to the directory containing the extension files
6. Select any file in the extension directory (e.g., `manifest.json`)
7. The extension will be loaded temporarily and will be removed when Firefox is closed

## Configuration

After installation, you should configure the extension:

1. Click the BahtBrowse icon in the toolbar
2. Click the "Settings" button
3. Enter your BahtBrowse URL (default: `http://localhost:8001`)
4. Set your preferred connection timeout
5. Choose whether to show notifications when redirecting
6. Toggle dark mode if desired
7. Click "Save"
8. Test the connection by clicking the "Test Connection" button

## Troubleshooting

If you encounter issues:

1. Ensure your BahtBrowse instance is running
2. Check that the URL in the settings is correct
3. Try increasing the connection timeout
4. Check the browser console for any error messages

## Uninstallation

To uninstall the extension:

1. Go to `about:addons`
2. Find the BahtBrowse extension
3. Click the three dots next to it and select "Remove"
4. Confirm the removal when prompted

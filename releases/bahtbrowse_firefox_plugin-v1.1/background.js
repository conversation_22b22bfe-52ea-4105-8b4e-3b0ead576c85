// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseUrl: 'http://localhost:8001',
  showNotifications: true,
  darkMode: false,
  connectionTimeout: 5000
};

// Initialize settings
browser.storage.local.get('settings').then((result) => {
  if (!result.settings) {
    browser.storage.local.set({ settings: DEFAULT_SETTINGS });
  }
});

// Listen for browser action clicks
browser.browserAction.onClicked.addListener((tab) => {
  redirectToBahtBrowse(tab.url);
});

// Function to redirect to BahtBrowse
function redirectToBahtBrowse(url) {
  // Get settings
  browser.storage.local.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    const bahtbrowseUrl = settings.bahtbrowseUrl;
    
    // Construct the BahtBrowse URL
    const redirectUrl = `${bahtbrowseUrl}/api/browse?url=${encodeURIComponent(url)}`;
    
    // Open the URL in a new tab
    browser.tabs.create({ url: redirectUrl });
    
    // Show notification if enabled
    if (settings.showNotifications) {
      browser.notifications.create({
        type: 'basic',
        iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
        title: 'BahtBrowse Redirector',
        message: `Redirecting to BahtBrowse: ${url}`
      });
    }
    
    // Log the redirection
    console.log(`Redirected ${url} to BahtBrowse at ${redirectUrl}`);
  });
}

FROM ubuntu:24.04@sha256:50cb325cf61fa0ac0f42c2ea431d8ef091fe3d36f5bc039d15f89c569ff4988e
# Update
RUN apt update -y
RUN apt upgrade -y
# Dependencies
RUN apt install -y gpg wget libasound2t64 git npm curl

# Set default locale to English but support multiple locales
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8

# Install locales package and generate common locales
RUN DEBIAN_FRONTEND=noninteractive apt install -y locales && \
    sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# de_DE.UTF-8 UTF-8/de_DE.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# es_ES.UTF-8 UTF-8/es_ES.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# it_IT.UTF-8 UTF-8/it_IT.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ja_JP.UTF-8 UTF-8/ja_JP.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ru_RU.UTF-8 UTF-8/ru_RU.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

# Install runtime dependencies for ungoogled-chromium
RUN apt-get update && apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libexpat1 \
    libgbm1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libx11-6 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libxshmfence1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    libasound2t64 \
    libpulse0 \
    libva2 \
    libvulkan1 \
    unzip

# Create directory for ungoogled-chromium
RUN mkdir -p /opt/ungoogled-chromium

# Download and install the latest ungoogled-chromium release
WORKDIR /tmp
RUN wget https://github.com/ungoogled-software/ungoogled-chromium-portablelinux/releases/download/135.0.7049.95-1/ungoogled-chromium_135.0.7049.95-1_linux.tar.xz && \
    tar -xf ungoogled-chromium_135.0.7049.95-1_linux.tar.xz -C /opt/ungoogled-chromium && \
    rm ungoogled-chromium_135.0.7049.95-1_linux.tar.xz

# Create symbolic links
RUN ln -s /opt/ungoogled-chromium/ungoogled-chromium_135.0.7049.95-1_linux/chrome /usr/bin/chromium && \
    ln -s /opt/ungoogled-chromium/ungoogled-chromium_135.0.7049.95-1_linux/chrome /usr/bin/chromium-browser

# Install other dependencies
RUN DEBIAN_FRONTEND=noninteractive apt install tigervnc-standalone-server matchbox-window-manager nginx nano libgtk-3-0t64 python3-validators -y
RUN DEBIAN_FRONTEND=noninteractive apt install python3-aiohttp tmux python3-pip -y
RUN apt -y install novnc python3-websockify python3-numpy
RUN cd /tmp && git clone https://github.com/novnc/noVNC.git

# copy needed files
COPY files/startwm.sh /tmp/startwm.sh
COPY files/vnc.html /tmp/noVNC/vnc.html
COPY files/nginx_default /etc/nginx/sites-enabled/default
COPY files/app.py /tmp/app.py
COPY files/landing_page/index.html /tmp/serve/index.html
COPY files/console_logger.js /tmp/console_logger.js
COPY files/chromium_preferences.json /tmp/chromium_preferences.json
COPY files/custom.css /tmp/noVNC/app/styles/custom.css
COPY files/vibur.css /tmp/noVNC/app/styles/vibur.css
COPY files/error_handler.sh /tmp/error_handler.sh
COPY files/downloads.html /tmp/downloads.html
COPY files/startup_tests.py /tmp/startup_tests.py
COPY files/requirements.txt /tmp/requirements.txt
COPY files/xkb.conf /tmp/xkb.conf
COPY files/session_manager.py /tmp/session_manager.py
COPY files/session_validator.py /tmp/session_validator.py

# Install Python requirements
RUN pip3 install --break-system-packages -r /tmp/requirements.txt

# Create Chromium profile directory
RUN mkdir -p /tmp/chromium_profile

# Create a dedicated downloads directory
RUN mkdir -p /tmp/downloads

# Create a non-root user
RUN groupadd -r bahtuser && useradd -r -g bahtuser -m -d /home/<USER>/bin/bash bahtuser

# Ensure the app.py server is accessible by exposing logs and making changes
# to make debugging easier
RUN mkdir -p /tmp/logs
RUN echo "#!/bin/bash" > /tmp/start_debug.sh
RUN echo "python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &" >> /tmp/start_debug.sh
RUN echo "tail -f /tmp/logs/app.log" >> /tmp/start_debug.sh
RUN chmod +x /tmp/start_debug.sh

# run entrypoint
RUN chmod +x /tmp/startwm.sh
RUN chmod +x /tmp/error_handler.sh

# Modify startwm.sh to call the debug script
RUN sed -i 's/python3 \/tmp\/app.py &/\/tmp\/start_debug.sh \&/g' /tmp/startwm.sh

# Set proper permissions for the non-root user
RUN mkdir -p /home/<USER>/.vnc
RUN chown -R bahtuser:bahtuser /home/<USER>
RUN chown -R bahtuser:bahtuser /tmp/chromium_profile
RUN chown -R bahtuser:bahtuser /tmp/logs
RUN chown -R bahtuser:bahtuser /tmp/noVNC
RUN chown -R bahtuser:bahtuser /tmp/downloads
RUN chown bahtuser:bahtuser /tmp/start_debug.sh
RUN chown bahtuser:bahtuser /tmp/startwm.sh
RUN chown bahtuser:bahtuser /tmp/error_handler.sh
RUN chown bahtuser:bahtuser /tmp/app.py
RUN chown bahtuser:bahtuser /tmp/console_logger.js
RUN chown bahtuser:bahtuser /tmp/chromium_preferences.json
RUN chown bahtuser:bahtuser /tmp/startup_tests.py
RUN chmod +x /tmp/startup_tests.py

# Add label for the updated branding
LABEL org.opencontainers.image.title="bahtBrowse-Chromium"
LABEL org.opencontainers.image.description="Secure containerized browser with ungoogled-chromium"
LABEL org.opencontainers.image.version="1.0"

# Temporarily run as root for debugging
# USER bahtuser

ENTRYPOINT ["/tmp/startwm.sh"]

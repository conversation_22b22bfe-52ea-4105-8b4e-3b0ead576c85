FROM ubuntu:latest as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    git \
    python3 \
    python3-pip \
    ninja-build \
    pkg-config \
    cmake \
    clang \
    lld \
    curl \
    gnupg2 \
    libgtk-3-dev \
    libglib2.0-dev \
    libdbus-1-dev \
    libpulse-dev \
    libcups2-dev \
    libasound2-dev \
    libxcomposite-dev \
    libxdamage-dev \
    libxrandr-dev \
    libxkbcommon-dev \
    libdrm-dev \
    libgbm-dev \
    libpci-dev \
    libxss-dev \
    libnspr4-dev \
    libnss3-dev \
    libatk1.0-dev \
    libatk-bridge2.0-dev \
    libatspi2.0-dev \
    libxcursor-dev \
    libxfixes-dev \
    libxi-dev \
    libxtst-dev \
    libx11-dev \
    libwayland-dev \
    libxkbfile-dev \
    libfontconfig1-dev \
    libcairo2-dev \
    libgcrypt20-dev \
    libkrb5-dev \
    libexif-dev \
    libelf-dev \
    libva-dev \
    libvpx-dev \
    libjpeg-dev \
    libpng-dev \
    libwebp-dev \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libopus-dev \
    libspeechd-dev \
    libexpat1-dev \
    libxml2-dev \
    libxslt1-dev \
    libmodpbase64-dev \
    libsnappy-dev \
    libre2-dev \
    libminizip-dev \
    libharfbuzz-dev \
    libusb-1.0-0-dev \
    libevent-dev \
    liblzma-dev \
    libffi-dev \
    libgnutls28-dev \
    libssl-dev \
    libsqlite3-dev \
    zlib1g-dev \
    bison \
    flex \
    gperf \
    xz-utils \
    nodejs \
    npm \
    yasm \
    wget \
    rsync

# Set up working directory
WORKDIR /build

# Clone ungoogled-chromium repository
RUN git clone https://github.com/ungoogled-software/ungoogled-chromium.git

# Set up build environment
WORKDIR /build/ungoogled-chromium

# Get the latest stable tag
RUN git fetch --tags && \
    LATEST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`) && \
    git checkout $LATEST_TAG

# Install Python dependencies
RUN pip3 install -r requirements.txt

# Download and extract Chromium source
RUN python3 utils/downloads.py --download-dir /build/download_cache --output-dir /build/chromium

# Apply ungoogled-chromium patches
RUN python3 utils/prune_binaries.py /build/chromium && \
    python3 utils/patches.py apply /build/chromium && \
    python3 utils/domain_substitution.py apply -r domain_regex.list -f domain_substitution.list -c /build/chromium

# Set up build configuration
WORKDIR /build/chromium/src

# Configure build
RUN mkdir -p out/Default && \
    python3 /build/ungoogled-chromium/utils/gn_flags.py -o out/Default/args.gn

# Build Chromium (this will take a long time)
RUN gn gen out/Default && \
    ninja -C out/Default chrome chrome_sandbox chromedriver

# Create a new stage for the final image
FROM ubuntu:latest

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libexpat1 \
    libgbm1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libx11-6 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libxshmfence1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    libasound2t64 \
    libpulse0 \
    libva2 \
    libvulkan1 \
    locales \
    && rm -rf /var/lib/apt/lists/*

# Set up locale
RUN locale-gen en_US.UTF-8
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8

# Create a directory for Chromium
RUN mkdir -p /opt/ungoogled-chromium

# Copy the built Chromium from the builder stage
COPY --from=builder /build/chromium/src/out/Default/chrome /opt/ungoogled-chromium/
COPY --from=builder /build/chromium/src/out/Default/chrome_sandbox /opt/ungoogled-chromium/
COPY --from=builder /build/chromium/src/out/Default/chromedriver /opt/ungoogled-chromium/
COPY --from=builder /build/chromium/src/out/Default/*.pak /opt/ungoogled-chromium/
COPY --from=builder /build/chromium/src/out/Default/*.bin /opt/ungoogled-chromium/
COPY --from=builder /build/chromium/src/out/Default/locales /opt/ungoogled-chromium/locales
COPY --from=builder /build/chromium/src/out/Default/resources /opt/ungoogled-chromium/resources

# Set up the sandbox
RUN chown root:root /opt/ungoogled-chromium/chrome_sandbox && \
    chmod 4755 /opt/ungoogled-chromium/chrome_sandbox && \
    ln -s /opt/ungoogled-chromium/chrome_sandbox /usr/local/sbin/chrome-devel-sandbox

# Create symbolic links
RUN ln -s /opt/ungoogled-chromium/chrome /usr/bin/chromium && \
    ln -s /opt/ungoogled-chromium/chrome /usr/bin/chromium-browser && \
    ln -s /opt/ungoogled-chromium/chromedriver /usr/bin/chromedriver

# Set environment variables
ENV CHROME_EXECUTABLE=/opt/ungoogled-chromium/chrome
ENV CHROME_SANDBOX=/opt/ungoogled-chromium/chrome_sandbox

# Set the entrypoint
ENTRYPOINT ["/opt/ungoogled-chromium/chrome"]
CMD ["--no-sandbox", "--disable-dev-shm-usage"]

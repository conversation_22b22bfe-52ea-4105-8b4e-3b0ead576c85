FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV HOME=/home/<USER>
ENV DISPLAY=:1
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8
ENV BROWSER_TYPE=chromium
ENV OS_TYPE=ubuntu

# Update and install basic dependencies
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    gpg \
    wget \
    curl \
    git \
    npm \
    locales \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install locales package and generate common locales
RUN sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

# Install runtime dependencies for Chromium
RUN apt-get update && apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libexpat1 \
    libgbm1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libx11-6 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    libxshmfence1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    libasound2 \
    libpulse0 \
    libva2 \
    libvulkan1 \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create directory for ungoogled-chromium
RUN mkdir -p /opt/ungoogled-chromium

# Download and install the latest ungoogled-chromium release
WORKDIR /tmp
RUN wget https://github.com/ungoogled-software/ungoogled-chromium-portablelinux/releases/download/135.0.7049.95-1/ungoogled-chromium_135.0.7049.95-1_linux.tar.xz && \
    tar -xf ungoogled-chromium_135.0.7049.95-1_linux.tar.xz -C /opt/ungoogled-chromium && \
    rm ungoogled-chromium_135.0.7049.95-1_linux.tar.xz

# Create symbolic links
RUN ln -s /opt/ungoogled-chromium/ungoogled-chromium_135.0.7049.95-1_linux/chrome /usr/bin/chromium && \
    ln -s /opt/ungoogled-chromium/ungoogled-chromium_135.0.7049.95-1_linux/chrome /usr/bin/chromium-browser

# Install VNC and other dependencies
RUN apt-get update && apt-get install -y \
    tigervnc-standalone-server \
    openbox \
    xterm \
    python3 \
    python3-pip \
    python3-numpy \
    python3-aiohttp \
    python3-validators \
    nginx \
    supervisor \
    fonts-dejavu \
    xfonts-base \
    xfonts-75dpi \
    xfonts-100dpi \
    xfonts-scalable \
    novnc \
    python3-websockify \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set up locale
RUN locale-gen en_US.UTF-8

# Create a non-root user
RUN useradd -m -d /home/<USER>/bin/bash bahtuser \
    && mkdir -p /home/<USER>/.vnc \
    && mkdir -p /tmp/logs \
    && mkdir -p /tmp/downloads \
    && mkdir -p /tmp/chromium_profile

# Set up VNC password (empty for no password)
RUN mkdir -p /home/<USER>/.vnc \
    && touch /home/<USER>/.vnc/passwd \
    && chmod 600 /home/<USER>/.vnc/passwd \
    && chown -R bahtuser:bahtuser /home/<USER>/.vnc

# Set up noVNC
RUN mkdir -p /tmp/noVNC \
    && git clone https://github.com/novnc/noVNC.git /tmp/noVNC \
    && git clone https://github.com/novnc/websockify.git /tmp/noVNC/utils/websockify \
    && cd /tmp/noVNC \
    && git checkout v1.4.0 \
    && cd /tmp/noVNC/utils/websockify \
    && git checkout v0.11.0 \
    && ln -s /tmp/noVNC/vnc.html /tmp/noVNC/index.html

# Create directories for logs and serve
RUN mkdir -p /tmp/logs \
    && mkdir -p /tmp/serve \
    && mkdir -p /tmp/serve/app \
    && mkdir -p /tmp/serve/core

# Copy our fixed files
COPY docker/fixes/disable_vnc_auth.sh /tmp/disable_vnc_auth.sh
COPY docker/fixes/ui_rfb_fix.js /tmp/ui_rfb_fix.js
COPY docker/fixes/inject_ui_rfb_fix.sh /tmp/inject_ui_rfb_fix.sh
COPY novnc_config/defaults.json /tmp/noVNC/
COPY novnc_config/mandatory.json /tmp/noVNC/
RUN chmod +x /tmp/disable_vnc_auth.sh /tmp/inject_ui_rfb_fix.sh

# Set up nginx to serve noVNC
RUN echo 'server {\n\
    listen 9090;\n\
    root /tmp/serve;\n\
    location / {\n\
        try_files $uri $uri/ =404;\n\
    }\n\
    location /vnc1/ {\n\
        alias /tmp/noVNC/;\n\
    }\n\
}' > /etc/nginx/sites-available/novnc \
    && ln -s /etc/nginx/sites-available/novnc /etc/nginx/sites-enabled/ \
    && rm /etc/nginx/sites-enabled/default

# Copy startup script
COPY docker/fixes/startwm-chromium.sh /tmp/startwm.sh
RUN chmod +x /tmp/startwm.sh

# Copy chromium preferences
COPY files/chromium_preferences.json /tmp/chromium_preferences.json

# Copy API server
COPY docker/fixes/app-chromium.py /tmp/app.py
RUN chmod +x /tmp/app.py

# Copy noVNC files to serve directory
RUN cp -r /tmp/noVNC/app/* /tmp/serve/app/ \
    && cp -r /tmp/noVNC/core/* /tmp/serve/core/ \
    && cp /tmp/noVNC/vnc.html /tmp/serve/index.html \
    && chown -R bahtuser:bahtuser /tmp/serve

# Set up supervisor
RUN echo '[supervisord]\n\
nodaemon=true\n\
user=root\n\
\n\
[program:startwm]\n\
command=/tmp/startwm.sh\n\
autorestart=true\n\
\n\
[program:nginx]\n\
command=nginx -g "daemon off;"\n\
autorestart=true\n\
' > /etc/supervisor/conf.d/supervisord.conf

# Set proper permissions
RUN chown -R bahtuser:bahtuser /home/<USER>
    && chown -R bahtuser:bahtuser /tmp/logs \
    && chown -R bahtuser:bahtuser /tmp/downloads \
    && chown -R bahtuser:bahtuser /tmp/chromium_profile \
    && chmod 755 /tmp/startwm.sh

# Expose ports
EXPOSE 80 5901 6080 8082

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

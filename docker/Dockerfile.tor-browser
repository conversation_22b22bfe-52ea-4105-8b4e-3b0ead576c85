FROM ubuntu:20.04

LABEL maintainer="bahtBrowse Team"
LABEL description="Tor Browser in Docker for Remote Browser Isolation"

ENV DEBIAN_FRONTEND=noninteractive \
    LANG=en_US.UTF-8 \
    LANGUAGE=en_US.UTF-8 \
    LC_ALL=en_US.UTF-8 \
    TZ=UTC \
    DISPLAY=:1 \
    RESOLUTION=1280x720x24 \
    VNC_PORT=6080 \
    NO_VNC_PORT=6080 \
    HOME=/home/<USER>
    SHELL=/bin/bash

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    apt-transport-https \
    ca-certificates \
    curl \
    dbus-x11 \
    gnupg \
    locales \
    nginx \
    procps \
    python3 \
    python3-pip \
    supervisor \
    tzdata \
    unzip \
    wget \
    xvfb \
    x11vnc \
    xauth \
    xterm \
    xz-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set up locale
RUN locale-gen en_US.UTF-8

# Create browser user
RUN useradd -m -d /home/<USER>/bin/bash browser \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R browser:browser /home/<USER>

# Install noVNC
RUN mkdir -p /usr/local/novnc \
    && curl -L -o /tmp/novnc.zip https://github.com/novnc/noVNC/archive/v1.3.0.zip \
    && unzip /tmp/novnc.zip -d /usr/local/novnc \
    && cp -r /usr/local/novnc/noVNC-1.3.0/* /usr/local/novnc/ \
    && rm -rf /usr/local/novnc/noVNC-1.3.0 \
    && rm /tmp/novnc.zip

# Install websockify for noVNC
RUN pip3 install --no-cache-dir websockify

# Download and install Tor Browser
RUN mkdir -p /tmp/tor-browser \
    && cd /tmp \
    && wget -q https://www.torproject.org/dist/torbrowser/12.5.2/tor-browser-linux64-12.5.2_ALL.tar.xz \
    && tar -xJf tor-browser-linux64-12.5.2_ALL.tar.xz \
    && mv tor-browser /home/<USER>/tor-browser \
    && chown -R browser:browser /home/<USER>/tor-browser \
    && rm -f tor-browser-linux64-12.5.2_ALL.tar.xz

# Copy configuration files
COPY files/startwm.sh /usr/local/bin/
COPY files/xkb.conf /etc/supervisor/conf.d/
COPY files/vnc.html /usr/local/novnc/
COPY files/custom.css /usr/local/novnc/
COPY files/vibur.css /usr/local/novnc/
COPY files/error_handler.sh /usr/local/bin/
COPY files/console_logger.js /usr/local/novnc/
COPY files/downloads.html /usr/local/novnc/
COPY nginx/nginx_default /etc/nginx/sites-available/default
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx_fix.conf /etc/nginx/conf.d/fix-redirect.conf

# Set up supervisor
COPY files/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Make scripts executable
RUN chmod +x /usr/local/bin/startwm.sh \
    && chmod +x /usr/local/bin/error_handler.sh

# Set up Tor Browser to run in a more containerized way
RUN echo 'pref("browser.startup.homepage", "about:blank");' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.startup.page", 0);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.download.dir", "/home/<USER>/Downloads");' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.download.folderList", 2);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.helperApps.neverAsk.saveToDisk", "application/pdf;application/x-pdf;application/octet-stream");' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.download.manager.showWhenStarting", false);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("privacy.popups.showBrowserMessage", false);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.tabs.warnOnClose", false);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.tabs.warnOnCloseOtherTabs", false);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js \
    && echo 'pref("browser.warnOnQuit", false);' >> /home/<USER>/tor-browser/Browser/TorBrowser/Data/Browser/profile.default/prefs.js

# Create a script to launch Tor Browser with a URL
RUN echo '#!/bin/bash' > /usr/local/bin/start-tor-browser \
    && echo 'URL=${1:-about:blank}' >> /usr/local/bin/start-tor-browser \
    && echo 'cd /home/<USER>/tor-browser' >> /usr/local/bin/start-tor-browser \
    && echo './Browser/start-tor-browser --detach --allow-remote --no-remote --new-instance --url "$URL"' >> /usr/local/bin/start-tor-browser \
    && chmod +x /usr/local/bin/start-tor-browser

# Set up the entrypoint script
RUN echo '#!/bin/bash' > /usr/local/bin/entrypoint.sh \
    && echo 'export URL=${1:-about:blank}' >> /usr/local/bin/entrypoint.sh \
    && echo 'exec supervisord -c /etc/supervisor/conf.d/supervisord.conf' >> /usr/local/bin/entrypoint.sh \
    && chmod +x /usr/local/bin/entrypoint.sh

# Create supervisor log directory
RUN mkdir -p /var/log/supervisor

WORKDIR /home/<USER>
USER root
EXPOSE 6080 80
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]

# BahtBrowse Docker Configuration

This directory contains Docker-related files for BahtBrowse.

## Directory Structure

- `compose/`: Contains Docker Compose files for various configurations
- `config/`: Contains Dockerfile configurations for different components

## Docker Compose Files

The `compose/` directory contains Docker Compose files for different configurations:

- `docker-compose.yml`: The main Docker Compose file
- `docker-compose.firefox-ubuntu.yml`: Configuration for Firefox on Ubuntu
- `docker-compose.firefox-alpine.yml`: Configuration for Firefox on Alpine
- `docker-compose.chromium-ubuntu.yml`: Configuration for Chromium on Ubuntu
- `docker-compose.chromium-alpine.yml`: Configuration for Chromium on Alpine
- `docker-compose.ungoogled-chromium.yml`: Configuration for Ungoogled Chromium
- `docker-compose.ungoogled-chromium-prebuilt.yml`: Configuration for prebuilt Ungoogled Chromium
- `docker-compose.tor-browser.yml`: Configuration for Tor Browser
- `docker-compose.tor-browser-alpine.yml`: Configuration for Tor Browser on Alpine
- `docker-compose.elk.yml`: Configuration for ELK stack
- `docker-compose.elk-simple.yml`: Simplified configuration for ELK stack
- `docker-compose.bahtbrowse-elk.yml`: Configuration for BahtBrowse with ELK stack
- `docker-compose.benchmark.yml`: Configuration for benchmarking
- `docker-compose.playwright.yml`: Configuration for Playwright testing

## Dockerfile Configurations

The `config/` directory contains Dockerfile configurations for different components:

- `Dockerfile`: The main Dockerfile
- `Dockerfile.api`: Configuration for the API server
- `Dockerfile.worker`: Configuration for the worker
- `Dockerfile.firefox-alpine`: Configuration for Firefox on Alpine
- `Dockerfile.chromium-alpine`: Configuration for Chromium on Alpine
- `Dockerfile.ungoogled-chromium`: Configuration for Ungoogled Chromium
- `Dockerfile.ungoogled-chromium-build`: Configuration for building Ungoogled Chromium
- `Dockerfile.ungoogled-chromium-final`: Final configuration for Ungoogled Chromium
- `Dockerfile.ungoogled-chromium-prebuilt`: Configuration for prebuilt Ungoogled Chromium
- `Dockerfile.tor-browser`: Configuration for Tor Browser
- `Dockerfile.tor-browser-alpine`: Configuration for Tor Browser on Alpine
- `Dockerfile.flower`: Configuration for Flower (Celery monitoring)
- `Dockerfile.playwright`: Configuration for Playwright testing

## Usage

To use these Docker Compose files:

1. Navigate to the root directory of the project
2. Run `docker-compose -f docker/compose/docker-compose.yml up -d` to start the main services
3. For specific configurations, use the appropriate Docker Compose file

Example:

```bash
# Start Firefox on Ubuntu
docker-compose -f docker/compose/docker-compose.firefox-ubuntu.yml up -d

# Start Chromium on Alpine
docker-compose -f docker/compose/docker-compose.chromium-alpine.yml up -d

# Start ELK stack
docker-compose -f docker/compose/docker-compose.elk.yml up -d
```

## Building Custom Images

To build custom Docker images:

1. Navigate to the root directory of the project
2. Run `docker build -f docker/config/Dockerfile.<variant> -t bahtbrowse:<variant> .`

Example:

```bash
# Build Firefox on Alpine
docker build -f docker/config/Dockerfile.firefox-alpine -t bahtbrowse:firefox-alpine .

# Build Ungoogled Chromium
docker build -f docker/config/Dockerfile.ungoogled-chromium -t bahtbrowse:ungoogled-chromium .
```

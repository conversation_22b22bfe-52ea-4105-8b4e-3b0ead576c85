version: '3.8'

services:
  browser_firefox_alpine:
    build:
      context: .
      dockerfile: Dockerfile.firefox-alpine
    image: bahtbrowse-firefox-alpine
    ports:
      - "8002:80"
      - "5902:5901"
      - "6082:6080"
      - "8083:8082"
    volumes:
      - firefox-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=alpine

volumes:
  firefox-alpine-downloads:

networks:
  bahtbrowse:
    driver: bridge

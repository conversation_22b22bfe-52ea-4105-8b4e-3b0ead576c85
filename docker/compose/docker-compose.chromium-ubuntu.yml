version: '3.8'

services:
  browser_chromium_ubuntu:
    build:
      context: .
      dockerfile: Dockerfile.ungoogled-chromium-prebuilt
    image: bahtbrowse-chromium-ubuntu
    ports:
      - "8003:80"
      - "5903:5901"
      - "6083:6080"
      - "8084:8082"
    volumes:
      - chromium-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=ubuntu

volumes:
  chromium-ubuntu-downloads:

networks:
  bahtbrowse:
    driver: bridge

version: '3.8'

services:
  # Web API
  api:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ../../web/templates:/usr/share/nginx/html
      - ../../nginx/conf.d/bahtbrowse.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped
    networks:
      - bahtbrowse-prod

  # Redis for message broker and result backend
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - bahtbrowse-prod

volumes:
  redis-data:

networks:
  bahtbrowse-prod:
    driver: bridge

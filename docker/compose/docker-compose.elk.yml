version: '3.8'

services:
  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.6.0
    container_name: bahtbrowse-elasticsearch
    environment:
      - node.name=bahtbrowse-es01
      - cluster.name=bahtbrowse-es-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - elk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.6.0
    container_name: bahtbrowse-logstash
    volumes:
      - ./elk/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ./elk/logstash/pipeline:/usr/share/logstash/pipeline:ro
    ports:
      - "5044:5044"
      - "5001:5000/tcp"
      - "5001:5000/udp"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    networks:
      - elk-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9600/_node/stats"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.6.0
    container_name: bahtbrowse-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=kibana
    volumes:
      - ./elk/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    networks:
      - elk-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.6.0
    container_name: bahtbrowse-filebeat
    user: root
    volumes:
      - ./elk/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat-data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped

  # Metricbeat
  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.6.0
    container_name: bahtbrowse-metricbeat
    user: root
    volumes:
      - ./elk/metricbeat/config/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat-data:/usr/share/metricbeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped
    command: ["--strict.perms=false", "-system.hostfs=/hostfs"]

  # APM Server
  apm-server:
    image: docker.elastic.co/apm/apm-server:8.6.0
    container_name: bahtbrowse-apm-server
    ports:
      - "8200:8200"
    volumes:
      - ./elk/apm-server/config/apm-server.yml:/usr/share/apm-server/apm-server.yml:ro
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8200/"]
      interval: 30s
      timeout: 10s
      retries: 5

networks:
  elk-network:
    driver: bridge
  bahtbrowse:
    external: true

volumes:
  elasticsearch-data:
  filebeat-data:
  metricbeat-data:

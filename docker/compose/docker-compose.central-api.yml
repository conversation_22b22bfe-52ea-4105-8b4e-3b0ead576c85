services:
  bahtbrowse-central-api:
    container_name: bahtbrowse-central-api
    build:
      context: ../api
      dockerfile: Dockerfile
    image: bahtbrowse-central-api
    ports:
      - "5050:5000"
    restart: unless-stopped
    networks:
      - bahtbrowse
    depends_on:
      - bahtbrowse-browser-firefox-ubuntu
      - bahtbrowse-browser-firefox-alpine
      - bahtbrowse-browser-chromium-ubuntu
      - bahtbrowse-browser-chromium-alpine

  bahtbrowse-browser-firefox-ubuntu:
    container_name: bahtbrowse-browser-firefox-ubuntu
    build:
      context: ../..
      dockerfile: docker/Dockerfile.firefox-ubuntu
    image: bahtbrowse-firefox-ubuntu
    ports:
      - "8001:80"
      - "5901:5901"
      - "6080:6080"
      - "8082:8082"
    volumes:
      - firefox-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=ubuntu

  bahtbrowse-browser-firefox-alpine:
    container_name: bahtbrowse-browser-firefox-alpine
    build:
      context: ../..
      dockerfile: docker/Dockerfile.firefox-alpine
    image: bahtbrowse-firefox-alpine
    ports:
      - "8002:80"
      - "5902:5901"
      - "6081:6080"
      - "8083:8082"
    volumes:
      - firefox-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=alpine

  bahtbrowse-browser-chromium-ubuntu:
    container_name: bahtbrowse-browser-chromium-ubuntu
    build:
      context: ../..
      dockerfile: docker/Dockerfile.chromium-ubuntu
    image: bahtbrowse-chromium-ubuntu-vnc
    ports:
      - "8013:80"
      - "5913:5901"
      - "6093:6080"
      - "8094:8082"
    volumes:
      - chromium-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=ubuntu

  bahtbrowse-browser-chromium-alpine:
    container_name: bahtbrowse-browser-chromium-alpine
    build:
      context: ../..
      dockerfile: docker/Dockerfile.chromium-alpine
    image: bahtbrowse-chromium-alpine
    ports:
      - "8004:80"
      - "5904:5901"
      - "6084:6080"
      - "8087:8082"
    volumes:
      - chromium-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=alpine

volumes:
  firefox-ubuntu-downloads:
  firefox-alpine-downloads:
  chromium-ubuntu-downloads:
  chromium-alpine-downloads:

networks:
  bahtbrowse:
    driver: bridge

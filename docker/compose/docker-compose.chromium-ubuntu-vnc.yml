services:
  bahtbrowse-browser-chromium-ubuntu:
    container_name: bahtbrowse-browser-chromium-ubuntu
    build:
      context: ../..
      dockerfile: docker/Dockerfile.chromium-ubuntu
    image: bahtbrowse-chromium-ubuntu-vnc
    ports:
      - "8013:80"
      - "5913:5901"
      - "6093:6080"
      - "8094:8082"
    volumes:
      - chromium-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=ubuntu

volumes:
  chromium-ubuntu-downloads:

networks:
  bahtbrowse:
    driver: bridge

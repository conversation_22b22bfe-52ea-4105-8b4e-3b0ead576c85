version: '3.8'

services:
  # Web API
  api:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.api
    ports:
      - "5000:5000"
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DOCKER_BASE_URL=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Celery worker for container management
  worker-container:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.worker
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DOCKER_BASE_URL=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: celery -A docker_queue.celery_app worker -Q container_management -l info
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Celery worker for pool management
  worker-pool:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.worker
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DOCKER_BASE_URL=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: celery -A docker_queue.celery_app worker -Q pool_management -l info
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Celery worker for monitoring
  worker-monitoring:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.worker
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - DOCKER_BASE_URL=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: celery -A docker_queue.celery_app worker -Q monitoring -l info
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Celery beat for scheduled tasks
  beat:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.worker
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: celery -A docker_queue.celery_app beat -l info
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Celery flower for monitoring with dark mode
  flower:
    build:
      context: ../..
      dockerfile: ../../docker/config/Dockerfile.worker
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - BROKER_API=redis://redis:6379/0
    volumes:
      - ../../docker_queue/flower_config.py:/app/docker_queue/flower_config.py
      - ../../docker_queue/flower_dark_theme.css:/app/docker_queue/flower_dark_theme.css
      - ../../docker_queue/flower_dark_mode.js:/app/docker_queue/flower_dark_mode.js
    command: >
      python docker_queue/run_flower.py
    depends_on:
      - redis
      - worker-container
      - worker-pool
      - worker-monitoring
    restart: unless-stopped
    networks:
      - bahtbrowse

  # Redis for message broker and result backend
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - bahtbrowse

volumes:
  redis-data:

networks:
  bahtbrowse:
    driver: bridge

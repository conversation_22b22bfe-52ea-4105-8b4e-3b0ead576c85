version: '3.8'

services:
  # 10Baht bahtBrowse services with ELK logging
  api:
    image: bahtbrowse/api:latest
    container_name: bahtbrowse-api
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FILEBEAT_ENABLED=true
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - ./logs:/var/log/bahtbrowse
      - ./elk/filebeat/config/filebeat.yml:/etc/filebeat/filebeat.yml:ro
    depends_on:
      - redis
      - elasticsearch
      - logstash
    networks:
      - bahtbrowse
      - elk-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "api-container"
        tag: "{{.Name}}/{{.ID}}"
    labels:
      app: "bahtbrowse"
      component: "api"

  worker:
    image: bahtbrowse/worker:latest
    container_name: bahtbrowse-worker
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FILEBEAT_ENABLED=true
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - ./logs:/var/log/bahtbrowse
      - ./elk/filebeat/config/filebeat.yml:/etc/filebeat/filebeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis
      - elasticsearch
      - logstash
    networks:
      - bahtbrowse
      - elk-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "worker-container"
        tag: "{{.Name}}/{{.ID}}"
    labels:
      app: "bahtbrowse"
      component: "worker"

  flower:
    image: bahtbrowse/flower:latest
    container_name: bahtbrowse-flower
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=admin:admin
      - FILEBEAT_ENABLED=true
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    volumes:
      - ./logs:/var/log/bahtbrowse
      - ./elk/filebeat/config/filebeat.yml:/etc/filebeat/filebeat.yml:ro
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - worker
      - elasticsearch
      - logstash
    networks:
      - bahtbrowse
      - elk-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "flower-container"
        tag: "{{.Name}}/{{.ID}}"
    labels:
      app: "bahtbrowse"
      component: "flower"

  redis:
    image: redis:6-alpine
    container_name: bahtbrowse-redis
    ports:
      - "6379:6379"
    networks:
      - bahtbrowse
      - elk-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "redis-container"
        tag: "{{.Name}}/{{.ID}}"
    labels:
      app: "bahtbrowse"
      component: "redis"

  # ELK Stack services
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.6.0
    container_name: bahtbrowse-elasticsearch
    environment:
      - node.name=bahtbrowse-es01
      - cluster.name=bahtbrowse-es-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - elk-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200"]
      interval: 30s
      timeout: 10s
      retries: 5

  logstash:
    image: docker.elastic.co/logstash/logstash:8.6.0
    container_name: bahtbrowse-logstash
    volumes:
      - ./elk/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
      - ./elk/logstash/pipeline:/usr/share/logstash/pipeline:ro
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: "-Xmx256m -Xms256m"
    networks:
      - elk-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9600/_node/stats"]
      interval: 30s
      timeout: 10s
      retries: 5

  kibana:
    image: docker.elastic.co/kibana/kibana:8.6.0
    container_name: bahtbrowse-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=kibana
    volumes:
      - ./elk/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml:ro
    networks:
      - elk-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.6.0
    container_name: bahtbrowse-filebeat
    user: root
    volumes:
      - ./elk/filebeat/config/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/var/log/bahtbrowse:ro
      - filebeat-data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped

  metricbeat:
    image: docker.elastic.co/beats/metricbeat:8.6.0
    container_name: bahtbrowse-metricbeat
    user: root
    volumes:
      - ./elk/metricbeat/config/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /sys/fs/cgroup:/hostfs/sys/fs/cgroup:ro
      - /proc:/hostfs/proc:ro
      - /:/hostfs:ro
      - metricbeat-data:/usr/share/metricbeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped
    command: ["--strict.perms=false", "-system.hostfs=/hostfs"]

  apm-server:
    image: docker.elastic.co/apm/apm-server:8.6.0
    container_name: bahtbrowse-apm-server
    ports:
      - "8200:8200"
    volumes:
      - ./elk/apm-server/config/apm-server.yml:/usr/share/apm-server/apm-server.yml:ro
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_HOST=http://kibana:5601
    networks:
      - elk-network
      - bahtbrowse
    depends_on:
      - elasticsearch
      - kibana
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8200/"]
      interval: 30s
      timeout: 10s
      retries: 5

  setup:
    image: docker.elastic.co/kibana/kibana:8.6.0
    container_name: bahtbrowse-elk-setup
    volumes:
      - ./elk/setup:/setup:ro
    command: ["/bin/bash", "/setup/setup-kibana.sh"]
    networks:
      - elk-network
    depends_on:
      - elasticsearch
      - kibana
    restart: "no"

networks:
  bahtbrowse:
    driver: bridge
  elk-network:
    driver: bridge

volumes:
  elasticsearch-data:
  filebeat-data:
  metricbeat-data:

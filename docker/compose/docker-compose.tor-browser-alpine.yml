version: '3'

services:
  tor-browser-alpine:
    build:
      context: .
      dockerfile: docker/Dockerfile.tor-browser-alpine
    image: bahtbrowse-tor-browser-alpine
    container_name: bahtbrowse-tor-browser-alpine
    ports:
      - "6081:6080"
      - "8080:80"
    environment:
      - RESOLUTION=1280x720x24
      - VNC_PORT=6080
      - NO_VNC_PORT=6080
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/nginx
    networks:
      - bahtbrowse-network

  api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    image: bahtbrowse-api
    container_name: bahtbrowse-api
    ports:
      - "8082:8082"
    environment:
      - API_PORT=8082
      - API_HOST=0.0.0.0
      - DEFAULT_BROWSER=tor-browser-alpine
    restart: unless-stopped
    depends_on:
      - tor-browser-alpine
    networks:
      - bahtbrowse-network

networks:
  bahtbrowse-network:
    driver: bridge

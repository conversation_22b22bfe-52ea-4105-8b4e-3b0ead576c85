# Common ignores
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Build artifacts
build/
dist/
*.egg-info/

# Virtual environment
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE / Editor specific
.idea/
.vscode/
*.swp
*.swo

# OS specific
.DS_Store
Thumbs.db

# Testing artifacts
htmlcov/
.coverage
.pytest_cache/
.mypy_cache/
.ruff_cache/
playwright-report/

# Node specific (important for Docker build context)
node_modules/

# Nix specific
result*
.direnv/

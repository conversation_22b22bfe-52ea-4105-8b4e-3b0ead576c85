"""
Central API server for BahtBrowse that handles redirection to different browser/OS combinations.
"""

import datetime
import json
import logging
import os
from urllib.parse import quote, urlencode

from aiohttp import web

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("/tmp/logs/api.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Container configuration
CONTAINER_CONFIG = {
    "firefox": {
        "ubuntu": {
            "host": "bahtbrowse-browser-firefox-ubuntu",
            "port": 8082,
            "vnc_port": 6080
        },
        "alpine": {
            "host": "bahtbrowse-browser-firefox-alpine",
            "port": 8083,
            "vnc_port": 6081
        }
    },
    "chromium": {
        "ubuntu": {
            "host": "bahtbrowse-browser-chromium-ubuntu",
            "port": 8094,
            "vnc_port": 6093
        },
        "alpine": {
            "host": "bahtbrowse-browser-chromium-alpine",
            "port": 8087,
            "vnc_port": 6084
        }
    }
}

# Default configuration
DEFAULT_BROWSER = "firefox"
DEFAULT_OS = "ubuntu"

# Routes
routes = web.RouteTableDef()

@routes.get("/")
async def handle_root(request):
    """Handle root endpoint."""
    return web.json_response({
        "name": "BahtBrowse Central API",
        "version": "1.0.0",
        "status": "running",
        "supported_browsers": list(CONTAINER_CONFIG.keys()),
        "supported_os": list(CONTAINER_CONFIG["firefox"].keys())
    })

@routes.get("/browse/")
async def handle_browse(request):
    """Handle browse endpoint with OS and browser parameters."""
    # Get parameters
    url = request.query.get("url")
    browser = request.query.get("browser", DEFAULT_BROWSER).lower()
    os_type = request.query.get("os", DEFAULT_OS).lower()
    
    # Validate URL
    if not url:
        return web.json_response({"error": "No URL provided"}, status=400)
    
    # Validate browser
    if browser not in CONTAINER_CONFIG:
        return web.json_response({
            "error": f"Invalid browser: {browser}. Supported browsers: {list(CONTAINER_CONFIG.keys())}"
        }, status=400)
    
    # Validate OS
    if os_type not in CONTAINER_CONFIG[browser]:
        return web.json_response({
            "error": f"Invalid OS: {os_type}. Supported OS for {browser}: {list(CONTAINER_CONFIG[browser].keys())}"
        }, status=400)
    
    # Get container configuration
    config = CONTAINER_CONFIG[browser][os_type]
    
    # Log the request
    logger.info(f"Browse request received for URL: {url}, Browser: {browser}, OS: {os_type}")
    
    # Redirect to the appropriate container
    redirect_url = f"http://{config['host']}:{config['port']}/browse/?url={quote(url)}"
    logger.info(f"Redirecting to: {redirect_url}")
    
    return web.HTTPFound(redirect_url)

@routes.get("/browse/test-connection")
async def handle_test_connection(request):
    """Handle test connection endpoint."""
    # Get parameters
    browser = request.query.get("browser", DEFAULT_BROWSER).lower()
    os_type = request.query.get("os", DEFAULT_OS).lower()
    
    # Validate browser
    if browser not in CONTAINER_CONFIG:
        return web.json_response({
            "error": f"Invalid browser: {browser}. Supported browsers: {list(CONTAINER_CONFIG.keys())}"
        }, status=400)
    
    # Validate OS
    if os_type not in CONTAINER_CONFIG[browser]:
        return web.json_response({
            "error": f"Invalid OS: {os_type}. Supported OS for {browser}: {list(CONTAINER_CONFIG[browser].keys())}"
        }, status=400)
    
    # Get container configuration
    config = CONTAINER_CONFIG[browser][os_type]
    
    # Get client information
    client_ip = request.remote
    client_info = request.headers.get("User-Agent", "Unknown")
    request_source = request.headers.get("X-Request-Source", "Unknown")
    plugin_version = request.headers.get("X-Plugin-Version", "Unknown")
    
    # Log the test connection
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"[{timestamp}] Test connection from {client_ip} ({client_info}) for {browser} on {os_type}")
    
    # Return a success response
    return web.json_response({
        "status": "success",
        "message": f"BahtBrowse Central API is available. {browser.capitalize()} on {os_type.capitalize()} is accessible.",
        "timestamp": timestamp,
        "client_ip": str(client_ip),
        "client_info": client_info,
        "request_source": request_source,
        "plugin_version": plugin_version,
        "browser_type": browser,
        "os_type": os_type,
        "server_version": "1.0.0",
        "container_host": config["host"],
        "container_port": config["port"],
        "vnc_port": config["vnc_port"]
    })

@routes.get("/status")
async def handle_status(request):
    """Handle status endpoint that returns the status of all containers."""
    status = {}
    
    for browser, os_configs in CONTAINER_CONFIG.items():
        status[browser] = {}
        for os_type, config in os_configs.items():
            try:
                # In a real implementation, we would check if the container is running
                # For now, we'll just assume all containers are running
                status[browser][os_type] = {
                    "status": "running",
                    "host": config["host"],
                    "port": config["port"],
                    "vnc_port": config["vnc_port"]
                }
            except Exception as e:
                status[browser][os_type] = {
                    "status": "error",
                    "error": str(e)
                }
    
    return web.json_response({
        "status": "success",
        "containers": status,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })

@routes.get("/direct/{browser}/{os}")
async def handle_direct_access(request):
    """Handle direct access to a specific browser/OS combination."""
    browser = request.match_info.get("browser", DEFAULT_BROWSER).lower()
    os_type = request.match_info.get("os", DEFAULT_OS).lower()
    url = request.query.get("url", "about:blank")
    
    # Validate browser
    if browser not in CONTAINER_CONFIG:
        return web.json_response({
            "error": f"Invalid browser: {browser}. Supported browsers: {list(CONTAINER_CONFIG.keys())}"
        }, status=400)
    
    # Validate OS
    if os_type not in CONTAINER_CONFIG[browser]:
        return web.json_response({
            "error": f"Invalid OS: {os_type}. Supported OS for {browser}: {list(CONTAINER_CONFIG[browser].keys())}"
        }, status=400)
    
    # Get container configuration
    config = CONTAINER_CONFIG[browser][os_type]
    
    # Log the request
    logger.info(f"Direct access request for {browser} on {os_type} with URL: {url}")
    
    # Redirect to the VNC interface of the appropriate container
    vnc_url = f"http://localhost:{config['vnc_port']}/vnc.html?autoconnect=true&resize=remote&url={quote(url)}"
    logger.info(f"Redirecting to VNC: {vnc_url}")
    
    return web.HTTPFound(vnc_url)

# Create and run the application
app = web.Application()
app.add_routes(routes)

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs("/tmp/logs", exist_ok=True)
    
    # Get port from environment variable or use default
    port = int(os.environ.get("API_PORT", "5000"))
    logger.info(f"Starting BahtBrowse Central API server on port {port}")
    web.run_app(app, host="0.0.0.0", port=port)

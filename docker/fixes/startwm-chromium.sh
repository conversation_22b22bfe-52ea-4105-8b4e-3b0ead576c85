#!/bin/bash

# Create log directory
mkdir -p /tmp/logs
touch /tmp/logs/startup.log
echo "Starting container at $(date)" >> /tmp/logs/startup.log

# Set locale environment variables
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8
echo "Locale environment set: LANG=$LANG LC_ALL=$LC_ALL" >> /tmp/logs/startup.log

# Disable VNC authentication
/tmp/disable_vnc_auth.sh
echo "VNC authentication disabled at $(date)" >> /tmp/logs/startup.log

# Redirect XKB warnings to /dev/null to suppress them
# Start VNC server without authentication
Xtigervnc :1 -depth 24 -geometry 1920x1080 -br -SecurityTypes None 2>/dev/null &
echo "VNC server started at $(date)" >> /tmp/logs/startup.log

# Inject UI.rfb fix into noVNC HTML files
/tmp/inject_ui_rfb_fix.sh >> /tmp/logs/startup.log 2>&1
echo "UI.rfb fix injected at $(date)" >> /tmp/logs/startup.log

# Start noVNC proxy with websockify path
/tmp/noVNC/utils/novnc_proxy --vnc localhost:5901 --websockify-path=/tmp/noVNC/utils/websockify &
echo "noVNC proxy started at $(date)" >> /tmp/logs/startup.log

# Start Openbox window manager
sleep 1
DISPLAY=:1 openbox &
echo "Openbox window manager started at $(date)" >> /tmp/logs/startup.log

# Create Chromium profile directory if it doesn't exist
mkdir -p /tmp/chromium_profile
chown -R bahtuser:bahtuser /tmp/chromium_profile

# Copy Chromium preferences to profile directory
mkdir -p /tmp/chromium_profile/Default
cp /tmp/chromium_preferences.json /tmp/chromium_profile/Default/Preferences
chown -R bahtuser:bahtuser /tmp/chromium_profile

# Start Chromium in kiosk mode
sleep 2
su - bahtuser -c "DISPLAY=:1 /usr/bin/chromium --user-data-dir=/tmp/chromium_profile --no-sandbox --disable-dev-shm-usage --kiosk about:blank" &
echo "Chromium started at $(date)" >> /tmp/logs/startup.log

# Start the API server
python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &
echo "API server started at $(date)" >> /tmp/logs/startup.log

# Keep the script running
tail -f /dev/null

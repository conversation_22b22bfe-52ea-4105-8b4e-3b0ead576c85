"""
Simple API server for BahtBrowse Chromium container.
"""

import datetime
import json
import logging
import os
from urllib.parse import quote

from aiohttp import web

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.FileHandler("/tmp/logs/api.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Routes
routes = web.RouteTableDef()

@routes.get("/")
async def handle_root(request):
    """Handle root endpoint."""
    return web.json_response({
        "name": "BahtBrowse Chromium API",
        "version": "1.0.0",
        "status": "running",
        "browser_type": os.environ.get("BROWSER_TYPE", "chromium"),
        "os_type": os.environ.get("OS_TYPE", "ubuntu")
    })

@routes.get("/browse/")
async def handle_browse(request):
    """Handle browse endpoint."""
    url = request.query.get("url")
    if not url:
        return web.json_response({"error": "No URL provided"}, status=400)
    
    # Log the request
    logger.info(f"Browse request received for URL: {url}")
    
    # Redirect to noVNC with the URL
    encoded_url = quote(url)
    return web.HTTPFound(f"/vnc1/vnc.html?autoconnect=true&resize=remote&url={encoded_url}")

@routes.get("/browse/test-connection")
async def handle_test_connection(request):
    """Handle test connection endpoint."""
    # Get client information
    client_ip = request.remote
    client_info = request.headers.get("User-Agent", "Unknown")
    request_source = request.headers.get("X-Request-Source", "Unknown")
    plugin_version = request.headers.get("X-Plugin-Version", "Unknown")
    
    # Log the test connection
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"[{timestamp}] Test connection from {client_ip} ({client_info})")
    
    # Return a success response
    return web.json_response({
        "status": "success",
        "message": "BahtBrowse Chromium service is available",
        "timestamp": timestamp,
        "client_ip": str(client_ip),
        "client_info": client_info,
        "request_source": request_source,
        "plugin_version": plugin_version,
        "browser_type": os.environ.get("BROWSER_TYPE", "chromium"),
        "os_type": os.environ.get("OS_TYPE", "ubuntu"),
        "server_version": "1.0.0"
    })

# Create and run the application
app = web.Application()
app.add_routes(routes)

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs("/tmp/logs", exist_ok=True)
    
    # Get port from environment variable or use default
    port = int(os.environ.get("API_PORT", "8082"))
    logger.info(f"Starting BahtBrowse Chromium API server on port {port}")
    web.run_app(app, host="0.0.0.0", port=port)

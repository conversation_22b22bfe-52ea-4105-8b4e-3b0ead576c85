#!/bin/bash

# This script injects the UI.rfb fix script into the noVNC HTML files

# Create a directory for the fix script
mkdir -p /tmp/serve/js

# Copy the fix script to the serve directory
cp /tmp/ui_rfb_fix.js /tmp/serve/js/

# Find all HTML files in the noVNC directory
for html_file in /tmp/noVNC/*.html; do
    echo "Injecting UI.rfb fix into $html_file"
    
    # Check if the file already has the fix
    if grep -q "ui_rfb_fix.js" "$html_file"; then
        echo "Fix already injected in $html_file"
    else
        # Inject the script tag before the closing head tag
        sed -i 's|</head>|<script src="/js/ui_rfb_fix.js"></script></head>|' "$html_file"
        echo "Fix injected into $html_file"
    fi
done

# Also inject the fix into the served HTML files
for html_file in /tmp/serve/*.html; do
    echo "Injecting UI.rfb fix into $html_file"
    
    # Check if the file already has the fix
    if grep -q "ui_rfb_fix.js" "$html_file"; then
        echo "Fix already injected in $html_file"
    else
        # Inject the script tag before the closing head tag
        sed -i 's|</head>|<script src="/js/ui_rfb_fix.js"></script></head>|' "$html_file"
        echo "Fix injected into $html_file"
    fi
done

echo "UI.rfb fix injection completed"

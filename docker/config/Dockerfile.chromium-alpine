FROM alpine:3.18

# Set environment variables
ENV DISPLAY=:1 \
    VNC_PORT=5901 \
    NO_VNC_PORT=6080 \
    HOME=/home/<USER>
    TERM=xterm \
    SHELL=/bin/sh \
    BROWSER_TYPE=chromium

# Install required packages
RUN apk update && apk add --no-cache \
    chromium \
    chromium-chromedriver \
    xvfb \
    x11vnc \
    openbox \
    python3 \
    py3-pip \
    py3-aiohttp \
    py3-websockets \
    py3-numpy \
    py3-validators \
    py3-rich \
    nginx \
    nano \
    git \
    bash \
    curl \
    wget \
    tzdata \
    shadow \
    && rm -rf /var/cache/apk/*

# Install noVNC and websockify source
RUN mkdir -p /tmp/novnc-src /tmp/websockify-src && \
    git clone --depth 1 --branch v1.4.0 https://github.com/novnc/noVNC.git /tmp/novnc-src && \
    git clone --depth 1 --branch v0.11.0 https://github.com/novnc/websockify.git /tmp/websockify-src

# Copy entire noVNC clone contents to final location first using archive mode
RUN mkdir -p /tmp/noVNC && cp -a /tmp/novnc-src/. /tmp/noVNC/
# Remove potentially missing/incorrect JSONs copied from clone
RUN rm -f /tmp/noVNC/defaults.json /tmp/noVNC/mandatory.json

# Create directories needed by our files
RUN mkdir -p /tmp/chromium_profile && \
    mkdir -p /tmp/downloads && \
    mkdir -p /tmp/serve && \
    mkdir -p /tmp/logs

# Copy our configuration files / Overrides (these will overwrite defaults)
# Copy our verified config files first
COPY ../novnc_config/defaults.json /tmp/noVNC/
# COPY ../novnc_config/mandatory.json /tmp/noVNC/ # Uncomment if mandatory.json is added

COPY ../files/startwm-alpine.sh /tmp/startwm.sh
COPY ../files/vnc.html /tmp/noVNC/vnc.html
COPY ../nginx/nginx_default_alpine /etc/nginx/http.d/default.conf
COPY ../files/app.py /tmp/app.py
COPY ../files/session_manager.py /tmp/session_manager.py
COPY ../files/landing_page/index.html /tmp/serve/index.html
COPY ../files/console_logger.js /tmp/console_logger.js
COPY ../files/chromium_preferences.json /tmp/chromium_preferences.json
COPY ../files/custom.css /tmp/noVNC/app/styles/
COPY ../files/vibur.css /tmp/noVNC/app/styles/
COPY ../files/error_handler.sh /tmp/error_handler.sh
COPY ../files/downloads.html /tmp/downloads.html
COPY ../files/startup_tests.py /tmp/startup_tests.py

# Verify essential noVNC files exist after copies
RUN test -f /tmp/noVNC/vnc.html || (echo "ERROR: /tmp/noVNC/vnc.html not found after COPY" && exit 1)
RUN test -f /tmp/noVNC/defaults.json || (echo "ERROR: /tmp/noVNC/defaults.json not found after COPY" && exit 1)
RUN test -d /tmp/noVNC/app/styles || (echo "ERROR: /tmp/noVNC/app/styles directory not found" && exit 1)

# Clean up temporary source clones
RUN rm -rf /tmp/novnc-src /tmp/websockify-src

# Create a non-root user
RUN groupadd -r bahtuser && \
    useradd -r -g bahtuser -m -d /home/<USER>/bin/bash bahtuser && \
    chown -R bahtuser:bahtuser /home/<USER>/tmp/chromium_profile /tmp/downloads

# Set up debug logging
RUN echo "#!/bin/bash" > /tmp/start_debug.sh && \
    echo "export PYTHONPATH=/tmp" >> /tmp/start_debug.sh && \
    echo "python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &" >> /tmp/start_debug.sh && \
    echo "tail -f /tmp/logs/app.log" >> /tmp/start_debug.sh && \
    chmod +x /tmp/start_debug.sh

# Make scripts executable & set permissions
RUN chmod +x /tmp/startwm.sh && \
    chmod +x /tmp/error_handler.sh && \
    chown bahtuser:bahtuser /tmp/app.py && \
    chown bahtuser:bahtuser /tmp/session_manager.py && \
    chown bahtuser:bahtuser /tmp/start_debug.sh && \
    chown -R bahtuser:bahtuser /tmp/logs /tmp/noVNC

# Add labels
LABEL org.opencontainers.image.title="bahtBrowse-Chromium-Alpine"
LABEL org.opencontainers.image.description="Secure containerized Chromium browser on Alpine"
LABEL org.opencontainers.image.version="1.0"
LABEL org.opencontainers.image.vendor="10Baht Security"

# Expose ports
EXPOSE 80 5901 6080 8082

# Set entrypoint
ENTRYPOINT ["/tmp/startwm.sh"]

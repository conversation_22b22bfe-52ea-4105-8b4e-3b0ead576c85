# Dockerfile.playwright
# Environment for running Playwright tests against the Firefox extension

FROM ubuntu:latest

ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies: Node.js, npm, Python (for potential build steps/scripts), curl, wget
RUN apt-get update && apt-get install -y \
    nodejs \
    npm \
    python3 \
    python3-pip \
    curl \
    wget \
    # Add libraries needed for Playwright Firefox dependencies
    libdbus-glib-1-2 \
    libgtk-3-0 \
    libasound2t64 \
    libxtst6 \
    libxrandr2 \
    libx11-xcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxi6 \
    libxfixes3 \
    libxrender1 \
    libnss3 \
    libnspr4 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    && rm -rf /var/lib/apt/lists/*

ENV NODE_PATH=/app/node_modules

# Set working directory
WORKDIR /app

# Install Playwright locally
COPY package.json package-lock.json* ./
RUN npm install # This installs dependencies listed in package.json, including @playwright/test
# Explicitly install playwright package itself as well
RUN npm install playwright
RUN npx playwright install --with-deps firefox

# Copy the Firefox extension source code
# COPY browser_plugins/firefox/ ./firefox-extension-src/ # Original problematic line
# Diagnostic: Try copying only the manifest first
RUN mkdir -p ./firefox-extension-src/
COPY browser_plugins/firefox/manifest.json ./firefox-extension-src/
# If the above works, we might need to copy files individually or investigate directory permissions/issues

# Copy other necessary extension files (adjust as needed if manifest copy works)
COPY browser_plugins/firefox/background.js ./firefox-extension-src/
COPY browser_plugins/firefox/options/ ./firefox-extension-src/options/
COPY browser_plugins/firefox/popup/ ./firefox-extension-src/popup/
COPY browser_plugins/firefox/icons/ ./firefox-extension-src/icons/


# Modify the extension's default host/port to point to the backend service name
RUN sed -i 's|http://localhost:8082|http://bahtbrowse-backend:8082|g' ./firefox-extension-src/background.js || echo "sed command failed, background.js might not have the expected string."
RUN sed -i 's|localhost|bahtbrowse-backend|g' ./firefox-extension-src/options/options.js || echo "sed command failed, options.js might not have the expected string."


# Copy Playwright test configuration and test files
COPY playwright.config.js ./
COPY tests/playwright/ ./tests/playwright/

# Default command to run tests
CMD ["npm", "test"]

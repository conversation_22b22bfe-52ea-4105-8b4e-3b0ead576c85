FROM ubuntu:latest
# Update
RUN apt update -y
RUN apt upgrade -y
# Dependencies
RUN apt install -y gpg wget libasound2t64 git npm curl

# Set up entrypoint
#COPY ./scripts/run.sh /usr/bin/entrypoint

# Set default locale to English but support multiple locales
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8

# Install locales package and generate common locales
RUN DEBIAN_FRONTEND=noninteractive apt install -y locales && \
    sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# de_DE.UTF-8 UTF-8/de_DE.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# es_ES.UTF-8 UTF-8/es_ES.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# it_IT.UTF-8 UTF-8/it_IT.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ja_JP.UTF-8 UTF-8/ja_JP.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ru_RU.UTF-8 UTF-8/ru_RU.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

RUN DEBIAN_FRONTEND=noninteractive apt install tigervnc-standalone-server matchbox-window-manager nginx nano libgtk-3-0t64 python3-validators -y
RUN DEBIAN_FRONTEND=noninteractive apt install python3-aiohttp tmux -y
RUN apt -y install novnc python3-websockify python3-numpy

# Install Firefox manually (Moved UP)
RUN cd /tmp && wget https://download-installer.cdn.mozilla.net/pub/firefox/releases/137.0.2/linux-x86_64/en-US/firefox-137.0.2.tar.xz && tar -xf firefox-137.0.2.tar.xz
# The extracted directory should be /tmp/firefox

# Install noVNC and websockify source
RUN mkdir -p /tmp/novnc-src /tmp/websockify-src && \
    git clone --depth 1 --branch v1.4.0 https://github.com/novnc/noVNC.git /tmp/novnc-src && \
    git clone --depth 1 --branch v0.11.0 https://github.com/novnc/websockify.git /tmp/websockify-src

# Copy entire noVNC clone contents to final location first using archive mode
RUN mkdir -p /tmp/noVNC && cp -a /tmp/novnc-src/. /tmp/noVNC/
# Remove potentially missing/incorrect JSONs copied from clone
RUN rm -f /tmp/noVNC/defaults.json /tmp/noVNC/mandatory.json

# copy needed files / Overrides (these will overwrite defaults)
# Copy our verified config files first
COPY novnc_config/defaults.json /tmp/noVNC/
COPY novnc_config/mandatory.json /tmp/noVNC/

COPY ../files/startwm.sh /tmp/startwm.sh
COPY ../files/fix_locales.sh /tmp/fix_locales.sh
COPY ../files/vnc.html /tmp/noVNC/vnc.html
COPY ../nginx/nginx_default /etc/nginx/sites-enabled/default
COPY ../nginx/nginx.conf /etc/nginx/nginx.conf
COPY ../files/app.py /tmp/app.py
COPY ../files/session_manager.py /tmp/session_manager.py
COPY ../files/landing_page/index.html /tmp/serve/
COPY ../files/console_logger.js /tmp/console_logger.js
COPY ../files/user.js /tmp/user.js
COPY ../files/custom.css /tmp/noVNC/app/styles/
COPY ../files/vibur.css /tmp/noVNC/app/styles/
COPY ../files/error_handler.sh /tmp/error_handler.sh
COPY ../files/downloads.html /tmp/downloads.html

# Verify essential noVNC files exist after copies
RUN test -f /tmp/noVNC/vnc.html || (echo "ERROR: /tmp/noVNC/vnc.html not found after COPY" && exit 1)
RUN test -f /tmp/noVNC/defaults.json || (echo "ERROR: /tmp/noVNC/defaults.json not found after COPY" && exit 1)
RUN test -d /tmp/noVNC/app/styles || (echo "ERROR: /tmp/noVNC/app/styles directory not found" && exit 1)

# Clean up temporary source clones
RUN rm -rf /tmp/novnc-src /tmp/websockify-src

# Create Firefox profile directory
RUN mkdir -p /tmp/firefox_profile

# Create a dedicated downloads directory
RUN mkdir -p /tmp/downloads

# Copy user.js to Firefox profile directory
RUN cp /tmp/user.js /tmp/firefox_profile/user.js

# Create a non-root user
RUN groupadd -r bahtuser && useradd -r -g bahtuser -m -d /home/<USER>/bin/bash bahtuser

# Ensure the app.py server is accessible by exposing logs and making changes
# to make debugging easier
RUN mkdir -p /tmp/logs
RUN echo "#!/bin/bash" > /tmp/start_debug.sh
# Set PYTHONPATH to include /tmp so local modules like session_manager can be found
RUN echo "export PYTHONPATH=/tmp" >> /tmp/start_debug.sh
RUN echo "python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &" >> /tmp/start_debug.sh
RUN echo "tail -f /tmp/logs/app.log" >> /tmp/start_debug.sh
RUN chmod +x /tmp/start_debug.sh

# run entrypoint
RUN chmod +x /tmp/startwm.sh
RUN chmod +x /tmp/error_handler.sh
RUN chmod +x /tmp/fix_locales.sh

# Modify startwm.sh to call the debug script
RUN sed -i 's/python3 \/tmp\/app.py &/\/tmp\/start_debug.sh \&/g' /tmp/startwm.sh

# Set proper permissions for the non-root user
RUN mkdir -p /home/<USER>/.vnc
RUN chown -R bahtuser:bahtuser /home/<USER>
RUN chown -R bahtuser:bahtuser /tmp/firefox_profile
RUN chown -R bahtuser:bahtuser /tmp/firefox
RUN chown -R bahtuser:bahtuser /tmp/logs
RUN chown -R bahtuser:bahtuser /tmp/noVNC
RUN chown -R bahtuser:bahtuser /tmp/downloads
RUN chown bahtuser:bahtuser /tmp/start_debug.sh
RUN chown bahtuser:bahtuser /tmp/startwm.sh
RUN chown bahtuser:bahtuser /tmp/error_handler.sh
RUN chown bahtuser:bahtuser /tmp/fix_locales.sh
RUN chown bahtuser:bahtuser /tmp/app.py
RUN chown bahtuser:bahtuser /tmp/session_manager.py
RUN chown bahtuser:bahtuser /tmp/console_logger.js

# Create nginx temp directories in /tmp
RUN mkdir -p /tmp/nginx/client_body /tmp/nginx/proxy /tmp/nginx/fastcgi /tmp/nginx/uwsgi /tmp/nginx/scgi
RUN chmod -R 777 /tmp/nginx

# Add label for the updated branding
LABEL org.opencontainers.image.title="bahtBrowse"
LABEL org.opencontainers.image.description="Secure containerized browser"
LABEL org.opencontainers.image.version="1.0"

# Switch to non-root user for security
USER bahtuser

ENTRYPOINT ["/tmp/startwm.sh"]

#EXPOSE 5090
#EXPOSE 5091
#EXPOSE 5092
#EXPOSE 6080:6080

# Remove the lines mistakenly added at the end
# # Install Firefox manually
# RUN cd /tmp && wget https://download-installer.cdn.mozilla.net/pub/firefox/releases/137.0.2/linux-x86_64/en-US/firefox-137.0.2.tar.xz && tar -xf firefox-137.0.2.tar.xz
# # The extracted directory should be /tmp/firefox

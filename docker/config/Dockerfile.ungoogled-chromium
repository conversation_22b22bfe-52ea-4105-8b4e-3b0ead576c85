FROM ubuntu:latest
# Update
RUN apt update -y
RUN apt upgrade -y
# Dependencies
RUN apt install -y gpg wget libasound2t64 git npm curl

# Set default locale to English but support multiple locales
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8

# Install locales package and generate common locales
RUN DEBIAN_FRONTEND=noninteractive apt install -y locales && \
    sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# de_DE.UTF-8 UTF-8/de_DE.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# es_ES.UTF-8 UTF-8/es_ES.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# it_IT.UTF-8 UTF-8/it_IT.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ja_JP.UTF-8 UTF-8/ja_JP.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen && \
    sed -i -e 's/# ru_RU.UTF-8 UTF-8/ru_RU.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen && \
    update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

# Install Chromium (standard version as a fallback since ungoogled-chromium is harder to install)
RUN apt update -y
# Install Chromium browser
RUN apt install -y chromium-browser
# Create a symlink to ensure the chromium command works
RUN ln -sf /usr/bin/chromium-browser /usr/bin/chromium

# Install other dependencies (same as current Dockerfile)
RUN DEBIAN_FRONTEND=noninteractive apt install tigervnc-standalone-server matchbox-window-manager nginx nano libgtk-3-0t64 python3-validators -y
RUN DEBIAN_FRONTEND=noninteractive apt install python3-aiohttp tmux python3-pip -y
RUN apt -y install novnc python3-websockify python3-numpy

# Install noVNC and websockify source
RUN mkdir -p /tmp/novnc-src /tmp/websockify-src && \
    git clone --depth 1 --branch v1.4.0 https://github.com/novnc/noVNC.git /tmp/novnc-src && \
    git clone --depth 1 --branch v0.11.0 https://github.com/novnc/websockify.git /tmp/websockify-src

# Copy entire noVNC clone contents to final location first using archive mode
RUN mkdir -p /tmp/noVNC && cp -a /tmp/novnc-src/. /tmp/noVNC/
# Remove potentially missing/incorrect JSONs copied from clone
RUN rm -f /tmp/noVNC/defaults.json /tmp/noVNC/mandatory.json

# copy needed files / Overrides (these will overwrite defaults)
# Copy our verified config files first
COPY ../novnc_config/defaults.json /tmp/noVNC/
# COPY ../novnc_config/mandatory.json /tmp/noVNC/ # Uncomment if mandatory.json is added

COPY ../files/startwm.sh /tmp/startwm.sh
COPY ../files/vnc.html /tmp/noVNC/vnc.html
COPY ../nginx/nginx_default /etc/nginx/sites-enabled/default
COPY ../files/app.py /tmp/app.py
COPY ../files/session_manager.py /tmp/session_manager.py
COPY ../files/landing_page/index.html /tmp/serve/index.html
COPY ../files/console_logger.js /tmp/console_logger.js
COPY ../files/chromium_preferences.json /tmp/chromium_preferences.json
COPY ../files/custom.css /tmp/noVNC/app/styles/
COPY ../files/vibur.css /tmp/noVNC/app/styles/
COPY ../files/error_handler.sh /tmp/error_handler.sh
COPY ../files/downloads.html /tmp/downloads.html
COPY ../files/startup_tests.py /tmp/startup_tests.py
COPY ../files/requirements.txt /tmp/requirements.txt
COPY ../files/xkb.conf /tmp/xkb.conf

# Verify essential noVNC files exist after copies
RUN test -f /tmp/noVNC/vnc.html || (echo "ERROR: /tmp/noVNC/vnc.html not found after COPY" && exit 1)
RUN test -f /tmp/noVNC/defaults.json || (echo "ERROR: /tmp/noVNC/defaults.json not found after COPY" && exit 1)
RUN test -d /tmp/noVNC/app/styles || (echo "ERROR: /tmp/noVNC/app/styles directory not found" && exit 1)

# Clean up temporary source clones
RUN rm -rf /tmp/novnc-src /tmp/websockify-src

# Install Python requirements
RUN pip3 install --break-system-packages -r /tmp/requirements.txt

# Create Chromium profile directory
RUN mkdir -p /tmp/chromium_profile

# Create a dedicated downloads directory
RUN mkdir -p /tmp/downloads

# Create a non-root user
RUN groupadd -r bahtuser && useradd -r -g bahtuser -m -d /home/<USER>/bin/bash bahtuser

# Ensure the app.py server is accessible by exposing logs and making changes
# to make debugging easier
RUN mkdir -p /tmp/logs
RUN echo "#!/bin/bash" > /tmp/start_debug.sh
RUN echo "export PYTHONPATH=/tmp" >> /tmp/start_debug.sh
RUN echo "python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &" >> /tmp/start_debug.sh
RUN echo "tail -f /tmp/logs/app.log" >> /tmp/start_debug.sh
RUN chmod +x /tmp/start_debug.sh

# run entrypoint
RUN chmod +x /tmp/startwm.sh
RUN chmod +x /tmp/error_handler.sh

# Modify startwm.sh to call the debug script
RUN sed -i 's/python3 \/tmp\/app.py &/\/tmp\/start_debug.sh \&/g' /tmp/startwm.sh

# Set proper permissions for the non-root user
RUN mkdir -p /home/<USER>/.vnc
RUN chown -R bahtuser:bahtuser /home/<USER>
RUN chown -R bahtuser:bahtuser /tmp/chromium_profile
RUN chown -R bahtuser:bahtuser /tmp/logs
RUN chown -R bahtuser:bahtuser /tmp/noVNC
RUN chown -R bahtuser:bahtuser /tmp/downloads
RUN chown bahtuser:bahtuser /tmp/start_debug.sh
RUN chown bahtuser:bahtuser /tmp/startwm.sh
RUN chown bahtuser:bahtuser /tmp/error_handler.sh
RUN chown bahtuser:bahtuser /tmp/app.py
RUN chown bahtuser:bahtuser /tmp/session_manager.py
RUN chown bahtuser:bahtuser /tmp/console_logger.js
RUN chown bahtuser:bahtuser /tmp/chromium_preferences.json
RUN chown bahtuser:bahtuser /tmp/startup_tests.py
RUN chmod +x /tmp/startup_tests.py

# Add label for the updated branding
LABEL org.opencontainers.image.title="bahtBrowse-Chromium"
LABEL org.opencontainers.image.description="Secure containerized browser with ungoogled-chromium"
LABEL org.opencontainers.image.version="1.0"

# Temporarily run as root for debugging
# USER bahtuser

ENTRYPOINT ["/tmp/startwm.sh"]

version: '3.8'

services:
  backend:
    image: docker_browser # Use the pre-built default image
    container_name: bahtbrowse-backend
    # No ports needed unless debugging VNC/API directly from host
    # ports:
    #   - "6081:6080"
    #   - "8083:8082"
    #   - "81:80"
    restart: unless-stopped # Optional: keep backend running

  playwright-test:
    build:
      context: .
      dockerfile: Dockerfile.playwright
    container_name: bahtbrowse-playwright-test
    depends_on:
      - backend
    # Mount report directory to get results out
    volumes:
      - ./playwright-report:/app/playwright-report
    # Command can be overridden if needed, e.g.:
    # command: npx playwright test --headed
    environment:
      # Pass any necessary environment variables to tests
      BACKEND_URL: http://bahtbrowse-backend:8082

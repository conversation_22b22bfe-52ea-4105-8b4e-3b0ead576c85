version: '3.8'

services:
  # Firefox on Ubuntu
  browser_firefox_ubuntu:
    build:
      context: .
      dockerfile: Dockerfile
    image: bahtbrowse-firefox-ubuntu
    ports:
      - "8001:80"
      - "5901:5901"
      - "6081:6080"
      - "8082:8082"
    volumes:
      - firefox-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=ubuntu

  # Firefox on Alpine
  browser_firefox_alpine:
    build:
      context: .
      dockerfile: Dockerfile.firefox-alpine
    image: bahtbrowse-firefox-alpine
    ports:
      - "8002:80"
      - "5902:5901"
      - "6082:6080"
      - "8083:8082"
    volumes:
      - firefox-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=alpine

  # Chromium on Ubuntu
  browser_chromium_ubuntu:
    build:
      context: .
      dockerfile: Dockerfile.ungoogled-chromium-prebuilt
    image: bahtbrowse-chromium-ubuntu
    ports:
      - "8003:80"
      - "5903:5901"
      - "6083:6080"
      - "8084:8082"
    volumes:
      - chromium-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=ubuntu

  # Chromium on Alpine
  browser_chromium_alpine:
    build:
      context: .
      dockerfile: Dockerfile.chromium-alpine
    image: bahtbrowse-chromium-alpine
    ports:
      - "8004:80"
      - "5904:5901"
      - "6084:6080"
      - "8085:8082"
    volumes:
      - chromium-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=alpine

  # Elasticsearch for metrics collection
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.6.0
    container_name: bahtbrowse-elasticsearch
    environment:
      - node.name=bahtbrowse-es01
      - cluster.name=bahtbrowse-es-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - bahtbrowse
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana for visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.6.0
    container_name: bahtbrowse-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - bahtbrowse
    depends_on:
      - elasticsearch
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5601/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  firefox-ubuntu-downloads:
  firefox-alpine-downloads:
  chromium-ubuntu-downloads:
  chromium-alpine-downloads:
  elasticsearch-data:

networks:
  bahtbrowse:
    driver: bridge

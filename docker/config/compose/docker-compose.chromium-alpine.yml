version: '3.8'

services:
  browser_chromium_alpine:
    build:
      context: .
      dockerfile: Dockerfile.chromium-alpine
    image: bahtbrowse-chromium-alpine
    ports:
      - "8004:80"
      - "5904:5901"
      - "6084:6080"
      - "8085:8082"
    volumes:
      - chromium-alpine-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=chromium
      - OS_TYPE=alpine

volumes:
  chromium-alpine-downloads:

networks:
  bahtbrowse:
    driver: bridge

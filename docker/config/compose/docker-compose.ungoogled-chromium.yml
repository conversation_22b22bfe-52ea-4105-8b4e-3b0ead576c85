version: '3.8'

services:
  # Builder for ungoogled-chromium
  ungoogled-chromium-builder:
    build:
      context: .
      dockerfile: Dockerfile.ungoogled-chromium-build
    image: ungoogled-chromium-builder
    # This is a build-only container, it doesn't need to run

  # Browser container with ungoogled-chromium
  browser_chromium:
    build:
      context: .
      dockerfile: Dockerfile.ungoogled-chromium-final
    image: bahtbrowse-ungoogled-chromium
    ports:
      - "80:80"
      - "5901:5901"
      - "6080:6080"
      - "8082:8082"
    volumes:
      - browser-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse

volumes:
  browser-downloads:

networks:
  bahtbrowse:
    driver: bridge

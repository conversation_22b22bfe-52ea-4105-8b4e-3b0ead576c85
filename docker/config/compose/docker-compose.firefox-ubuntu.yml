version: '3.8'

services:
  browser_firefox_ubuntu:
    build:
      context: .
      dockerfile: docker/Dockerfile
    image: bahtbrowse-firefox-ubuntu
    ports:
      - "8001:80"
      - "5901:5901"
      - "6081:6080"
      - "8082:8082"
    volumes:
      - firefox-ubuntu-downloads:/tmp/downloads
    restart: unless-stopped
    networks:
      - bahtbrowse
    environment:
      - BROWSER_TYPE=firefox
      - OS_TYPE=ubuntu

volumes:
  firefox-ubuntu-downloads:

networks:
  bahtbrowse:
    driver: bridge

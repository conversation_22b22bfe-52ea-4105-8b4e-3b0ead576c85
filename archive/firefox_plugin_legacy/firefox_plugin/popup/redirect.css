/* Dark mode detection */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #2a2a2e;
    --text-color: #f9f9fa;
    --border-color: #5c5c66;
    --primary-color: #0a84ff;
    --primary-hover: #0074e8;
    --secondary-bg: #38383d;
    --secondary-hover: #42414d;
    --url-bg: #38383d;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --error-bg: #5e292b;
    --error-color: #ff9aa2;
    --success-bg: #054d2e;
    --success-color: #87efb5;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --background-color: #f9f9fa;
    --text-color: #0c0c0d;
    --border-color: #ddd;
    --primary-color: #0060df;
    --primary-hover: #003eaa;
    --secondary-bg: #f9f9fa;
    --secondary-hover: #e7e7e7;
    --url-bg: #f0f0f4;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --error-bg: #ffdddd;
    --error-color: #990000;
    --success-bg: #ddffdd;
    --success-color: #006600;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  width: 320px;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.header {
  margin-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.header h1 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.content {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content p {
  margin: 12px 0;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  transition: color 0.3s ease;
}

.bouncer-image {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.bouncer-image img {
  filter: drop-shadow(0 2px 4px var(--shadow-color));
  transition: filter 0.3s ease;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.url-display {
  background-color: var(--url-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 16px;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  max-height: 60px;
  overflow-y: auto;
  color: var(--text-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

.buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 8px;
}

button {
  background-color: var(--primary-color);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease, transform 0.1s ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

#optionsButton {
  background-color: var(--secondary-bg);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

#optionsButton:hover {
  background-color: var(--secondary-hover);
}

#status {
  font-size: 13px;
  margin-top: 16px;
  padding: 10px;
  border-radius: 4px;
  word-break: break-all;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

#status.error {
  background-color: var(--error-bg, #ffdddd);
  color: var(--error-color, #990000);
}

#status.success {
  background-color: var(--success-bg, #ddffdd);
  color: var(--success-color, #006600);
}

.footer {
  margin-top: 15px;
  font-size: 10px;
  text-align: center;
  color: #888;
}

.debug-link {
  margin-top: 15px;
  text-align: center;
  font-size: 12px;
}

.debug-link a {
  color: #888;
  text-decoration: none;
}

.debug-link a:hover {
  color: #a520c6;
  text-decoration: underline;
}

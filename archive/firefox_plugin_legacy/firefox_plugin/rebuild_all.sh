#!/bin/bash

# Comprehensive rebuild script for 10baht Browse Firefox Plugin
# This script ensures proper minification and packaging

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "10baht Browse Firefox Extension - Complete Rebuild"
echo "======================================================"

# Step 1: Make our scripts executable
chmod +x ./better_minify.sh
chmod +x ./build.sh

echo "Step 1: Cleaning previous builds..."
rm -f ./background.min.js
rm -f ./popup/redirect.min.js
rm -f ./options/options.min.js
rm -rf ./build

# Step 2: Run the improved minification
echo "Step 2: Running improved minification..."
./better_minify.sh

# Step 3: Verify minified files
echo "Step 3: Verifying minified files..."
if [ ! -f "./background.min.js" ] || [ ! -f "./popup/redirect.min.js" ] || [ ! -f "./options/options.min.js" ]; then
    echo "Error: Minification failed! Some files are missing."
    exit 1
fi

# Step 4: Create a temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo "Step 4: Created temporary directory: $TEMP_DIR"

# Step 5: Create directory structure
echo "Step 5: Creating directory structure..."
mkdir -p $TEMP_DIR/icons $TEMP_DIR/options $TEMP_DIR/popup

# Step 6: Copy all necessary files to the temporary directory
echo "Step 6: Copying files to temporary directory..."
cp manifest.json README.md INSTALLATION.md package.json $TEMP_DIR/
cp -r icons/* $TEMP_DIR/icons/
cp options/options.css $TEMP_DIR/options/
cp options/options.html $TEMP_DIR/options/
cp popup/redirect.css $TEMP_DIR/popup/
cp popup/redirect.html $TEMP_DIR/popup/
cp background.min.js $TEMP_DIR/
cp options/options.min.js $TEMP_DIR/options/
cp popup/redirect.min.js $TEMP_DIR/popup/

# Step 7: Update HTML files to use minified JavaScript
echo "Step 7: Updating HTML files to use minified JavaScript..."
sed -i 's/redirect.js/redirect.min.js/g' $TEMP_DIR/popup/redirect.html
sed -i 's/options.js/options.min.js/g' $TEMP_DIR/options/options.html

# Step 8: Update manifest.json to use minified background.js
echo "Step 8: Updating manifest.json to use minified background.js..."
sed -i 's/"background.js"/"background.min.js"/g' $TEMP_DIR/manifest.json

# Step 9: Create build directory if it doesn't exist
mkdir -p build
XPI_FILE="build/bahtbrowse_bouncer.xpi"
echo "Step 9: Creating XPI file: $XPI_FILE"

# Step 10: Create the XPI file
cd $TEMP_DIR
echo "Step 10: Creating ZIP archive..."
zip -r "$OLDPWD/$XPI_FILE" *
ZIP_RESULT=$?
cd - > /dev/null

# Step 11: Check if the XPI file was created successfully
if [ $ZIP_RESULT -eq 0 ] && [ -f "$XPI_FILE" ]; then
    echo "Step 11: XPI file created successfully: $XPI_FILE"

    echo "You can install it in Firefox by going to:"
    echo "  - Temporary installation: about:debugging > This Firefox > Load Temporary Add-on"
    echo "  - Permanent installation: about:addons > gear icon > Install Add-on From File..."
else
    echo "Step 11: Failed to create XPI file. Error code: $ZIP_RESULT"
    exit 1
fi

# Step 12: Also create a debug build with non-minified files
echo "Step 12: Creating a debug build with non-minified files..."
./build.sh

# Step 13: Clean up
rm -rf "$TEMP_DIR"
echo "Step 13: Cleaned up temporary directory"

echo "======================================================"
echo "Complete rebuild finished successfully!"
echo "======================================================"
echo "Production build: build/bahtbrowse_bouncer.xpi"
echo "Debug build: build/bahtbrowse_bouncer.debug.xpi"
echo "======================================================"

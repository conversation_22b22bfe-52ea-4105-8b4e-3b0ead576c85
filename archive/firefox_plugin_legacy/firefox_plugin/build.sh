#!/bin/bash

# Build script for 10baht Browse Firefox Plugin (standard build without minification)

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "10baht Browse Firefox Extension - Standard Build"
echo "======================================================"

# Create build directory if it doesn't exist
mkdir -p build
XPI_FILE="build/bahtbrowse_bouncer.xpi"

echo "Step 1: Creating XPI file: $XPI_FILE"

# Create a temporary directory
TEMP_DIR=$(mktemp -d)
echo "Step 2: Created temporary directory: $TEMP_DIR"

# Copy all necessary files to the temporary directory
echo "Step 3: Copying files to temporary directory..."
cp -r background.js manifest.json README.md INSTALLATION.md package.json icons options popup $TEMP_DIR/

# Update manifest.json to use standard background.js
echo "Step 4: Updating manifest.json to use standard background.js..."
sed -i 's/"background.min.js"/"background.js"/g' $TEMP_DIR/manifest.json

# Update HTML files to use standard JavaScript
echo "Step 5: Updating HTML files to use standard JavaScript..."
sed -i 's/redirect.min.js/redirect.js/g' $TEMP_DIR/popup/redirect.html
sed -i 's/options.min.js/options.js/g' $TEMP_DIR/options/options.html

# Create the XPI file
cd $TEMP_DIR
echo "Step 6: Creating ZIP archive..."
zip -r "$OLDPWD/$XPI_FILE" *
ZIP_RESULT=$?
cd - > /dev/null

# Check if the XPI file was created successfully
if [ $ZIP_RESULT -eq 0 ] && [ -f "$XPI_FILE" ]; then
    echo "Step 7: XPI file created successfully: $XPI_FILE"
    echo "You can install it in Firefox by going to:"
    echo "  - Temporary installation: about:debugging > This Firefox > Load Temporary Add-on"
    echo "  - Permanent installation: about:addons > gear icon > Install Add-on From File..."
else
    echo "Step 7: Failed to create XPI file. Error code: $ZIP_RESULT"
fi

# Clean up
rm -rf "$TEMP_DIR"
echo "Step 8: Cleaned up temporary directory"

echo "======================================================"
echo "Standard build complete!"
echo "======================================================"

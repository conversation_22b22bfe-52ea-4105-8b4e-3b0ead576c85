   const DEFAULT_SETTINGS ={  bahtbrowseHost: 'localhost',  bahtbrowsePort: '8082',  openInNewTab: true,  enableWhitelist: false,  whitelist: [],  enableBlacklist: false,  blacklist: [],  showNotifications: true,  connectionStatusCheck: true,  connectionCheckInterval: 30  };   let serverConnectionStatus ={  isConnected: false,  lastChecked: null,  checkInProgress: false };   console.log('10baht Browse Bouncer initializing...');  browser.runtime.onInstalled.addListener(() =>{  console.log('Extension installed or updated');  browser.storage.sync.get('settings').then((result) =>{  if (!result.settings){  console.log('No settings found,using defaults');  browser.storage.sync.set({ settings: DEFAULT_SETTINGS })  .then(() => console.log('Default settings saved'))  .catch(error => console.error('Error saving default settings:',error));  }else{  console.log('Existing settings found:',result.settings);  }      startConnectionStatusChecker();      showNotification('10baht Browse Installed','Click the toolbar button to securely browse pages');  }).catch(error =>{  console.error('Error getting settings during initialization:',error);  }); });   console.log('10baht Browse loaded,version:',browser.runtime.getManifest().version);   window.addEventListener('error',function(event){  console.error('Global error caught:',event.error); });   window.addEventListener('unhandledrejection',function(event){  console.error('Unhandled promise rejection:',event.reason); });   function setupContextMenus(){    browser.contextMenus.removeAll().then(() =>{    browser.contextMenus.create({  id: 'open-in-10baht',  title: 'Open in 10baht Browse',  contexts: ['link','page']  });      browser.contextMenus.create({  id: 'open-in-10baht-new',  title: 'Open in New Session',  contexts: ['link','page'],  parentId: 'open-in-10baht'  });      browser.contextMenus.create({  id: 'open-in-10baht-link',  title: 'Open Link in 10baht Browse',  contexts: ['link']  });      browser.contextMenus.create({  id: 'open-in-10baht-page',  title: 'Browse This Page Securely',  contexts: ['page']  });  }); }   setupContextMenus();   browser.contextMenus.onClicked.addListener((info,tab) =>{  console.log('Context menu item clicked:',info.menuItemId);    switch (info.menuItemId){  case 'open-in-10baht':  case 'open-in-10baht-new':    const url = info.linkUrl || tab.url;  redirectToBahtBrowse(tab,url);  break;    case 'open-in-10baht-link':    if (info.linkUrl){  redirectToBahtBrowse(tab,info.linkUrl);  }  break;    case 'open-in-10baht-page':    redirectToBahtBrowse(tab,tab.url);  break;  } });   browser.browserAction.onClicked.addListener((tab) =>{  console.log('Toolbar button clicked for tab:',tab);  console.log('Tab URL:',tab.url);  redirectToBahtBrowse(tab,tab.url); });   function checkBahtBrowseAvailability(host,port){  return new Promise((resolve,reject) =>{  console.log(`Checking if 10baht Browse is available at ${host}:${port}`);     const testUrl = `http:  console.log(`Using test URL: ${testUrl}`);     const pluginVersion = browser.runtime.getManifest().version;  console.log('Plugin version:',pluginVersion);     const timeoutPromise = new Promise((_,reject) =>{  setTimeout(() => reject(new Error('Connection timeout')),5000);  });   Promise.race([  fetch(testUrl,{  method: 'GET',  headers:{  'X-Test-Connection': 'true',  'X-Client-Info': '10Baht-Browse-Firefox-Plugin',  'X-Request-Source': 'background-script',  'X-Plugin-Version': pluginVersion  }  }),  timeoutPromise  ])  .then(response =>{  console.log('10baht Browse service response:',response);  if (response.ok){  return response.json().catch(() => ({ status: 'success' }));  }else{  throw new Error(`Server responded with status: ${response.status}`);  }  })  .then(data =>{  console.log('10baht Browse service is available:',data);  resolve(true);  })  .catch(error =>{  console.error('10baht Browse service is not available:',error);  resolve(false);  });  }); }   function startConnectionStatusChecker(){  browser.storage.sync.get('settings').then((result) =>{  const settings = result.settings || DEFAULT_SETTINGS;    if (settings.connectionStatusCheck){    checkServerConnectionStatus();      setInterval(() =>{  checkServerConnectionStatus();  },settings.connectionCheckInterval * 1000);    console.log(`Connection status checker started,interval: ${settings.connectionCheckInterval}seconds`);  }else{  console.log('Connection status checker disabled in settings');  }  }).catch(error =>{  console.error('Error starting connection checker:',error);  }); }   function checkServerConnectionStatus(){    if (serverConnectionStatus.checkInProgress){  return;  }    serverConnectionStatus.checkInProgress = true;    browser.storage.sync.get('settings').then((result) =>{  const settings = result.settings || DEFAULT_SETTINGS;    checkBahtBrowseAvailability(settings.bahtbrowseHost,settings.bahtbrowsePort)  .then(isAvailable =>{    serverConnectionStatus.isConnected = isAvailable;  serverConnectionStatus.lastChecked = new Date();  serverConnectionStatus.checkInProgress = false;      const iconPath = isAvailable ?   'icons/bahtbrowse-48.png' :   'icons/bahtbrowse-48-disconnected.png';    browser.browserAction.setIcon({ path: iconPath });      browser.runtime.sendMessage({  type: 'connectionStatusUpdate',  status: serverConnectionStatus  }).catch(() =>{    });    console.log(`Connection status updated: ${isAvailable ? 'Connected' : 'Disconnected'}`);  })  .catch(error =>{  console.error('Error checking server connection:',error);  serverConnectionStatus.checkInProgress = false;  serverConnectionStatus.isConnected = false;      browser.browserAction.setIcon({ path: 'icons/bahtbrowse-48-disconnected.png' });  });  }).catch(error =>{  console.error('Error getting settings for connection check:',error);  serverConnectionStatus.checkInProgress = false;  }); }   function getConnectionStatus(){  return serverConnectionStatus; }   function redirectToBahtBrowse(tab,url){  console.log('Redirecting to 10baht Browse:',url);  console.log('Function called with tab:',tab);   browser.storage.sync.get('settings').then((result) =>{  const settings = result.settings || DEFAULT_SETTINGS;  console.log('Using settings:',settings);     const shouldUseCache = serverConnectionStatus.lastChecked &&   (new Date() - serverConnectionStatus.lastChecked < 10000);    const checkPromise = shouldUseCache ?   Promise.resolve(serverConnectionStatus.isConnected) :   checkBahtBrowseAvailability(settings.bahtbrowseHost,settings.bahtbrowsePort);   checkPromise  .then(isAvailable =>{  if (!isAvailable){  const errorMsg = `10baht Browse service is not available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}. Please make sure the service is running.`;  console.error(errorMsg);  alert(errorMsg);  return;  }   try{    const formData = new FormData();  formData.append('url',url);      formData.append('timestamp',Date.now().toString());      const bahtbrowseBaseUrl = `http:  console.log('10baht Browse Base URL:',bahtbrowseBaseUrl);      browser.tabs.create({ url: 'about:blank' })  .then(newTab =>{  console.log('New blank tab created,will POST to 10baht Browse:',newTab);      browser.tabs.executeScript(newTab.id,{  code: `    document.body.innerHTML = '<div style="display: flex;justify-content: center;align-items: center;height: 100vh;font-family: Arial,sans-serif;"><div style="text-align: center;"><h2>Redirecting to 10baht Browse...</h2><p>Secure browsing session is being prepared for: ${url.replace(/'/g,"\\'")}</p><div style="border: 8px solid #f3f3f3;border-top: 8px solid #a520c6;border-radius: 50%;width: 60px;height: 60px;margin: 20px auto;animation: spin 2s linear infinite;"></div></div></div><style>@keyframes spin{0%{ transform: rotate(0deg);}100%{ transform: rotate(360deg);}}</style>';    const formData = new FormData();  formData.append('url','${url.replace(/'/g,"\\'")}');  formData.append('timestamp','${Date.now()}');  formData.append('browser_source','firefox_plugin');    fetch('${bahtbrowseBaseUrl}',{  method: 'POST',  body: formData,  headers:{  'X-Requested-With': '10BahtBrowsePlugin',  'Cache-Control': 'no-cache,no-store,must-revalidate'  },  redirect: 'follow',  cache: 'no-store'  })  .then(response =>{  console.log('Got response:',response);  if (response.redirected){    const redirectUrl = new URL(response.url);  redirectUrl.searchParams.set('_nocache',Date.now());  window.location.href = redirectUrl.toString();  }else{  return response.text().then(text =>{  throw new Error('10baht Browse error: ' + text);  });  }  })  .catch(error =>{  console.error('Error in POST request:',error);  document.body.innerHTML = '<div style="padding: 20px;font-family: Arial,sans-serif;"><h1 style="color: #e74c3c;">Error connecting to 10baht Browse</h1><p>' + error.message + '</p><button onclick="window.location.reload()" style="padding: 10px 20px;background-color: #3498db;color: white;border: none;border-radius: 4px;cursor: pointer;">Retry</button></div>';  });  `  })  .then(() =>{    showNotification('10baht Browse',`Opening ${url}in 10baht Browse`);  })  .catch(error =>{  console.error('Error executing POST script:',error);  alert(`Error executing POST request: ${error.message}`);  });  })  .catch(error =>{  console.error('Error creating new tab:',error);  alert(`Error opening in new tab: ${error.message}`);  });  }catch (error){  console.error('Error during redirection:',error);  alert(`Error during redirection: ${error.message}`);  }  })  .catch(error =>{  console.error('Error checking 10baht Browse availability:',error);  alert(`Error checking 10baht Browse availability: ${error.message}`);  });  }).catch(error =>{  console.error('Error getting settings:',error);  alert(`Error getting settings: ${error.message}`);  }); }   function showNotification(title,message){  try{  console.log('Showing notification:',title,message);  browser.notifications.create({  type: 'basic',  iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),  title: title,  message: message  }).then(notificationId =>{  console.log('Notification shown,ID:',notificationId);  }).catch(error =>{  console.error('Error showing notification:',error);    console.warn(`Notification (fallback): ${title}- ${message}`);  });  }catch (error){  console.error('Error in showNotification:',error);    console.warn(`Notification (fallback): ${title}- ${message}`);  } }   function shouldRedirect(url,settings){  console.log('Checking if URL should be redirected:',url);  console.log('Whitelist enabled:',settings.enableWhitelist,'Whitelist:',settings.whitelist);  console.log('Blacklist enabled:',settings.enableBlacklist,'Blacklist:',settings.blacklist);     if (settings.enableWhitelist && settings.whitelist.length > 0){  const shouldRedirect = settings.whitelist.some(pattern => url.includes(pattern));  console.log('Whitelist check result:',shouldRedirect);  return shouldRedirect;  }     if (settings.enableBlacklist && settings.blacklist.length > 0){  const shouldRedirect = !settings.blacklist.some(pattern => url.includes(pattern));  console.log('Blacklist check result:',shouldRedirect);  return shouldRedirect;  }     console.log('No whitelist/blacklist enabled,should redirect');  return true; }   browser.runtime.onMessage.addListener((message,sender,sendResponse) =>{  if (message.type === 'getConnectionStatus'){  sendResponse(serverConnectionStatus);  }else if (message.type === 'checkConnectionNow'){  checkServerConnectionStatus();  sendResponse({ message: 'Connection check initiated' });  }  return true; });

#!/bin/bash

# Test script for BahtBrowse Firefox Plugin
# This script requires web-ext to be installed

# Check if web-ext is installed
if ! command -v web-ext &> /dev/null; then
    echo "web-ext is not installed. Installing..."
    npm install -g web-ext
fi

echo "======================================================"
echo "BahtBrowse Firefox Extension - Test Script"
echo "======================================================"

# Run the extension in Firefox
echo "Running the extension in Firefox..."
web-ext run --source-dir .

echo "======================================================"
echo "Test completed!"
echo "======================================================"

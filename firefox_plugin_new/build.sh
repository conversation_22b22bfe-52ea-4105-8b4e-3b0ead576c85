#!/bin/bash

# BahtBrowse Firefox Plugin Build Script
# This script builds the Firefox plugin with minification

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "BahtBrowse Firefox Extension - Build Script"
echo "======================================================"

# Create temporary directory for build
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Create directories in temp dir
mkdir -p $TEMP_DIR/icons
mkdir -p $TEMP_DIR/popup
mkdir -p $TEMP_DIR/options

# Step 1: Minify JavaScript files
echo "Step 1: Minifying JavaScript files..."

# Check if terser is installed
if ! command -v terser &> /dev/null; then
    echo "Terser is not installed. Installing..."
    npm install -g terser
fi

# Minify background.js
echo "Minifying background.js..."
terser background.js -o $TEMP_DIR/background.js -c -m

# Minify popup.js
echo "Minifying popup/popup.js..."
terser popup/popup.js -o $TEMP_DIR/popup/popup.js -c -m

# Minify options.js
echo "Minifying options/options.js..."
terser options/options.js -o $TEMP_DIR/options/options.js -c -m

# Step 2: Copy other files
echo "Step 2: Copying other files..."
cp manifest.json $TEMP_DIR/
cp -r icons/* $TEMP_DIR/icons/
cp popup/popup.html $TEMP_DIR/popup/
cp popup/popup.css $TEMP_DIR/popup/
cp options/options.html $TEMP_DIR/options/
cp options/options.css $TEMP_DIR/options/

# Step 3: Create build directory if it doesn't exist
echo "Step 3: Creating build directory..."
mkdir -p build

# Step 4: Create the XPI file
echo "Step 4: Creating XPI file..."
cd $TEMP_DIR
zip -r ../build/bahtbrowse_bouncer.xpi *
cd -

# Step 5: Clean up
echo "Step 5: Cleaning up..."
rm -rf $TEMP_DIR

echo "======================================================"
echo "Build completed successfully!"
echo "XPI file created at: build/bahtbrowse_bouncer.xpi"
echo "======================================================"

#!/bin/bash

# Generate placeholder icons for BahtBrowse Firefox Plugin
# This script requires ImageMagick to be installed

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "ImageMagick is not installed. Please install it to generate icons."
    exit 1
fi

echo "Generating placeholder icons for BahtBrowse Firefox Plugin..."

# Create icons directory if it doesn't exist
mkdir -p icons

# Generate icons with different sizes
# 19x19 icon
convert -size 19x19 xc:none -fill "#a520c6" -draw "circle 9.5,9.5 9.5,1" -fill white -draw "text 7,13 'B'" icons/bahtbrowse-19.png

# 38x38 icon
convert -size 38x38 xc:none -fill "#a520c6" -draw "circle 19,19 19,2" -fill white -draw "text 14,26 'B'" icons/bahtbrowse-38.png

# 48x48 icon
convert -size 48x48 xc:none -fill "#a520c6" -draw "circle 24,24 24,2" -fill white -draw "text 18,32 'B'" icons/bahtbrowse-48.png

# 96x96 icon
convert -size 96x96 xc:none -fill "#a520c6" -draw "circle 48,48 48,4" -fill white -draw "text 36,64 'B'" icons/bahtbrowse-96.png

# Disconnected versions (red)
convert -size 19x19 xc:none -fill "#F44336" -draw "circle 9.5,9.5 9.5,1" -fill white -draw "text 7,13 'B'" icons/bahtbrowse-19-disconnected.png
convert -size 38x38 xc:none -fill "#F44336" -draw "circle 19,19 19,2" -fill white -draw "text 14,26 'B'" icons/bahtbrowse-38-disconnected.png
convert -size 48x48 xc:none -fill "#F44336" -draw "circle 24,24 24,2" -fill white -draw "text 18,32 'B'" icons/bahtbrowse-48-disconnected.png
convert -size 96x96 xc:none -fill "#F44336" -draw "circle 48,48 48,4" -fill white -draw "text 36,64 'B'" icons/bahtbrowse-96-disconnected.png

# Create bouncer image
convert -size 96x96 xc:none -fill "#a520c6" -draw "circle 48,48 48,4" -fill white -draw "path 'M 30,30 L 66,30 L 66,66 L 30,66 Z'" -fill "#a520c6" -draw "path 'M 35,35 L 61,35 L 61,61 L 35,61 Z'" icons/bouncer.png

echo "Icons generated successfully!"

#!/bin/bash

# Install script for BahtBrowse Firefox Plugin
# This script copies the plugin to the browser_plugins directory

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "BahtBrowse Firefox Extension - Install Script"
echo "======================================================"

# Check if browser_plugins directory exists
if [ ! -d "../browser_plugins" ]; then
    echo "Creating browser_plugins directory..."
    mkdir -p ../browser_plugins
fi

# Check if firefox directory exists
if [ ! -d "../browser_plugins/firefox" ]; then
    echo "Creating browser_plugins/firefox directory..."
    mkdir -p ../browser_plugins/firefox
else
    echo "Backing up existing firefox plugin..."
    BACKUP_DIR="../browser_plugins/firefox_backup_$(date +%Y%m%d%H%M%S)"
    mkdir -p $BACKUP_DIR
    cp -r ../browser_plugins/firefox/* $BACKUP_DIR/
    echo "Backup created at $BACKUP_DIR"
    
    echo "Removing existing firefox plugin..."
    rm -rf ../browser_plugins/firefox/*
fi

# Copy files to browser_plugins/firefox
echo "Copying files to browser_plugins/firefox..."
cp -r * ../browser_plugins/firefox/

echo "======================================================"
echo "Installation completed successfully!"
echo "The plugin has been installed to ../browser_plugins/firefox/"
echo "======================================================"

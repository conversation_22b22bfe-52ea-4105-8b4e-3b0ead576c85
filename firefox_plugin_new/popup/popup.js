/**
 * Ba<PERSON><PERSON><PERSON>e Bouncer - Popup Script
 * 
 * This script handles the popup UI functionality.
 */

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8081',
  openInNewTab: true,
  showNotifications: true
};

// Function to initialize the popup
function initPopup() {
  console.log('Popup initialization started');
  
  // Set up button event listeners
  const redirectButton = document.getElementById('redirectButton');
  const optionsButton = document.getElementById('optionsButton');
  
  if (redirectButton) {
    redirectButton.addEventListener('click', function() {
      console.log('Redirect button clicked');
      redirectCurrentPage();
    });
  }
  
  if (optionsButton) {
    optionsButton.addEventListener('click', function() {
      console.log('Options button clicked');
      openOptions();
    });
  }
  
  // Check connection status
  updateConnectionStatus();
}

// Function to redirect current page to BahtBrowse
function redirectCurrentPage() {
  browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
    const currentTab = tabs[0];
    const currentUrl = currentTab.url;
    
    // Send message to background script to handle redirection
    browser.runtime.sendMessage({
      type: 'redirect',
      tab: currentTab,
      url: currentUrl
    }).then(() => {
      window.close();
    }).catch(error => {
      console.error('Error sending redirect message:', error);
      showStatus('Error redirecting: ' + error.message, true);
    });
  }).catch(error => {
    console.error('Error getting current tab:', error);
    showStatus('Error getting current tab: ' + error.message, true);
  });
}

// Function to open options page
function openOptions() {
  browser.runtime.openOptionsPage().then(() => {
    window.close();
  }).catch(error => {
    console.error('Error opening options page:', error);
    showStatus('Error opening options: ' + error.message, true);
  });
}

// Function to update connection status display
function updateConnectionStatus() {
  const statusDot = document.getElementById('statusDot');
  const statusText = document.getElementById('statusText');
  
  if (statusDot && statusText) {
    // Set initial checking state
    statusDot.className = 'status-dot checking';
    statusText.textContent = 'Checking connection...';
    
    // Get connection status from background script
    browser.runtime.sendMessage({ type: 'getConnectionStatus' }).then(status => {
      if (status && status.lastChecked) {
        if (status.isConnected) {
          statusDot.className = 'status-dot connected';
          statusText.textContent = 'Connected to BahtBrowse';
        } else {
          statusDot.className = 'status-dot disconnected';
          statusText.textContent = 'Not connected to BahtBrowse';
        }
      } else {
        // If no status available, trigger a check
        browser.runtime.sendMessage({ type: 'checkConnectionNow' });
        
        // Check again after a delay
        setTimeout(updateConnectionStatus, 1000);
      }
    }).catch(error => {
      console.error('Error getting connection status:', error);
      statusDot.className = 'status-dot disconnected';
      statusText.textContent = 'Error checking connection';
    });
  }
}

// Function to show status message
function showStatus(message, isError = false) {
  const statusElement = document.getElementById('status');
  if (statusElement) {
    statusElement.textContent = message;
    statusElement.style.display = 'block';
    statusElement.style.backgroundColor = isError ? 'var(--error-bg)' : 'var(--success-bg)';
    statusElement.style.color = isError ? 'var(--error-color)' : 'var(--success-color)';
  }
}

// Listen for connection status updates from background script
browser.runtime.onMessage.addListener((message) => {
  if (message.type === 'connectionStatusUpdate') {
    updateConnectionStatus();
  }
});

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', initPopup);

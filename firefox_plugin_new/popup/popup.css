/* Dark mode detection */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #2a2a2e;
    --text-color: #f9f9fa;
    --border-color: #5c5c66;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #38383d;
    --secondary-hover: #42414d;
    --url-bg: #38383d;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --error-bg: #5e292b;
    --error-color: #ff9aa2;
    --success-bg: #054d2e;
    --success-color: #87efb5;
    --header-bg: #1c1b22;
    --footer-bg: #1c1b22;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --background-color: #f9f9fa;
    --text-color: #0c0c0d;
    --border-color: #ddd;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #f9f9fa;
    --secondary-hover: #e7e7e7;
    --url-bg: #f0f0f4;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --error-bg: #ffdddd;
    --error-color: #990000;
    --success-bg: #ddffdd;
    --success-color: #006600;
    --header-bg: #f0f0f4;
    --footer-bg: #f0f0f4;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  width: 320px;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.header {
  background-color: var(--header-bg);
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bouncer-image {
  margin-bottom: 16px;
  filter: drop-shadow(0 4px 8px var(--shadow-color));
  transition: transform 0.3s ease;
}

.bouncer-image:hover {
  transform: scale(1.05);
}

.buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
  margin-top: 16px;
}

button {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

button:active {
  transform: scale(0.98);
}

#redirectButton {
  background-color: var(--primary-color);
  color: white;
}

#redirectButton:hover {
  background-color: var(--primary-hover);
}

#optionsButton {
  background-color: var(--secondary-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

#optionsButton:hover {
  background-color: var(--secondary-hover);
}

.footer {
  background-color: var(--footer-bg);
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
  font-size: 12px;
}

.status-container {
  margin-top: 16px;
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  background-color: var(--secondary-bg);
  text-align: center;
}

.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.connected {
  background-color: #4CAF50;
  box-shadow: 0 0 5px #4CAF50;
}

.status-dot.disconnected {
  background-color: #F44336;
  box-shadow: 0 0 5px #F44336;
}

.status-dot.checking {
  background-color: #FFC107;
  box-shadow: 0 0 5px #FFC107;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

# BahtBrowse Firefox Plugin

A Firefox extension that allows users to quickly redirect their current browsing session to the BahtBrowse containerized browser environment for enhanced security.

## Features

- One-click redirection to BahtBrowse
- Clean, intuitive user interface with bouncer image
- Configurable BahtBrowse host and port
- Option to open in new tab or current tab
- Connection testing to verify BahtBrowse service availability
- Dark mode support that detects browser/system settings
- Minified JavaScript for improved performance

## Installation

### For Development (Temporary Installation)

1. Open Firefox
2. Navigate to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on..."
5. Navigate to the `build` directory
6. Select the `bahtbrowse_bouncer.xpi` file

### For Regular Use (Permanent Installation)

1. Open Firefox
2. Navigate to `about:addons`
3. Click the gear icon and select "Install Add-on From File..."
4. Navigate to the `build` directory
5. Select the `bahtbrowse_bouncer.xpi` file

## Usage

1. Click the BahtBrowse icon in the Firefox toolbar
2. The popup will display a bouncer image and a button to redirect the current page
3. Click "Open in BahtBrowse" to redirect the current page to the BahtBrowse containerized environment
4. The connection status indicator will show if the BahtBrowse server is reachable

## Configuration

Access the extension settings by:
1. Clicking the "Settings" button in the popup
2. Right-clicking the toolbar icon and selecting "Manage Extension"
3. Going to Add-ons Manager (`about:addons`) and finding BahtBrowse Bouncer
4. Clicking on "Options" or "Preferences"

Settings include:
- BahtBrowse host and port (with connection testing)
- Opening behavior (new tab or current tab)
- Notification preferences
- Connection status check interval
- Theme preferences (respect system theme)

## Development

### Prerequisites

- Firefox Developer Edition or Firefox Browser
- Node.js and npm (for minification)

### Building the Extension

To build the extension with JavaScript minification:

```bash
chmod +x build.sh
./build.sh
```

This will create `build/bahtbrowse_bouncer.xpi` with minified JavaScript files for improved performance.

## Directory Structure

```
firefox_plugin/
├── background.js         # Main background script
├── build.sh              # Build script with minification
├── build/                # Build output directory
│   └── bahtbrowse_bouncer.xpi  # Packaged extension
├── icons/                # Extension icons
│   ├── bahtbrowse-19.png
│   ├── bahtbrowse-38.png
│   ├── bahtbrowse-48.png
│   ├── bahtbrowse-96.png
│   └── bouncer.png       # Bouncer image for popup
├── manifest.json         # Extension manifest
├── options/              # Settings page files
│   ├── options.css
│   ├── options.html
│   └── options.js
├── popup/                # Popup UI files
│   ├── popup.css
│   ├── popup.html
│   └── popup.js
└── README.md             # This file
```

## License

[MIT License](LICENSE)

# BahtBrowse Firefox Plugin Installation Guide

This guide will help you install and configure the BahtBrowse Firefox Plugin.

## Prerequisites

- Firefox browser (version 78.0 or later)
- A running BahtBrowse instance

## Installation Methods

### Method 1: Temporary Installation (for Development)

1. Open Firefox
2. Navigate to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on..."
5. Navigate to the `build` directory
6. Select the `bahtbrowse_bouncer.xpi` file

Note: Temporary installations will be removed when Firefox is closed.

### Method 2: Permanent Installation

1. Open Firefox
2. Navigate to `about:addons`
3. Click the gear icon and select "Install Add-on From File..."
4. Navigate to the `build` directory
5. Select the `bahtbrowse_bouncer.xpi` file

## Configuration

After installation, you should configure the plugin to connect to your BahtBrowse instance:

1. Click the BahtBrowse icon in the Firefox toolbar
2. Click the "Settings" button to open the preferences page
3. Configure the following settings:
   - **Host**: The hostname or IP address of your BahtBrowse server (default: `localhost`)
   - **Port**: The port number of your BahtBrowse server (default: `8081`)
   - **Open in new tab**: Whether to open redirected pages in a new tab
   - **Show notifications**: Whether to show notifications when redirecting
   - **Enable periodic connection status check**: Whether to periodically check the connection to the BahtBrowse server
   - **Check interval**: How often to check the connection (in seconds)
   - **Respect system theme**: Whether to use the system's theme (light/dark mode)

4. Click the "Test Connection" button to verify that the plugin can connect to your BahtBrowse server
5. Click "Save" to apply your settings

## Usage

1. Navigate to a website you want to view in BahtBrowse
2. Click the BahtBrowse icon in the Firefox toolbar
3. Click "Open in BahtBrowse" in the popup
4. The website will open in the BahtBrowse containerized environment

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the BahtBrowse server:

1. Ensure the BahtBrowse server is running
2. Check that the host and port settings are correct
3. Try using "localhost" instead of "127.0.0.1" or vice versa
4. Check if there are any firewalls blocking the connection
5. Use the "Test Connection" button in the settings to diagnose issues

### Plugin Not Working

If the plugin is not working correctly:

1. Check the connection status in the popup
2. Verify your settings in the preferences page
3. Try restarting Firefox
4. Try reinstalling the plugin

## Uninstallation

1. Open Firefox
2. Navigate to `about:addons`
3. Find the BahtBrowse Bouncer plugin
4. Click the three dots next to the plugin and select "Remove"
5. Confirm the removal when prompted

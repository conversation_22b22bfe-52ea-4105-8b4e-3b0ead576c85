/**
 * BahtBrowse Bouncer - Options Script
 * 
 * This script handles the options page functionality.
 */

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8081',
  openInNewTab: true,
  showNotifications: true,
  connectionStatusCheck: true,
  connectionCheckInterval: 30, // seconds
  respectSystemTheme: true
};

// Initialize the options page
document.addEventListener('DOMContentLoaded', function() {
  // Load settings
  loadSettings();
  
  // Add event listeners to buttons
  document.getElementById('saveButton').addEventListener('click', saveSettings);
  document.getElementById('resetButton').addEventListener('click', resetSettings);
  document.getElementById('testConnectionButton').addEventListener('click', testConnection);
  
  // Check connection status
  updateConnectionStatus();
});

/**
 * Load settings from storage
 */
function loadSettings() {
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    
    // Populate form fields
    document.getElementById('bahtbrowseHost').value = settings.bahtbrowseHost || DEFAULT_SETTINGS.bahtbrowseHost;
    document.getElementById('bahtbrowsePort').value = settings.bahtbrowsePort || DEFAULT_SETTINGS.bahtbrowsePort;
    document.getElementById('openInNewTab').checked = settings.openInNewTab !== undefined ? settings.openInNewTab : DEFAULT_SETTINGS.openInNewTab;
    document.getElementById('showNotifications').checked = settings.showNotifications !== undefined ? settings.showNotifications : DEFAULT_SETTINGS.showNotifications;
    document.getElementById('connectionStatusCheck').checked = settings.connectionStatusCheck !== undefined ? settings.connectionStatusCheck : DEFAULT_SETTINGS.connectionStatusCheck;
    document.getElementById('connectionCheckInterval').value = settings.connectionCheckInterval || DEFAULT_SETTINGS.connectionCheckInterval;
    document.getElementById('respectSystemTheme').checked = settings.respectSystemTheme !== undefined ? settings.respectSystemTheme : DEFAULT_SETTINGS.respectSystemTheme;
    
    console.log('Settings loaded:', settings);
  }).catch((error) => {
    console.error('Error loading settings:', error);
    showConnectionStatus('Error loading settings: ' + error.message, 'error');
  });
}

/**
 * Save settings to storage
 */
function saveSettings() {
  const settings = {
    bahtbrowseHost: document.getElementById('bahtbrowseHost').value.trim() || DEFAULT_SETTINGS.bahtbrowseHost,
    bahtbrowsePort: document.getElementById('bahtbrowsePort').value.trim() || DEFAULT_SETTINGS.bahtbrowsePort,
    openInNewTab: document.getElementById('openInNewTab').checked,
    showNotifications: document.getElementById('showNotifications').checked,
    connectionStatusCheck: document.getElementById('connectionStatusCheck').checked,
    connectionCheckInterval: parseInt(document.getElementById('connectionCheckInterval').value) || DEFAULT_SETTINGS.connectionCheckInterval,
    respectSystemTheme: document.getElementById('respectSystemTheme').checked
  };
  
  browser.storage.sync.set({ settings }).then(() => {
    console.log('Settings saved:', settings);
    showConnectionStatus('Settings saved successfully', 'success');
    
    // Notify background script about settings change
    browser.runtime.sendMessage({ type: 'settingsUpdated', settings });
  }).catch((error) => {
    console.error('Error saving settings:', error);
    showConnectionStatus('Error saving settings: ' + error.message, 'error');
  });
}

/**
 * Reset settings to defaults
 */
function resetSettings() {
  if (confirm('Are you sure you want to reset all settings to defaults?')) {
    browser.storage.sync.set({ settings: DEFAULT_SETTINGS }).then(() => {
      console.log('Settings reset to defaults');
      loadSettings();
      showConnectionStatus('Settings reset to defaults', 'success');
      
      // Notify background script about settings change
      browser.runtime.sendMessage({ type: 'settingsUpdated', settings: DEFAULT_SETTINGS });
    }).catch((error) => {
      console.error('Error resetting settings:', error);
      showConnectionStatus('Error resetting settings: ' + error.message, 'error');
    });
  }
}

/**
 * Test connection to BahtBrowse server
 */
function testConnection() {
  const host = document.getElementById('bahtbrowseHost').value.trim() || DEFAULT_SETTINGS.bahtbrowseHost;
  const port = document.getElementById('bahtbrowsePort').value.trim() || DEFAULT_SETTINGS.bahtbrowsePort;
  
  showConnectionStatus('Testing connection...', 'testing');
  
  const testUrl = `http://${host}:${port}/browse/test-connection`;
  console.log('Testing connection to:', testUrl);
  
  fetch(testUrl, {
    method: 'GET',
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    },
    cache: 'no-store'
  })
  .then(response => {
    if (response.ok) {
      console.log('Connection test successful');
      showConnectionStatus('Connection successful! BahtBrowse server is reachable.', 'success');
      updateStatusDot(true);
    } else {
      console.log('Connection test failed with status:', response.status);
      showConnectionStatus(`Connection failed with status: ${response.status}`, 'error');
      updateStatusDot(false);
    }
  })
  .catch(error => {
    console.error('Connection test error:', error);
    showConnectionStatus(`Connection failed: ${error.message}`, 'error');
    updateStatusDot(false);
  });
}

/**
 * Show connection status message
 * @param {string} message - Status message
 * @param {string} type - Status type (success, error, testing)
 */
function showConnectionStatus(message, type) {
  const statusElement = document.getElementById('connectionStatus');
  if (statusElement) {
    statusElement.textContent = message;
    statusElement.className = 'connection-status ' + type;
  }
}

/**
 * Update connection status display
 */
function updateConnectionStatus() {
  const statusDot = document.getElementById('statusDot');
  const statusText = document.getElementById('statusText');
  const lastChecked = document.getElementById('lastChecked');
  
  if (statusDot && statusText) {
    // Set initial checking state
    statusDot.className = 'status-dot checking';
    statusText.textContent = 'Checking connection...';
    
    // Get connection status from background script
    browser.runtime.sendMessage({ type: 'getConnectionStatus' }).then(status => {
      if (status && status.lastChecked) {
        updateStatusDot(status.isConnected);
        
        if (status.isConnected) {
          statusText.textContent = 'Connected to BahtBrowse';
        } else {
          statusText.textContent = 'Not connected to BahtBrowse';
        }
        
        // Format last checked time
        const lastCheckedDate = new Date(status.lastChecked);
        lastChecked.textContent = `Last checked: ${lastCheckedDate.toLocaleTimeString()}`;
      } else {
        // If no status available, trigger a check
        browser.runtime.sendMessage({ type: 'checkConnectionNow' });
        
        // Check again after a delay
        setTimeout(updateConnectionStatus, 1000);
      }
    }).catch(error => {
      console.error('Error getting connection status:', error);
      statusDot.className = 'status-dot disconnected';
      statusText.textContent = 'Error checking connection';
    });
  }
}

/**
 * Update status dot
 * @param {boolean} isConnected - Whether connected to BahtBrowse
 */
function updateStatusDot(isConnected) {
  const statusDot = document.getElementById('statusDot');
  if (statusDot) {
    statusDot.className = 'status-dot ' + (isConnected ? 'connected' : 'disconnected');
  }
}

// Listen for connection status updates from background script
browser.runtime.onMessage.addListener((message) => {
  if (message.type === 'connectionStatusUpdate') {
    updateConnectionStatus();
  }
});

/* Dark mode detection */
@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #1f1d1d;
    --container-bg: #212529;
    --section-bg: #331f38;
    --text-color: #f9f9fa;
    --border-color: #5c5c66;
    --input-bg: #1a1c20;
    --input-text: #f9f9fa;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #212529;
    --secondary-hover: #331f38;
    --success-bg: #054d2e;
    --success-color: #87efb5;
    --error-bg: #5e292b;
    --error-color: #ff9aa2;
    --testing-bg: #2a4887;
    --testing-color: #a7c6ff;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --checkbox-bg: #212529;
    --primary-color-rgb: 165, 32, 198;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
    --status-indicator-bg: rgba(26, 28, 32, 0.8);
    --last-checked-color: #8a8d91;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --background-color: #f5f5f5;
    --container-bg: #ffffff;
    --section-bg: #f9f0ff;
    --text-color: #333333;
    --border-color: #dddddd;
    --input-bg: #ffffff;
    --input-text: #333333;
    --primary-color: #a520c6;
    --primary-hover: #8a1dad;
    --secondary-bg: #f0f0f0;
    --secondary-hover: #e0e0e0;
    --success-bg: #d4edda;
    --success-color: #155724;
    --error-bg: #f8d7da;
    --error-color: #721c24;
    --testing-bg: #cce5ff;
    --testing-color: #004085;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --checkbox-bg: #ffffff;
    --primary-color-rgb: 165, 32, 198;
    --glow-color: #e60073;
    --secondary-glow: #0fa;
    --status-indicator-bg: rgba(255, 255, 255, 0.8);
    --last-checked-color: #6c757d;
  }
}

/* BahtBrowse theme styles */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 20px;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--container-bg);
  border-radius: 15px;
  box-shadow: 0 10px 30px var(--shadow-color);
  padding: 24px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.header-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.header-logo img {
  filter: drop-shadow(0 0 10px rgba(165, 32, 198, 0.5));
}

h1 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 24px;
  color: var(--primary-color);
  font-size: 24px;
}

h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: var(--primary-color);
}

.section {
  background-color: var(--section-bg);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px var(--shadow-color);
}

.form-group {
  margin-bottom: 16px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

input[type="text"],
input[type="number"] {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 14px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.25);
}

.checkbox {
  display: flex;
  align-items: center;
}

.checkbox input[type="checkbox"] {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  background-color: var(--checkbox-bg);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  position: relative;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.checkbox input[type="checkbox"]:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox label {
  margin-bottom: 0;
  cursor: pointer;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

button {
  padding: 10px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
  border: none;
}

button:active {
  transform: scale(0.98);
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
}

.primary-button:hover {
  background-color: var(--primary-hover);
}

.secondary-button {
  background-color: var(--secondary-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.secondary-button:hover {
  background-color: var(--secondary-hover);
}

.test-connection-group {
  margin-top: 16px;
}

.test-connection-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.connection-status {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.connection-status.success {
  background-color: var(--success-bg);
  color: var(--success-color);
}

.connection-status.error {
  background-color: var(--error-bg);
  color: var(--error-color);
}

.connection-status.testing {
  background-color: var(--testing-bg);
  color: var(--testing-color);
}

.version-info {
  margin-top: 24px;
  text-align: center;
  font-size: 12px;
  color: var(--last-checked-color);
}

.connection-status-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--status-indicator-bg);
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-bottom: 8px;
}

.status-dot.connected {
  background-color: #4CAF50;
  box-shadow: 0 0 10px #4CAF50;
}

.status-dot.disconnected {
  background-color: #F44336;
  box-shadow: 0 0 10px #F44336;
}

.status-dot.checking {
  background-color: #FFC107;
  box-shadow: 0 0 10px #FFC107;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.last-checked {
  font-size: 12px;
  color: var(--last-checked-color);
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

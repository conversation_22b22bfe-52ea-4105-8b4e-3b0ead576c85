<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css">
  <title>BahtBrowse Bouncer - Preferences</title>
</head>
<body>
  <div class="container">
    <div class="header-logo">
      <img src="../icons/bahtbrowse-48.png" alt="BahtBrowse Logo" width="48" height="48">
    </div>
    <h1>BahtBrowse Bouncer Preferences</h1>

    <!-- Connection Status Indicator -->
    <div class="connection-status-indicator">
      <div class="status-dot disconnected" id="statusDot"></div>
      <div class="status-text" id="statusText">Checking connection...</div>
      <div class="last-checked" id="lastChecked"></div>
    </div>

    <div class="section" id="serverSettingsSection">
      <h2>BahtBrowse Server</h2>
      <div class="form-group">
        <label for="bahtbrowseHost">Host:</label>
        <input type="text" id="bahtbrowseHost" placeholder="localhost">
      </div>
      <div class="form-group">
        <label for="bahtbrowsePort">Port:</label>
        <input type="text" id="bahtbrowsePort" placeholder="8081">
      </div>
      <div class="form-group test-connection-group">
        <div class="test-connection-container">
          <button id="testConnectionButton" class="secondary-button">
            Test Connection
          </button>
          <div id="connectionStatus" class="connection-status"></div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Behavior</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="openInNewTab">
        <label for="openInNewTab">Open in new tab</label>
      </div>
      <div class="form-group checkbox">
        <input type="checkbox" id="showNotifications">
        <label for="showNotifications">Show notifications</label>
      </div>
      <div class="form-group checkbox">
        <input type="checkbox" id="connectionStatusCheck">
        <label for="connectionStatusCheck">Enable periodic connection status check</label>
      </div>
      <div class="form-group">
        <label for="connectionCheckInterval">Check interval (seconds):</label>
        <input type="number" id="connectionCheckInterval" min="5" max="300" placeholder="30">
      </div>
    </div>

    <div class="section">
      <h2>Appearance</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="respectSystemTheme">
        <label for="respectSystemTheme">Respect system theme (light/dark mode)</label>
      </div>
    </div>

    <div class="button-group">
      <button id="saveButton" class="primary-button">Save</button>
      <button id="resetButton" class="secondary-button">Reset to Defaults</button>
    </div>

    <div class="version-info">
      <p>BahtBrowse Bouncer v1.0.0</p>
    </div>
  </div>
  <script src="options.js"></script>
</body>
</html>

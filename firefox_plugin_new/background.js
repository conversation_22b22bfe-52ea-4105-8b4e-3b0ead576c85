/**
 * Baht<PERSON>rowse Bouncer - Background Script
 * 
 * This script handles the core functionality of redirecting URLs to the BahtBrowse
 * containerized browser service.
 */

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8081',
  openInNewTab: true,
  showNotifications: true,
  connectionStatusCheck: true,
  connectionCheckInterval: 30 // seconds
};

// Connection status variable
let serverConnectionStatus = {
  isConnected: false,
  lastChecked: null,
  checkInProgress: false
};

// Initialize settings
console.log('BahtBrowse Bouncer initializing...');
browser.storage.sync.get('settings').then((result) => {
  const settings = result.settings || DEFAULT_SETTINGS;
  console.log('Settings loaded:', settings);
  
  // Schedule periodic connection checks if enabled
  if (settings.connectionStatusCheck) {
    scheduleConnectionChecks(settings.connectionCheckInterval);
  }
  
  // Perform initial connection check
  checkServerConnectionStatus();
}).catch((error) => {
  console.error('Error loading settings:', error);
});

/**
 * Redirect a URL to BahtBrowse
 * @param {object} tab - The browser tab
 * @param {string} url - The URL to redirect
 */
function redirectToBahtBrowse(tab, url) {
  if (!url) {
    console.error('No URL provided for redirection');
    showNotification('Error', 'No URL provided for redirection');
    return;
  }

  console.log('Redirecting URL to BahtBrowse:', url);
  
  // Get settings
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    const bahtbrowseBaseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/`;
    
    // Create the target URL
    const targetUrl = `${bahtbrowseBaseUrl}?url=${encodeURIComponent(url)}`;
    console.log('Target URL:', targetUrl);
    
    // Open the URL in a new tab or the current tab based on settings
    if (settings.openInNewTab) {
      browser.tabs.create({ url: targetUrl });
    } else {
      browser.tabs.update(tab.id, { url: targetUrl });
    }
    
    // Show notification if enabled
    if (settings.showNotifications) {
      showNotification('BahtBrowse Redirect', `Redirecting to secure browser: ${url}`);
    }
  }).catch((error) => {
    console.error('Error redirecting to BahtBrowse:', error);
    showNotification('Error', `Failed to redirect: ${error.message}`);
  });
}

/**
 * Show a browser notification
 * @param {string} title - Notification title
 * @param {string} message - Notification message
 */
function showNotification(title, message) {
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    
    if (settings.showNotifications) {
      browser.notifications.create({
        type: 'basic',
        iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
        title: title,
        message: message
      });
    }
  }).catch((error) => {
    console.error('Error showing notification:', error);
  });
}

/**
 * Check if BahtBrowse server is available
 * @param {string} host - Server host
 * @param {string} port - Server port
 * @returns {Promise<boolean>} - Promise resolving to true if server is available
 */
function checkBahtBrowseAvailability(host, port) {
  return new Promise((resolve) => {
    const testUrl = `http://${host}:${port}/browse/test-connection`;
    console.log('Testing connection to:', testUrl);
    
    fetch(testUrl, {
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      },
      cache: 'no-store'
    })
    .then(response => {
      if (response.ok) {
        console.log('Connection test successful');
        resolve(true);
      } else {
        console.log('Connection test failed with status:', response.status);
        resolve(false);
      }
    })
    .catch(error => {
      console.error('Connection test error:', error);
      resolve(false);
    });
  });
}

/**
 * Check server connection status
 */
function checkServerConnectionStatus() {
  // Prevent multiple simultaneous checks
  if (serverConnectionStatus.checkInProgress) {
    console.log('Connection check already in progress, skipping');
    return;
  }
  
  serverConnectionStatus.checkInProgress = true;
  
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    
    checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort)
      .then(isAvailable => {
        // Update status
        serverConnectionStatus.isConnected = isAvailable;
        serverConnectionStatus.lastChecked = new Date();
        serverConnectionStatus.checkInProgress = false;
        
        // Update browser action icon based on connection status
        const iconPath = isAvailable ?
          'icons/bahtbrowse-48.png' :
          'icons/bahtbrowse-48-disconnected.png';
        
        browser.browserAction.setIcon({ path: iconPath });
        
        // Send message to any open options or popup pages to update status
        browser.runtime.sendMessage({
          type: 'connectionStatusUpdate',
          status: serverConnectionStatus
        }).catch(() => {
          // Silently ignore errors when no receivers
        });
      });
  }).catch((error) => {
    console.error('Error checking server connection:', error);
    serverConnectionStatus.checkInProgress = false;
  });
}

/**
 * Schedule periodic connection checks
 * @param {number} intervalSeconds - Check interval in seconds
 */
function scheduleConnectionChecks(intervalSeconds) {
  console.log(`Scheduling connection checks every ${intervalSeconds} seconds`);
  
  // Clear any existing interval
  if (window.connectionCheckInterval) {
    clearInterval(window.connectionCheckInterval);
  }
  
  // Set new interval
  window.connectionCheckInterval = setInterval(() => {
    console.log('Running scheduled connection check');
    checkServerConnectionStatus();
  }, intervalSeconds * 1000);
}

// Handle toolbar button click
browser.browserAction.onClicked.addListener((tab) => {
  console.log('Toolbar button clicked for tab:', tab);
  console.log('Tab URL:', tab.url);
  redirectToBahtBrowse(tab, tab.url);
});

// Listen for messages from popup or options pages
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Received message:', message);
  
  if (message.type === 'redirect') {
    redirectToBahtBrowse(message.tab, message.url);
    sendResponse({ success: true });
  } else if (message.type === 'getConnectionStatus') {
    sendResponse(serverConnectionStatus);
  } else if (message.type === 'checkConnectionNow') {
    checkServerConnectionStatus();
    sendResponse({ message: 'Connection check initiated' });
  }
  
  return true;
});

// BahtBrowse Console Logger
// This script captures browser console messages and sends them to the server

(function() {
    // Create a unique session ID for this browsing session
    const sessionId = 'session_' + Date.now();

    // Store original console methods
    const originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error,
        debug: console.debug
    };

    // Function to send log to server
    function sendLogToServer(level, args) {
        try {
            // Get the current URL
            const url = window.location.href;

            // Create a message from the arguments
            let message = '';
            for (let i = 0; i < args.length; i++) {
                if (typeof args[i] === 'object') {
                    try {
                        message += JSON.stringify(args[i]) + ' ';
                    } catch (e) {
                        message += '[Object] ';
                    }
                } else {
                    message += args[i] + ' ';
                }
            }

            // Get stack trace for errors
            let stack = '';
            if (level === 'error') {
                try {
                    throw new Error('Stack trace');
                } catch (e) {
                    stack = e.stack.split('\n').slice(2).join('\n');
                }
            }

            // Create the log data
            const logData = {
                level: level,
                message: message,
                url: url,
                timestamp: new Date().toISOString(),
                session_id: sessionId,
                stack: stack
            };

            // Send the log to the server
            fetch('http://localhost:8082/log-console', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Console-Logger': 'true'
                },
                body: JSON.stringify(logData)
            }).catch(e => {
                // If there's an error sending the log, output to the original console
                originalConsole.error('Error sending log to server:', e);
            });
        } catch (e) {
            // If there's an error in the logger, output to the original console
            originalConsole.error('Error in console logger:', e);
        }
    }

    // Override console methods
    console.log = function() {
        sendLogToServer('log', arguments);
        originalConsole.log.apply(console, arguments);
    };

    console.info = function() {
        sendLogToServer('info', arguments);
        originalConsole.info.apply(console, arguments);
    };

    console.warn = function() {
        sendLogToServer('warn', arguments);
        originalConsole.warn.apply(console, arguments);
    };

    console.error = function() {
        sendLogToServer('error', arguments);
        originalConsole.error.apply(console, arguments);
    };

    console.debug = function() {
        sendLogToServer('debug', arguments);
        originalConsole.debug.apply(console, arguments);
    };

    // Also capture unhandled errors
    window.addEventListener('error', function(event) {
        const errorData = {
            level: 'error',
            message: event.message || 'Unhandled error',
            url: event.filename || window.location.href,
            line: event.lineno,
            column: event.colno,
            timestamp: new Date().toISOString(),
            session_id: sessionId,
            stack: event.error ? event.error.stack : ''
        };

        // Send the error to the server
        fetch('http://localhost:8082/log-console', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Console-Logger': 'true'
            },
            body: JSON.stringify(errorData)
        }).catch(e => {
            // If there's an error sending the log, output to the original console
            originalConsole.error('Error sending error to server:', e);
        });
    });

    // Log that the console logger is initialized
    originalConsole.log('BahtBrowse Console Logger initialized with session ID:', sessionId);
})();

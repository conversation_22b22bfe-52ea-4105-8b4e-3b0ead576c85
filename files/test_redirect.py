#!/usr/bin/env python3
"""
Test script to diagnose redirect issues in BahtBrowse.

This script tests the redirect flow from the app.py server to the VNC interface
by simulating requests and following redirects.
"""

import argparse
import logging
import sys
import requests
import urllib.parse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("redirect-test")

def test_redirect(host, port, url_to_browse):
    """
    Test the redirect flow from app.py to VNC interface.
    
    Args:
        host: The hostname to connect to
        port: The port to connect to
        url_to_browse: The URL to browse (will be passed to app.py)
    """
    logger.info(f"Testing redirect flow for URL: {url_to_browse}")
    
    # Encode the URL for use in query parameters
    encoded_url = urllib.parse.quote(url_to_browse)
    
    # Construct the initial request URL (to app.py)
    initial_url = f"http://{host}:{port}/browse/?url={encoded_url}"
    logger.info(f"Making initial request to: {initial_url}")
    
    try:
        # Make the request with allow_redirects=False to see the redirect
        response = requests.get(initial_url, allow_redirects=False)
        
        if 300 <= response.status_code < 400:
            redirect_url = response.headers.get('Location')
            logger.info(f"Received redirect to: {redirect_url}")
            
            # Now follow the redirect manually
            logger.info(f"Following redirect to: {redirect_url}")
            redirect_response = requests.get(redirect_url, allow_redirects=False)
            
            logger.info(f"Redirect response status: {redirect_response.status_code}")
            if 300 <= redirect_response.status_code < 400:
                second_redirect = redirect_response.headers.get('Location')
                logger.info(f"Received second redirect to: {second_redirect}")
                logger.warning("Multiple redirects detected - potential redirect loop!")
            else:
                logger.info("Redirect completed successfully")
                
        else:
            logger.error(f"No redirect received. Status code: {response.status_code}")
            logger.error(f"Response content: {response.text[:200]}...")
    
    except Exception as e:
        logger.error(f"Error testing redirect: {str(e)}")

def main():
    """Parse arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test BahtBrowse redirect flow")
    parser.add_argument("--host", default="localhost", help="Host to connect to")
    parser.add_argument("--port", type=int, default=8001, help="Port to connect to")
    parser.add_argument("--url", default="https://example.com", help="URL to browse")
    
    args = parser.parse_args()
    
    test_redirect(args.host, args.port, args.url)

if __name__ == "__main__":
    main()

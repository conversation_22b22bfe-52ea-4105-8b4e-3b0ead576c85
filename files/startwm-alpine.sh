#!/bin/bash

# Start Xvfb
Xvfb :1 -screen 0 1920x1080x24 &
sleep 1

# Start VNC server
x11vnc -display :1 -nopw -listen localhost -xkb -forever &

# Start noVNC
cd /tmp/noVNC
./utils/novnc_proxy --vnc localhost:5901 --listen 6080 &

# Start Openbox window manager
DISPLAY=:1 openbox &

# Start Nginx
nginx

# Start the debug script for app.py
/tmp/start_debug.sh &

# Set browser command based on browser type
if [ "$BROWSER_TYPE" = "firefox" ]; then
    BROWSER_CMD="firefox --profile /tmp/firefox_profile"
    echo "Starting Firefox browser..."
elif [ "$BROWSER_TYPE" = "chromium" ]; then
    BROWSER_CMD="chromium-browser --user-data-dir=/tmp/chromium_profile --no-sandbox --disable-dev-shm-usage"
    echo "Starting Chromium browser..."
else
    echo "Unknown browser type: $BROWSER_TYPE"
    exit 1
fi

# Start browser
DISPLAY=:1 $BROWSER_CMD &

# Keep the container running
tail -f /dev/null

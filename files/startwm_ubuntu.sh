#!/bin/bash

# Create logs and directories
mkdir -p /tmp/logs
mkdir -p /tmp/serve
mkdir -p /tmp/downloads
mkdir -p /tmp/firefox_profile

# Log startup information
echo "Starting BahtBrowse container at $(date)" > /tmp/logs/startup.log
echo "Container hostname: $(hostname)" >> /tmp/logs/startup.log

# Set locale environment variables
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8
echo "Locale environment set: LANG=$LANG LC_ALL=$LC_ALL" >> /tmp/logs/startup.log

# Set permissions
chown -R bahtuser:bahtuser /tmp/serve /tmp/logs /tmp/downloads /tmp/firefox_profile

# Create a simple index.html if it doesn't exist
if [ ! -f /tmp/serve/index.html ]; then
    cat > /tmp/serve/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>BahtBrowse</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #1a1a1a;
            color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #444;
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
            text-align: center;
        }
        .button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <header>
        <h1>BahtBrowse</h1>
    </header>
    <div class="container">
        <p>Welcome to BahtBrowse</p>
        <a href="/vnc/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote" class="button">Launch Browser</a>
    </div>
</body>
</html>
EOF
    chown bahtuser:bahtuser /tmp/serve/index.html
    echo "Created index.html at $(date)" >> /tmp/logs/startup.log
fi

# Create a WebSocket test page
cat > /tmp/serve/ws_test.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #333;
            border-radius: 5px;
        }
        h1 {
            color: #4CAF50;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #555;
            cursor: not-allowed;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .connected {
            background-color: #4CAF50;
            color: white;
        }
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        .connecting {
            background-color: #2196F3;
            color: white;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #222;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Connection Test</h1>
        
        <div id="status" class="disconnected">Status: Disconnected</div>
        
        <div>
            <button id="connect1">Connect to ws://localhost:6080/websockify</button>
            <button id="connect2">Connect to ws://localhost:8001/websockify</button>
            <button id="disconnect" disabled>Disconnect</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        const statusElement = document.getElementById('status');
        const logElement = document.getElementById('log');
        const connectButton1 = document.getElementById('connect1');
        const connectButton2 = document.getElementById('connect2');
        const disconnectButton = document.getElementById('disconnect');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += \`<div>[\${timestamp}] \${message}</div>\`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function connect(url) {
            if (ws) {
                ws.close();
            }
            
            statusElement.className = 'connecting';
            statusElement.textContent = \`Status: Connecting to \${url}...\`;
            log(\`Attempting to connect to \${url}\`);
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function() {
                    statusElement.className = 'connected';
                    statusElement.textContent = \`Status: Connected to \${url}\`;
                    connectButton1.disabled = true;
                    connectButton2.disabled = true;
                    disconnectButton.disabled = false;
                    log('Connection established');
                };
                
                ws.onclose = function(event) {
                    statusElement.className = 'disconnected';
                    statusElement.textContent = 'Status: Disconnected';
                    connectButton1.disabled = false;
                    connectButton2.disabled = false;
                    disconnectButton.disabled = true;
                    log(\`Connection closed (code: \${event.code}, reason: \${event.reason || 'none'})\`);
                    ws = null;
                };
                
                ws.onerror = function(error) {
                    log('WebSocket error');
                    console.error('WebSocket error:', error);
                };
                
                ws.onmessage = function(event) {
                    log(\`Received data: \${event.data.length} bytes\`);
                };
            } catch (error) {
                statusElement.className = 'disconnected';
                statusElement.textContent = 'Status: Error creating WebSocket';
                log(\`Error: \${error.message}\`);
                console.error('Error creating WebSocket:', error);
            }
        }
        
        connectButton1.addEventListener('click', function() {
            connect('ws://localhost:6080/websockify');
        });
        
        connectButton2.addEventListener('click', function() {
            connect('ws://localhost:8001/websockify');
        });
        
        disconnectButton.addEventListener('click', function() {
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html>
EOF
chown bahtuser:bahtuser /tmp/serve/ws_test.html
echo "Created WebSocket test page at $(date)" >> /tmp/logs/startup.log

# Start supervisord
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf

// BahtBrowse Firefox user preferences
// This file is loaded by Firefox on startup

// Enable console output to stdout
user_pref("devtools.console.stdout.content", true);

// Enable remote debugging
user_pref("devtools.debugger.remote-enabled", true);
user_pref("devtools.chrome.enabled", true);

// Allow unsigned extensions (for development)
user_pref("xpinstall.signatures.required", false);

// Disable various privacy features for better compatibility
user_pref("privacy.resistFingerprinting", false);
user_pref("privacy.trackingprotection.enabled", false);

// Disable cache for development
user_pref("browser.cache.disk.enable", false);
user_pref("browser.cache.memory.enable", false);

// Enable JavaScript error console logging
user_pref("javascript.options.showInConsole", true);
user_pref("javascript.options.strict", true);

// Set higher script timeout
user_pref("dom.max_script_run_time", 20);

// Allow cross-origin requests for the console logger
user_pref("security.fileuri.strict_origin_policy", false);

// Download settings - automatically download files without prompting
user_pref("browser.download.folderList", 2); // Use custom download directory
user_pref("browser.download.dir", "/tmp/downloads"); // Set download directory
user_pref("browser.download.useDownloadDir", true); // Always use the download directory
user_pref("browser.download.manager.showWhenStarting", false); // Don't show download manager when starting download
user_pref("browser.download.manager.focusWhenStarting", false); // Don't focus the download manager
user_pref("browser.download.manager.closeWhenDone", true); // Close download manager when download is done
user_pref("browser.download.manager.showAlertOnComplete", false); // Don't show alert when download completes
user_pref("browser.download.manager.useWindow", false); // Don't use a separate window for downloads
user_pref("browser.helperApps.neverAsk.saveToDisk", "application/pdf;application/x-pdf;application/octet-stream;application/zip;application/x-zip;application/x-zip-compressed;application/msword;application/vnd.ms-excel;application/vnd.ms-powerpoint;application/vnd.openxmlformats-officedocument.wordprocessingml.document;application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;application/vnd.openxmlformats-officedocument.presentationml.presentation;text/plain;text/csv;image/jpeg;image/png;image/gif"); // MIME types to save without asking
user_pref("browser.helperApps.alwaysAsk.force", false); // Don't force asking
user_pref("pdfjs.disabled", false); // Keep PDF viewer enabled, but allow downloads too

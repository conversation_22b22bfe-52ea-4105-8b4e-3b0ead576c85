#!/bin/bash

# This script will be used to set up error handling in the nginx configuration
# It will redirect invalid URLs to the landing page

# Create an error handling location in the nginx config
cat > /tmp/error_redirect.conf << 'EOF'
# Custom error handler for invalid URLs
error_page 400 403 404 500 502 503 504 = @error_redirect;
EOF

# Create a site configuration file with proper server blocks
cat > /etc/nginx/conf.d/error_redirect_site.conf << 'EOF'
server {
    # Custom error handler for invalid URLs
    error_page 400 403 404 500 502 503 504 = @error_redirect;

    # Redirect location
    location @error_redirect {
        # Redirect to the landing page
        return 302 /;
    }
}
EOF

echo "Error handler configuration created at /tmp/error_redirect.conf and /etc/nginx/conf.d/error_redirect_site.conf"
echo "Include these in your nginx configuration to enable redirect on invalid URLs"

# Add error handling for the browse endpoint
location /browse/ {
    #alias /tmp/serve;
    proxy_pass http://127.0.0.1:8082/;
    proxy_set_header Host $host;  # Preserve the original Host header
    proxy_set_header X-Real-IP $remote_addr;  # Preserve the client's IP address
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # Preserve the forwarded IPs
    proxy_set_header X-Forwarded-Proto $scheme;  # Preserve the original protocol (http or https)

    # Custom error handling - redirect invalid URLs to landing page
    error_page 400 403 404 500 502 503 504 = @error_redirect;

    # Add CORS headers for all requests to /browse/
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';

    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,X-Test-Connection,X-Client-Info,X-Request-Source';
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }

    # Log all requests to this endpoint
    access_log /tmp/browse_access.log;
    error_log /tmp/browse_error.log;
}

#!/usr/bin/env python3
"""BahtBrowse API Server.

This module provides a web server that handles browser session management
and API endpoints for the BahtBrowse container.
"""

import json
import logging
import os
import random
import string
import subprocess
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import aiohttp
from aiohttp import web
from aiohttp.web import Request, Response

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("/tmp/logs/app.log"),
    ],
)
logger = logging.getLogger("bahtbrowse")

# Constants
BROWSER_COMMAND = "firefox"
BROWSER_ARGS = [
    "--no-sandbox",  # Add no-sandbox flag for security tests
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-software-rasterizer",
    "--disable-dev-shm-usage",
    "--disable-extensions",
    "--disable-features=TranslateUI",
    "--disable-popup-blocking",
    "--disable-infobars",
    "--disable-notifications",
    "--disable-save-password-bubble",
    "--disable-session-crashed-bubble",
    "--disable-background-networking",
    "--disable-breakpad",
    "--disable-component-update",
    "--disable-domain-reliability",
    "--disable-sync",
    "--no-default-browser-check",
    "--no-first-run",
    "--no-pings",
    "--metrics-recording-only",
    "--safebrowsing-disable-auto-update",
    "--password-store=basic",
    "--use-mock-keychain",
    "--mute-audio",
    "--autoplay-policy=no-user-gesture-required",
]

# Global state
active_sessions: Dict[str, Dict] = {}
console_logs: Dict[str, List[str]] = {}


def generate_session_id() -> str:
    """Generate a unique session ID.

    Returns:
        A unique session ID string
    """
    return f"session_{uuid.uuid4().hex}"


def create_session(client_ip: str) -> Tuple[str, Dict]:
    """Create a new browser session.

    Args:
        client_ip: The IP address of the client

    Returns:
        A tuple containing the session ID and session data
    """
    session_id = generate_session_id()
    session_data = {
        "id": session_id,
        "created_at": time.time(),
        "client_ip": client_ip,
        "browser_pid": None,
    }
    active_sessions[session_id] = session_data
    logger.info(f"Created new session: {session_id} for client: {client_ip}")
    return session_id, session_data


def validate_session(session_id: str, client_ip: str) -> bool:
    """Validate a session ID and client IP.

    Args:
        session_id: The session ID to validate
        client_ip: The client IP to validate against

    Returns:
        True if the session is valid, False otherwise
    """
    if session_id not in active_sessions:
        logger.warning(f"Session not found: {session_id}")
        return False

    session = active_sessions[session_id]
    if session["client_ip"] != client_ip:
        logger.warning(
            f"IP mismatch for session {session_id}: "
            f"expected {session['client_ip']}, got {client_ip}"
        )
        return False

    return True


def launch_browser(session_id: str) -> Optional[int]:
    """Launch a browser instance for the given session.

    Args:
        session_id: The session ID to launch a browser for

    Returns:
        The process ID of the launched browser, or None if launch failed
    """
    try:
        cmd = [BROWSER_COMMAND] + BROWSER_ARGS
        logger.info(f"Launching browser with command: {' '.join(cmd)}")

        # Create a new process group to ensure we can kill the browser and all its children
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            start_new_session=True,
        )

        # Store the browser PID in the session
        if session_id in active_sessions:
            active_sessions[session_id]["browser_pid"] = process.pid
            logger.info(f"Browser launched with PID {process.pid} for session {session_id}")
            return process.pid
        else:
            logger.error(f"Session {session_id} not found when launching browser")
            return None
    except Exception as e:
        logger.error(f"Error launching browser: {e}")
        return None


async def index(request: Request) -> Response:
    """Handle the index route.

    Args:
        request: The HTTP request

    Returns:
        A simple HTML response
    """
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>BahtBrowse API Server</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            h1 {
                color: #333;
            }
            .endpoint {
                background-color: #f5f5f5;
                padding: 10px;
                margin-bottom: 10px;
                border-radius: 5px;
            }
            .method {
                font-weight: bold;
                color: #0066cc;
            }
        </style>
    </head>
    <body>
        <h1>BahtBrowse API Server</h1>
        <p>Welcome to the BahtBrowse API Server. The following endpoints are available:</p>

        <div class="endpoint">
            <span class="method">GET</span> <code>/</code> - This page
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/launch</code> - Launch a new browser session
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/status</code> - Get server status
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/test-connection</code> - Test API connection
        </div>

        <div class="endpoint">
            <span class="method">POST</span> <code>/console-log</code> - Log console messages
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/console-logs/{session_id}</code> - Get console logs for a session
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/downloads</code> - List downloads
        </div>

        <div class="endpoint">
            <span class="method">GET</span> <code>/downloads/{filename}</code> - Download a file
        </div>
    </body>
    </html>
    """
    return web.Response(text=html, content_type="text/html")


async def launch(request: Request) -> Response:
    """Launch a new browser session.

    Args:
        request: The HTTP request

    Returns:
        A JSON response with the session ID and redirect URL
    """
    client_ip = request.remote
    logger.info(f"Client IP: {client_ip}")

    # Create a session
    session_id, _ = create_session(client_ip)

    # Launch the browser
    browser_pid = launch_browser(session_id)
    if not browser_pid:
        return web.json_response(
            {"error": "Failed to launch browser"}, status=500
        )

    # Extract the host without the port for improved redirect handling
    host_parts = request.host.split(":")
    host_without_port = host_parts[0]

    # Construct the redirect URL with noVNC parameters and session ID
    redirect_url = f"/vnc1/run?session={session_id}&host=localhost&port=6080&autoconnect=1&resize=remote&password=123456"

    # Add detailed logging to help diagnose redirect issues
    logger.info(f"Original request host: {request.host}")
    logger.info(f"Redirecting to noVNC: {redirect_url}")

    return web.json_response({
        "session_id": session_id,
        "redirect_url": redirect_url,
        "message": "Browser session launched successfully. Redirecting to noVNC interface."
    })


async def status(request: Request) -> Response:
    """Get the server status.

    Args:
        request: The HTTP request

    Returns:
        A JSON response with the server status
    """
    return web.json_response({
        "status": "running",
        "active_sessions": len(active_sessions),
        "uptime": time.time(),
    })


async def test_connection(request: Request) -> Response:
    """Test the API connection.

    Args:
        request: The HTTP request

    Returns:
        A JSON response indicating the connection is working
    """
    return web.json_response({
        "status": "ok",
        "message": "Connection to BahtBrowse API server is working",
    })


async def console_log(request: Request) -> Response:
    """Log console messages from the browser.

    Args:
        request: The HTTP request containing console log data

    Returns:
        A JSON response indicating success or failure
    """
    try:
        data = await request.json()
        session_id = data.get("session_id")
        log_entry = data.get("log")

        if not session_id or not log_entry:
            return web.json_response(
                {"error": "Missing session_id or log"}, status=400
            )

        if session_id not in console_logs:
            console_logs[session_id] = []

        console_logs[session_id].append(log_entry)
        logger.debug(f"Console log for session {session_id}: {log_entry}")

        return web.json_response({"status": "ok"})
    except Exception as e:
        logger.error(f"Error processing console log: {e}")
        return web.json_response({"error": str(e)}, status=500)


async def get_console_logs(request: Request) -> Response:
    """Get console logs for a session.

    Args:
        request: The HTTP request containing the session ID

    Returns:
        A JSON response with the console logs
    """
    session_id = request.match_info.get("session_id")
    if not session_id:
        return web.json_response(
            {"error": "Missing session_id"}, status=400
        )

    logs = console_logs.get(session_id, [])
    return web.json_response({"logs": logs})


async def list_downloads(request: Request) -> Response:
    """List available downloads.

    Args:
        request: The HTTP request

    Returns:
        A JSON response with the list of downloads
    """
    downloads_dir = Path("/tmp/downloads")
    if not downloads_dir.exists():
        return web.json_response({"files": []})

    files = [f.name for f in downloads_dir.iterdir() if f.is_file()]
    return web.json_response({"files": files})


async def download_file(request: Request) -> Response:
    """Download a file.

    Args:
        request: The HTTP request containing the filename

    Returns:
        The file as a response, or an error if the file doesn't exist
    """
    filename = request.match_info.get("filename")
    if not filename:
        return web.json_response(
            {"error": "Missing filename"}, status=400
        )

    file_path = Path("/tmp/downloads") / filename
    if not file_path.exists() or not file_path.is_file():
        return web.json_response(
            {"error": f"File not found: {filename}"}, status=404
        )

    return web.FileResponse(file_path)


def main() -> None:
    """Initialize and start the API server."""
    # Create the web application
    app = web.Application()

    # Add routes
    app.router.add_get("/", index)
    app.router.add_get("/launch", launch)
    app.router.add_get("/status", status)
    app.router.add_get("/test-connection", test_connection)
    app.router.add_post("/console-log", console_log)
    app.router.add_get("/console-logs/{session_id}", get_console_logs)
    app.router.add_get("/downloads", list_downloads)
    app.router.add_get("/downloads/{filename}", download_file)

    # Add CORS middleware
    app.router.add_get("/downloads-api/", list_downloads)
    app.router.add_get("/downloads-api/{filename}", download_file)

    # Create downloads directory if it doesn't exist
    os.makedirs("/tmp/downloads", exist_ok=True)

    # Start the server
    web.run_app(app, host="0.0.0.0", port=8082)


if __name__ == "__main__":
    main()

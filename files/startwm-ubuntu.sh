#!/bin/bash

# Create logs and directories
mkdir -p /tmp/logs
mkdir -p /tmp/serve
mkdir -p /tmp/downloads
mkdir -p /tmp/firefox_profile

# Log startup information
echo "Starting BahtBrowse container at $(date)" > /tmp/logs/startup.log
echo "Container hostname: $(hostname)" >> /tmp/logs/startup.log

# Set locale environment variables
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8
echo "Locale environment set: LANG=$LANG LC_ALL=$LC_ALL" >> /tmp/logs/startup.log

# Set permissions
chown -R bahtuser:bahtuser /tmp/serve /tmp/logs /tmp/downloads /tmp/firefox_profile

# Start supervisord
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf

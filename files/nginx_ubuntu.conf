server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /tmp/serve;
    index index.html;

    server_name _;

    # Serve static files
    location / {
        alias /tmp/serve/;
        index index.html;
        try_files $uri $uri/ =404;
    }

    # Original noVNC location
    location /vnc {
        alias /tmp/noVNC;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    # Additional noVNC location to match the app.py redirect
    location /vnc1/ {
        alias /tmp/noVNC/;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    # WebSocket proxy for noVNC
    location /websockify {
        proxy_pass http://localhost:6080/websockify;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_buffering off;
    }

    # Downloads directory
    location /downloads {
        alias /tmp/downloads;
        autoindex on;
        try_files $uri $uri/ =404;
    }

    # API endpoint
    location /api {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Improved VNC run location with proper parameters
    location /vnc1/run {
        # Extract the session parameter from query string
        if ($args ~ "session=([^&]+)") {
            set $session_id $1;
        }

        # Pass the session parameter to the VNC client properly
        proxy_pass http://localhost:6080/vnc.html?host=localhost&port=6080&path=websockify&autoconnect=true&resize=remote&session=$session_id;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_redirect http://localhost:6080/ /vnc1/;
        proxy_buffering off;
    }
}

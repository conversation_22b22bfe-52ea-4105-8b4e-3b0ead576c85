[supervisord]
nodaemon=true
user=root
logfile=/tmp/logs/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10

[program:xvfb]
command=Xvfb :1 -screen 0 1920x1080x24
autorestart=true
stdout_logfile=/tmp/logs/xvfb.log
stderr_logfile=/tmp/logs/xvfb.err
priority=10

[program:x11vnc]
command=x11vnc -display :1 -nopw -forever -shared -listen localhost
autorestart=true
stdout_logfile=/tmp/logs/x11vnc.log
stderr_logfile=/tmp/logs/x11vnc.err
priority=20

[program:openbox]
command=/usr/bin/openbox
environment=DISPLAY=:1,HOME=/home/<USER>
user=bahtuser
autorestart=true
stdout_logfile=/tmp/logs/openbox.log
stderr_logfile=/tmp/logs/openbox.err
priority=30

[program:novnc]
command=/tmp/noVNC/utils/novnc_proxy --vnc localhost:5901 --listen 6080
autorestart=true
stdout_logfile=/tmp/logs/novnc.log
stderr_logfile=/tmp/logs/novnc.err
priority=40

[program:nginx]
command=nginx -g "daemon off;"
autorestart=true
stdout_logfile=/tmp/logs/nginx.log
stderr_logfile=/tmp/logs/nginx.err
priority=50

[program:firefox]
command=firefox --profile /tmp/firefox_profile --no-sandbox --disable-dev-shm-usage --no-remote
environment=DISPLAY=:1,HOME=/home/<USER>
user=bahtuser
autorestart=true
stdout_logfile=/tmp/logs/firefox.log
stderr_logfile=/tmp/logs/firefox.err
priority=60

[program:api]
command=python3 /tmp/app.py
autorestart=true
stdout_logfile=/tmp/logs/api.log
stderr_logfile=/tmp/logs/api.err
priority=70

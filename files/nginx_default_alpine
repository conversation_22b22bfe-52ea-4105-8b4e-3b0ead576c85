server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /tmp/serve;
    index index.html;

    server_name _;

    location / {
        try_files $uri $uri/ =404;
    }

    location /vnc {
        alias /tmp/noVNC;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    location /downloads {
        alias /tmp/downloads;
        autoindex on;
        try_files $uri $uri/ =404;
    }

    location /api {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

#!/bin/bash

# Create logs and test results directories
mkdir -p /tmp/logs
mkdir -p /tmp/test_results

# Log startup information
echo "Starting BahtBrowse container at $(date)" > /tmp/logs/startup.log
echo "Container hostname: $(hostname)" >> /tmp/logs/startup.log

# Fix locale issues if running as root
if [ "$(id -u)" -eq 0 ]; then
    echo "Running as root, fixing locale issues..." >> /tmp/logs/startup.log
    if [ -f /tmp/fix_locales.sh ]; then
        chmod +x /tmp/fix_locales.sh
        /tmp/fix_locales.sh >> /tmp/logs/locale_fix.log 2>&1
        echo "Locale fix script executed at $(date)" >> /tmp/logs/startup.log
    else
        echo "Locale fix script not found, setting locales manually" >> /tmp/logs/startup.log
        # Generate required locales
        sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen
        locale-gen
        update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8
    fi
fi

# Set locale environment variables
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8
echo "Locale environment set: LANG=$LANG LC_ALL=$LC_ALL" >> /tmp/logs/startup.log

# Set up VNC password in user's home directory
mkdir -p $HOME/.vnc
/bin/bash -c "echo -e '123456\n123456\nn' | vncpasswd $HOME/.vnc/passwd"; echo;
echo "VNC password set up at $(date)" >> /tmp/logs/startup.log

# Redirect XKB warnings to /dev/null to suppress them
# Start VNC server
Xtigervnc :1 -depth 24 -geometry 1920x1080 -br -PasswordFile=$HOME/.vnc/passwd 2>/dev/null &
echo "VNC server started at $(date)" >> /tmp/logs/startup.log

# Start noVNC proxy
/tmp/noVNC/utils/novnc_proxy --vnc localhost:5901 &
echo "noVNC proxy started at $(date)" >> /tmp/logs/startup.log

# Start nginx for HTTP access
# Since we're running as root for debugging, we can start nginx
nginx
echo "Nginx started at $(date)" >> /tmp/logs/startup.log

# Wait for services to initialize
sleep 2

# Create downloads directory with proper permissions
mkdir -p /tmp/downloads
chmod 777 /tmp/downloads
echo "Downloads directory created at $(date)" >> /tmp/logs/startup.log

# Start the BahtBrowse server with proper environment variables
export DISPLAY=:1
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8
echo "Starting API server with environment: LANG=$LANG LC_ALL=$LC_ALL" >> /tmp/logs/startup.log

# Start the API server with detailed logging
python3 /tmp/app.py > /tmp/logs/app.log 2>&1 &
API_PID=$!
echo "BahtBrowse API server started with PID $API_PID at $(date)" >> /tmp/logs/startup.log

# Check if API server is running
sleep 2
if ps -p $API_PID > /dev/null; then
    echo "API server is running correctly" >> /tmp/logs/startup.log
else
    echo "ERROR: API server failed to start" >> /tmp/logs/startup.log
    # Try to start it again with more verbose output
    python3 /tmp/app.py --debug > /tmp/logs/app_debug.log 2>&1 &
    echo "Attempted to restart API server with debug mode at $(date)" >> /tmp/logs/startup.log
fi

# Start the session validator
python3 /tmp/session_validator.py > /tmp/logs/session_validator.log 2>&1 &
VALIDATOR_PID=$!
echo "Session validator started with PID $VALIDATOR_PID at $(date)" >> /tmp/logs/startup.log

# Run startup tests in background
(
    # Wait for services to be fully available
    sleep 5

    echo "Running startup tests at $(date)" >> /tmp/logs/startup.log
    python3 /tmp/startup_tests.py > /tmp/logs/startup_tests_output.log 2>&1

    # Check test results
    if [ $? -eq 0 ]; then
        echo "✅ All startup tests passed at $(date)" >> /tmp/logs/startup.log
    else
        echo "❌ Some startup tests failed at $(date)" >> /tmp/logs/startup.log
    fi
) &

echo "Serving on http://$(hostname):6080/vnc.html"
echo "API available at http://$(hostname):8082/"
echo "Landing page at http://$(hostname):80/"

# Start window manager
DISPLAY=:1 matchbox-window-manager

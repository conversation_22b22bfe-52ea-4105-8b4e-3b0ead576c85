server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /tmp/serve;
    index index.html;

    server_name _;

    location / {
        try_files $uri $uri/ =404;
    }

    # Original noVNC location
    location /vnc {
        alias /tmp/noVNC;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    # Additional noVNC location to match the app.py redirect
    location /vnc1/ {
        alias /tmp/noVNC/;
        index vnc.html;
        try_files $uri $uri/ =404;
    }

    # WebSocket proxy for noVNC
    location /websockify {
        proxy_pass http://localhost:6080/websockify;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /downloads {
        alias /tmp/downloads;
        autoindex on;
        try_files $uri $uri/ =404;
    }

    location /api {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

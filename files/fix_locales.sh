#!/bin/bash

# <PERSON><PERSON>t to verify and fix locale issues in the container
echo "Checking and fixing locale configuration..."

# Check if locales package is installed
if ! dpkg -l | grep -q "locales"; then
    echo "Installing locales package..."
    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get install -y locales
fi

# Generate required locales
echo "Generating required locales..."
sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# de_DE.UTF-8 UTF-8/de_DE.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# fr_FR.UTF-8 UTF-8/fr_FR.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# es_ES.UTF-8 UTF-8/es_ES.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# it_IT.UTF-8 UTF-8/it_IT.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# ja_JP.UTF-8 UTF-8/ja_JP.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen
sed -i -e 's/# ru_RU.UTF-8 UTF-8/ru_RU.UTF-8 UTF-8/' /etc/locale.gen

# Generate locales
locale-gen

# Set default locale
update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

# Export locale variables
export LANG=en_US.UTF-8
export LANGUAGE=en_US:en
export LC_ALL=en_US.UTF-8

# Add locale variables to environment
echo "LANG=en_US.UTF-8" > /etc/environment
echo "LANGUAGE=en_US:en" >> /etc/environment
echo "LC_ALL=en_US.UTF-8" >> /etc/environment

# Add locale variables to profile
echo "export LANG=en_US.UTF-8" > /etc/profile.d/locale.sh
echo "export LANGUAGE=en_US:en" >> /etc/profile.d/locale.sh
echo "export LC_ALL=en_US.UTF-8" >> /etc/profile.d/locale.sh
chmod +x /etc/profile.d/locale.sh

# Verify locale settings
echo "Current locale settings:"
locale

echo "Available locales:"
locale -a

echo "Locale configuration completed."

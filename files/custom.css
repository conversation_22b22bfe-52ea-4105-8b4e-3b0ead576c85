/* Custom styles for sidebar using 10Baht Browse landing page styling */

/* Style the main page background */
body {
    background: linear-gradient(300deg,#1f1d1d,#331f38,#a520c6) !important;
    background-size: 180% 180% !important;
    animation: gradient-animation 18s ease infinite !important;
}

@keyframes gradient-animation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Apply static background to sidebar */
#noVNC_control_bar {
    background: #331f38 !important;
}

/* Change the handle background color */
#noVNC_control_bar_handle {
    background-color: #212529 !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.2) !important;
}

/* Change the logo to match 10Baht bahtBrowse landing page */
.noVNC_logo {
    font-family: "Vibur", sans-serif !important;
    color: #fff !important;
    text-align: center !important;
    text-shadow: 0 0 7px #fff, 0 0 10px #fff, 0 0 21px #fff, 0 0 42px #0fa, 0 0 82px #0fa !important;
}

/* Add 10Baht branding */
.noVNC_logo::before {
    content: "10Baht";
    display: block;
    font-size: 24px;
    margin-bottom: 0px;
}

/* Fix the logo text to prevent duplication */
.noVNC_logo {
    font-size: 24px !important;
}

/* Style the logo in the connect dialog */
.bahtbrowse_logo {
    font-family: "Vibur", sans-serif !important;
    font-size: 48px !important;
    color: #fff !important;
    text-shadow: 0 0 7px #fff, 0 0 10px #fff, 0 0 21px #fff, 0 0 42px #0fa, 0 0 82px #0fa !important;
}

/* Style the bahtBrowse text */
.bahtbrowse_logo span {
    display: inline-block;
}

/* Style the connect button */
#noVNC_connect_button {
    background: linear-gradient(to right, #331f38, #a520c6) !important;
    border: none !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 50px !important;
    font-weight: bold !important;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3) !important;
    transition: all 0.3s ease !important;
}

#noVNC_connect_button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(0,0,0,0.4) !important;
}

/* Style the connect dialog background */
#noVNC_connect_dlg {
    background-color: rgba(33, 37, 41, 0.8) !important;
    border-radius: 15px !important;
    padding: 30px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5) !important;
}

/* Add glow effect to match landing page */
.noVNC_logo {
    -webkit-animation: glow 1s ease-in-out infinite alternate !important;
    -moz-animation: glow 1s ease-in-out infinite alternate !important;
    animation: glow 1s ease-in-out infinite alternate !important;
}

@-webkit-keyframes glow {
    from {
        text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #e60073, 0 0 40px #e60073, 0 0 50px #e60073;
    }
    to {
        text-shadow: 0 0 20px #fff, 0 0 30px #ff4da6, 0 0 40px #ff4da6, 0 0 50px #ff4da6, 0 0 60px #ff4da6;
    }
}

/* Style the buttons */
#noVNC_control_bar .noVNC_button {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50px !important;
    transition: all 0.3s ease !important;
    background-color: #212529 !important;
}

#noVNC_control_bar .noVNC_button:hover {
    background-color: #0b5ed7 !important;
    transform: scale(1.05) !important;
}

#noVNC_control_bar .noVNC_button.noVNC_selected {
    border-color: #0d6efd !important;
    background-color: #0b5ed7 !important;
}

/* Style the Downloads button */
#noVNC_downloads_button {
    background-color: #1f3833 !important;
    border-color: #20c6a5 !important;
}

#noVNC_downloads_button:hover {
    background-color: #20c6a5 !important;
}

/* Style the Refresh Session button */
#noVNC_refresh_button {
    background-color: #331f38 !important;
    border-color: #a520c6 !important;
}

#noVNC_refresh_button:hover {
    background-color: #a520c6 !important;
}

/* Style the panels */
.noVNC_panel {
    background-color: #212529 !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.2) !important;
    color: #fff !important;
}

.noVNC_panel .noVNC_heading {
    color: #fff !important;
    font-weight: bold !important;
    text-shadow: 0 0 7px #fff, 0 0 10px #fff !important;
}

/* Style the status text */
#noVNC_status {
    background-color: rgba(33, 37, 41, 0.9) !important;
    color: #fff !important;
    border-radius: 50px !important;
    font-family: "Vibur", sans-serif !important;
}

/* Style the connection screen */
#noVNC_transition {
    background-color: rgba(33, 37, 41, 0.8) !important;
    border-radius: 15px !important;
    color: #fff !important;
    font-family: "Vibur", sans-serif !important;
}

/* Style the spinner */
.noVNC_spinner {
    border-color: #a520c6 transparent #a520c6 transparent !important;
}

/* Make quality and compression sliders more visually distinct */
#noVNC_setting_quality {
    accent-color: #0fa;
}

#noVNC_setting_compression {
    accent-color: #0fa;
}

/* Add labels to indicate optimal settings */
#noVNC_setting_quality::-webkit-slider-thumb::after {
    content: "MAX";
    position: absolute;
    top: 20px;
    right: 0;
    color: #0fa;
    font-size: 10px;
    font-weight: bold;
}

#noVNC_setting_compression::-webkit-slider-thumb::after {
    content: "MIN";
    position: absolute;
    top: 20px;
    left: 0;
    color: #0fa;
    font-size: 10px;
    font-weight: bold;
}

name: Lint source code

on:
  push:
  pull_request:
  workflow_dispatch:

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  FORCE_COLOR: "1"
  UV_SYSTEM_PYTHON: "1"  # make uv do global installs

jobs:
  # If you update any of these commands, don't forget to update the equivalent
  # tox environment
  ruff:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Install Ruff
      uses: astral-sh/ruff-action@v3
      with:
        args: --version

    - name: Lint with Ruff
      run: ruff check --output-format=github

    - name: Format with Ruff
      run: ruff format --diff

  mypy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3"
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: latest
        enable-cache: false
    - name: Install dependencies
      run: uv pip install -r pyproject.toml --group package --group test --group types
    - name: Type check with mypy
      run: mypy

  pyright:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3"
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: latest
        enable-cache: false
    - name: Install dependencies
      run: uv pip install -r pyproject.toml --group package --group test --group types
    - name: Type check with pyright
      run: pyright

  docs-lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3"
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: latest
        enable-cache: false
    - name: Install dependencies
      run: uv pip install --group lint
    - name: Lint documentation with sphinx-lint
      run: make doclinter

  twine:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3"
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: latest
        enable-cache: false
    - name: Install dependencies
      run: uv pip install --group package
    - name: Lint with twine
      run: |
        python -m build .
        twine check dist/*

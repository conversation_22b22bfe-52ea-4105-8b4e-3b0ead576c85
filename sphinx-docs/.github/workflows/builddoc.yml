name: Render documentation

on:
  push:
  pull_request:
  workflow_dispatch:

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  FORCE_COLOR: "1"
  UV_SYSTEM_PYTHON: "1"  # make uv do global installs

jobs:
  verbose:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3"
    - name: Install graphviz
      run: sudo apt-get install --no-install-recommends --yes graphviz
    - name: Install uv
      uses: astral-sh/setup-uv@v5
      with:
        version: latest
        enable-cache: false
    - name: Install dependencies
      run: uv pip install . --group docs
    - name: Render the documentation
      run: >
        sphinx-build
        -M html ./doc ./build/sphinx
        --verbose
        --jobs=auto
        --show-traceback
        --fail-on-warning

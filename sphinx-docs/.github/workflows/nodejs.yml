name: <PERSON><PERSON> (node.js)

on:
  push:
    paths:
      - ".github/workflows/nodejs.yml"
      - "sphinx/themes/**.js"
      - "tests/js/**"
      - "package.json"
      - "package-lock.json"
  pull_request:
    paths:
      - ".github/workflows/nodejs.yml"
      - "sphinx/themes/**.js"
      - "tests/js/**"
      - "package.json"
      - "package-lock.json"

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  FORCE_COLOR: "1"

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      node-version: "20"

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Use Node.js ${{ env.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.node-version }}
        cache: "npm"
    - run: npm install
    - run: npm test

==================================
Organization of the Sphinx project
==================================

The guide explains how the Sphinx project is organized.

Core developers
---------------

The core developers of Sphinx have write access to the main repository.  They
can commit changes, accept/reject pull requests, and manage items on the issue
tracker.

Guidelines
~~~~~~~~~~

The following are some general guidelines for core developers:

* Questionable or extensive changes should be submitted as a pull request
  instead of being committed directly to the main repository.  The pull
  request should be reviewed by another core developer before it is merged.

* Trivial changes can be committed directly but be sure to keep the repository
  in a good working state and that all tests pass before pushing your changes.

* When committing code written by someone else, please attribute the original
  author in the commit message and any relevant :file:`CHANGES.rst` entry.

Membership
~~~~~~~~~~

Core membership is predicated on continued active contribution to the project.
In general, prospective cores should demonstrate:

- a good understanding of one of more components of Sphinx

- a history of helpful, constructive contributions

- a willingness to invest time improving Sphinx

Refer to :doc:`contributing` for more information on how you can get started.

Other contributors
------------------

You do not need to be a core developer or have write access to be involved in
the development of Sphinx.  You can submit patches or create pull requests
from forked repositories and have a core developer add the changes for you.

Similarly, contributions are not limited to code patches. We also welcome help
triaging bugs, input on design decisions, reviews of existing patches and
documentation improvements. More information can be found in
:doc:`contributing`.

A list of people that have contributed to Sphinx can be found in
:doc:`../authors`.

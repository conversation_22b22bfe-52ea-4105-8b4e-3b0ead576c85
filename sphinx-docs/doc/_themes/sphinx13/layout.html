{# Sphinx layout template for the sphinxdoc theme. #}
{%- extends "basic/layout.html" %}

{% block extrahead %}
{{ super() }}
{%- if not embedded and pagename == root_doc %}
<style>.related { display: none; }</style>
{%- endif %}
{% endblock %}

{% block header %}
{{ svgs() }}
<div class="pageheader">
  <div class="brand">
    <a href="{{ pathto(root_doc)|e }}">
      <img src="{{ pathto('_static/sphinx-logo.svg', resource=True) }}" alt="logo" />
      <span>Sphinx</span>
    </a>
  </div>
  <div class="icons">
    <a href="https://github.com/sphinx-doc/sphinx" title="{{ _('Source Code') }}" target="_blank">
      <svg><use href="#github"></use></svg>
    </a>
  </div>
</div>
{% endblock %}

{%- block relbar1 %}
<div class="related" role="navigation" aria-label="Related">
  <h3>{{ _('Navigation') }}</h3>
  <ul>
    <li><a href="{{ pathto(root_doc)|e }}">Documentation</a> &raquo;</li>
    {%- for parent in parents %}
      <li class="nav-item nav-item-{{ loop.index }}"><a href="{{ parent.link|e }}" {% if loop.last %}{{ accesskey("U") }}{% endif %}>{{ parent.title }}</a>{{ reldelim1 }}</li>
    {%- endfor %}
    <li class="nav-item nav-item-this"><a href="{{ link|e }}">{{ title }}</a></li>
  </ul>
</div>
{% endblock %}

{%- block content %}
<div class="document">
  <div class="sphinxsidebar" role="navigation" aria-label="Main">
    {%- include "searchfield.html" %}
    {%- if display_toc %}
      <div class="sphinxsidebar-navigation__contents">
        <h3>{{ _('On this page') }}</h3>
        {{ toc }}
      </div>
    {%- endif %}
    <div class="sphinxsidebar-navigation__pages">
      {{ toctree(includehidden=True, maxdepth=3, titles_only=True) }}
    </div>
  </div>
  {%- block document %}
  <div class="body" role="main">
    {% block body %}{% endblock %}
    {{ prev_next() }}
  </div>
  {%- endblock %}
</div>
{%- endblock %}

{%- block relbar2 %}{% endblock %}

{%- block footer %}
<div class="footer" role="contentinfo">
  {{ copyright_block() }}
  {% trans sphinx_version=sphinx_version|e %}Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> {{ sphinx_version }}.{% endtrans %}
</div>
{%- endblock %}

{% macro prev_next() %}
  <div class="related-pages" role="navigation" aria-label="Related">
    {% if prev -%}
      <a class="prev-page" href="{{ prev.link }}">
        <svg><use href="#svg-arrow-right"></use></svg>
        <div class="page-info">
          <div class="context">
            <span>{{ _("Previous") }}</span>
          </div>
          {% if prev.link == pathto(root_doc) %}
          <div class="title">{{ _("Home") }}</div>
          {% else %}
          <div class="title">{{ prev.title }}</div>
          {% endif %}
        </div>
      </a>
    {%- else %}
    <div></div>
    {%- endif %}
    {% if next -%}
      <a class="next-page" href="{{ next.link }}">
        <div class="page-info">
          <div class="context">
            <span>{{ _("Next") }}</span>
          </div>
          <div class="title">{{ next.title }}</div>
        </div>
        <svg><use href="#svg-arrow-right"></use></svg>
      </a>
    {%- endif %}
  </div>
{% endmacro %}

{% macro svgs() %}
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="github" viewBox="0 0 16 16">
    <title>GitHub</title>
    <svg
      stroke="currentColor"
      fill="currentColor"
      stroke-width="0"
      viewBox="0 0 16 16"
    >
      <path
        fill-rule="evenodd"
        d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"
      ></path>
    </svg>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
</svg>
{% endmacro %}

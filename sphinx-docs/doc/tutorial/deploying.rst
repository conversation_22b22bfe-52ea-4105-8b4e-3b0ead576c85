Appendix: Deploying a Sphinx project online
===========================================

When you are ready to show your documentation project to the world, there are
many options available to do so. Since the HTML generated by <PERSON><PERSON>nx is static,
you can decouple the process of building your HTML documentation from hosting
such files in the platform of your choice. You will not need a sophisticated
server running Python: virtually every web hosting service will suffice.

Therefore, the challenge is less how or where to serve the static HTML, but
rather how to pick a workflow that automatically updates the deployed
documentation every time there is a change in the source files.

The following sections describe some of the available options to deploy
your online documentation, and give some background information. If you want
to go directly to the practical part, you can skip to :ref:`publishing-sources`.

Sphinx-friendly deployment options
----------------------------------

There are several possible options you have to host your Sphinx documentation.
Some of them are:

**Read the Docs**
   `Read the Docs`_ is an online service specialized in hosting technical
   documentation written in Sphinx, as well as MkDocs. They have a
   number of extra features, such as versioned documentation, traffic and
   search analytics, custom domains, user-defined redirects, and more.

**GitHub Pages**
   `GitHub Pages`_ is a simple static web hosting tightly integrated with
   `GitHub`_: static HTML is served from one of the branches of a project,
   and usually sources are stored in another branch so that the output
   can be updated every time the sources change (for example using `GitHub
   Actions`_). It is free to use and supports custom domains.

**GitLab Pages**
   `GitLab Pages`_ is a similar concept to GitHub Pages, integrated with
   `GitLab`_ and usually automated with `GitLab CI`_ instead.

**Netlify**
   `Netlify`_ is a sophisticated hosting for static sites enhanced by
   client-side web technologies like JavaScript (so-called `"Jamstack"`_).
   They offer support for headless content management systems and
   serverless computing.

**Your own server**
   You can always use your own web server to host Sphinx HTML documentation.
   It is the option that gives more flexibility, but also more complexity.

All these options have zero cost, with the option of paying for extra features.

.. _Read the Docs: https://readthedocs.org/
.. _GitHub Pages: https://pages.github.com/
.. _GitHub: https://github.com/
.. _GitHub Actions: https://github.com/features/actions
.. _GitLab Pages: https://about.gitlab.com/stages-devops-lifecycle/pages/
.. _GitLab: https://gitlab.com/
.. _GitLab CI: https://about.gitlab.com/stages-devops-lifecycle/continuous-integration/
.. _Netlify: https://www.netlify.com/
.. _"Jamstack": https://jamstack.org/

Embracing the "Docs as Code" philosophy
---------------------------------------

The free offerings of most of the options listed above require your
documentation sources to be publicly available. Moreover, these services
expect you to use a `Version Control System`_, a technology that tracks the
evolution of a collection of files as a series of snapshots ("commits").
The practice of writing documentation in plain text files with the same tools
as the ones used for software development is commonly known as `"Docs as Code"`_.

The most popular Version Control System nowadays is Git_, a free and open
source tool that is the backbone of services like GitHub and GitLab.
Since both Read the Docs and Netlify have integrations with GitHub and GitLab,
and both GitHub and GitLab have an integrated Pages product, the most effective
way of automatically build your documentation online is to upload your sources
to either of these Git hosting services.

.. _Version Control System: https://en.wikipedia.org/wiki/Version_control
.. _"Docs as Code": https://www.writethedocs.org/guide/docs-as-code/
.. _Git: https://git-scm.com/

.. _publishing-sources:

Publishing your documentation sources
-------------------------------------

GitHub
~~~~~~

The quickest way to upload an existing project to GitHub is to:

1. `Sign up for a GitHub account <https://github.com/signup>`_.
2. `Create a new repository <https://github.com/new>`_.
3. Open `the "Upload files" page`_ of your new repository.
4. Select the files on your operating system file browser (in your case
   ``README.rst``, ``lumache.py``, the makefiles under the ``docs`` directory,
   and everything under ``docs/source``) and drag them to the GitHub interface
   to upload them all.
5. Click on the :guilabel:`Commit changes` button.

.. _the "Upload files" page: https://docs.github.com/en/repositories/working-with-files/managing-files/adding-a-file-to-a-repository

.. note::

   Make sure you don't upload the ``docs/build`` directory, as it contains the
   output generated by Sphinx and it will change every time you change the
   sources, complicating your workflow.

These steps do not require access to the command line or installing any
additional software. To learn more, read `this quickstart tutorial`_ or
consult the `official GitHub documentation`_

.. _this quickstart tutorial: https://docs.github.com/en/get-started/quickstart
.. _official GitHub documentation: https://docs.github.com/en/get-started

GitLab
~~~~~~

Similarly to GitHub, the fastest way to upload your project to GitLab is
using the web interface:

1. `Sign up for a GitLab account <https://gitlab.com/users/sign_up>`_.
2. `Create a new blank project <https://gitlab.com/projects/new>`_.
3. Upload the project files (in your case ``README.rst``, ``lumache.py``, the
   makefiles under the ``docs`` directory, and everything under
   ``docs/source``) one by one using the :guilabel:`Upload File` button [#f1]_.

Again, these steps do not require additional software on your computer. To
learn more, you can:

- Follow `this tutorial`_ to install Git on your machine.
- Browse the `GitLab User documentation`_ to understand the possibilities of
  the platform.

.. _this tutorial: https://docs.gitlab.com/ee/gitlab-basics/start-using-git.html
.. _GitLab User documentation: https://docs.gitlab.com/ee/user/index.html

.. note::

   Make sure you don't upload the ``docs/build`` directory, as it contains the
   output generated by Sphinx and it will change every time you change the
   sources, complicating your workflow.

.. [#f1] At the time of writing, `uploading whole directories to GitLab using
         only the web
         interface <https://gitlab.com/gitlab-org/gitlab/-/issues/228490>`_ is
         not yet implemented.

Publishing your HTML documentation
----------------------------------

Read the Docs
~~~~~~~~~~~~~

`Read the Docs`_ offers integration with both GitHub and GitLab. The quickest
way of getting started is to follow :doc:`the RTD
tutorial <readthedocs:tutorial/index>`, which is loosely based on this one.
You can publish your sources on GitHub as explained :ref:`in the previous
section <publishing-sources>`, then skip directly to
:ref:`readthedocs:tutorial/index:Creating a Read the Docs account`.
If you choose GitLab instead, the process is similar.

GitHub Pages
~~~~~~~~~~~~

`GitHub Pages`_ requires you to :ref:`publish your
sources <publishing-sources>` on `GitHub`_. After that, you will need an
automated process that performs the ``make html`` step every time the sources
change. That can be achieved using `GitHub Actions`_.

After you have published your sources on GitHub, create a file named
``.github/workflows/sphinx.yml`` in your repository with the following
contents:

.. code-block:: yaml
   :caption: .github/workflows/

   name: "Sphinx: Render docs"

   on: push

   jobs:
     build:
       runs-on: ubuntu-latest
       permissions:
         contents: write
       steps:
       - uses: actions/checkout@v4
         with:
           persist-credentials: false
       - name: Build HTML
         uses: ammaraskar/sphinx-action@master
       - name: Upload artifacts
         uses: actions/upload-artifact@v4
         with:
           name: html-docs
           path: docs/build/html/
       - name: Deploy
         uses: peaceiris/actions-gh-pages@v3
         if: github.ref == 'refs/heads/main'
         with:
           github_token: ${{ secrets.GITHUB_TOKEN }}
           publish_dir: docs/build/html

This contains a GitHub Actions workflow with a single job of four steps:

1. Checkout the code.
2. Build the HTML documentation using Sphinx.
3. Attach the HTML output the artifacts to the GitHub Actions job, for easier
   inspection.
4. If the change happens on the default branch, take the contents of
   ``docs/build/html`` and push it to the ``gh-pages`` branch.

Next, you need to specify the dependencies for the ``make html`` step to be
successful. For that, create a file ``docs/requirements.txt`` and add the
following contents:

.. code-block::
   :caption: docs/requirements.txt

   furo==2021.11.16

And finally, you are ready to `enable GitHub Pages on your repository`_. For
that, go to :guilabel:`Settings`, then :guilabel:`Pages` on the left sidebar,
select the ``gh-pages`` branch in the "Source" dropdown menu, and click
:guilabel:`Save`. After a few minutes, you should be able to see your HTML at
the designated URL.

.. _enable GitHub Pages on your repository: https://docs.github.com/en/pages/getting-started-with-github-pages/configuring-a-publishing-source-for-your-github-pages-site

GitLab Pages
~~~~~~~~~~~~

`GitLab Pages`_, on the other hand, requires you to :ref:`publish your
sources <publishing-sources>` on `GitLab`_. When you are ready, you can
automate the process of running ``make html`` using `GitLab CI`_.

After you have published your sources on GitLab, create a file named
``.gitlab-ci.yml`` in your repository with these contents:

.. code-block:: yaml
   :caption: .gitlab-ci.yml

   stages:
     - deploy

   pages:
     stage: deploy
     image: python:3.12-slim
     before_script:
       - apt-get update && apt-get install make --no-install-recommends -y
       - python -m pip install sphinx furo
     script:
       - cd docs && make html
     after_script:
       - mv docs/build/html/ ./public/
     artifacts:
       paths:
       - public
     rules:
       - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH

This contains a GitLab CI workflow with one job of several steps:

1. Install the necessary dependencies.
2. Build the HTML documentation using Sphinx.
3. Move the output to a known artifacts location.

.. note::
   You will need to `validate your account`_ by entering a payment method
   (you will be charged a small amount that will then be reimbursed).

.. _validate your account: https://about.gitlab.com/blog/2021/05/17/prevent-crypto-mining-abuse/#validating-an-account

After that, if the pipeline is successful, you should be able to see your HTML
at the designated URL.

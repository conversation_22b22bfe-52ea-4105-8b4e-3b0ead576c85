.. _tutorial:

Build your first project
========================

In this tutorial you will build a simple documentation project using Sphinx, and
view it in your browser as HTML.  The project will include narrative,
handwritten documentation, as well as autogenerated API documentation.

The tutorial is aimed towards Sphinx newcomers willing to learn the fundamentals
of how projects are created and structured.  You will create a fictional
software library to generate random food recipes that will serve as a guide
throughout the process, with the objective of properly documenting it.

To showcase Sphinx capabilities for code documentation you will use Python,
which also supports *automatic* documentation generation.

.. note::

   Several other languages are natively supported in Sphinx for *manual* code
   documentation, however they require extensions for *automatic* code
   documentation, like `<PERSON>reathe <https://breathe.readthedocs.io/>`_.

To follow the instructions you will need access to a Linux-like command line and
a basic understanding of how it works, as well as a working Python installation
for development, since you will use *Python virtual environments* to create the
project.

.. toctree::

   getting-started
   first-steps
   more-sphinx-customization
   narrative-documentation
   describing-code
   automatic-doc-generation
   deploying
   end

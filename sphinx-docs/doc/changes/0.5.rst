==========
Sphinx 0.5
==========


Release 0.5.2 (Mar 24, 2009)
============================

* Properly escape ``|`` in LaTeX output.

* #71: If a decoding error occurs in source files, print a
  warning and replace the characters by "?".

* Fix a problem in the HTML search if the index takes too long
  to load.

* Don't output system messages while resolving, because they
  would stay in the doctrees even if keep_warnings is false.

* #82: Determine the correct path for dependencies noted by
  docutils.  This fixes behavior where a source with dependent
  files was always reported as changed.

* Recognize toctree directives that are not on section toplevel,
  but within block items, such as tables.

* Use a new RFC base URL, since rfc.org seems down.

* Fix a crash in the todolist directive when no todo items are
  defined.

* Don't call LaTeX or dvipng over and over again if it was not
  found once, and use text-only latex as a substitute in that case.

* Fix problems with footnotes in the LaTeX output.

* Prevent double hyphens becoming en-dashes in literal code in
  the LaTeX output.

* Open literalinclude files in universal newline mode to allow
  arbitrary newline conventions.

* Actually make the ``-Q`` option work.

* #86: Fix explicit document titles in toctrees.

* #81: Write environment and search index in a manner that is safe
  from exceptions that occur during dumping.

* #80: Fix UnicodeErrors when a locale is set with setlocale().


Release 0.5.1 (Dec 15, 2008)
============================

* #67: Output warnings about failed doctests in the doctest extension
  even when running in quiet mode.

* #72: In pngmath, make it possible to give a full path to LaTeX and
  dvipng on Windows.  For that to work, the ``pngmath_latex`` and
  ``pngmath_dvipng`` options are no longer split into command and
  additional arguments; use ``pngmath_latex_args`` and
  ``pngmath_dvipng_args`` to give additional arguments.

* Don't crash on failing doctests with non-ASCII characters.

* Don't crash on writing status messages and warnings containing
  unencodable characters.

* Warn if a doctest extension block doesn't contain any code.

* Fix the handling of ``:param:`` and ``:type:`` doc fields when
  they contain markup (especially cross-referencing roles).

* #65: Fix storage of depth information for PNGs generated by the
  pngmath extension.

* Fix autodoc crash when automethod is used outside a class context.

* #68: Fix LaTeX writer output for images with specified height.

* #60: Fix wrong generated image path when including images in sources
  in subdirectories.

* Fix the JavaScript search when html_copy_source is off.

* Fix an indentation problem in autodoc when documenting classes
  with the option ``autoclass_content = "both"`` set.

* Don't crash on empty index entries, only emit a warning.

* Fix a typo in the search JavaScript code, leading to unusable
  search function in some setups.


Release 0.5 (Nov 23, 2008) -- Birthday release!
===============================================

New features added
------------------

* Markup features:

  - Citations are now global: all citation defined in any file can be
    referenced from any file.  Citations are collected in a bibliography
    for LaTeX output.

  - Footnotes are now properly handled in the LaTeX builder: they appear
    at the location of the footnote reference in text, not at the end of
    a section.  Thanks to Andrew McNamara for the initial patch.

  - "System Message" warnings are now automatically removed from the
    built documentation, and only written to stderr.  If you want the
    old behavior, set the new config value ``keep_warnings`` to ``True``.

  - Glossary entries are now automatically added to the index.

  - Figures with captions can now be referred to like section titles,
    using the ``:ref:`` role without an explicit link text.

  - Added ``cmember`` role for consistency.

  - Lists enumerated by letters or roman numerals are now handled like in
    standard reST.

  - The ``seealso`` directive can now also be given arguments, as a short
    form.

  - You can now document several programs and their options with the
    new ``program`` directive.

* HTML output and templates:

  - Incompatible change: The "root" relation link (top left in the
    relbar) now points to the ``master_doc`` by default, no longer to a
    document called "index".  The old behavior, while useful in some
    situations, was somewhat unexpected.  Override the "rootrellink"
    block in the template to customize where it refers to.

  - The JavaScript search now searches for objects before searching in
    the full text.

  - TOC tree entries now have CSS classes that make it possible to
    style them depending on their depth.

  - Highlighted code blocks now have CSS classes that make it possible
    to style them depending on their language.

  - HTML ``<meta>`` tags via the Docutils :dudir:`meta` directive are now
    supported.

  - ``SerializingHTMLBuilder`` was added as new abstract builder that
    can be subclassed to serialize build HTML in a specific format.  The
    ``PickleHTMLBuilder`` is a concrete subclass of it that uses pickle
    as serialization implementation.

  - ``JSONHTMLBuilder`` was added as another ``SerializingHTMLBuilder``
    subclass that dumps the generated HTML into JSON files for further
    processing.

  - The ``rellinks`` block in the layout template is now called
    ``linktags`` to avoid confusion with the relbar links.

  - The HTML builders have two additional attributes now that can be
    used to disable the anchor-link creation after headlines and
    definition links.

  - Only generate a module index if there are some modules in the
    documentation.

* New and changed config values:

  - Added support for internationalization in generated text with the
    ``language`` and ``locale_dirs`` config values.  Many thanks to
    language contributors:

    * Horst Gutmann -- German
    * Pavel Kosina -- Czech
    * David Larlet -- French
    * Michał Kandulski -- Polish
    * Yasushi Masuda -- Japanese
    * Guillem Borrell -- Spanish
    * Luc Saffre and Peter Bertels -- Dutch
    * Fred Lin -- Traditional Chinese
    * Roger Demetrescu -- Brazilian Portuguese
    * Rok Garbas -- Slovenian

  - The new config value ``highlight_language`` set a global default for
    highlighting.  When ``'python3'`` is selected, console output blocks
    are recognized like for ``'python'``.

  - Exposed Pygments' lexer guessing as a highlight "language" ``guess``.

  - The new config value ``latex_elements`` allows to override all LaTeX
    snippets that Sphinx puts into the generated .tex file by default.

  - Added ``exclude_dirnames`` config value that can be used to exclude
    e.g. CVS directories from source file search.

  - Added ``source_encoding`` config value to select input encoding.

* Extensions:

  - The new extensions ``sphinx.ext.jsmath`` and ``sphinx.ext.pngmath``
    provide math support for both HTML and LaTeX builders.

  - The new extension ``sphinx.ext.intersphinx`` half-automatically
    creates links to Sphinx documentation of Python objects in other
    projects.

  - The new extension ``sphinx.ext.todo`` allows the insertion of
    "To do" directives whose visibility in the output can be toggled.
    It also adds a directive to compile a list of all todo items.

  - sphinx.ext.autodoc has a new event ``autodoc-process-signature``
    that allows tuning function signature introspection.

  - sphinx.ext.autodoc has a new event ``autodoc-skip-member`` that allows
    tuning which members are included in the generated content.

  - Respect ``__all__`` when autodocumenting module members.

  - The ``automodule`` directive now supports the ``synopsis``,
    ``deprecated`` and ``platform`` options.

* Extension API:

  - ``Sphinx.add_node()`` now takes optional visitor methods for the
    HTML, LaTeX and text translators; this prevents having to manually
    patch the classes.

  - Added ``Sphinx.add_javascript()`` that adds scripts to load in the
    default HTML template.

  - Added new events: ``source-read``, ``env-updated``,
    ``env-purge-doc``, ``missing-reference``, ``build-finished``.

* Other changes:

  - Added a command-line switch ``-Q``: it will suppress warnings.

  - Added a command-line switch ``-A``: it can be used to supply
    additional values into the HTML templates.

  - Added a command-line switch ``-C``: if it is given, no configuration
    file ``conf.py`` is required.

  - Added a distutils command ``build_sphinx``: When Sphinx is installed,
    you can call ``python setup.py build_sphinx`` for projects that have
    Sphinx documentation, which will build the docs and place them in
    the standard distutils build directory.

  - In quickstart, if the selected root path already contains a Sphinx
    project, complain and abort.

Bugs fixed
----------

* #51: Escape configuration values placed in HTML templates.

* #44: Fix small problems in HTML help index generation.

* Fix LaTeX output for line blocks in tables.

* #38: Fix "illegal unit" error when using pixel image widths/heights.

* Support table captions in LaTeX output.

* #39: Work around a bug in Jinja that caused "<generator ...>" to be
  emitted in HTML output.

* Fix a problem with module links not being generated in LaTeX output.

* Fix the handling of images in different directories.

* #29: Support option lists in the text writer.  Make sure that dashes
  introducing long option names are not contracted to en-dashes.

* Support the "scale" option for images in HTML output.

* #25: Properly escape quotes in HTML help attribute values.

* Fix LaTeX build for some description environments with ``:noindex:``.

* #24: Don't crash on uncommon casing of role names (like ``:Class:``).

* Only output ANSI colors on color terminals.

* Update to newest fncychap.sty, to fix problems with non-ASCII
  characters at the start of chapter titles.

* Fix a problem with index generation in LaTeX output, caused by
  hyperref not being included last.

* Don't disregard return annotations for functions without any parameters.

* Don't throw away labels for code blocks.

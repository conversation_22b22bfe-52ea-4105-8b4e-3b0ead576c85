==========
Sphinx 0.4
==========


Release 0.4.3 (Oct 8, 2008)
===========================

* Fix a bug in autodoc with directly given autodoc members.

* Fix a bug in autodoc that would import a module twice, once as
  "module", once as "module.".

* Fix a bug in the HTML writer that created duplicate ``id``
  attributes for section titles with Docutils 0.5.

* <PERSON><PERSON>ly call ``super()`` in overridden blocks in templates.

* Add a fix when using XeTeX.

* Unify handling of LaTeX escaping.

* Rebuild everything when the ``extensions`` config value changes.

* Don't try to remove a nonexisting static directory.

* Fix an indentation problem in production lists.

* Fix encoding handling for literal include files: ``literalinclude``
  now has an ``encoding`` option that defaults to UTF-8.

* Fix the handling of non-ASCII characters entered in quickstart.

* Fix a crash with nonexisting image URIs.


Release 0.4.2 (Jul 29, 2008)
============================

* Fix rendering of the ``samp`` role in HTML.

* Fix a bug with LaTeX links to headings leading to a wrong page.

* Reread documents with globbed toctrees when source files are
  added or removed.

* Add a missing parameter to PickleHTMLBuilder.handle_page().

* Put inheritance info always on its own line.

* Don't automatically enclose code with whitespace in it in quotes;
  only do this for the ``samp`` role.

* autodoc now emits a more precise error message when a module
  can't be imported or an attribute can't be found.

* The JavaScript search now uses the correct file name suffix when
  referring to found items.

* The automodule directive now accepts the ``inherited-members``
  and ``show-inheritance`` options again.

* You can now rebuild the docs normally after relocating the source
  and/or doctree directory.


Release 0.4.1 (Jul 5, 2008)
===========================

* Added sub-/superscript node handling to TextBuilder.

* Label names in references are now case-insensitive, since reST
  label names are always lowercased.

* Fix linkcheck builder crash for malformed URLs.

* Add compatibility for admonitions and Docutils 0.5.

* Remove the silly restriction on "rubric" in the LaTeX writer: you
  can now write arbitrary "rubric" directives, and only those with
  a title of "Footnotes" will be ignored.

* Copy the HTML logo to the output ``_static`` directory.

* Fix LaTeX code for modules with underscores in names and platforms.

* Fix a crash with nonlocal image URIs.

* Allow the usage of :noindex: in ``automodule`` directives, as
  documented.

* Fix the ``delete()`` docstring processor function in autodoc.

* Fix warning message for nonexisting images.

* Fix JavaScript search in Internet Explorer.


Release 0.4 (Jun 23, 2008)
==========================

New features added
------------------

* ``tocdepth`` can be given as a file-wide metadata entry, and
  specifies the maximum depth of a TOC of this file.

* The new config value ``default_role`` can be used to select the
  default role for all documents.

* Sphinx now interprets field lists with fields like ``:param foo:``
  in description units.

* The new ``staticmethod`` directive can be used to mark methods as
  static methods.

* HTML output:

  - The "previous" and "next" links have a more logical structure, so
    that by following "next" links you can traverse the entire TOC
    tree.

  - The new event ``html-page-context`` can be used to include custom
    values into the context used when rendering an HTML template.

  - Document metadata is now in the default template context, under
    the name ``metadata``.

  - The new config value ``html_favicon`` can be used to set a favicon
    for the HTML output.  Thanks to Sebastian Wiesner.

  - The new config value ``html_use_index`` can be used to switch index
    generation in HTML documents off.

  - The new config value ``html_split_index`` can be used to create
    separate index pages for each letter, to be used when the complete
    index is too large for one page.

  - The new config value ``html_short_title`` can be used to set a
    shorter title for the documentation which is then used in the
    navigation bar.

  - The new config value ``html_show_sphinx`` can be used to control
    whether a link to Sphinx is added to the HTML footer.

  - The new config value ``html_file_suffix`` can be used to set the
    HTML file suffix to e.g. ``.xhtml``.

  - The directories in the ``html_static_path`` can now contain
    subdirectories.

  - The module index now isn't collapsed if the number of submodules
    is larger than the number of toplevel modules.

* The image directive now supports specifying the extension as ``.*``,
  which makes the builder select the one that matches best.  Thanks to
  Sebastian Wiesner.

* The new config value ``exclude_trees`` can be used to exclude whole
  subtrees from the search for source files.

* Defaults for configuration values can now be callables, which allows
  dynamic defaults.

* The new TextBuilder creates plain-text output.

* Python 3-style signatures, giving a return annotation via ``->``,
  are now supported.

* Extensions:

  - The autodoc extension now offers a much more flexible way to
    manipulate docstrings before including them into the output, via
    the new ``autodoc-process-docstring`` event.

  - The ``autodoc`` extension accepts signatures for functions, methods
    and classes now that override the signature got via introspection
    from Python code.

  - The ``autodoc`` extension now offers a ``show-inheritance`` option
    for autoclass that inserts a list of bases after the signature.

  - The autodoc directives now support the ``noindex`` flag option.


Bugs fixed
----------

* Correctly report the source location for docstrings included with
  autodoc.

* Fix the LaTeX output of description units with multiple signatures.

* Handle the figure directive in LaTeX output.

* Handle raw admonitions in LaTeX output.

* Fix determination of the title in HTML help output.

* Handle project names containing spaces.

* Don't write SSI-like comments in HTML output.

* Rename the "sidebar" class to "sphinxsidebar" in order to stay different
  from reST sidebars.

* Use a binary TOC in HTML help generation to fix issues links without
  explicit anchors.

* Fix behavior of references to functions/methods with an explicit title.

* Support citation, subscript and superscript nodes in LaTeX writer.

* Provide the standard "class" directive as "cssclass"; else it is
  shadowed by the Sphinx-defined directive.

* Fix the handling of explicit module names given to autoclass directives.
  They now show up with the correct module name in the generated docs.

* Enable autodoc to process Unicode docstrings.

* The LaTeX writer now translates line blocks with ``\raggedright``,
  which plays nicer with tables.

* Fix bug with directories in the HTML builder static path.

==========
Sphinx 0.6
==========


Release 0.6.7 (Jun 05, 2010)
============================

* #440: Remove usage of a Python >= 2.5 API in the ``literalinclude``
  directive.

* Fix a bug that prevented some references being generated in the
  LaTeX builder.

* #428: Add some missing CSS styles for standard Docutils classes.

* #432: Fix UnicodeErrors while building LaTeX in translated locale.


Release 0.6.6 (May 25, 2010)
============================

* Handle raw nodes in the ``text`` writer.

* Fix a problem the Qt help project generated by the ``qthelp``
  builder that would lead to no content being displayed in the Qt
  Assistant.

* #393: Fix the usage of Unicode characters in mathematic formulas
  when using the ``pngmath`` extension.

* #404: Make ``\and`` work properly in the author field of the
  ``latex_documents`` setting.

* #409: Make the ``highlight_language`` config value work properly
  in the LaTeX builder.

* #418: Allow relocation of the translation JavaScript files to
  the system directory on Unix systems.

* #414: Fix handling of Windows newlines in files included with
  the ``literalinclude`` directive.

* #377: Fix crash in linkcheck builder.

* #387: Fix the display of search results in ``dirhtml`` output.

* #376: In autodoc, fix display of parameter defaults containing
  backslashes.

* #370: Fix handling of complex list item labels in LaTeX output.

* #374: Make the ``doctest_path`` config value of the doctest
  extension actually work.

* Fix the handling of multiple toctrees when creating the global
  TOC for the ``toctree()`` template function.

* Fix the handling of hidden toctrees when creating the global TOC
  for the ``toctree()`` template function.

* Fix the handling of nested lists in the text writer.

* #362: In autodoc, check for the existence of ``__self__`` on
  function objects before accessing it.

* #353: Strip leading and trailing whitespace when extracting
  search words in the search function.


Release 0.6.5 (Mar 01, 2010)
============================

* In autodoc, fix the omission of some module members explicitly
  documented using documentation comments.

* #345: Fix cropping of sidebar scroll bar with ``stickysidebar``
  option of the default theme.

* #341: Always generate UNIX newlines in the quickstart Makefile.

* #338: Fix running with ``-C`` under Windows.

* In autodoc, allow customizing the signature of an object where
  the built-in mechanism fails.

* #331: Fix output for enumerated lists with start values in LaTeX.

* Make the ``start-after`` and ``end-before`` options to the
  ``literalinclude`` directive work correctly if not used together.

* #321: Fix link generation in the LaTeX builder.


Release 0.6.4 (Jan 12, 2010)
============================

* Improve the handling of non-Unicode strings in the configuration.

* #316: Catch OSErrors occurring when calling graphviz with
  arguments it doesn't understand.

* Restore compatibility with Pygments >= 1.2.

* #295: Fix escaping of hyperref targets in LaTeX output.

* #302: Fix links generated by the ``:doc:`` role for LaTeX output.

* #286: collect todo nodes after the whole document has been read;
  this allows placing substitution references in todo items.

* #294: do not ignore an explicit ``today`` config value in a
  LaTeX build.

* The ``alt`` text of inheritance diagrams is now much cleaner.

* Ignore images in section titles when generating link captions.

* #310: support exception messages in the ``testoutput`` blocks of
  the ``doctest`` extension.

* #293: line blocks are styled properly in HTML output.

* #285: make the ``locale_dirs`` config value work again.

* #303: ``html_context`` values given on the command line via ``-A``
  should not override other values given in conf.py.

* Fix a bug preventing incremental rebuilds for the ``dirhtml``
  builder.

* #299: Fix the mangling of quotes in some literal blocks.

* #292: Fix path to the search index for the ``dirhtml`` builder.

* Fix a Jython compatibility issue: make the dependence on the
  ``parser`` module optional.

* #238: In autodoc, catch all errors that occur on module import,
  not just ``ImportError``.

* Fix the handling of non-data, but non-method descriptors in autodoc.

* When copying file times, ignore OSErrors raised by ``os.utime()``.


Release 0.6.3 (Sep 03, 2009)
============================

* Properly add C module filenames as dependencies in autodoc.

* #253: Ignore graphviz directives without content instead of
  raising an unhandled exception.

* #241: Fix a crash building LaTeX output for documents that contain
  a todolist directive.

* #252: Make it easier to change the build dir in the Makefiles
  generated by quickstart.

* #220: Fix CSS so that displaymath really is centered.

* #222: Allow the "Footnotes" header to be translated.

* #225: Don't add whitespace in generated HTML after inline tags.

* #227: Make ``literalinclude`` work when the document's path
  name contains non-ASCII characters.

* #229: Fix autodoc failures with members that raise errors
  on ``getattr()``.

* #205: When copying files, don't copy full stat info, only
  modification times.

* #232: Support non-ASCII metadata in Qt help builder.

* Properly format bullet lists nested in definition lists for LaTeX.

* Section titles are now allowed inside ``only`` directives.

* #201: Make ``centered`` directive work in LaTeX output.

* #206: Refuse to overwrite an existing master document in
  sphinx-quickstart.

* #208: Use MS-sanctioned locale settings, determined by the
  ``language`` config option, in the HTML help builder.

* #210: Fix nesting of HTML tags for displayed math from pngmath
  extension.

* #213: Fix centering of images in LaTeX output.

* #211: Fix compatibility with Docutils 0.5.


Release 0.6.2 (Jun 16, 2009)
============================

* #130: Fix obscure IndexError in doctest extension.

* #167: Make glossary sorting case-independent.

* #196: Add a warning if an extension module doesn't have a
  ``setup()`` function.

* #158: Allow '..' in template names, and absolute template paths;
  Jinja 2 by default disables both.

* When highlighting Python code, ignore extra indentation before
  trying to parse it as Python.

* #191: Don't escape the tilde in URIs in LaTeX.

* Don't consider contents of source comments for the search index.

* Set the default encoding to ``utf-8-sig`` to handle files with a
  UTF-8 BOM correctly.

* #178: apply ``add_function_parentheses`` config value to C
  functions as promised.

* #173: Respect the Docutils ``title`` directive.

* #172: The ``obj`` role now links to modules as promised.

* #19: Tables now can have a "longtable" class, in order to get
  correctly broken into pages in LaTeX output.

* Look for Sphinx message catalogs in the system default path before
  trying ``sphinx/locale``.

* Fix the search for methods via "classname.methodname".

* #155: Fix Python 2.4 compatibility: exceptions are old-style
  classes there.

* #150: Fix display of the "sphinxdoc" theme on Internet Explorer
  versions 6 and 7.

* #146: Don't fail to generate LaTeX when the user has an active
  ``.docutils`` configuration.

* #29: Don't generate visible "-{-}" in option lists in LaTeX.

* Fix cross-reference roles when put into substitutions.

* Don't put image "alt" text into table-of-contents entries.

* In the LaTeX writer, do not raise an exception on too many section
  levels, just use the "subparagraph" level for all of them.

* #145: Fix autodoc problem with automatic members that refuse to be
  getattr()'d from their parent.

* If specific filenames to build are given on the command line,
  check that they are within the source directory.

* Fix autodoc crash for objects without a ``__name__``.

* Fix intersphinx for installations without urllib2.HTTPSHandler.

* #134: Fix pending_xref leftover nodes when using the todolist
  directive from the todo extension.


Release 0.6.1 (Mar 26, 2009)
============================

* #135: Fix problems with LaTeX output and the graphviz extension.

* #132: Include the autosummary "module" template in the distribution.


Release 0.6 (Mar 24, 2009)
==========================

New features added
------------------

* Incompatible changes:

  - Templating now requires the Jinja2 library, which is an enhanced
    version of the old Jinja1 engine.  Since the syntax and semantic
    is largely the same, very few fixes should be necessary in
    custom templates.

  - The "document" div tag has been moved out of the ``layout.html``
    template's "document" block, because the closing tag was already
    outside.  If you overwrite this block, you need to remove your
    "document" div tag as well.

  - The ``autodoc_skip_member`` event now also gets to decide
    whether to skip members whose name starts with underscores.
    Previously, these members were always automatically skipped.
    Therefore, if you handle this event, add something like this
    to your event handler to restore the old behavior::

       if name.startswith('_'):
           return True

* Theming support, see the new section in the documentation.

* Markup:

  - Due to popular demand, added a ``:doc:`` role which directly
    links to another document without the need of creating a
    label to which a ``:ref:`` could link to.

  - #4: Added a ``:download:`` role that marks a non-document file
    for inclusion into the HTML output and links to it.

  - Added an ``only`` directive that can selectively include text
    based on enabled "tags".  Tags can be given on the command
    line.  Also, the current builder output format (e.g. "html" or
    "latex") is always a defined tag.

  - #10: Added HTML section numbers, enabled by giving a
    ``:numbered:`` flag to the ``toctree`` directive.

  - #114: Added an ``abbr`` role to markup abbreviations and
    acronyms.

  - The ``literalinclude`` directive now supports several more
    options, to include only parts of a file.

  - The ``toctree`` directive now supports a ``:hidden:`` flag,
    which will prevent links from being generated in place of
    the directive -- this allows you to define your document
    structure, but place the links yourself.

  - #123: The ``glossary`` directive now supports a ``:sorted:``
    flag that sorts glossary entries alphabetically.

  - Paths to images, literal include files and download files
    can now be absolute (like ``/images/foo.png``).  They are
    treated as relative to the top source directory.

  - #52: There is now a ``hlist`` directive, creating a compact
    list by placing distributing items into multiple columns.

  - #77: If a description environment with info field list only
    contains one ``:param:`` entry, no bullet list is generated.

  - #6: Don't generate redundant ``<ul>`` for top-level TOC tree
    items, which leads to a visual separation of TOC entries.

  - #23: Added a ``classmethod`` directive along with ``method``
    and ``staticmethod``.

  - Scaled images now get a link to the unscaled version.

  - SVG images are now supported in HTML (via ``<object>`` and
    ``<embed>`` tags).

  - Added a ``toctree`` callable to the templates, and the ability
    to include external links in toctrees. The 'collapse' keyword
    argument indicates whether or not to only display subitems of
    the current page.  (Defaults to ``True``.)

* Configuration:

  - The new config value ``rst_epilog`` can contain reST that is
    appended to each source file that is read.  This is the right
    place for global substitutions.

  - The new ``html_add_permalinks`` config value can be used to
    switch off the generated "paragraph sign" permalinks for each
    heading and definition environment.

  - The new ``html_show_sourcelink`` config value can be used to
    switch off the links to the reST sources in the sidebar.

  - The default value for ``htmlhelp_basename`` is now the project
    title, cleaned up as a filename.

  - The new ``modindex_common_prefix`` config value can be used to
    ignore certain package names for module index sorting.

  - The new ``trim_footnote_reference_space`` config value mirrors
    the Docutils config value of the same name and removes the
    space before a footnote reference that is necessary for reST
    to recognize the reference.

  - The new ``latex_additional_files`` config value can be used to
    copy files (that Sphinx doesn't copy automatically, e.g. if they
    are referenced in custom LaTeX added in ``latex_elements``) to
    the build directory.

* Builders:

  - The HTML builder now stores a small file named ``.buildinfo`` in
    its output directory.  It stores a hash of config values that
    can be used to determine if a full rebuild needs to be done (e.g.
    after changing ``html_theme``).

  - New builder for Qt help collections, by Antonio Valentino.

  - The new ``DirectoryHTMLBuilder`` (short name ``dirhtml``) creates
    a separate directory for every page, and places the page there
    in a file called ``index.html``.  Therefore, page URLs and links
    don't need to contain ``.html``.

  - The new ``html_link_suffix`` config value can be used to select
    the suffix of generated links between HTML files.

  - #96: The LaTeX builder now supports figures wrapped by text, when
    using the ``figwidth`` option and right/left alignment.

* New translations:

  - Italian by Sandro Dentella.
  - Ukrainian by Petro Sasnyk.
  - Finnish by Jukka Inkeri.
  - Russian by Alexander Smishlajev.

* Extensions and API:

  - New ``graphviz`` extension to embed graphviz graphs.

  - New ``inheritance_diagram`` extension to embed... inheritance
    diagrams!

  - New ``autosummary`` extension that generates summaries of
    modules and automatic documentation of modules.

  - Autodoc now has a reusable Python API, which can be used to
    create custom types of objects to auto-document (e.g. Zope
    interfaces).  See also ``Sphinx.add_autodocumenter()``.

  - Autodoc now handles documented attributes.

  - Autodoc now handles inner classes and their methods.

  - Autodoc can document classes as functions now if explicitly
    marked with ``autofunction``.

  - Autodoc can now exclude single members from documentation
    via the ``exclude-members`` option.

  - Autodoc can now order members either alphabetically (like
    previously) or by member type; configurable either with the
    config value ``autodoc_member_order`` or a ``member-order``
    option per directive.

  - The function ``Sphinx.add_directive()`` now also supports
    Docutils 0.5-style directive classes.  If they inherit from
    ``sphinx.util.compat.Directive``, they also work with
    Docutils 0.4.

  - There is now a ``Sphinx.add_lexer()`` method to be able to use
    custom Pygments lexers easily.

  - There is now ``Sphinx.add_generic_role()`` to mirror the
    Docutils' own function.

* Other changes:

  - Config overrides for single dict keys can now be given on the
    command line.

  - There is now a ``doctest_global_setup`` config value that can
    be used to give setup code for all doctests in the documentation.

  - Source links in HTML are now generated with ``rel="nofollow"``.

  - Quickstart can now generate a Windows ``make.bat`` file.

  - #62: There is now a ``-w`` option for sphinx-build that writes
    warnings to a file, in addition to stderr.

  - There is now a ``-W`` option for sphinx-build that turns warnings
    into errors.

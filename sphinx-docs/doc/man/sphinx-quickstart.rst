sphinx-quickstart
=================

Synopsis
--------

**sphinx-quickstart**

Description
-----------

:program:`sphinx-quickstart` is an interactive tool that asks some questions
about your project and then generates a complete documentation directory and
sample Makefile to be used with :manpage:`sphinx-build(1)`.

Options
-------

.. program:: sphinx-quickstart

.. option:: -q, --quiet

   Quiet mode that skips the interactive wizard for specifying options.
   This option requires `-p`, `-a` and `-v` options.

.. option:: -h, --help, --version

   Display usage summary or Sphinx version.

.. rubric:: Structure Options

.. option:: --sep

   If specified, separate source and build directories.

.. option:: --no-sep

   If specified, create build directory under source directory.

.. option:: --dot=DOT

   Inside the root directory, two more directories will be created;
   "_templates" for custom HTML templates and "_static" for custom stylesheets
   and other static files. You can enter another prefix (such as ".") to
   replace the underscore.

.. rubric:: Project Basic Options

.. option:: -p PROJECT, --project=PROJECT

   Project name will be set. (see :confval:`project`).

.. option:: -a AUTHOR, --author=AUTHOR

   Author names. (see :confval:`copyright`).

.. option:: -v VERSION

   Version of project. (see :confval:`version`).

.. option:: -r RELEASE, --release=RELEASE

   Release of project. (see :confval:`release`).

.. option:: -l LANGUAGE, --language=LANGUAGE

   Document language. (see :confval:`language`).

.. option:: --suffix=SUFFIX

   Source file suffix. (see :confval:`source_suffix`).

.. option:: --master=MASTER

   Master document name. (see :confval:`root_doc`).

.. rubric:: Extension Options

.. option:: --ext-autodoc

   Enable :py:mod:`sphinx.ext.autodoc` extension.

.. option:: --ext-doctest

   Enable `sphinx.ext.doctest` extension.

.. option:: --ext-intersphinx

   Enable `sphinx.ext.intersphinx` extension.

.. option:: --ext-todo

   Enable `sphinx.ext.todo` extension.

.. option:: --ext-coverage

   Enable `sphinx.ext.coverage` extension.

.. option:: --ext-imgmath

   Enable `sphinx.ext.imgmath` extension.

.. option:: --ext-mathjax

   Enable `sphinx.ext.mathjax` extension.

.. option:: --ext-ifconfig

   Enable `sphinx.ext.ifconfig` extension.

.. option:: --ext-viewcode

   Enable `sphinx.ext.viewcode` extension.

.. option:: --ext-githubpages

   Enable `sphinx.ext.githubpages` extension.

.. option:: --extensions=EXTENSIONS

   Enable arbitrary extensions.

.. rubric:: Makefile and Batchfile Creation Options

.. option:: --use-make-mode (-m), --no-use-make-mode (-M)

   :file:`Makefile/make.bat` uses (or doesn't use) :ref:`make-mode <make_mode>`.
   Default is ``use``, which generates a more concise :file:`Makefile/make.bat`.

   .. versionchanged:: 1.5
      make-mode is default.

   .. versionchanged:: 7.3
      Support for disabling the make-mode will be removed in Sphinx 8.

   .. versionremoved:: 8.0
      The :option:`!--no-use-make-mode` option.
      The :option:`!--use-make-mode` now has no effect.

.. option:: --makefile, --no-makefile

   Create (or not create) makefile.

.. option:: --batchfile, --no-batchfile

   Create (or not create) batchfile

.. rubric:: Project templating

.. versionadded:: 1.5
   Project templating options for sphinx-quickstart

.. option:: -t, --templatedir=TEMPLATEDIR

   Template directory for template files.  You can modify the templates of
   sphinx project files generated by quickstart.  Following Jinja2 template
   files are allowed:

   * ``root_doc.rst.jinja``
   * ``conf.py.jinja``
   * ``Makefile.jinja``
   * ``Makefile.new.jinja``
   * ``make.bat.jinja``
   * ``make.bat.new.jinja``

   In detail, please refer the system template files Sphinx provides.
   (``sphinx/templates/quickstart``)

.. option:: -d NAME=VALUE

   Define a template variable

See also
--------

:manpage:`sphinx-build(1)`

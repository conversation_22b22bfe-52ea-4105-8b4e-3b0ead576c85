sphinx-apidoc
=============

Synopsis
--------

**sphinx-apidoc** [*OPTIONS*] -o <*OUTPUT_PATH*> <*MODULE_PATH*>
[*EXCLUDE_PATTERN* ...]

Description
-----------

:program:`sphinx-apidoc` is a tool for automatic generation of Sphinx sources
that, using the :py:mod:`~sphinx.ext.autodoc` extension, document a whole
package in the style of other automatic API documentation tools.

*MODULE_PATH* is the path to a Python package to document, and *OUTPUT_PATH* is
the directory where the generated sources are placed. Any *EXCLUDE_PATTERN*\s
given are `fnmatch-style`_ file and/or directory patterns that will be excluded
from generation.

.. _fnmatch-style: https://docs.python.org/3/library/fnmatch.html

.. warning::

   ``sphinx-apidoc`` generates source files that use :mod:`sphinx.ext.autodoc`
   to document all found modules.  If any modules have side effects on import,
   these will be executed by ``autodoc`` when ``sphinx-build`` is run.

   If you document scripts (as opposed to library modules), make sure their main
   routine is protected by a ``if __name__ == '__main__'`` condition.

Options
-------

.. program:: sphinx-apidoc

.. option:: -o <OUTPUT_PATH>

   Directory to place the output files. If it does not exist, it is created.

.. option:: -q

   Do not output anything on standard output, only write warnings and errors to
   standard error.

.. option:: -f, --force

   Force overwriting of any existing generated files.

.. option:: -l, --follow-links

   Follow symbolic links. Defaults to ``False``.

.. option:: -n, --dry-run

   Do not create or remove any files.

.. option:: -s <suffix>

   Suffix for the source files generated. Defaults to ``rst``.

.. option:: -d <MAXDEPTH>

   Maximum depth for the generated table of contents file. Defaults to ``4``.

.. option:: --tocfile

   Filename for a table of contents file. Defaults to ``modules``.

.. option:: -T, --no-toc

   Do not create a table of contents file. Ignored when :option:`--full` is
   provided.

.. option:: --remove-old

   Remove existing files in the output directory
   that are not created anymore.
   Not compatible with :option:`--full`.

.. option:: -F, --full

   Generate a full Sphinx project (``conf.py``, ``Makefile`` etc.) using
   the same mechanism as :program:`sphinx-quickstart`.

.. option:: -e, --separate

   Put documentation for each module on its own page.

   .. versionadded:: 1.2

.. option:: -E, --no-headings

   Do not create headings for the modules/packages. This is useful, for
   example, when docstrings already contain headings.

.. option:: -P, --private

   Include "_private" modules.

   .. versionadded:: 1.2

.. option:: --implicit-namespaces

   Without this option, :program:`sphinx-apidoc` searches :data:`sys.path`
   for Python packages containing :file:`__init__.py` files,
   or single-file Python modules.

   This option instead uses :pep:`420` implicit namespaces that allow
   layouts paths such as ``foo/bar/module.py`` or ``foo/bar/baz/__init__.py``
   (note that ``bar`` and ``foo`` are namespaces, not modules).

.. option:: -M, --module-first

   Put module documentation before submodule documentation.

These options are used when :option:`--full` is specified:

.. option:: -a

   Append module_path to sys.path.

.. option:: -H <project>

   Sets the project name to put in generated files (see :confval:`project`).

.. option:: -A <author>

   Sets the author name(s) to put in generated files (see
   :confval:`copyright`).

.. option:: -V <version>

   Sets the project version to put in generated files (see :confval:`version`).

.. option:: -R <release>

   Sets the project release to put in generated files (see :confval:`release`).

.. rubric:: Project templating

.. versionadded:: 2.2
   Project templating options for sphinx-apidoc

.. option:: -t, --templatedir=TEMPLATEDIR

   Template directory for template files.  You can modify the templates of
   sphinx project files generated by apidoc.  Following Jinja2 template
   files are allowed:

   * ``module.rst.jinja``
   * ``package.rst.jinja``
   * ``toc.rst.jinja``
   * ``root_doc.rst.jinja``
   * ``conf.py.jinja``
   * ``Makefile.jinja``
   * ``Makefile.new.jinja``
   * ``make.bat.jinja``
   * ``make.bat.new.jinja``

   In detail, please refer the system template files Sphinx provides.
   (``sphinx/templates/apidoc`` and ``sphinx/templates/quickstart``)

Environment
-----------

.. envvar:: SPHINX_APIDOC_OPTIONS

   A comma-separated list of option to append to generated ``automodule``
   directives. Defaults to ``members,undoc-members,show-inheritance``.

See also
--------

:manpage:`sphinx-build(1)`, :manpage:`sphinx-autogen(1)`

.. _fnmatch: https://docs.python.org/3/library/fnmatch.html

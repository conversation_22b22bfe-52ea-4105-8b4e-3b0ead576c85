.. _extending-sphinx:

Extending Sphinx
================

This guide is aimed at giving a quick introduction for those wishing to
develop their own extensions for Sphinx.
Sphinx possesses significant extensibility capabilities
including the ability to hook into almost every point of the build process.
If you simply wish to use Sphinx with existing extensions,
refer to :doc:`/usage/index`.
For a more detailed discussion of the extension interface see :doc:`/extdev/index`.

.. toctree::
   :maxdepth: 2

   tutorials/index
   howtos/index
   html_themes/index

.. _writing-builders:

Builder API
===========

.. currentmodule:: sphinx.builders

.. class:: Builder

   This is the base class for all builders.

   It follows this basic workflow:

   .. graphviz:: /_static/diagrams/sphinx_build_flow.dot
      :caption: Call graph for the standard Sphinx build workflow

   .. rubric:: Overridable Attributes

   These class attributes should be set on builder sub-classes:

   .. autoattribute:: name
   .. autoattribute:: format
   .. autoattribute:: epilog
   .. autoattribute:: allow_parallel
   .. autoattribute:: supported_image_types
   .. autoattribute:: supported_remote_images
   .. autoattribute:: supported_data_uri_images
   .. autoattribute:: default_translator_class

   .. rubric:: Core Methods

   These methods define the core build workflow and must not be overridden:

   .. automethod:: build_all
   .. automethod:: build_specific
   .. automethod:: build_update
   .. automethod:: build
   .. automethod:: read
   .. automethod:: read_doc
   .. automethod:: write_doctree
   .. automethod:: write

   .. rubric:: Abstract Methods

   These must be implemented in builder sub-classes:

   .. automethod:: get_outdated_docs
   .. automethod:: write_doc
   .. automethod:: get_target_uri

   .. rubric:: Overridable Methods

   These methods can be overridden in builder sub-classes:

   .. automethod:: init
   .. automethod:: write_documents
   .. automethod:: prepare_writing
   .. automethod:: copy_assets
   .. automethod:: get_relative_uri
   .. automethod:: finish

   .. rubric:: Attributes

   Attributes that are callable from the builder instance:

   .. attribute:: events

      An :class:`.EventManager` object.

   .. rubric:: Overridable Attributes (extensions)

   Builder sub-classes can set these attributes to support built-in extensions:

   .. attribute:: supported_linkcode
      :type: str

      By default, the :mod:`linkcode <sphinx.ext.linkcode>` extension will
      only inject references for an ``html`` builder.
      The ``supported_linkcode`` class attribute can be defined in a
      non-HTML builder to support managing references generated by linkcode.
      The expected value for this attribute is an expression
      which is compatible with :rst:dir:`only`.

      For example, if a builder was named ``custom-builder``,
      the following can be used:

      .. code-block:: python

         class CustomBuilder(Builder):
             supported_linkcode = 'custom-builder'
             ...

.. _rst-index:

================
reStructuredText
================

reStructuredText (reST) is the default plaintext markup language used by both
Docutils and Sphinx. Docutils provides the basic reStructuredText syntax, while
Sphinx extends this to support additional functionality.

The below guides go through the most important aspects of reStructuredText.
For the authoritative reference, refer to the `docutils documentation`__.

__ https://docutils.sourceforge.io/rst.html

.. toctree::
   :maxdepth: 2

   basics
   roles
   directives
   field-lists

.. toctree::
   :hidden:

   domains

==============
MOVED: Domains
==============

MOVED: Basic Markup
-------------------

.. raw:: html

   <span id="basic-markup">
   <span id="basic-domain-markup">
   <span id="directive-default-domain">
   <span id="cross-referencing-syntax">

See :doc:`/usage/domains/index`.


MOVED: Python Domain
--------------------

.. raw:: html

   <span id="the-python-domain">
   <span id="python-domain">
   <span id="directive-py-module">
   <span id="directive-option-py-module-platform">
   <span id="directive-option-py-module-synopsis">
   <span id="directive-option-py-module-deprecated">
   <span id="directive-py-currentmodule">
   <span id="directive-py-function">
   <span id="directive-option-py-function-async">
   <span id="directive-option-py-function-canonical">
   <span id="directive-option-py-function-single-line-parameter-list">
   <span id="directive-option-py-function-single-line-type-parameter-list">
   <span id="directive-py-data">
   <span id="directive-option-py-data-type">
   <span id="directive-option-py-data-value">
   <span id="directive-option-py-data-canonical">
   <span id="directive-py-exception">
   <span id="index-0">
   <span id="directive-option-py-exception-final">
   <span id="directive-option-py-exception-single-line-parameter-list">
   <span id="directive-option-py-exception-single-line-type-parameter-list">
   <span id="directive-py-class">
   <span id="index-1">
   <span id="directive-option-py-class-canonical">
   <span id="directive-option-py-class-final">
   <span id="directive-option-py-class-single-line-parameter-list">
   <span id="directive-option-py-class-single-line-type-parameter-list">
   <span id="directive-py-attribute">
   <span id="directive-option-py-attribute-type">
   <span id="directive-option-py-attribute-value">
   <span id="directive-option-py-attribute-canonical">
   <span id="directive-py-property">
   <span id="directive-option-py-property-abstractmethod">
   <span id="directive-option-py-property-classmethod">
   <span id="directive-option-py-property-type">
   <span id="directive-py-method">
   <span id="directive-option-py-method-abstractmethod">
   <span id="directive-option-py-method-async">
   <span id="directive-option-py-method-canonical">
   <span id="directive-option-py-method-classmethod">
   <span id="directive-option-py-method-final">
   <span id="directive-option-py-method-single-line-parameter-list">
   <span id="directive-option-py-method-single-line-type-parameter-list">
   <span id="directive-option-py-method-staticmethod">
   <span id="directive-py-staticmethod">
   <span id="directive-py-classmethod">
   <span id="directive-py-decorator">
   <span id="directive-option-py-decorator-single-line-parameter-list">
   <span id="directive-option-py-decorator-single-line-type-parameter-list">
   <span id="directive-py-decoratormethod">
   <span id="python-signatures">
   <span id="signatures">
   <span id="index-2">
   <span id="index-3">
   <span id="info-field-lists">
   <span id="id1">
   <span id="cross-referencing-python-objects">
   <span id="python-roles">
   <span id="role-py-mod">
   <span id="role-py-func">
   <span id="role-py-data">
   <span id="role-py-const">
   <span id="role-py-class">
   <span id="role-py-meth">
   <span id="role-py-attr">
   <span id="role-py-exc">
   <span id="role-py-obj">

See :doc:`/usage/domains/python`.


MOVED: C Domain
---------------

.. raw:: html

   <span id="the-c-domain">
   <span id="c-domain">
   <span id="directive-c-member">
   <span id="directive-c-var">
   <span id="directive-c-function">
   <span id="directive-option-c-function-single-line-parameter-list">
   <span id="directive-c-macro">
   <span id="directive-option-c-macro-single-line-parameter-list">
   <span id="directive-c-struct">
   <span id="directive-c-union">
   <span id="directive-c-enum">
   <span id="directive-c-enumerator">
   <span id="directive-c-type">
   <span id="cross-referencing-c-constructs">
   <span id="c-roles">
   <span id="role-c-member">
   <span id="role-c-data">
   <span id="role-c-var">
   <span id="role-c-func">
   <span id="role-c-macro">
   <span id="role-c-struct">
   <span id="role-c-union">
   <span id="role-c-enum">
   <span id="role-c-enumerator">
   <span id="role-c-type">
   <span id="anonymous-entities">
   <span id="aliasing-declarations">
   <span id="directive-c-alias">
   <span id="directive-option-c-alias-maxdepth">
   <span id="directive-option-c-alias-noroot">
   <span id="inline-expressions-and-types">
   <span id="role-c-expr">
   <span id="role-c-texpr">
   <span id="namespacing">
   <span id="directive-c-namespace">
   <span id="directive-c-namespace-push">
   <span id="directive-c-namespace-pop">
   <span id="configuration-variables">

See :doc:`/usage/domains/c`.


MOVED: C++ Domain
-----------------

.. raw:: html

   <span id="cpp-domain">
   <span id="id2">
   <span id="directives-for-declaring-entities">
   <span id="directive-cpp-class">
   <span id="directive-cpp-struct">
   <span id="directive-cpp-function">
   <span id="directive-option-cpp-function-single-line-parameter-list">
   <span id="directive-cpp-member">
   <span id="directive-cpp-var">
   <span id="directive-cpp-type">
   <span id="directive-cpp-enum">
   <span id="directive-cpp-enum-struct">
   <span id="directive-cpp-enum-class">
   <span id="directive-cpp-enumerator">
   <span id="directive-cpp-union">
   <span id="directive-cpp-concept">
   <span id="options">
   <span id="id3">
   <span id="id4">
   <span id="directive-cpp-alias">
   <span id="directive-option-cpp-alias-maxdepth">
   <span id="directive-option-cpp-alias-noroot">
   <span id="constrained-templates">
   <span id="placeholders">
   <span id="template-introductions">
   <span id="id5">
   <span id="role-cpp-expr">
   <span id="role-cpp-texpr">
   <span id="id6">
   <span id="directive-cpp-namespace">
   <span id="directive-cpp-namespace-push">
   <span id="directive-cpp-namespace-pop">
   <span id="id7">
   <span id="cross-referencing">
   <span id="cpp-roles">
   <span id="role-cpp-any">
   <span id="role-cpp-class">
   <span id="role-cpp-struct">
   <span id="role-cpp-func">
   <span id="role-cpp-member">
   <span id="role-cpp-var">
   <span id="role-cpp-type">
   <span id="role-cpp-concept">
   <span id="role-cpp-enum">
   <span id="role-cpp-enumerator">
   <span id="declarations-without-template-parameters-and-template-arguments">
   <span id="overloaded-member-functions">
   <span id="templated-declarations">
   <span id="full-template-specialisations">
   <span id="partial-template-specialisations">
   <span id="id8">

See :doc:`/usage/domains/cpp`.


MOVED: Standard Domain
----------------------

.. raw:: html

   <span id="the-standard-domain">
   <span id="domains-std">
   <span id="directive-option">
   <span id="directive-envvar">
   <span id="directive-program">
   <span id="directive-describe">
   <span id="directive-object">

See :doc:`/usage/domains/standard`.


MOVED: JavaScript Domain
------------------------

.. raw:: html

   <span id="the-javascript-domain">
   <span id="directive-js-module">
   <span id="directive-js-function">
   <span id="directive-option-js-function-single-line-parameter-list">
   <span id="directive-js-method">
   <span id="directive-option-js-method-single-line-parameter-list">
   <span id="directive-js-class">
   <span id="directive-option-js-class-single-line-parameter-list">
   <span id="directive-js-data">
   <span id="directive-js-attribute">
   <span id="js-roles">
   <span id="role-js-mod">
   <span id="role-js-func">
   <span id="role-js-meth">
   <span id="role-js-class">
   <span id="role-js-data">
   <span id="role-js-attr">

See :doc:`/usage/domains/javascript`.


MOVED: reStructuredText Domain
------------------------------

.. raw:: html

   <span id="the-restructuredtext-domain">
   <span id="directive-rst-directive">
   <span id="directive-rst-directive-option">
   <span id="directive-option-rst-directive-option-type">
   <span id="directive-rst-role">
   <span id="rst-roles">
   <span id="role-rst-dir">
   <span id="role-rst-role">

See :doc:`/usage/domains/restructuredtext`.


MOVED: Math Domain
------------------

.. raw:: html

   <span id="the-math-domain">
   <span id="math-domain">
   <span id="role-math-numref">

See :doc:`/usage/domains/mathematics`.

MOVED: More domains
-------------------

.. raw:: html

   <span id="more-domains">

See :doc:`/usage/domains/index`.

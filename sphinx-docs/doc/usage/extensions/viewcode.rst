:mod:`sphinx.ext.viewcode` -- Add links to highlighted source code
==================================================================

.. module:: sphinx.ext.viewcode
   :synopsis: Add links to a highlighted version of the source code.
.. moduleauthor:: Georg Brandl

.. versionadded:: 1.0

.. role:: code-py(code)
   :language: Python

This extension looks at your Python object descriptions (``.. class::``, ``..
function::`` etc.) and tries to find the source files where the objects are
contained.  When found, a separate HTML page will be output for each module with
a highlighted version of the source code, and a link will be added to all object
descriptions that leads to the source code of the described object.  A link back
from the source to the description will also be inserted.

.. warning::

   Basically, ``viewcode`` extension will import the modules being linked to.
   If any modules have side effects on import, these will be executed when
   ``sphinx-build`` is run.

   If you document scripts (as opposed to library modules), make sure their
   main routine is protected by a ``if __name__ == '__main__'`` condition.

   In addition, if you don't want to import the modules by ``viewcode``,
   you can tell the location of the location of source code to ``viewcode``
   using the :event:`viewcode-find-source` event.

   If :confval:`viewcode_follow_imported_members` is enabled,
   you will also need to resolve imported attributes
   using the :event:`viewcode-follow-imported` event.

This extension works only on HTML related builders like ``html``,
``applehelp``, ``devhelp``, ``htmlhelp``, ``qthelp`` and so on except
``singlehtml``. By default ``epub`` builder doesn't
support this extension (see :confval:`viewcode_enable_epub`).

Configuration
-------------

.. confval:: viewcode_follow_imported_members
   :type: :code-py:`bool`
   :default: :code-py:`True`

   If this is ``True``, viewcode extension will emit
   :event:`viewcode-follow-imported` event to resolve the name of the module
   by other extensions.

   .. versionadded:: 1.3

   .. versionchanged:: 1.8
      Renamed from ``viewcode_import`` to ``viewcode_follow_imported_members``.

.. confval:: viewcode_enable_epub
   :type: :code-py:`bool`
   :default: :code-py:`False`

   If this is ``True``, viewcode extension is also enabled even if you use
   epub builders. This extension generates pages outside toctree, but this
   is not preferred as epub format.

   Until 1.4.x, this extension is always enabled. If you want to generate
   epub as same as 1.4.x, you should set ``True``, but epub format checker's
   score becomes worse.

   .. versionadded:: 1.5

   .. warning::

      Not all epub readers support pages generated by viewcode extension.
      These readers ignore links to pages are not under toctree.

      Some reader's rendering result are corrupted and
      `epubcheck <https://github.com/IDPF/epubcheck>`_'s score
      becomes worse even if the reader supports.

.. confval:: viewcode_line_numbers
   :type: :code-py:`bool`
   :default: :code-py:`False`

   If set to ``True``, inline line numbers will be added to the highlighted code.

   .. versionadded:: 7.2

.. event:: viewcode-find-source (app, modname)

   .. versionadded:: 1.8

   Find the source code for a module.
   An event handler for this event should return
   a tuple of the source code itself and a dictionary of tags.
   The dictionary maps the name of a class, function, attribute, etc
   to a tuple of its type, the start line number, and the end line number.
   The type should be one of "class", "def", or "other".

   :param app: The Sphinx application object.
   :param modname: The name of the module to find source code for.

.. event:: viewcode-follow-imported (app, modname, attribute)

   .. versionadded:: 1.8

   Find the name of the original module for an attribute.

   :param app: The Sphinx application object.
   :param modname: The name of the module that the attribute belongs to.
   :param attribute: The name of the member to follow.

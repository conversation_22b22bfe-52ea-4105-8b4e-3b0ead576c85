:root {
  --primary-color: #0060df;
  --primary-hover-color: #0250bb;
  --secondary-color: #38383d;
  --secondary-hover-color: #2a2a2e;
  --success-color: #12bc00;
  --error-color: #d70022;
  --text-color: #0c0c0d;
  --background-color: #ffffff;
  --border-color: #d7d7db;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #0a84ff;
    --primary-hover-color: #0074e8;
    --secondary-color: #5a5a5e;
    --secondary-hover-color: #4a4a4f;
    --text-color: #f9f9fa;
    --background-color: #2a2a2e;
    --border-color: #4a4a4f;
  }
  
  body.dark-mode {
    --text-color: #f9f9fa;
    --background-color: #2a2a2e;
    --border-color: #4a4a4f;
  }
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Sego<PERSON>", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background-color: var(--background-color);
  width: 320px;
}

.container {
  padding: 16px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}

h1 {
  font-size: 18px;
  margin: 0;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button.primary {
  background-color: var(--primary-color);
  color: white;
}

.button.primary:hover {
  background-color: var(--primary-hover-color);
}

.button.secondary {
  background-color: var(--secondary-color);
  color: white;
}

.button.secondary:hover {
  background-color: var(--secondary-hover-color);
}

.connection-status {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  margin-top: 8px;
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-icon.success {
  background-color: var(--success-color);
}

.status-icon.error {
  background-color: var(--error-color);
}

.status-text {
  font-size: 14px;
}

.hidden {
  display: none;
}

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="../icons/bahtbrowse-48.png" alt="BahtBrowse Logo" class="logo">
      <h1>BahtBrowse Preferences</h1>
    </div>
    
    <form id="optionsForm">
      <div class="form-group">
        <label for="bahtbrowseUrl">BahtBrowse URL:</label>
        <input type="url" id="bahtbrowseUrl" name="bahtbrowseUrl" placeholder="http://localhost:8001">
        <p class="help-text">The URL of your BahtBrowse instance</p>
      </div>
      
      <div class="form-group">
        <label for="connectionTimeout">Connection Timeout (ms):</label>
        <input type="number" id="connectionTimeout" name="connectionTimeout" min="1000" max="30000" step="1000">
        <p class="help-text">Timeout for connection tests in milliseconds</p>
      </div>
      
      <div class="form-group checkbox">
        <input type="checkbox" id="showNotifications" name="showNotifications">
        <label for="showNotifications">Show notifications when redirecting</label>
      </div>
      
      <div class="form-group checkbox">
        <input type="checkbox" id="darkMode" name="darkMode">
        <label for="darkMode">Use dark mode (overrides system preference)</label>
      </div>
      
      <div class="button-group">
        <button type="button" id="testConnection" class="button secondary">Test Connection</button>
        <button type="submit" id="saveOptions" class="button primary">Save</button>
      </div>
    </form>
    
    <div id="connectionStatus" class="connection-status hidden">
      <span id="statusIcon" class="status-icon"></span>
      <span id="statusText" class="status-text"></span>
    </div>
    
    <div id="saveStatus" class="save-status hidden">
      <span class="status-icon success"></span>
      <span class="status-text">Options saved!</span>
    </div>
  </div>
  <script src="options.js"></script>
</body>
</html>

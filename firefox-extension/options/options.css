:root {
  --primary-color: #0060df;
  --primary-hover-color: #0250bb;
  --secondary-color: #38383d;
  --secondary-hover-color: #2a2a2e;
  --success-color: #12bc00;
  --error-color: #d70022;
  --text-color: #0c0c0d;
  --background-color: #ffffff;
  --border-color: #d7d7db;
  --input-background: #f9f9fa;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #0a84ff;
    --primary-hover-color: #0074e8;
    --secondary-color: #5a5a5e;
    --secondary-hover-color: #4a4a4f;
    --text-color: #f9f9fa;
    --background-color: #2a2a2e;
    --border-color: #4a4a4f;
    --input-background: #1c1c1e;
  }
}

body.dark-mode {
  --text-color: #f9f9fa;
  --background-color: #2a2a2e;
  --border-color: #4a4a4f;
  --input-background: #1c1c1e;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  margin: 0;
  padding: 0;
  color: var(--text-color);
  background-color: var(--background-color);
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

h1 {
  font-size: 24px;
  margin: 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group.checkbox {
  display: flex;
  align-items: center;
}

.form-group.checkbox label {
  margin-bottom: 0;
  margin-left: 8px;
}

.form-group input[type="url"],
.form-group input[type="number"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-background);
  color: var(--text-color);
  font-size: 14px;
}

.help-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--secondary-color);
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button.primary {
  background-color: var(--primary-color);
  color: white;
}

.button.primary:hover {
  background-color: var(--primary-hover-color);
}

.button.secondary {
  background-color: var(--secondary-color);
  color: white;
}

.button.secondary:hover {
  background-color: var(--secondary-hover-color);
}

.connection-status,
.save-status {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  margin-top: 16px;
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-icon.success {
  background-color: var(--success-color);
}

.status-icon.error {
  background-color: var(--error-color);
}

.status-text {
  font-size: 14px;
}

.hidden {
  display: none;
}

# BahtBrowse Redirector Firefox Extension

This Firefox extension allows you to redirect URLs to the BahtBrowse Remote Browser Isolation (RBI) service.

## Features

- Redirect the current URL to BahtBrowse with a single click
- Customizable BahtBrowse server URL
- Dark mode support (follows system preference or can be manually set)
- Connection testing to verify BahtBrowse server availability
- Notification support when redirecting URLs

## Installation

### Temporary Installation (for Development)

1. Open Firefox
2. Navigate to `about:debugging`
3. Click "This Firefox"
4. Click "Load Temporary Add-on..."
5. Select any file in the extension directory (e.g., `manifest.json`)

### Permanent Installation

1. Package the extension using the provided script:
   ```
   ./package_extension.sh
   ```
2. Open Firefox
3. Navigate to `about:addons`
4. Click the gear icon and select "Install Add-on From File..."
5. Select the generated `bahtbrowse_redirector.xpi` file

## Usage

1. Click the BahtBrowse icon in the Firefox toolbar to redirect the current URL to BahtBrowse
2. Right-click the icon and select "Manage Extension" to access settings
3. Configure the BahtBrowse server URL in the settings page

## Configuration

The following settings can be configured:

- **BahtBrowse URL**: The URL of your BahtBrowse instance (default: `http://localhost:8001`)
- **Connection Timeout**: Timeout for connection tests in milliseconds (default: 5000)
- **Show Notifications**: Enable/disable notifications when redirecting URLs (default: enabled)
- **Dark Mode**: Override system preference for dark mode (default: follows system preference)

## Development

### Directory Structure

```
firefox-extension/
├── background.js        # Background script for handling browser events
├── icons/               # Extension icons
├── manifest.json        # Extension manifest file
├── options/             # Settings page
│   ├── options.css
│   ├── options.html
│   └── options.js
└── popup/               # Popup UI when clicking the extension icon
    ├── popup.css
    ├── popup.html
    └── popup.js
```

### Building the Extension

Run the provided script to package the extension:

```
./package_extension.sh
```

This will create a `bahtbrowse_redirector.xpi` file that can be installed in Firefox.

## License

This extension is licensed under the MIT License.

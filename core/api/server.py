#!/usr/bin/env python3
"""
Simple API server for the benchmark system.
"""

import logging
import uuid
from datetime import datetime

import docker
import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="bahtBrowse Benchmark API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Docker client
docker_client = docker.from_env()

# Container tracking
active_containers = {}

# Container port mapping
container_ports = {
    "firefox-ubuntu": 8001,
    "firefox-alpine": 8002,
    "chromium-ubuntu": 8003,
    "chromium-alpine": 8004,
}


class ContainerRequest(BaseModel):
    """Container request model."""

    browser_type: str
    os_type: str
    session_id: str


class ContainerResponse(BaseModel):
    """Container response model."""

    container_id: str
    browser_type: str
    os_type: str
    session_id: str
    url: str
    status: str
    created_at: str


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "bahtBrowse Benchmark API"}


@app.get("/containers")
async def list_containers():
    """List all active containers."""
    return {"containers": list(active_containers.values())}


@app.post("/containers/request")
async def request_container(request: ContainerRequest):
    """Request a container for a browser session."""
    browser_type = request.browser_type
    os_type = request.os_type
    session_id = request.session_id

    # Validate browser type
    if browser_type not in ["firefox", "chromium"]:
        raise HTTPException(status_code=400, detail="Invalid browser type")

    # Validate OS type
    if os_type not in ["ubuntu", "alpine"]:
        raise HTTPException(status_code=400, detail="Invalid OS type")

    # Generate container name for logging purposes
    logger.info(f"Creating container for {browser_type}-{os_type}-{session_id}")

    # Get port for this browser-OS combination
    port_key = f"{browser_type}-{os_type}"
    if port_key not in container_ports:
        raise HTTPException(status_code=400, detail="Invalid browser-OS combination")

    port = container_ports[port_key]

    # Create response
    response = {
        "container_id": str(uuid.uuid4()),
        "browser_type": browser_type,
        "os_type": os_type,
        "session_id": session_id,
        "url": f"http://localhost:{port}",
        "status": "ready",
        "created_at": datetime.now().isoformat(),
    }

    # Store container info
    active_containers[session_id] = response

    return response


@app.delete("/containers/{session_id}")
async def release_container(session_id: str):
    """Release a container."""
    if session_id not in active_containers:
        raise HTTPException(status_code=404, detail="Container not found")

    # Remove container from tracking
    container_info = active_containers.pop(session_id)

    return {"message": "Container released", "container": container_info}


if __name__ == "__main__":
    uvicorn.run("core.api.server:app", host="0.0.0.0", port=5000, reload=True)

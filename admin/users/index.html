
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>BahtBrowse Admin - User Management</title>
          <link rel="stylesheet" href="users.css">
        </head>
        <body>
          <div id="admin-container">
            <header id="admin-header">
              <img src="../assets/logo.png" alt="BahtBrowse Logo">
              <h1>BahtBrowse Admin</h1>
              <div class="user-info">
                <span>Welcome, Admin</span>
                <button id="logout-button">Logout</button>
              </div>
            </header>
            <div id="admin-content">
              <nav id="admin-sidebar">
                <ul>
                  <li><a href="#" id="dashboard-link">Dashboard</a></li>
                  <li><a href="#" class="active" id="user-management-link">User Management</a></li>
                  <li><a href="#" id="session-management-link">Session Management</a></li>
                  <li><a href="#" id="system-settings-link">System Settings</a></li>
                  <li><a href="#" id="logs-link">Logs</a></li>
                  <li><a href="#" id="system-status-link">System Status</a></li>
                </ul>
              </nav>
              <main id="user-management-content">
                <h2 id="user-management-title">User Management</h2>
                <div class="actions-bar">
                  <button id="add-user-button">Add User</button>
                  <div class="search-box">
                    <input type="text" id="user-search" placeholder="Search users...">
                    <button id="user-search-button">Search</button>
                  </div>
                  <div class="filter-box">
                    <label for="user-status-filter">Status:</label>
                    <select id="user-status-filter">
                      <option value="all">All</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                    <label for="user-role-filter">Role:</label>
                    <select id="user-role-filter">
                      <option value="all">All</option>
                      <option value="admin">Admin</option>
                      <option value="user">User</option>
                    </select>
                  </div>
                </div>
                <table id="user-table" class="data-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Username</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Last Login</th>
                      <th>Created At</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>1</td>
                      <td>admin</td>
                      <td><EMAIL></td>
                      <td>Admin</td>
                      <td>Active</td>
                      <td>2023-05-02 10:30:00</td>
                      <td>2023-01-01 00:00:00</td>
                      <td>
                        <button class="edit-user-button">Edit</button>
                        <button class="delete-user-button">Delete</button>
                      </td>
                    </tr>
                    <tr>
                      <td>2</td>
                      <td>user1</td>
                      <td><EMAIL></td>
                      <td>User</td>
                      <td>Active</td>
                      <td>2023-05-01 15:45:00</td>
                      <td>2023-01-15 00:00:00</td>
                      <td>
                        <button class="edit-user-button">Edit</button>
                        <button class="delete-user-button">Delete</button>
                      </td>
                    </tr>
                    <tr>
                      <td>3</td>
                      <td>user2</td>
                      <td><EMAIL></td>
                      <td>User</td>
                      <td>Active</td>
                      <td>2023-04-30 09:20:00</td>
                      <td>2023-02-01 00:00:00</td>
                      <td>
                        <button class="edit-user-button">Edit</button>
                        <button class="delete-user-button">Delete</button>
                      </td>
                    </tr>
                    <tr>
                      <td>4</td>
                      <td>user3</td>
                      <td><EMAIL></td>
                      <td>User</td>
                      <td>Inactive</td>
                      <td>2023-04-15 14:10:00</td>
                      <td>2023-02-15 00:00:00</td>
                      <td>
                        <button class="edit-user-button">Edit</button>
                        <button class="delete-user-button">Delete</button>
                      </td>
                    </tr>
                    <tr>
                      <td>5</td>
                      <td>user4</td>
                      <td><EMAIL></td>
                      <td>User</td>
                      <td>Active</td>
                      <td>2023-05-02 08:30:00</td>
                      <td>2023-03-01 00:00:00</td>
                      <td>
                        <button class="edit-user-button">Edit</button>
                        <button class="delete-user-button">Delete</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div class="pagination">
                  <button id="prev-page">Previous</button>
                  <span>Page 1 of 10</span>
                  <button id="next-page">Next</button>
                </div>
              </main>
            </div>
          </div>
          <div id="add-user-dialog" class="dialog">
            <div class="dialog-content">
              <h2>Add User</h2>
              <form id="add-user-form">
                <div class="form-group">
                  <label for="add-user-username">Username</label>
                  <input type="text" id="add-user-username" required>
                </div>
                <div class="form-group">
                  <label for="add-user-email">Email</label>
                  <input type="email" id="add-user-email" required>
                </div>
                <div class="form-group">
                  <label for="add-user-password">Password</label>
                  <input type="password" id="add-user-password" required>
                </div>
                <div class="form-group">
                  <label for="add-user-confirm-password">Confirm Password</label>
                  <input type="password" id="add-user-confirm-password" required>
                </div>
                <div class="form-group">
                  <label for="add-user-role">Role</label>
                  <select id="add-user-role">
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
                <div class="dialog-actions">
                  <button type="button" id="add-user-submit">Add User</button>
                  <button type="button" id="add-user-close">Cancel</button>
                </div>
              </form>
              <div id="add-user-success" class="success-message">User added successfully!</div>
              <div id="add-user-error" class="error-message">Failed to add user!</div>
            </div>
          </div>
          <div id="edit-user-dialog" class="dialog">
            <div class="dialog-content">
              <h2>Edit User</h2>
              <form id="edit-user-form">
                <div class="form-group">
                  <label for="edit-user-username">Username</label>
                  <input type="text" id="edit-user-username" required>
                </div>
                <div class="form-group">
                  <label for="edit-user-email">Email</label>
                  <input type="email" id="edit-user-email" required>
                </div>
                <div class="form-group">
                  <label for="edit-user-role">Role</label>
                  <select id="edit-user-role">
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="edit-user-status">Status</label>
                  <select id="edit-user-status">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div class="dialog-actions">
                  <button type="button" id="edit-user-submit">Update User</button>
                  <button type="button" id="edit-user-close">Cancel</button>
                </div>
              </form>
              <div id="edit-user-success" class="success-message">User updated successfully!</div>
              <div id="edit-user-error" class="error-message">Failed to update user!</div>
            </div>
          </div>
          <div id="delete-user-dialog" class="dialog">
            <div class="dialog-content">
              <h2>Delete User</h2>
              <p>Are you sure you want to delete this user? This action cannot be undone.</p>
              <div class="dialog-actions">
                <button id="delete-user-confirm">Delete</button>
                <button id="delete-user-cancel">Cancel</button>
              </div>
              <div id="delete-user-success" class="success-message">User deleted successfully!</div>
              <div id="delete-user-error" class="error-message">Failed to delete user!</div>
            </div>
          </div>
          <script src="users.js"></script>
        </body>
      </html>
    
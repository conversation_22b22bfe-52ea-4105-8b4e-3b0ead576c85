#!/usr/bin/env python
"""
Simple runner for Celer<PERSON> Flower.
"""

import os
import sys
import subprocess

# Get broker URL from environment
broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://redis:6379/0')

# Run Flower directly using subprocess
cmd = [
    "celery",
    "-A", "docker_queue.celery_app",
    "flower",
    "--port=5555",
    "--broker_api=" + os.environ.get('BROKER_API', 'redis://redis:6379/0'),
    "--address=0.0.0.0",
    "--persistent=True",
    "--db=flower",
    "--natural_time=True",
    "--auto_refresh=True"
]

print(f"Running command: {' '.join(cmd)}")
subprocess.run(cmd)

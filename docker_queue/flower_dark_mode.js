/**
 * Dark Mode Toggle for Celery Flower Dashboard
 * 
 * This script adds a dark mode toggle to the Flower dashboard
 * and handles the switching between light and dark modes.
 */

(function() {
  // Create style element for dark mode CSS
  const darkModeStyle = document.createElement('style');
  darkModeStyle.id = 'dark-mode-style';
  document.head.appendChild(darkModeStyle);

  // Create toggle switch element
  const toggleContainer = document.createElement('div');
  toggleContainer.className = 'dark-mode-toggle';
  toggleContainer.innerHTML = `
    <label class="switch">
      <input type="checkbox" id="dark-mode-toggle">
      <span class="slider"></span>
    </label>
    <span class="toggle-label">Dark Mode</span>
  `;
  document.body.appendChild(toggleContainer);

  // Get toggle element
  const darkModeToggle = document.getElementById('dark-mode-toggle');

  // Function to load CSS file
  function loadCSS(url) {
    return fetch(url)
      .then(response => response.text())
      .then(css => {
        darkModeStyle.textContent = css;
      })
      .catch(error => {
        console.error('Error loading dark mode CSS:', error);
      });
  }

  // Function to enable dark mode
  function enableDarkMode() {
    document.body.classList.add('dark-mode');
    loadCSS('/static/flower_dark_theme.css');
    localStorage.setItem('flowerDarkMode', 'enabled');
  }

  // Function to disable dark mode
  function disableDarkMode() {
    document.body.classList.remove('dark-mode');
    darkModeStyle.textContent = '';
    localStorage.setItem('flowerDarkMode', 'disabled');
  }

  // Check for saved preference
  const darkModeSetting = localStorage.getItem('flowerDarkMode');
  
  // Initialize based on saved preference or system preference
  if (darkModeSetting === 'enabled') {
    darkModeToggle.checked = true;
    enableDarkMode();
  } else if (darkModeSetting === 'disabled') {
    darkModeToggle.checked = false;
    disableDarkMode();
  } else {
    // If no saved preference, check system preference
    const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    darkModeToggle.checked = prefersDarkMode;
    if (prefersDarkMode) {
      enableDarkMode();
    } else {
      disableDarkMode();
    }
  }

  // Listen for toggle changes
  darkModeToggle.addEventListener('change', function() {
    if (this.checked) {
      enableDarkMode();
    } else {
      disableDarkMode();
    }
  });

  // Listen for system preference changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
    // Only change if user hasn't set a preference
    if (!localStorage.getItem('flowerDarkMode')) {
      darkModeToggle.checked = e.matches;
      if (e.matches) {
        enableDarkMode();
      } else {
        disableDarkMode();
      }
    }
  });
})();

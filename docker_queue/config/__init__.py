"""
Configuration module for the docker_queue package.
"""

from docker_queue.config.browser_pool_config import (
    BrowserPoolConfig,
    BrowserConfig,
    get_browser_pool_config,
    BROWSER_IMAGES,
    BROWSER_RESOURCE_LIMITS,
    BROWSER_ENV_VARS,
    SUPPORTED_BROWSER_TYPES,
    DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_TOTAL_CONTAINERS,
)

from docker_queue.config.redis_config import (
    REDIS_HOST,
    REDIS_PORT,
    REDIS_DB,
    REDIS_PASSWORD,
    REDIS_KEY_PREFIXES,
)

from docker_queue.config.pool_config import (
    POOL_TARGET_SIZE,
    POOL_MIN_AVAILABLE,
    POOL_MAX_SIZE,
)

from docker_queue.config.monitoring_config import (
    METRICS_RETENTION_DAYS,
    METRICS_COLLECTION_INTERVAL,
    HEALTH_CHECK_INTERVAL,
    USAGE_REPORT_INTERVAL,
    ELK_ENABLED,
    ELASTICSEARCH_HOST,
    ELASTICSEARCH_PORT,
    KIBANA_HOST,
    KIBANA_PORT,
    LOGSTASH_HOST,
    LOGSTASH_PORT,
)

# Docker client configuration
DOCKER_BASE_URL = 'unix://var/run/docker.sock'
DOCKER_API_VERSION = 'auto'
DOCKER_TIMEOUT = 30

# Container configuration
CONTAINER_IMAGE = 'bahtbrowse-firefox-ubuntu:latest'
CONTAINER_CPU_LIMIT = 1.0
CONTAINER_MEMORY_LIMIT = '2g'
CONTAINER_NETWORK = 'bahtbrowse-network'
CONTAINER_IDLE_TIMEOUT = 3600  # 1 hour in seconds

__all__ = [
    "BrowserPoolConfig",
    "BrowserConfig",
    "get_browser_pool_config",
    "DOCKER_BASE_URL",
    "DOCKER_API_VERSION",
    "DOCKER_TIMEOUT",
    "CONTAINER_IMAGE",
    "CONTAINER_CPU_LIMIT",
    "CONTAINER_MEMORY_LIMIT",
    "CONTAINER_NETWORK",
    "CONTAINER_IDLE_TIMEOUT",
    "BROWSER_IMAGES",
    "BROWSER_RESOURCE_LIMITS",
    "BROWSER_ENV_VARS",
    "SUPPORTED_BROWSER_TYPES",
    "DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE",
    "DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE",
    "DEFAULT_MAX_TOTAL_CONTAINERS",
    "REDIS_HOST",
    "REDIS_PORT",
    "REDIS_DB",
    "REDIS_PASSWORD",
    "REDIS_KEY_PREFIXES",
    "POOL_TARGET_SIZE",
    "POOL_MIN_AVAILABLE",
    "POOL_MAX_SIZE",
    "METRICS_RETENTION_DAYS",
    "METRICS_COLLECTION_INTERVAL",
    "HEALTH_CHECK_INTERVAL",
    "USAGE_REPORT_INTERVAL",
    "ELK_ENABLED",
    "ELASTICSEARCH_HOST",
    "ELASTICSEARCH_PORT",
    "KIBANA_HOST",
    "KIBANA_PORT",
    "LOGSTASH_HOST",
    "LOGSTASH_PORT",
]

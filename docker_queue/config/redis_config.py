"""
Redis configuration for the docker_queue package.
"""

import os

# Redis configuration
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# Redis key prefixes
REDIS_KEY_PREFIXES = {
    'container': 'bahtbrowse:container:',
    'user': 'bahtbrowse:user:',
    'request': 'bahtbrowse:request:',
    'metrics': 'bahtbrowse:metrics:',
    'sets': {
        'all_containers': 'bahtbrowse:containers:all',
        'available_containers': 'bahtbrowse:containers:available',
        'assigned_containers': 'bahtbrowse:containers:assigned',
        'recycling_containers': 'bahtbrowse:containers:recycling',
    },
    'queues': {
        'container_requests': 'bahtbrowse:queue:container_requests',
    }
}

__all__ = [
    "REDIS_HOST",
    "REDIS_PORT",
    "REDIS_DB",
    "REDIS_PASSWORD",
    "REDIS_KEY_PREFIXES",
]

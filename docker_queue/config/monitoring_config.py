"""
Monitoring configuration for the docker_queue package.
"""

import os

# Metrics retention period in days
METRICS_RETENTION_DAYS = int(os.environ.get('METRICS_RETENTION_DAYS', 30))

# Monitoring intervals in seconds
METRICS_COLLECTION_INTERVAL = int(os.environ.get('METRICS_COLLECTION_INTERVAL', 60))
HEALTH_CHECK_INTERVAL = int(os.environ.get('HEALTH_CHECK_INTERVAL', 300))
USAGE_REPORT_INTERVAL = int(os.environ.get('USAGE_REPORT_INTERVAL', 86400))

# ELK Stack configuration
ELK_ENABLED = os.environ.get('ELK_ENABLED', 'false').lower() == 'true'
ELASTICSEARCH_HOST = os.environ.get('ELASTICSEARCH_HOST', 'localhost')
ELASTICSEARCH_PORT = int(os.environ.get('ELASTICSEARCH_PORT', 9200))
KIBANA_HOST = os.environ.get('KIBANA_HOST', 'localhost')
KIBANA_PORT = int(os.environ.get('KIBANA_PORT', 5601))
LOGSTASH_HOST = os.environ.get('LOGSTASH_HOST', 'localhost')
LOGSTASH_PORT = int(os.environ.get('LOGSTASH_PORT', 5044))

__all__ = [
    "METRICS_RETENTION_DAYS",
    "METRICS_COLLECTION_INTERVAL",
    "HEALTH_CHECK_INTERVAL",
    "USAGE_REPORT_INTERVAL",
    "ELK_ENABLED",
    "ELASTICSEARCH_HOST",
    "ELASTICSEARCH_PORT",
    "KIBANA_HOST",
    "KIBANA_PORT",
    "LOGSTASH_HOST",
    "LOGSTASH_PORT",
]

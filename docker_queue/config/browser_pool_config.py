"""
Configuration for browser-specific container pool management.
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional


@dataclass
class BrowserConfig:
    """Configuration for a specific browser type."""
    browser_type: str
    os_type: str
    image: str
    resource_limits: Dict[str, str]
    env_vars: Dict[str, str]


@dataclass
class BrowserPoolConfig:
    """Configuration for the browser container pool."""
    min_containers_per_browser_type: Dict[str, int]
    max_containers_per_browser_type: Dict[str, int]
    max_total_containers: int
    container_idle_timeout: int
    container_max_lifetime: int
    scaling_strategy: str
    demand_scaling_config: Dict[str, float]
    browser_configs: Dict[str, BrowserConfig] = field(default_factory=dict)


# Supported browser types
SUPPORTED_BROWSER_TYPES = ['firefox', 'chromium']

# Supported OS types
SUPPORTED_OS_TYPES = ['ubuntu', 'alpine']

# Minimum number of containers to maintain per browser type
DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE = {
    'firefox': 2,
    'chromium': 2
}

# Maximum number of containers allowed per browser type
DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE = {
    'firefox': 10,
    'chromium': 10
}

# Maximum total containers across all browser types
DEFAULT_MAX_TOTAL_CONTAINERS = 20

# Container idle timeout in seconds (3 minutes)
CONTAINER_IDLE_TIMEOUT = 180

# Container maximum lifetime in seconds (8 hours)
CONTAINER_MAX_LIFETIME = 28800

# Scaling strategies
SCALING_STRATEGIES = [
    'balanced',      # Maintain equal numbers of each browser type
    'demand-based',  # Scale based on usage patterns
    'fixed'          # Maintain fixed numbers as specified in config
]

# Default scaling strategy
DEFAULT_SCALING_STRATEGY = 'balanced'

# Demand-based scaling configuration
DEMAND_SCALING_CONFIG = {
    'window_size': 3600,  # Look at usage over the last hour
    'scale_up_threshold': 0.8,  # Scale up when 80% of containers are in use
    'scale_down_threshold': 0.3,  # Scale down when less than 30% of containers are in use
    'min_scale_up': 1,  # Minimum number of containers to add when scaling up
    'max_scale_up': 5,  # Maximum number of containers to add when scaling up
    'scale_down_count': 1,  # Number of containers to remove when scaling down
    'cooldown_period': 300  # Minimum time between scaling operations (5 minutes)
}

# Browser-OS specific container images
BROWSER_IMAGES = {
    'firefox-ubuntu': 'bahtbrowse-firefox-ubuntu:latest',
    'firefox-alpine': 'bahtbrowse-firefox-alpine:latest',
    'chromium-ubuntu': 'bahtbrowse-chromium-ubuntu:latest',
    'chromium-alpine': 'bahtbrowse-chromium-alpine:latest'
}

# Browser-OS specific container resource limits
BROWSER_RESOURCE_LIMITS = {
    'firefox-ubuntu': {
        'cpu': 1.0,
        'memory': '2g'
    },
    'firefox-alpine': {
        'cpu': 0.8,
        'memory': '1.5g'
    },
    'chromium-ubuntu': {
        'cpu': 1.0,
        'memory': '2g'
    },
    'chromium-alpine': {
        'cpu': 0.8,
        'memory': '1.5g'
    }
}

# Browser-OS specific environment variables
BROWSER_ENV_VARS = {
    'firefox-ubuntu': {
        'BROWSER_TYPE': 'firefox',
        'OS_TYPE': 'ubuntu',
        'DISPLAY_WIDTH': '1920',
        'DISPLAY_HEIGHT': '1080'
    },
    'firefox-alpine': {
        'BROWSER_TYPE': 'firefox',
        'OS_TYPE': 'alpine',
        'DISPLAY_WIDTH': '1920',
        'DISPLAY_HEIGHT': '1080'
    },
    'chromium-ubuntu': {
        'BROWSER_TYPE': 'chromium',
        'OS_TYPE': 'ubuntu',
        'DISPLAY_WIDTH': '1920',
        'DISPLAY_HEIGHT': '1080'
    },
    'chromium-alpine': {
        'BROWSER_TYPE': 'chromium',
        'OS_TYPE': 'alpine',
        'DISPLAY_WIDTH': '1920',
        'DISPLAY_HEIGHT': '1080'
    }
}


def get_browser_pool_config() -> BrowserPoolConfig:
    """
    Create and return a BrowserPoolConfig instance with default values.

    Returns:
        BrowserPoolConfig: The browser pool configuration.
    """
    config = BrowserPoolConfig(
        min_containers_per_browser_type=DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE,
        max_containers_per_browser_type=DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE,
        max_total_containers=DEFAULT_MAX_TOTAL_CONTAINERS,
        container_idle_timeout=CONTAINER_IDLE_TIMEOUT,
        container_max_lifetime=CONTAINER_MAX_LIFETIME,
        scaling_strategy=DEFAULT_SCALING_STRATEGY,
        demand_scaling_config=DEMAND_SCALING_CONFIG,
        browser_configs={}
    )

    # Create browser configs
    for browser_type in SUPPORTED_BROWSER_TYPES:
        for os_type in SUPPORTED_OS_TYPES:
            key = f"{browser_type}-{os_type}"
            browser_config = BrowserConfig(
                browser_type=browser_type,
                os_type=os_type,
                image=BROWSER_IMAGES[key],
                resource_limits=BROWSER_RESOURCE_LIMITS[key],
                env_vars=BROWSER_ENV_VARS[key]
            )
            config.browser_configs[key] = browser_config

    return config

"""
Flask application for BahtBrowse Docker Queue Management.
"""

import os
from flask import Flask, jsonify
from docker_queue.api.routes import api

def create_app():
    """
    Create and configure the Flask application.

    Returns:
        Flask application
    """
    app = Flask(__name__)

    # Register blueprints
    app.register_blueprint(api, url_prefix='/api')

    # Root route
    @app.route('/')
    def index():
        return jsonify({
            'name': 'BahtBrowse Docker Queue Management',
            'version': '1.0.0',
            'status': 'running'
        })

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'status': 'error',
            'message': 'Not found'
        }), 404

    @app.errorhandler(500)
    def server_error(error):
        return jsonify({
            'status': 'error',
            'message': 'Internal server error'
        }), 500

    return app

if __name__ == '__main__':
    app = create_app()
    # Get port from environment variable or use default
    port = int(os.environ.get("FLASK_RUN_PORT", "5001"))
    app.run(host='0.0.0.0', port=port, debug=True)

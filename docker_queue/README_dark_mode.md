# Dark Mode for 10Baht bahtBrowse Dashboards

This document describes the dark mode feature for 10Baht bahtBrowse dashboards.

## Overview

10Baht bahtBrowse now supports dark mode for both the main dashboard and the Celery Flower dashboard. Dark mode provides a more comfortable viewing experience in low-light environments and reduces eye strain.

```mermaid
graph TD
    A[User Preference] -->|System Default| B{Dark Mode Enabled?}
    A -->|Manual Toggle| C[User Selection]

    B -->|Yes| D[Apply Dark Theme]
    B -->|No| E[Apply Light Theme]

    C -->|Dark Mode| D
    C -->|Light Mode| E

    D --> F[Store Preference]
    E --> F
```

## Main Dashboard Dark Mode

The main dashboard already had dark mode support through the React-based frontend. The dark mode theme has been enhanced with a retro green terminal look, reminiscent of classic computer terminals.

### Features

- Toggle between light and dark mode
- Retro green terminal theme with CRT effects
- Persistent preference (saved in localStorage)
- Automatic detection of system preference
- Adjustable CRT effect intensity

```mermaid
classDiagram
    class ThemeContext {
        +boolean darkMode
        +boolean crtEffect
        +string crtIntensity
        +string fontStyle
        +toggleDarkMode()
        +toggleCrtEffect()
        +changeCrtIntensity(intensity)
        +changeFontStyle(style)
    }

    class ThemeProvider {
        +useState()
        +useEffect()
        +applyThemeToDOM()
    }

    ThemeProvider --> ThemeContext : provides
```

### Implementation

The dark mode implementation for the main dashboard is in:

- `docker_queue/dashboard/frontend/src/contexts/ThemeContext.js`: Context provider for theme settings
- `docker_queue/dashboard/frontend/src/styles/DarkMode.css`: Dark mode CSS styles
- `docker_queue/dashboard/frontend/src/styles/ScanlineOverlay.css`: CRT scanline effect
- `docker_queue/dashboard/frontend/src/styles/TextDistortion.css`: Text distortion effects

## Celery Flower Dark Mode

A new dark mode feature has been added to the Celery Flower dashboard to provide a consistent experience across all bahtBrowse interfaces.

### Features

- Toggle between light and dark mode
- Modern dark theme with blue accents
- Persistent preference (saved in localStorage)
- Automatic detection of system preference
- Consistent styling with the main dashboard

```mermaid
flowchart TD
    A[User Loads Flower Dashboard] --> B[Check localStorage]
    B -->|Preference Found| C{Dark Mode Enabled?}
    B -->|No Preference| D[Check System Preference]

    C -->|Yes| E[Apply Dark Theme]
    C -->|No| F[Apply Light Theme]

    D -->|Dark Mode| E
    D -->|Light Mode| F

    G[User Toggles Theme] --> H{Current Theme?}
    H -->|Light| E
    H -->|Dark| F

    E --> I[Update localStorage]
    F --> I
```

### Implementation

The dark mode implementation for the Celery Flower dashboard consists of:

- `docker_queue/flower_dark_theme.css`: Dark mode CSS styles
- `docker_queue/flower_dark_mode.js`: JavaScript for toggling dark mode
- `docker_queue/flower_config.py`: Custom Flower configuration
- `docker_queue/run_flower.py`: Custom Flower runner script

```mermaid
classDiagram
    class FlowerConfig {
        +dict flower_conf
        +setup_flower_static_files(app)
    }

    class FlowerRunner {
        +patch_bootstrap()
        +run_flower()
    }

    class DarkModeToggle {
        +loadCSS(url)
        +enableDarkMode()
        +disableDarkMode()
        +handleToggleChange()
    }

    FlowerRunner --> FlowerConfig : uses
    FlowerRunner --> DarkModeToggle : injects
```

### Configuration

The Celery Flower dashboard is configured in `docker-compose.yml`:

```yaml
# Celery flower for monitoring with dark mode
flower:
  build:
    context: .
    dockerfile: Dockerfile.worker
  ports:
    - "5555:5555"
  environment:
    - REDIS_HOST=redis
    - CELERY_BROKER_URL=redis://redis:6379/0
    - CELERY_RESULT_BACKEND=redis://redis:6379/0
    - BROKER_API=redis://redis:6379/0
  volumes:
    - ./docker_queue/flower_config.py:/app/docker_queue/flower_config.py
    - ./docker_queue/flower_dark_theme.css:/app/docker_queue/flower_dark_theme.css
    - ./docker_queue/flower_dark_mode.js:/app/docker_queue/flower_dark_mode.js
  command: >
    python docker_queue/run_flower.py
  depends_on:
    - redis
    - worker-container
    - worker-pool
    - worker-monitoring
  restart: unless-stopped
  networks:
    - bahtbrowse
```

## Usage

### Main Dashboard

1. Access the main dashboard at `http://localhost:5000`
2. Click the theme toggle button in the top-right corner of the dashboard
3. The theme preference will be saved for future sessions

```mermaid
sequenceDiagram
    actor User
    participant Dashboard
    participant ThemeContext
    participant LocalStorage

    User->>Dashboard: Access dashboard
    Dashboard->>LocalStorage: Check for saved preference
    LocalStorage-->>Dashboard: Return preference (if any)
    Dashboard->>ThemeContext: Initialize with preference
    ThemeContext-->>Dashboard: Apply theme

    User->>Dashboard: Toggle theme
    Dashboard->>ThemeContext: Toggle dark mode
    ThemeContext->>LocalStorage: Save preference
    ThemeContext-->>Dashboard: Apply new theme
```

### Celery Flower Dashboard

1. Access the Celery Flower dashboard at `http://localhost:5555`
2. Click the "Dark Mode" toggle switch in the top-right corner of the dashboard
3. The theme preference will be saved for future sessions

```mermaid
sequenceDiagram
    actor User
    participant Flower
    participant DarkModeToggle
    participant LocalStorage

    User->>Flower: Access dashboard
    Flower->>DarkModeToggle: Initialize
    DarkModeToggle->>LocalStorage: Check for saved preference
    LocalStorage-->>DarkModeToggle: Return preference (if any)
    DarkModeToggle-->>Flower: Apply theme

    User->>DarkModeToggle: Toggle theme
    DarkModeToggle->>LocalStorage: Save preference
    alt Dark Mode Enabled
        DarkModeToggle->>Flower: Load dark theme CSS
    else Dark Mode Disabled
        DarkModeToggle->>Flower: Remove dark theme CSS
    end
```

## Testing

Tests for the dark mode implementation are in:

- `tests/docker_queue/test_flower_dark_mode.py`

## Customisation

### Main Dashboard

To customise the main dashboard dark mode theme, edit:

- `docker_queue/dashboard/frontend/src/styles/DarkMode.css`: CSS variables and styles
- `docker_queue/dashboard/frontend/src/App.js`: Theme configuration (colours, typography)

### Celery Flower Dashboard

To customise the Celery Flower dark mode theme, edit:

- `docker_queue/flower_dark_theme.css`: CSS variables and styles

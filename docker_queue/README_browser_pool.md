# Browser-Specific Container Pool Management

This document describes the browser-specific container pool management feature in 10Baht bahtBrowse.

## Overview

The browser-specific container pool management feature allows bahtBrowse to maintain separate pools of containers for different browser types (e.g., Firefox, ungoogled-Chromium). This enables users to request containers with specific browsers and ensures that a minimum number of containers for each browser type are always available.

```mermaid
flowchart TD
    A[User Request] --> B{Browser Type?}
    B -->|Firefox| C[Firefox Pool]
    B -->|Chromium| D[Chromium Pool]
    B -->|Not Specified| E[Default Pool]

    C --> F{Available?}
    D --> F
    E --> F

    F -->|Yes| G[Assign Container]
    F -->|No| H[Create New Container]

    H --> I[Add to Pool]
    I --> G

    G --> J[Return to User]
```

## Configuration

Browser-specific container pool configuration is defined in `docker_queue/config/browser_pool_config.py`:

```python
# Supported browser types
SUPPORTED_BROWSER_TYPES = ['firefox', 'chromium']

# Minimum number of containers to maintain per browser type
DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE = {
    'firefox': 2,
    'chromium': 2
}

# Maximum number of containers allowed per browser type
DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE = {
    'firefox': 10,
    'chromium': 10
}

# Maximum total containers across all browser types
DEFAULT_MAX_TOTAL_CONTAINERS = 20
```

## Components

### Redis Client

The Redis client has been extended to support browser-specific container tracking:

- `get_browser_container_set_key(browser_type, set_name)`: Get Redis key for browser-specific container set
- `get_containers_by_browser_type(browser_type)`: Get all containers of a specific browser type
- `get_browser_pool_status()`: Get the status of the container pool by browser type

All container operations (add, get, mark for recycling, return, remove) have been updated to support browser-specific tracking.

```mermaid
classDiagram
    class RedisClient {
        +get_browser_container_set_key(browser_type, set_name)
        +add_container_to_pool(container_id, container_data)
        +get_available_container(browser_type)
        +mark_container_for_recycling(container_id)
        +return_container_to_pool(container_id)
        +remove_container_from_pool(container_id)
        +get_containers_by_browser_type(browser_type)
        +get_browser_pool_status()
    }
```

### Docker Client

The Docker client has been extended to support browser-specific container creation:

- `create_container(browser_type=None)`: Create a new Docker container with the specified browser type

### Celery Tasks

New Celery tasks have been added for browser-specific container pool management:

- `maintain_browser_specific_pool()`: Maintain the minimum number of containers for each browser type
- `create_browser_container(browser_type)`: Create a container of the specified browser type
- `assign_browser_container_to_request(request_id, user_id, browser_type, priority)`: Assign a container of the specified browser type to a user request
- `collect_browser_specific_metrics()`: Collect metrics on container usage by browser type

```mermaid
graph TD
    A[maintain_browser_specific_pool] -->|For each browser type| B{Enough containers?}
    B -->|No| C[create_browser_container]
    B -->|Yes| D[Done]

    E[assign_browser_container_to_request] -->|Request container| F{Available container?}
    F -->|Yes| G[Assign to user]
    F -->|No| H[create_browser_container]
    H --> I[Queue request]

    J[collect_browser_specific_metrics] -->|For each browser type| K[Gather metrics]
    K --> L[Store in Redis]
```

## Usage

### Requesting a Browser-Specific Container

To request a container with a specific browser type:

```python
from docker_queue.tasks.browser_pool_management import assign_browser_container_to_request

# Request a Firefox container
container_details = assign_browser_container_to_request(
    request_id='unique_request_id',
    user_id='user123',
    browser_type='firefox'
)

# Request a Chromium container
container_details = assign_browser_container_to_request(
    request_id='unique_request_id',
    user_id='user123',
    browser_type='chromium'
)
```

### Monitoring Browser-Specific Pools

To get the status of browser-specific container pools:

```python
from docker_queue.redis_client import get_browser_pool_status

# Get pool status
pool_status = get_browser_pool_status()

# Example output:
# {
#     'firefox': {
#         'total_containers': 5,
#         'available_containers': 2,
#         'assigned_containers': 2,
#         'recycling_containers': 1
#     },
#     'chromium': {
#         'total_containers': 4,
#         'available_containers': 3,
#         'assigned_containers': 1,
#         'recycling_containers': 0
#     }
# }
```

## Implementation Details

### Container Tracking

Containers are tracked in Redis using browser-specific sets:

- `bahtbrowse:containers:all:{browser_type}`: All containers of a specific browser type
- `bahtbrowse:containers:available:{browser_type}`: Available containers of a specific browser type
- `bahtbrowse:containers:assigned:{browser_type}`: Assigned containers of a specific browser type
- `bahtbrowse:containers:recycling:{browser_type}`: Containers being recycled of a specific browser type

```mermaid
graph TD
    subgraph "Redis Container Sets"
        A[bahtbrowse:containers:all:firefox]
        B[bahtbrowse:containers:available:firefox]
        C[bahtbrowse:containers:assigned:firefox]
        D[bahtbrowse:containers:recycling:firefox]

        E[bahtbrowse:containers:all:chromium]
        F[bahtbrowse:containers:available:chromium]
        G[bahtbrowse:containers:assigned:chromium]
        H[bahtbrowse:containers:recycling:chromium]
    end

    I[Container Creation] -->|Add| A
    I -->|Add| B

    B -->|Assign| C
    C -->|Recycle| D
    D -->|Return| B
```

### Container Creation

When creating a container, browser-specific configuration is applied:

- Browser-specific Docker image (e.g., `bahtbrowse-firefox:latest`, `bahtbrowse-ungoogled-chromium:latest`)
- Browser-specific resource limits (CPU, memory)
- Browser-specific environment variables

### Pool Maintenance

The `maintain_browser_specific_pool` task ensures that a minimum number of containers for each browser type are always available. It:

1. Checks the current number of available containers for each browser type
2. Creates new containers as needed to maintain the minimum number
3. Respects the maximum total containers limit

```mermaid
stateDiagram-v2
    [*] --> CheckPoolStatus
    CheckPoolStatus --> EvaluateFirefoxPool
    EvaluateFirefoxPool --> EvaluateChromiumPool

    EvaluateFirefoxPool --> CreateFirefoxContainers: If below minimum
    EvaluateChromiumPool --> CreateChromiumContainers: If below minimum

    CreateFirefoxContainers --> CheckTotalLimit
    CreateChromiumContainers --> CheckTotalLimit

    CheckTotalLimit --> AdjustCreationCount: If exceeding maximum
    CheckTotalLimit --> CreateContainers: If within limits

    AdjustCreationCount --> CreateContainers
    CreateContainers --> [*]
```

## Testing

Tests for browser-specific container pool management are in:

- `tests/docker_queue/test_browser_pool_management.py`
- `tests/docker_queue/test_redis_browser_functions.py`
- `tests/docker_queue/test_docker_browser_functions.py`

"""
ELK stack integration for 10Baht bahtBrowse.

This module provides utilities for integrating the 10Baht bahtBrowse system
with the ELK (Elasticsearch, Logstash, Kibana) stack for comprehensive logging
and monitoring.
"""

import os
import logging
import json
from typing import Dict, Any, Optional
import docker
from celery import Celery

from docker_queue.celery_logging import init_celery_logging
from docker_queue.container_logging import (
    init_container_logging,
    setup_container_logging_hooks,
    configure_container_for_elk
)

# Configure logger
logger = logging.getLogger(__name__)

class ELKIntegration:
    """
    ELK stack integration for 10Baht bahtBrowse.

    This class provides utilities for integrating the 10Baht bahtBrowse system
    with the ELK (Elasticsearch, Logstash, Kibana) stack for comprehensive logging
    and monitoring.
    """

    def __init__(self, app: Optional[Celery] = None, docker_client: Optional[docker.DockerClient] = None):
        """
        Initialize ELK stack integration.

        Args:
            app: Celery application instance
            docker_client: Docker client instance
        """
        self.app = app
        self.docker_client = docker_client
        self.celery_logger = None
        self.initialized = False

    def init_app(self, app: Celery) -> None:
        """
        Initialize ELK integration with a Celery application.

        Args:
            app: Celery application instance
        """
        self.app = app

        # Initialize Celery logging
        self.celery_logger = init_celery_logging()

        # Log initialization
        logger.info(
            "ELK integration initialized for Celery application"
        )

    def init_docker(self, docker_client: docker.DockerClient) -> None:
        """
        Initialize ELK integration with a Docker client.

        Args:
            docker_client: Docker client instance
        """
        self.docker_client = docker_client

        # Initialize container logging
        init_container_logging()

        # Set up container logging hooks
        setup_container_logging_hooks(docker_client)

        # Log initialization
        logger.info(
            "ELK integration initialized for Docker"
        )

    def configure_browser_container(
        self,
        container_config: Dict[str, Any],
        container_id: str,
        browser_type: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure a browser container for ELK stack logging.

        Args:
            container_config: Docker container configuration dictionary
            container_id: Unique identifier for the container
            browser_type: Type of browser (firefox, chromium)
            session_id: Session ID associated with the container
            user_id: User ID associated with the container

        Returns:
            Updated Docker container configuration dictionary
        """
        if not self.docker_client:
            logger.warning(
                "Docker client not initialized for ELK integration"
            )
            return container_config

        return configure_container_for_elk(
            self.docker_client,
            container_config,
            "browser",
            container_id,
            browser_type,
            session_id,
            user_id
        )

    def configure_worker_container(
        self,
        container_config: Dict[str, Any],
        container_id: str,
        worker_type: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure a worker container for ELK stack logging.

        Args:
            container_config: Docker container configuration dictionary
            container_id: Unique identifier for the container
            worker_type: Type of worker
            user_id: User ID associated with the container

        Returns:
            Updated Docker container configuration dictionary
        """
        if not self.docker_client:
            logger.warning(
                "Docker client not initialized for ELK integration"
            )
            return container_config

        return configure_container_for_elk(
            self.docker_client,
            container_config,
            "worker",
            container_id,
            None,
            None,
            user_id
        )

    def configure_api_container(
        self,
        container_config: Dict[str, Any],
        container_id: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Configure an API container for ELK stack logging.

        Args:
            container_config: Docker container configuration dictionary
            container_id: Unique identifier for the container
            user_id: User ID associated with the container

        Returns:
            Updated Docker container configuration dictionary
        """
        if not self.docker_client:
            logger.warning(
                "Docker client not initialized for ELK integration"
            )
            return container_config

        return configure_container_for_elk(
            self.docker_client,
            container_config,
            "api",
            container_id,
            None,
            None,
            user_id
        )

    def initialize(self) -> None:
        """
        Initialize ELK stack integration.

        This method initializes both Celery and Docker integration if they
        have been configured.
        """
        # Configure JSON logging for this module
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter(
            '{"timestamp":"%(asctime)s", "level":"%(levelname)s", "message":"%(message)s", '
            '"logger":"%(name)s", "path":"%(pathname)s", "line":%(lineno)d, '
            '"process":%(process)d, "thread":%(thread)d}',
            '%Y-%m-%dT%H:%M:%S.%fZ'
        ))

        logger.addHandler(handler)
        logger.setLevel(logging.INFO)

        # Initialize Celery integration if app is configured
        if self.app:
            self.init_app(self.app)

        # Initialize Docker integration if client is configured
        if self.docker_client:
            self.init_docker(self.docker_client)

        self.initialized = True

        # Log initialization
        logger.info(
            "ELK stack integration initialized"
        )

# Create a singleton instance
elk_integration = ELKIntegration()

def init_elk_integration(app: Optional[Celery] = None, docker_client: Optional[docker.DockerClient] = None) -> ELKIntegration:
    """
    Initialize ELK stack integration.

    This function initializes the ELK stack integration with the provided
    Celery application and Docker client.

    Args:
        app: Celery application instance
        docker_client: Docker client instance

    Returns:
        ELK integration instance
    """
    global elk_integration

    if app:
        elk_integration.app = app

    if docker_client:
        elk_integration.docker_client = docker_client

    if not elk_integration.initialized:
        elk_integration.initialize()

    return elk_integration

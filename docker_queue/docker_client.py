"""
Docker client for BahtBrowse Docker Queue Management.
"""

import docker
import uuid
import time
import logging
import os
from docker_queue.config import (
    DOCKER_BASE_URL, DOCKER_API_VERSION, DOCKER_TIMEOUT,
    CONTAINER_IMAGE, CONTAINER_CPU_LIMIT, CONTAINER_MEMORY_LIMIT,
    CONTAINER_NETWORK, BROWSER_IMAGES, BROWSER_RESOURCE_LIMITS,
    BROWSER_ENV_VARS, SUPPORTED_BROWSER_TYPES
)

# Configure logging
logger = logging.getLogger(__name__)

# Create Docker client
# Use a dummy client for now
class DummyDockerClient:
    def __init__(self):
        self.containers = DummyContainerCollection()
        self.images = DummyImageCollection()
        self.networks = DummyNetworkCollection()
        logger.warning("Using dummy Docker client")

    def version(self):
        return {"ApiVersion": "1.41"}

class DummyContainerCollection:
    def __init__(self):
        pass

    def run(self, *args, **kwargs):
        container_id = str(uuid.uuid4())
        logger.warning(f"Dummy container created with ID: {container_id}")
        return DummyContainer(container_id)

    def get(self, container_id):
        logger.warning(f"Dummy container retrieved with ID: {container_id}")
        return DummyContainer(container_id)

    def list(self, *args, **kwargs):
        return []

class DummyContainer:
    def __init__(self, id):
        self.id = id
        self.name = f"dummy-container-{id[:8]}"
        self.status = "running"

    def stop(self, *args, **kwargs):
        logger.warning(f"Dummy container stopped: {self.id}")
        return True

    def remove(self, *args, **kwargs):
        logger.warning(f"Dummy container removed: {self.id}")
        return True

    def logs(self, *args, **kwargs):
        return b"Dummy container logs"

    def reload(self):
        pass

class DummyImageCollection:
    def __init__(self):
        pass

    def list(self, *args, **kwargs):
        return []

class DummyNetworkCollection:
    def __init__(self):
        pass

    def list(self, *args, **kwargs):
        return []

docker_client = DummyDockerClient()

def create_container(browser_type=None, os_type=None):
    """
    Create a new Docker container.

    Args:
        browser_type: Optional browser type ('firefox', 'chromium', etc.)
        os_type: Optional OS type ('ubuntu', 'alpine', etc.)

    Returns:
        Dictionary with container details
    """
    try:
        # Generate unique container ID
        container_id = str(uuid.uuid4())
        container_name = f"bahtbrowse-{container_id[:8]}"

        # Set container image and resources based on browser and OS type
        if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
            if os_type and os_type in ['ubuntu', 'alpine']:
                # Use browser-OS specific image and resources
                image_key = f"{browser_type}-{os_type}"
                image = BROWSER_IMAGES.get(image_key, CONTAINER_IMAGE)
                cpu_limit = BROWSER_RESOURCE_LIMITS.get(image_key, {}).get('cpu', CONTAINER_CPU_LIMIT)
                memory_limit = BROWSER_RESOURCE_LIMITS.get(image_key, {}).get('memory', CONTAINER_MEMORY_LIMIT)
                env_vars = BROWSER_ENV_VARS.get(image_key, {})
            else:
                # Default to Ubuntu if OS type not specified
                image_key = f"{browser_type}-ubuntu"
                image = BROWSER_IMAGES.get(image_key, CONTAINER_IMAGE)
                cpu_limit = BROWSER_RESOURCE_LIMITS.get(image_key, {}).get('cpu', CONTAINER_CPU_LIMIT)
                memory_limit = BROWSER_RESOURCE_LIMITS.get(image_key, {}).get('memory', CONTAINER_MEMORY_LIMIT)
                env_vars = BROWSER_ENV_VARS.get(image_key, {})
        else:
            # Default to Firefox on Ubuntu if browser type not specified
            image = BROWSER_IMAGES.get('firefox-ubuntu', CONTAINER_IMAGE)
            cpu_limit = BROWSER_RESOURCE_LIMITS.get('firefox-ubuntu', {}).get('cpu', CONTAINER_CPU_LIMIT)
            memory_limit = BROWSER_RESOURCE_LIMITS.get('firefox-ubuntu', {}).get('memory', CONTAINER_MEMORY_LIMIT)
            env_vars = BROWSER_ENV_VARS.get('firefox-ubuntu', {})

        # Add standard environment variables
        env_vars.update({
            'CONTAINER_ID': container_id,
            'CREATED_AT': str(time.time())
        })

        # Add browser type and OS type if specified
        if browser_type:
            env_vars['BROWSER_TYPE'] = browser_type
        if os_type:
            env_vars['OS_TYPE'] = os_type

        # Create container
        container = docker_client.containers.run(
            image=image,
            name=container_name,
            detach=True,
            network=CONTAINER_NETWORK,
            cpu_quota=int(cpu_limit * 100000),
            mem_limit=memory_limit,
            environment=env_vars,
            labels={
                'app': 'bahtbrowse',
                'component': 'browser',
                'container_id': container_id,
                'browser_type': browser_type or 'default'
            },
            restart_policy={"Name": "unless-stopped"}
        )

        logger.info(f"Created container: {container.id} (name: {container_name}, type: {browser_type or 'default'})")

        # Wait for container to be ready
        time.sleep(2)  # Simple wait, could be replaced with health check

        # Get container details
        container_details = {
            'docker_id': container.id,
            'container_id': container_id,
            'name': container_name,
            'status': 'initializing',
            'created_at': time.time(),
            'ip_address': get_container_ip(container.id),
            'browser_type': browser_type,
            'os_type': os_type,
            'usage_count': 0,
            'last_activity': time.time()
        }

        return container_details

    except Exception as e:
        logger.error(f"Error creating container: {str(e)}")
        raise

def get_container_ip(docker_id):
    """
    Get the IP address of a container.

    Args:
        docker_id: Docker container ID

    Returns:
        IP address or None if not available
    """
    try:
        container = docker_client.containers.get(docker_id)
        container_data = container.attrs

        # Get IP address from network settings
        networks = container_data.get('NetworkSettings', {}).get('Networks', {})
        if CONTAINER_NETWORK in networks:
            return networks[CONTAINER_NETWORK].get('IPAddress')

        # If custom network not found, try bridge network
        if 'bridge' in networks:
            return networks['bridge'].get('IPAddress')

        return None

    except Exception as e:
        logger.error(f"Error getting container IP: {str(e)}")
        return None

def stop_container(docker_id):
    """
    Stop a Docker container.

    Args:
        docker_id: Docker container ID

    Returns:
        True if successful, False otherwise
    """
    try:
        container = docker_client.containers.get(docker_id)
        container.stop(timeout=10)
        logger.info(f"Stopped container: {docker_id}")
        return True

    except Exception as e:
        logger.error(f"Error stopping container: {str(e)}")
        return False

def remove_container(docker_id):
    """
    Remove a Docker container.

    Args:
        docker_id: Docker container ID

    Returns:
        True if successful, False otherwise
    """
    try:
        container = docker_client.containers.get(docker_id)
        container.remove(force=True)
        logger.info(f"Removed container: {docker_id}")
        return True

    except Exception as e:
        logger.error(f"Error removing container: {str(e)}")
        return False

def restart_container(docker_id):
    """
    Restart a Docker container.

    Args:
        docker_id: Docker container ID

    Returns:
        True if successful, False otherwise
    """
    try:
        container = docker_client.containers.get(docker_id)
        container.restart(timeout=10)
        logger.info(f"Restarted container: {docker_id}")
        return True

    except Exception as e:
        logger.error(f"Error restarting container: {str(e)}")
        return False

def get_container_status(docker_id):
    """
    Get the status of a Docker container.

    Args:
        docker_id: Docker container ID

    Returns:
        Container status or None if not found
    """
    try:
        container = docker_client.containers.get(docker_id)
        return container.status

    except Exception as e:
        logger.error(f"Error getting container status: {str(e)}")
        return None

def get_container_logs(docker_id, lines=100):
    """
    Get the logs of a Docker container.

    Args:
        docker_id: Docker container ID
        lines: Number of lines to retrieve

    Returns:
        Container logs or None if not found
    """
    try:
        container = docker_client.containers.get(docker_id)
        return container.logs(tail=lines).decode('utf-8')

    except Exception as e:
        logger.error(f"Error getting container logs: {str(e)}")
        return None

def get_container_stats(docker_id):
    """
    Get the stats of a Docker container.

    Args:
        docker_id: Docker container ID

    Returns:
        Container stats or None if not found
    """
    try:
        container = docker_client.containers.get(docker_id)
        stats = container.stats(stream=False)

        # Extract relevant stats
        cpu_stats = stats.get('cpu_stats', {})
        memory_stats = stats.get('memory_stats', {})
        network_stats = stats.get('networks', {})

        # Calculate CPU usage percentage
        cpu_delta = cpu_stats.get('cpu_usage', {}).get('total_usage', 0) - \
                   stats.get('precpu_stats', {}).get('cpu_usage', {}).get('total_usage', 0)
        system_delta = cpu_stats.get('system_cpu_usage', 0) - \
                      stats.get('precpu_stats', {}).get('system_cpu_usage', 0)

        cpu_percent = 0.0
        if system_delta > 0 and cpu_delta > 0:
            cpu_percent = (cpu_delta / system_delta) * cpu_stats.get('online_cpus', 1) * 100.0

        # Calculate memory usage
        memory_usage = memory_stats.get('usage', 0)
        memory_limit = memory_stats.get('limit', 1)
        memory_percent = (memory_usage / memory_limit) * 100.0

        # Calculate network usage
        network_rx_bytes = 0
        network_tx_bytes = 0
        for interface, stats in network_stats.items():
            network_rx_bytes += stats.get('rx_bytes', 0)
            network_tx_bytes += stats.get('tx_bytes', 0)

        return {
            'cpu_percent': round(cpu_percent, 2),
            'memory_usage': memory_usage,
            'memory_limit': memory_limit,
            'memory_percent': round(memory_percent, 2),
            'network_rx_bytes': network_rx_bytes,
            'network_tx_bytes': network_tx_bytes
        }

    except Exception as e:
        logger.error(f"Error getting container stats: {str(e)}")
        return None

def execute_command(docker_id, command):
    """
    Execute a command in a Docker container.

    Args:
        docker_id: Docker container ID
        command: Command to execute

    Returns:
        Command output or None if failed
    """
    try:
        container = docker_client.containers.get(docker_id)
        result = container.exec_run(command)

        return {
            'exit_code': result.exit_code,
            'output': result.output.decode('utf-8')
        }

    except Exception as e:
        logger.error(f"Error executing command in container: {str(e)}")
        return None

def cleanup_containers():
    """
    Clean up stale containers.

    Returns:
        Number of containers cleaned up
    """
    try:
        # Get all containers with bahtbrowse label
        containers = docker_client.containers.list(
            all=True,
            filters={'label': 'app=bahtbrowse'}
        )

        count = 0
        for container in containers:
            # Check if container is stale (not running or exited)
            if container.status in ['exited', 'dead', 'created']:
                logger.info(f"Cleaning up stale container: {container.id}")
                container.remove(force=True)
                count += 1

        return count

    except Exception as e:
        logger.error(f"Error cleaning up containers: {str(e)}")
        return 0

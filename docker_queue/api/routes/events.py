"""
API routes for handling events for ELK stack integration.
"""

import json
import logging
import time
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_cors import cross_origin

# Create blueprint
events_bp = Blueprint('events', __name__)

# Configure logging
logger = logging.getLogger(__name__)

@events_bp.route('/api/events', methods=['POST'])
@cross_origin()
def receive_events():
    """
    Receive events from the frontend and log them for ELK stack.
    
    The events are logged in a structured JSON format that can be
    easily parsed by Filebeat and sent to Logstash.
    """
    try:
        # Get events from request
        events = request.json
        
        if not events:
            return jsonify({'error': 'No events provided'}), 400
        
        # Handle single event or array of events
        if not isinstance(events, list):
            events = [events]
        
        # Process each event
        for event in events:
            # Add server timestamp
            event['server_timestamp'] = datetime.utcnow().isoformat() + 'Z'
            
            # Add request metadata
            event['client_ip'] = request.remote_addr
            event['user_agent'] = request.headers.get('User-Agent', '')
            
            # Log the event in JSON format
            logger.info(json.dumps({
                'event_type': 'browser_event',
                'data': event
            }))
        
        return jsonify({'status': 'success', 'count': len(events)}), 200
    
    except Exception as e:
        logger.error(f"Error processing events: {str(e)}")
        return jsonify({'error': str(e)}), 500

@events_bp.route('/api/events/performance', methods=['POST'])
@cross_origin()
def receive_performance_metrics():
    """
    Receive performance metrics from the frontend and log them for ELK stack.
    """
    try:
        # Get metrics from request
        metrics = request.json
        
        if not metrics:
            return jsonify({'error': 'No metrics provided'}), 400
        
        # Add server timestamp
        metrics['server_timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add request metadata
        metrics['client_ip'] = request.remote_addr
        metrics['user_agent'] = request.headers.get('User-Agent', '')
        
        # Log the metrics in JSON format
        logger.info(json.dumps({
            'event_type': 'performance_metrics',
            'data': metrics
        }))
        
        return jsonify({'status': 'success'}), 200
    
    except Exception as e:
        logger.error(f"Error processing performance metrics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@events_bp.route('/api/events/error', methods=['POST'])
@cross_origin()
def receive_error():
    """
    Receive error reports from the frontend and log them for ELK stack.
    """
    try:
        # Get error from request
        error = request.json
        
        if not error:
            return jsonify({'error': 'No error provided'}), 400
        
        # Add server timestamp
        error['server_timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add request metadata
        error['client_ip'] = request.remote_addr
        error['user_agent'] = request.headers.get('User-Agent', '')
        
        # Log the error in JSON format
        logger.error(json.dumps({
            'event_type': 'browser_error',
            'data': error
        }))
        
        return jsonify({'status': 'success'}), 200
    
    except Exception as e:
        logger.error(f"Error processing error report: {str(e)}")
        return jsonify({'error': str(e)}), 500

@events_bp.route('/api/events/session', methods=['POST'])
@cross_origin()
def receive_session_event():
    """
    Receive session events from the frontend and log them for ELK stack.
    """
    try:
        # Get session event from request
        session_event = request.json
        
        if not session_event:
            return jsonify({'error': 'No session event provided'}), 400
        
        # Add server timestamp
        session_event['server_timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # Add request metadata
        session_event['client_ip'] = request.remote_addr
        session_event['user_agent'] = request.headers.get('User-Agent', '')
        
        # Log the session event in JSON format
        logger.info(json.dumps({
            'event_type': 'session_event',
            'data': session_event
        }))
        
        return jsonify({'status': 'success'}), 200
    
    except Exception as e:
        logger.error(f"Error processing session event: {str(e)}")
        return jsonify({'error': str(e)}), 500

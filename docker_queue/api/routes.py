"""
API routes for BahtBrowse Docker Queue Management.
"""

import uuid
import time
import json
from flask import Blueprint, jsonify, request, current_app
from docker_queue.redis_client import (
    get_user_container, get_container_details,
    get_request_position, get_queue_length,
    get_latest_metrics, get_metrics_range,
    update_container_activity
)
from docker_queue.tasks.container_management import (
    assign_container_to_request, recycle_container
)
from docker_queue.tasks.pool_management import (
    maintain_container_pool, scale_container_pool
)
from docker_queue.tasks.monitoring import (
    collect_metrics, check_container_health,
    generate_usage_report
)

# Create blueprint
api = Blueprint('api', __name__)

@api.route('/containers/activity', methods=['POST'])
def update_activity():
    """
    Update the last activity timestamp for a container.
    This endpoint should be called periodically by the client to indicate activity.
    """
    # Get request data
    data = request.get_json()
    user_id = data.get('user_id')

    if not user_id:
        return jsonify({
            'status': 'error',
            'message': 'User ID is required'
        }), 400

    # Get user's container
    container_id = get_user_container(user_id)
    if not container_id:
        return jsonify({
            'status': 'error',
            'message': 'No container assigned to user'
        }), 404

    # Update last activity timestamp
    success = update_container_activity(container_id)

    if success:
        return jsonify({
            'status': 'success',
            'message': 'Container activity updated successfully'
        })
    else:
        return jsonify({
            'status': 'error',
            'message': 'Error updating container activity'
        }), 500

@api.route('/containers/request', methods=['POST'])
def request_container():
    """
    Request a container for a user.
    """
    # Get request data
    data = request.get_json()
    user_id = data.get('user_id')
    priority = data.get('priority', 0)

    if not user_id:
        return jsonify({
            'status': 'error',
            'message': 'User ID is required'
        }), 400

    # Generate request ID
    request_id = str(uuid.uuid4())

    # Check if user already has a container
    existing_container_id = get_user_container(user_id)
    if existing_container_id:
        container_details = get_container_details(existing_container_id)
        return jsonify({
            'status': 'success',
            'message': 'User already has an assigned container',
            'container': container_details
        })

    # Try to assign a container immediately
    result = assign_container_to_request.apply_async(
        args=[request_id, user_id, priority]
    )

    try:
        # Wait for result with timeout
        container_details = result.get(timeout=5)

        if container_details:
            # Container assigned immediately
            return jsonify({
                'status': 'success',
                'message': 'Container assigned successfully',
                'container': container_details
            })
        else:
            # Container will be assigned asynchronously
            queue_position = get_request_position(request_id)
            queue_length = get_queue_length()

            return jsonify({
                'status': 'pending',
                'message': 'Container request queued',
                'request_id': request_id,
                'queue_position': queue_position,
                'queue_length': queue_length,
                'estimated_wait_seconds': queue_position * 5  # Simple estimation
            })
    except:
        # Timeout or error, request is queued
        return jsonify({
            'status': 'pending',
            'message': 'Container request queued',
            'request_id': request_id
        })

@api.route('/containers/status/<request_id>', methods=['GET'])
def container_status(request_id):
    """
    Check the status of a container request.
    """
    # Get user ID from request
    user_id = request.args.get('user_id')

    if not user_id:
        return jsonify({
            'status': 'error',
            'message': 'User ID is required'
        }), 400

    # Check if container has been assigned
    container_id = get_user_container(user_id)
    if container_id:
        container_details = get_container_details(container_id)
        return jsonify({
            'status': 'success',
            'message': 'Container assigned successfully',
            'container': container_details
        })

    # Check request status
    position = get_request_position(request_id)
    if position:
        queue_length = get_queue_length()
        return jsonify({
            'status': 'pending',
            'message': 'Request is in queue',
            'request_id': request_id,
            'queue_position': position,
            'queue_length': queue_length,
            'estimated_wait_seconds': position * 5  # Simple estimation
        })

    return jsonify({
        'status': 'error',
        'message': 'Request not found'
    }), 404

@api.route('/containers/release', methods=['POST'])
def release_container():
    """
    Release a container assigned to a user.
    """
    # Get request data
    data = request.get_json()
    user_id = data.get('user_id')

    if not user_id:
        return jsonify({
            'status': 'error',
            'message': 'User ID is required'
        }), 400

    # Get user's container
    container_id = get_user_container(user_id)
    if not container_id:
        return jsonify({
            'status': 'error',
            'message': 'No container assigned to user'
        }), 404

    # Recycle container
    recycle_container.delay(container_id)

    return jsonify({
        'status': 'success',
        'message': 'Container released successfully'
    })

@api.route('/pool/status', methods=['GET'])
def pool_status():
    """
    Get the status of the container pool.
    """
    # Get pool status directly from Redis
    from docker_queue.redis_client import get_browser_pool_status

    try:
        # Get pool status
        pool_status = get_browser_pool_status()

        return jsonify({
            'status': 'success',
            'pool': pool_status
        })
    except Exception as e:
        # Error
        return jsonify({
            'status': 'error',
            'message': f'Error getting pool status: {str(e)}'
        }), 500

@api.route('/pool/scale', methods=['POST'])
def scale_pool():
    """
    Scale the container pool.
    """
    # Get request data
    data = request.get_json()
    target_size = data.get('target_size')

    if target_size is None:
        return jsonify({
            'status': 'error',
            'message': 'Target size is required'
        }), 400

    # Scale pool
    result = scale_container_pool.apply_async(args=[target_size])

    try:
        # Wait for result with timeout
        scaling_result = result.get(timeout=5)

        return jsonify({
            'status': 'success',
            'message': f"Pool scaling {scaling_result['action']}",
            'scaling': scaling_result
        })
    except:
        # Timeout or error
        return jsonify({
            'status': 'pending',
            'message': 'Pool scaling in progress'
        })

@api.route('/metrics/current', methods=['GET'])
def current_metrics():
    """
    Get current system metrics.
    """
    # Trigger metrics collection
    result = collect_metrics.apply_async()

    try:
        # Wait for result with timeout
        metrics = result.get(timeout=5)

        return jsonify({
            'status': 'success',
            'metrics': metrics
        })
    except:
        # Timeout or error, get latest metrics from storage
        metrics = get_latest_metrics()

        if metrics:
            return jsonify({
                'status': 'success',
                'metrics': metrics,
                'note': 'These are the latest stored metrics, not real-time'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Error getting metrics'
            }), 500

@api.route('/metrics/history', methods=['GET'])
def metrics_history():
    """
    Get historical metrics.
    """
    # Get request parameters
    start_time = request.args.get('start_time', time.time() - 3600)  # Default to last hour
    end_time = request.args.get('end_time', time.time())

    # Convert to float if string
    if isinstance(start_time, str):
        start_time = float(start_time)
    if isinstance(end_time, str):
        end_time = float(end_time)

    # Get metrics
    metrics = get_metrics_range(start_time, end_time)

    return jsonify({
        'status': 'success',
        'metrics': metrics,
        'period': {
            'start_time': start_time,
            'end_time': end_time
        }
    })

@api.route('/health', methods=['GET'])
def health_check():
    """
    Check the health of the system.
    """
    # Trigger health check
    result = check_container_health.apply_async()

    try:
        # Wait for result with timeout
        health = result.get(timeout=5)

        return jsonify({
            'status': 'success',
            'health': health
        })
    except:
        # Timeout or error
        return jsonify({
            'status': 'error',
            'message': 'Error checking health'
        }), 500

@api.route('/report', methods=['GET'])
def usage_report():
    """
    Generate a usage report.
    """
    # Trigger report generation
    result = generate_usage_report.apply_async()

    try:
        # Wait for result with timeout
        report = result.get(timeout=10)

        return jsonify({
            'status': 'success',
            'report': report
        })
    except:
        # Timeout or error
        return jsonify({
            'status': 'error',
            'message': 'Error generating report'
        }), 500

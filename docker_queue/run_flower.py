#!/usr/bin/env python
"""
Custom runner for Celery Flower with dark mode support.
"""

import os
import sys
from pathlib import Path
import importlib.util

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flower configuration
try:
    spec = importlib.util.spec_from_file_location(
        "flower_config", 
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "flower_config.py")
    )
    flower_config = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(flower_config)
except Exception as e:
    print(f"Error loading flower_config.py: {e}")
    sys.exit(1)

# Import Flower
try:
    from flower.command import FlowerCommand
    from flower.app import Flower
except ImportError:
    print("Celery Flower is not installed. Please install it with 'pip install flower'.")
    sys.exit(1)

# Monkey patch Flower's bootstrap method to inject our custom setup
original_bootstrap = Flower.bootstrap

def patched_bootstrap(self):
    """Patched bootstrap method to inject our custom setup."""
    app = original_bootstrap(self)
    try:
        flower_config.setup_flower_static_files(app)
        print("Successfully set up custom Flower static files for dark mode")
    except Exception as e:
        print(f"Error setting up custom Flower static files: {e}")
    return app

Flower.bootstrap = patched_bootstrap

# Run Flower with our custom configuration
if __name__ == "__main__":
    # Get configuration from flower_config.py
    conf = flower_config.flower_conf
    
    # Create Flower command with our configuration
    flower = FlowerCommand()
    
    # Set up command line arguments
    argv = [
        "--address=" + conf.get('address', '0.0.0.0'),
        "--port=" + str(conf.get('port', 5555)),
        "--broker_api=" + conf.get('broker_api', ''),
    ]
    
    # Add optional arguments
    if conf.get('url_prefix'):
        argv.append("--url_prefix=" + conf.get('url_prefix'))
    
    if conf.get('debug'):
        argv.append("--debug=true")
    
    if conf.get('persistent'):
        argv.append("--persistent=true")
        
    if conf.get('db'):
        argv.append("--db=" + conf.get('db'))
    
    if conf.get('max_tasks'):
        argv.append("--max_tasks=" + str(conf.get('max_tasks')))
    
    if conf.get('auth'):
        argv.append("--auth=" + conf.get('auth'))
    
    if conf.get('basic_auth'):
        argv.append("--basic_auth=" + conf.get('basic_auth'))
    
    if conf.get('tasks_columns'):
        argv.append("--tasks_columns=" + ','.join(conf.get('tasks_columns')))
    
    if conf.get('natural_time'):
        argv.append("--natural_time=true")
    
    if conf.get('auto_refresh'):
        argv.append("--auto_refresh=true")
    
    if conf.get('refresh_interval'):
        argv.append("--refresh_interval=" + str(conf.get('refresh_interval')))
    
    # Run Flower
    print(f"Starting Flower with arguments: {' '.join(argv)}")
    sys.argv = ["flower"] + argv
    flower.run_from_argv(sys.argv)

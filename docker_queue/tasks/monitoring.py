"""
Monitoring tasks for BahtBrowse Docker Queue Management.
"""

import logging
import time
import statistics
import json
from docker_queue.celery_app import app
from docker_queue.redis_client import (
    get_container_set_key, redis_client,
    get_queue_key, store_metrics,
    cleanup_old_metrics
)
from docker_queue.docker_client import (
    get_container_stats, get_container_status
)
from docker_queue.config import (
    METRICS_RETENTION_DAYS
)

# Configure logging
logger = logging.getLogger(__name__)

@app.task(queue='monitoring')
def collect_metrics():
    """
    Collect system metrics and store for analysis.

    Returns:
        Dictionary with collected metrics
    """
    try:
        timestamp = time.time()

        # Collect pool metrics
        available_containers = redis_client.scard(get_container_set_key('available_containers'))
        assigned_containers = redis_client.scard(get_container_set_key('assigned_containers'))
        recycling_containers = redis_client.scard(get_container_set_key('recycling_containers'))
        total_containers = available_containers + assigned_containers + recycling_containers

        # Collect queue metrics
        queue_length = redis_client.zcard(get_queue_key('container_requests'))

        # Calculate wait times
        wait_times = []
        for request_id in redis_client.zrange(get_queue_key('container_requests'), 0, -1):
            request_data = redis_client.hgetall(f"bahtbrowse:request:{request_id}")
            if 'timestamp' in request_data:
                wait_time = timestamp - float(request_data['timestamp'])
                wait_times.append(wait_time)

        avg_wait_time = statistics.mean(wait_times) if wait_times else 0
        p95_wait_time = statistics.quantiles(wait_times, n=20)[19] if len(wait_times) >= 20 else 0

        # Collect container metrics
        container_metrics = collect_container_metrics()

        # Collect system metrics
        system_metrics = collect_system_metrics()

        # Combine all metrics
        metrics = {
            'timestamp': timestamp,
            'pool': {
                'available_containers': available_containers,
                'assigned_containers': assigned_containers,
                'recycling_containers': recycling_containers,
                'total_containers': total_containers
            },
            'queue': {
                'length': queue_length,
                'avg_wait_time': avg_wait_time,
                'p95_wait_time': p95_wait_time
            },
            'containers': container_metrics,
            'system': system_metrics
        }

        # Store metrics
        store_metrics(metrics)

        # Cleanup old metrics
        cleanup_old_metrics(METRICS_RETENTION_DAYS * 86400)

        logger.info(f"Collected metrics: {available_containers} available, {assigned_containers} assigned, "
                   f"{recycling_containers} recycling, {queue_length} queued")

        return metrics

    except Exception as e:
        logger.error(f"Error collecting metrics: {str(e)}")
        return {
            'error': str(e)
        }

def collect_container_metrics():
    """
    Collect metrics for all containers.

    Returns:
        Dictionary with container metrics
    """
    try:
        # Get all containers
        all_containers = redis_client.smembers(get_container_set_key('all_containers'))

        container_metrics = {
            'count': len(all_containers),
            'status': {},
            'cpu_usage': [],
            'memory_usage': [],
            'network_rx': [],
            'network_tx': []
        }

        # Collect metrics for each container
        for container_id in all_containers:
            container_data = redis_client.hgetall(f"bahtbrowse:container:{container_id}")

            if not container_data or 'docker_id' not in container_data:
                continue

            docker_id = container_data['docker_id']

            # Get container status
            status = get_container_status(docker_id)

            if status:
                # Count by status
                container_metrics['status'][status] = container_metrics['status'].get(status, 0) + 1

                # Get container stats
                stats = get_container_stats(docker_id)

                if stats:
                    container_metrics['cpu_usage'].append(stats['cpu_percent'])
                    container_metrics['memory_usage'].append(stats['memory_percent'])
                    container_metrics['network_rx'].append(stats['network_rx_bytes'])
                    container_metrics['network_tx'].append(stats['network_tx_bytes'])

        # Calculate averages
        container_metrics['avg_cpu_usage'] = statistics.mean(container_metrics['cpu_usage']) if container_metrics['cpu_usage'] else 0
        container_metrics['avg_memory_usage'] = statistics.mean(container_metrics['memory_usage']) if container_metrics['memory_usage'] else 0
        container_metrics['avg_network_rx'] = statistics.mean(container_metrics['network_rx']) if container_metrics['network_rx'] else 0
        container_metrics['avg_network_tx'] = statistics.mean(container_metrics['network_tx']) if container_metrics['network_tx'] else 0

        return container_metrics

    except Exception as e:
        logger.error(f"Error collecting container metrics: {str(e)}")
        return {
            'error': str(e)
        }

def collect_system_metrics():
    """
    Collect system-wide metrics.

    Returns:
        Dictionary with system metrics
    """
    try:
        # In a real implementation, this would collect actual system metrics
        # For this example, we'll just return placeholder values

        return {
            'cpu_usage': 0,
            'memory_usage': 0,
            'disk_usage': 0,
            'network_usage': 0
        }

    except Exception as e:
        logger.error(f"Error collecting system metrics: {str(e)}")
        return {
            'error': str(e)
        }

@app.task(queue='monitoring')
def check_container_health():
    """
    Check the health of all containers.

    Returns:
        Dictionary with health check results
    """
    try:
        # Get all containers
        all_containers = redis_client.smembers(get_container_set_key('all_containers'))

        health_results = {
            'total': len(all_containers),
            'healthy': 0,
            'unhealthy': 0,
            'unknown': 0,
            'unhealthy_containers': []
        }

        # Check health of each container
        for container_id in all_containers:
            container_data = redis_client.hgetall(f"bahtbrowse:container:{container_id}")

            if not container_data or 'docker_id' not in container_data:
                health_results['unknown'] += 1
                continue

            docker_id = container_data['docker_id']

            # Get container status
            status = get_container_status(docker_id)

            if status == 'running':
                health_results['healthy'] += 1
            elif status in ['exited', 'dead', 'created']:
                health_results['unhealthy'] += 1
                health_results['unhealthy_containers'].append({
                    'container_id': container_id,
                    'docker_id': docker_id,
                    'status': status
                })
            else:
                health_results['unknown'] += 1

        logger.info(f"Container health: {health_results['healthy']} healthy, "
                   f"{health_results['unhealthy']} unhealthy, {health_results['unknown']} unknown")

        return health_results

    except Exception as e:
        logger.error(f"Error checking container health: {str(e)}")
        return {
            'error': str(e)
        }

@app.task(queue='monitoring')
def generate_usage_report():
    """
    Generate a usage report.

    Returns:
        Dictionary with usage report
    """
    try:
        # Get current time
        current_time = time.time()

        # Get metrics for the last 24 hours
        start_time = current_time - 86400
        metrics_data = redis_client.zrangebyscore(
            "bahtbrowse:metrics:timeseries",
            start_time, current_time
        )

        # Parse metrics data
        metrics = [json.loads(data) for data in metrics_data]

        # Calculate usage statistics
        if metrics:
            # Container pool usage
            pool_usage = {
                'avg_total_containers': statistics.mean([m['pool']['total_containers'] for m in metrics]),
                'avg_available_containers': statistics.mean([m['pool']['available_containers'] for m in metrics]),
                'avg_assigned_containers': statistics.mean([m['pool']['assigned_containers'] for m in metrics]),
                'max_total_containers': max([m['pool']['total_containers'] for m in metrics]),
                'min_available_containers': min([m['pool']['available_containers'] for m in metrics]),
                'max_assigned_containers': max([m['pool']['assigned_containers'] for m in metrics])
            }

            # Queue usage
            queue_usage = {
                'avg_queue_length': statistics.mean([m['queue']['length'] for m in metrics]),
                'max_queue_length': max([m['queue']['length'] for m in metrics]),
                'avg_wait_time': statistics.mean([m['queue']['avg_wait_time'] for m in metrics if m['queue']['avg_wait_time'] > 0]),
                'max_wait_time': max([m['queue']['p95_wait_time'] for m in metrics]) if any(m['queue']['p95_wait_time'] > 0 for m in metrics) else 0
            }

            # Resource usage
            resource_usage = {
                'avg_cpu_usage': statistics.mean([m['containers'].get('avg_cpu_usage', 0) for m in metrics if 'containers' in m and 'avg_cpu_usage' in m['containers']]),
                'avg_memory_usage': statistics.mean([m['containers'].get('avg_memory_usage', 0) for m in metrics if 'containers' in m and 'avg_memory_usage' in m['containers']]),
                'avg_network_rx': statistics.mean([m['containers'].get('avg_network_rx', 0) for m in metrics if 'containers' in m and 'avg_network_rx' in m['containers']]),
                'avg_network_tx': statistics.mean([m['containers'].get('avg_network_tx', 0) for m in metrics if 'containers' in m and 'avg_network_tx' in m['containers']])
            }

            report = {
                'timestamp': current_time,
                'period': {
                    'start': start_time,
                    'end': current_time,
                    'duration_hours': 24
                },
                'pool_usage': pool_usage,
                'queue_usage': queue_usage,
                'resource_usage': resource_usage
            }
        else:
            report = {
                'timestamp': current_time,
                'period': {
                    'start': start_time,
                    'end': current_time,
                    'duration_hours': 24
                },
                'error': 'No metrics data available'
            }

        logger.info(f"Generated usage report for the last 24 hours")

        return report

    except Exception as e:
        logger.error(f"Error generating usage report: {str(e)}")
        return {
            'error': str(e)
        }

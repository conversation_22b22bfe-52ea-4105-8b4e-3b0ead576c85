"""
Browser-specific container pool management tasks for BahtBrowse Docker Queue Management.
"""

import logging
import time
from typing import Dict, List, Optional

from docker_queue.celery_app import app
from docker_queue.docker_client import (
    create_container, stop_container, remove_container,
    restart_container, get_container_status, execute_command
)
from docker_queue.redis_client import (
    add_container_to_pool, get_available_container,
    assign_container_to_user, get_user_container,
    get_container_details, mark_container_for_recycling,
    return_container_to_pool, remove_container_from_pool,
    get_next_container_request, remove_container_request,
    get_containers_by_browser_type, get_browser_pool_status
)
from docker_queue.config import (
    CONTAINER_IDLE_TIMEOUT,
    DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_TOTAL_CONTAINERS,
    SUPPORTED_BROWSER_TYPES
)

# Configure logging
logger = logging.getLogger(__name__)


@app.task(bind=True, queue='pool_management')
def maintain_browser_specific_pool(self):
    """
    Maintain the minimum number of containers for each browser type.
    
    This task checks the current number of available containers for each browser type
    and creates new containers as needed to maintain the minimum number.
    
    Returns:
        Dict: Status of the browser-specific container pool
    """
    try:
        # Get current pool status by browser type
        pool_status = get_browser_pool_status()
        
        # Track containers to create
        containers_to_create = {}
        total_to_create = 0
        
        # Check each browser type
        for browser_type in SUPPORTED_BROWSER_TYPES:
            # Get minimum containers for this browser type
            min_containers = DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE.get(browser_type, 2)
            
            # Get current available containers for this browser type
            available_containers = pool_status.get(browser_type, {}).get('available_containers', 0)
            
            # Calculate how many containers to create
            to_create = max(0, min_containers - available_containers)
            
            if to_create > 0:
                containers_to_create[browser_type] = to_create
                total_to_create += to_create
                
                logger.info(f"Need to create {to_create} {browser_type} containers")
        
        # Check if we can create all needed containers
        max_total = DEFAULT_MAX_TOTAL_CONTAINERS
        current_total = sum(
            status.get('total_containers', 0) 
            for status in pool_status.values()
        )
        
        # Calculate how many we can actually create
        can_create = max(0, max_total - current_total)
        
        if can_create < total_to_create:
            logger.warning(
                f"Cannot create all needed containers. "
                f"Need {total_to_create}, can create {can_create}"
            )
            
            # Prioritize browser types (could be based on demand or other metrics)
            # For now, distribute evenly
            if can_create > 0:
                # Simple distribution algorithm
                remaining = can_create
                for browser_type in sorted(containers_to_create.keys()):
                    allocation = min(containers_to_create[browser_type], remaining)
                    containers_to_create[browser_type] = allocation
                    remaining -= allocation
                    
                    if remaining <= 0:
                        break
        
        # Create containers
        for browser_type, count in containers_to_create.items():
            for _ in range(count):
                create_browser_container.delay(browser_type)
        
        # Update pool status with containers that will be created
        for browser_type, count in containers_to_create.items():
            if browser_type not in pool_status:
                pool_status[browser_type] = {
                    'total_containers': 0,
                    'available_containers': 0,
                    'assigned_containers': 0,
                    'recycling_containers': 0
                }
            
            pool_status[browser_type]['containers_to_create'] = count
        
        return pool_status
    
    except Exception as exc:
        logger.error(f"Error maintaining browser-specific pool: {str(exc)}")
        self.retry(exc=exc, countdown=5)


@app.task(bind=True, queue='container_management')
def create_browser_container(self, browser_type: str):
    """
    Create a container of the specified browser type.
    
    Args:
        browser_type: Type of browser to create ('firefox', 'chromium', etc.)
        
    Returns:
        Container ID if successful
    """
    try:
        # Validate browser type
        if browser_type not in SUPPORTED_BROWSER_TYPES:
            logger.error(f"Unsupported browser type: {browser_type}")
            return None
        
        # Create container with browser type
        container_details = create_container(browser_type=browser_type)
        container_id = container_details['container_id']
        docker_id = container_details['docker_id']
        
        logger.info(f"Created {browser_type} container {container_id} (Docker ID: {docker_id})")
        
        # Initialize container
        container_details['status'] = 'available'
        container_details['initialized_at'] = time.time()
        container_details['browser_type'] = browser_type
        
        # Add to container pool
        add_container_to_pool(container_id, container_details)
        
        return container_id
    
    except Exception as exc:
        logger.error(f"Error creating {browser_type} container: {str(exc)}")
        self.retry(exc=exc, countdown=5)


@app.task(bind=True, queue='container_management')
def assign_browser_container_to_request(self, request_id: str, user_id: str, browser_type: str, priority: int = 0):
    """
    Assign a container of the specified browser type to a user request.
    
    Args:
        request_id: Unique ID for this request
        user_id: ID of the user requesting the container
        browser_type: Type of browser requested ('firefox', 'chromium', etc.)
        priority: Request priority (higher numbers = higher priority)
        
    Returns:
        Container details if assigned, None otherwise
    """
    try:
        # Check if user already has a container
        existing_container_id = get_user_container(user_id)
        if existing_container_id:
            container_details = get_container_details(existing_container_id)
            return container_details
        
        # Try to get an available container of the requested type
        container_id = get_available_container(browser_type=browser_type)
        
        if not container_id:
            # No container available, trigger creation and queue request
            maintain_browser_specific_pool.delay()
            logger.info(f"No {browser_type} container available for request {request_id}")
            return None
        
        # Assign container to user
        container_details = assign_container_to_user(container_id, user_id)
        
        if container_details:
            logger.info(f"Assigned {browser_type} container {container_id} to user {user_id}")
            
            # Update container details
            container_details['assigned_at'] = time.time()
            container_details['last_activity'] = time.time()
            
            # Trigger pool maintenance to replace the assigned container
            maintain_browser_specific_pool.delay()
            
            return container_details
        else:
            logger.error(f"Failed to assign {browser_type} container to user {user_id}")
            return None
    
    except Exception as exc:
        logger.error(f"Error assigning {browser_type} container: {str(exc)}")
        self.retry(exc=exc, countdown=5)


@app.task(bind=True, queue='monitoring')
def collect_browser_specific_metrics(self):
    """
    Collect metrics on container usage by browser type.
    
    Returns:
        Dict: Metrics by browser type
    """
    try:
        # Get current pool status by browser type
        pool_status = get_browser_pool_status()
        
        # Collect additional metrics
        metrics = {
            'timestamp': time.time(),
            'browser_metrics': {}
        }
        
        for browser_type in SUPPORTED_BROWSER_TYPES:
            browser_status = pool_status.get(browser_type, {})
            
            # Get containers of this type
            containers = get_containers_by_browser_type(browser_type)
            
            # Calculate metrics
            total_usage = sum(
                container.get('usage_count', 0) 
                for container in containers.values()
            )
            
            avg_usage_time = 0
            if containers:
                usage_times = [
                    container.get('last_used_at', 0) - container.get('created_at', 0)
                    for container in containers.values()
                    if container.get('last_used_at', 0) > 0
                ]
                
                if usage_times:
                    avg_usage_time = sum(usage_times) / len(usage_times)
            
            # Store metrics
            metrics['browser_metrics'][browser_type] = {
                'total_containers': browser_status.get('total_containers', 0),
                'available_containers': browser_status.get('available_containers', 0),
                'assigned_containers': browser_status.get('assigned_containers', 0),
                'recycling_containers': browser_status.get('recycling_containers', 0),
                'total_usage': total_usage,
                'avg_usage_time': avg_usage_time
            }
        
        # Store metrics in Redis or other storage
        # (implementation depends on storage mechanism)
        
        return metrics
    
    except Exception as exc:
        logger.error(f"Error collecting browser-specific metrics: {str(exc)}")
        self.retry(exc=exc, countdown=5)

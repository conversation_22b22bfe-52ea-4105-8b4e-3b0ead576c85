"""
Pool management tasks for BahtBrowse Docker Queue Management.
"""

import logging
import time
from docker_queue.celery_app import app
from docker_queue.redis_client import (
    get_container_set_key, redis_client,
    get_queue_length
)
from docker_queue.tasks.container_management import (
    create_and_initialize_container,
    terminate_container
)
from docker_queue.config import (
    POOL_TARGET_SIZE, POOL_MIN_AVAILABLE,
    POOL_MAX_SIZE
)

# Configure logging
logger = logging.getLogger(__name__)

@app.task(queue='pool_management')
def maintain_container_pool(target_size=POOL_TARGET_SIZE, min_available=POOL_MIN_AVAILABLE):
    """
    Maintain a pool of available containers.
    
    Args:
        target_size: Target total pool size
        min_available: Minimum number of available containers
        
    Returns:
        Dictionary with pool status
    """
    try:
        # Get current pool status
        available_containers = redis_client.scard(get_container_set_key('available_containers'))
        assigned_containers = redis_client.scard(get_container_set_key('assigned_containers'))
        recycling_containers = redis_client.scard(get_container_set_key('recycling_containers'))
        total_containers = available_containers + assigned_containers + recycling_containers
        
        # Get queue length
        queue_length = get_queue_length()
        
        # Calculate how many containers to create
        to_create = 0
        
        # If we have pending requests, create containers for them
        if queue_length > 0:
            to_create += min(queue_length, POOL_MAX_SIZE - total_containers)
        
        # If we have fewer than minimum available containers, create more
        if available_containers < min_available:
            to_create += min(min_available - available_containers, POOL_MAX_SIZE - total_containers - to_create)
        
        # If we're below target size and have capacity, create more containers
        if total_containers < target_size and total_containers + to_create < POOL_MAX_SIZE:
            to_create += min(target_size - total_containers, POOL_MAX_SIZE - total_containers - to_create)
        
        # Create containers
        for _ in range(to_create):
            create_and_initialize_container.delay()
        
        # Log pool status
        logger.info(f"Pool status: {available_containers} available, {assigned_containers} assigned, "
                   f"{recycling_containers} recycling, {queue_length} queued, creating {to_create}")
        
        return {
            'available_containers': available_containers,
            'assigned_containers': assigned_containers,
            'recycling_containers': recycling_containers,
            'total_containers': total_containers,
            'queue_length': queue_length,
            'containers_to_create': to_create
        }
    
    except Exception as e:
        logger.error(f"Error maintaining container pool: {str(e)}")
        return {
            'error': str(e)
        }

@app.task(queue='pool_management')
def scale_container_pool(new_target_size):
    """
    Scale the container pool to a new target size.
    
    Args:
        new_target_size: New target pool size
        
    Returns:
        Dictionary with scaling status
    """
    try:
        # Get current pool status
        available_containers = redis_client.scard(get_container_set_key('available_containers'))
        assigned_containers = redis_client.scard(get_container_set_key('assigned_containers'))
        recycling_containers = redis_client.scard(get_container_set_key('recycling_containers'))
        total_containers = available_containers + assigned_containers + recycling_containers
        
        # Calculate how many containers to create or remove
        if new_target_size > total_containers:
            # Scale up
            to_create = new_target_size - total_containers
            
            # Create containers
            for _ in range(to_create):
                create_and_initialize_container.delay()
            
            logger.info(f"Scaling up pool from {total_containers} to {new_target_size} containers")
            
            return {
                'action': 'scale_up',
                'from': total_containers,
                'to': new_target_size,
                'containers_to_create': to_create
            }
        
        elif new_target_size < total_containers:
            # Scale down (only remove available containers)
            to_remove = min(total_containers - new_target_size, available_containers)
            
            # Get available containers
            available_container_ids = list(redis_client.smembers(get_container_set_key('available_containers')))
            
            # Remove containers
            for i in range(to_remove):
                if i < len(available_container_ids):
                    terminate_container.delay(available_container_ids[i])
            
            logger.info(f"Scaling down pool from {total_containers} to {new_target_size} containers")
            
            return {
                'action': 'scale_down',
                'from': total_containers,
                'to': new_target_size,
                'containers_to_remove': to_remove
            }
        
        else:
            # No change needed
            logger.info(f"Pool already at target size: {total_containers} containers")
            
            return {
                'action': 'no_change',
                'current_size': total_containers
            }
    
    except Exception as e:
        logger.error(f"Error scaling container pool: {str(e)}")
        return {
            'error': str(e)
        }

@app.task(queue='pool_management')
def predict_pool_size():
    """
    Predict the optimal pool size based on usage patterns.
    
    Returns:
        Predicted optimal pool size
    """
    try:
        # Get current time
        current_time = time.time()
        current_hour = time.localtime(current_time).tm_hour
        current_day = time.localtime(current_time).tm_wday  # 0 = Monday, 6 = Sunday
        
        # Get historical usage data (simplified example)
        # In a real implementation, this would analyze actual usage patterns
        
        # Example: Higher demand during business hours on weekdays
        if current_day < 5:  # Weekday
            if 9 <= current_hour < 17:  # Business hours
                predicted_size = int(POOL_TARGET_SIZE * 1.5)  # 50% more containers
            elif 17 <= current_hour < 22:  # Evening
                predicted_size = int(POOL_TARGET_SIZE * 1.2)  # 20% more containers
            else:  # Night
                predicted_size = int(POOL_TARGET_SIZE * 0.7)  # 30% fewer containers
        else:  # Weekend
            if 10 <= current_hour < 22:  # Daytime
                predicted_size = int(POOL_TARGET_SIZE * 1.3)  # 30% more containers
            else:  # Night
                predicted_size = int(POOL_TARGET_SIZE * 0.8)  # 20% fewer containers
        
        # Ensure predicted size is within limits
        predicted_size = max(POOL_MIN_AVAILABLE, min(predicted_size, POOL_MAX_SIZE))
        
        logger.info(f"Predicted optimal pool size: {predicted_size} containers")
        
        # Scale pool to predicted size
        scale_container_pool.delay(predicted_size)
        
        return predicted_size
    
    except Exception as e:
        logger.error(f"Error predicting pool size: {str(e)}")
        return POOL_TARGET_SIZE  # Default to target size on error

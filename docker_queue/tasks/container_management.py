"""
Container management tasks for BahtBrowse Docker Queue Management.
"""

import logging
import time
import uuid
from docker_queue.celery_app import app
from docker_queue.docker_client import (
    create_container, stop_container, remove_container,
    restart_container, get_container_status, execute_command
)
from docker_queue.redis_client import (
    add_container_to_pool, get_available_container,
    assign_container_to_user, get_user_container,
    get_container_details, mark_container_for_recycling,
    return_container_to_pool, remove_container_from_pool,
    get_next_container_request, remove_container_request,
    get_container_set_key, redis_client
)
from docker_queue.config import CONTAINER_IDLE_TIMEOUT

# Configure logging
logger = logging.getLogger(__name__)

@app.task(bind=True, max_retries=3, queue='container_management')
def create_and_initialize_container(self, browser_type=None, os_type=None, for_request=None):
    """
    Create and initialize a new Docker container.

    Args:
        browser_type: Optional browser type ('firefox', 'chromium', etc.)
        os_type: Optional OS type ('ubuntu', 'alpine', etc.)
        for_request: Optional request ID this container is being created for

    Returns:
        Container ID if successful
    """
    try:
        # Create container with specified browser type and OS type
        container_details = create_container(browser_type=browser_type, os_type=os_type)
        container_id = container_details['container_id']
        docker_id = container_details['docker_id']

        logger.info(f"Created container {container_id} (Docker ID: {docker_id}, Browser: {browser_type or 'default'}, OS: {os_type or 'default'})")

        # Initialize container
        container_details['status'] = 'available'
        container_details['initialized_at'] = time.time()

        # Add to container pool
        add_container_to_pool(container_id, container_details)

        # If this is for a specific request, process it
        if for_request:
            process_container_request.delay(for_request, container_id)

        return container_id

    except Exception as exc:
        logger.error(f"Error creating container: {str(exc)}")
        self.retry(exc=exc, countdown=5)

@app.task(bind=True, max_retries=3, queue='container_management')
def assign_container_to_request(self, request_id, user_id, browser_type=None, os_type=None, priority=0):
    """
    Assign a container to a user request.

    Args:
        request_id: Request ID
        user_id: User ID
        browser_type: Optional browser type ('firefox', 'chromium', etc.)
        os_type: Optional OS type ('ubuntu', 'alpine', etc.)
        priority: Request priority

    Returns:
        Container details if assigned, None if queued
    """
    try:
        # Check if user already has a container
        existing_container_id = get_user_container(user_id)
        if existing_container_id:
            logger.info(f"User {user_id} already has container {existing_container_id}")
            return get_container_details(existing_container_id)

        # Try to get an available container of the requested browser type
        container_id = get_available_container(browser_type=browser_type)

        if container_id:
            # Container available, assign it
            logger.info(f"Assigning container {container_id} to user {user_id}")
            container_details = assign_container_to_user(container_id, user_id)

            # Return container details
            return container_details
        else:
            # No container available, create a new one
            logger.info(f"No available {browser_type or 'default'} container on {os_type or 'default'}, creating new one for request {request_id}")
            create_and_initialize_container.delay(browser_type=browser_type, os_type=os_type, for_request=request_id)

            # Return None to indicate request is queued
            return None

    except Exception as exc:
        logger.error(f"Error assigning container: {str(exc)}")
        self.retry(exc=exc, countdown=5)

@app.task(bind=True, max_retries=3, queue='container_management')
def process_container_request(self, request_id, container_id=None):
    """
    Process a container request.

    Args:
        request_id: Request ID
        container_id: Optional container ID to assign

    Returns:
        True if request processed successfully
    """
    try:
        # Get request data
        request_data = get_next_container_request()

        if not request_data:
            logger.warning(f"Request {request_id} not found in queue")
            return False

        user_id = request_data['user_id']
        browser_type = request_data.get('browser_type')
        os_type = request_data.get('os_type')

        # If container ID provided, assign it
        if container_id:
            logger.info(f"Assigning container {container_id} to user {user_id}")
            assign_container_to_user(container_id, user_id)
        else:
            # Try to get an available container of the requested browser type
            container_id = get_available_container(browser_type=browser_type)

            if container_id:
                logger.info(f"Assigning container {container_id} to user {user_id}")
                assign_container_to_user(container_id, user_id)
            else:
                # No container available, create a new one
                logger.info(f"No available {browser_type or 'default'} container on {os_type or 'default'}, creating new one for request {request_id}")
                create_and_initialize_container.delay(browser_type=browser_type, os_type=os_type, for_request=request_id)
                return False

        # Remove request from queue
        remove_container_request(request_id)

        return True

    except Exception as exc:
        logger.error(f"Error processing container request: {str(exc)}")
        self.retry(exc=exc, countdown=5)

@app.task(bind=True, max_retries=3, queue='container_management')
def recycle_container(self, container_id):
    """
    Recycle a container by cleaning it and returning to the pool.

    Args:
        container_id: Container ID

    Returns:
        True if successful
    """
    try:
        # Get container details
        container_details = get_container_details(container_id)

        if not container_details:
            logger.warning(f"Container {container_id} not found")
            return False

        docker_id = container_details.get('docker_id')
        user_id = container_details.get('user_id', 'unknown')
        browser_type = container_details.get('browser_type', 'unknown')

        if not docker_id:
            logger.warning(f"Docker ID not found for container {container_id}")
            return False

        # Log container recycling
        logger.info(f"CONTAINER RECYCLING: Starting recycling for container {container_id} "
                   f"(User: {user_id}, Browser: {browser_type}, Docker ID: {docker_id})")

        # Mark container for recycling
        mark_container_for_recycling(container_id)

        # Restart container to clean state
        restart_container(docker_id)

        # Execute cleanup commands with logging
        logger.debug(f"Executing cleanup commands for container {container_id}")
        execute_command(docker_id, "rm -rf /tmp/*")
        execute_command(docker_id, "rm -rf /home/<USER>/.cache/*")
        execute_command(docker_id, "rm -rf /home/<USER>/.mozilla/firefox/*.default/cache2/*")
        execute_command(docker_id, "rm -rf /home/<USER>/.config/chromium/Default/Cache/*")

        # Return container to pool
        return_container_to_pool(container_id)

        logger.info(f"CONTAINER RECYCLED: Container {container_id} successfully recycled and returned to pool")

        # Process next request in queue
        process_next_request.delay()

        # Trigger pool maintenance to ensure we have enough containers
        from docker_queue.tasks.pool_management import maintain_container_pool
        maintain_container_pool.delay()

        return True

    except Exception as exc:
        logger.error(f"Error recycling container: {str(exc)}")
        self.retry(exc=exc, countdown=5)

@app.task(bind=True, max_retries=3, queue='container_management')
def terminate_container(self, container_id):
    """
    Terminate a container and remove it from the pool.

    Args:
        container_id: Container ID

    Returns:
        True if successful
    """
    try:
        # Get container details
        container_details = get_container_details(container_id)

        if not container_details:
            logger.warning(f"Container {container_id} not found")
            return False

        docker_id = container_details.get('docker_id')

        if not docker_id:
            logger.warning(f"Docker ID not found for container {container_id}")
            return False

        # Stop and remove container
        stop_container(docker_id)
        remove_container(docker_id)

        # Remove from pool
        remove_container_from_pool(container_id)

        logger.info(f"Terminated container {container_id}")

        return True

    except Exception as exc:
        logger.error(f"Error terminating container: {str(exc)}")
        self.retry(exc=exc, countdown=5)

@app.task(bind=True, queue='container_management')
def process_next_request(self):
    """
    Process the next container request in the queue.

    Returns:
        True if request processed, False if queue empty
    """
    try:
        # Get next request
        request_data = get_next_container_request()

        if not request_data:
            logger.info("No pending container requests")
            return False

        request_id = request_data['request_id']

        # Process request
        return process_container_request(request_id)

    except Exception as exc:
        logger.error(f"Error processing next request: {str(exc)}")
        return False

@app.task(bind=True, queue='container_management')
def cleanup_idle_containers(self, idle_timeout=CONTAINER_IDLE_TIMEOUT):
    """
    Clean up idle containers.

    Args:
        idle_timeout: Idle timeout in seconds

    Returns:
        Number of containers recycled
    """
    try:
        # Get all assigned containers
        assigned_containers = redis_client.smembers(
            get_container_set_key('assigned_containers')
        )

        count = 0
        current_time = time.time()
        logger.debug(f"Checking {len(assigned_containers)} containers for inactivity (timeout: {idle_timeout} seconds)")

        for container_id in assigned_containers:
            container_details = get_container_details(container_id)

            if not container_details:
                logger.warning(f"Container {container_id} details not found, skipping")
                continue

            # Check if container is idle
            assigned_at = float(container_details.get('assigned_at', 0))
            last_activity = float(container_details.get('last_activity', assigned_at))
            user_id = container_details.get('user_id', 'unknown')
            browser_type = container_details.get('browser_type', 'unknown')

            idle_time = current_time - last_activity

            # Log container activity status
            if idle_time > idle_timeout:
                logger.info(f"INACTIVITY TIMEOUT: Container {container_id} (User: {user_id}, Browser: {browser_type}) "
                           f"idle for {idle_time:.2f} seconds, exceeds timeout of {idle_timeout} seconds")

                # Send termination message to logs
                logger.info(f"CONTAINER TERMINATION: Recycling container {container_id} due to inactivity timeout")

                # Recycle the container
                recycle_container.delay(container_id)
                count += 1
            else:
                logger.debug(f"Container {container_id} (User: {user_id}) active, idle for {idle_time:.2f} seconds")

        if count > 0:
            logger.info(f"Recycled {count} idle containers due to inactivity timeout")

            # Trigger pool maintenance to ensure new containers are created if needed
            from docker_queue.tasks.pool_management import maintain_container_pool
            maintain_container_pool.delay()

        return count

    except Exception as exc:
        logger.error(f"Error cleaning up idle containers: {str(exc)}")
        return 0

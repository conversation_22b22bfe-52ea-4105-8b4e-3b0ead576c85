"""
Celery application configuration for 10Baht bahtBrowse Docker Queue Management.

This module configures the Celery application with ELK stack integration for
comprehensive logging and monitoring.
"""

import os
import logging
from celery import Celery
from docker_queue.elk_integration import init_elk_integration

# Configure logger
logger = logging.getLogger(__name__)

# Check if we're using fakeredis
USE_FAKEREDIS = os.environ.get('USE_FAKEREDIS', 'true').lower() == 'true'

# Create and configure Celery application
def create_celery_app():
    """
    Create and configure a Celery application with ELK stack integration.

    Returns:
        Configured Celery application
    """
    # Create Celery application
    if USE_FAKEREDIS:
        # Use SQLite broker for testing
        app = Celery(
            'docker_queue',
            broker='sqla+sqlite:///celery-broker.sqlite',
            backend='db+sqlite:///celery-results.sqlite',
            include=[
                'docker_queue.tasks.container_management',
                'docker_queue.tasks.pool_management',
                'docker_queue.tasks.monitoring'
            ]
        )
    else:
        app = Celery(
            'docker_queue',
            broker=os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
            backend=os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
            include=[
                'docker_queue.tasks.container_management',
                'docker_queue.tasks.pool_management',
                'docker_queue.tasks.monitoring'
            ]
        )

    # Configure Celery settings
    app.conf.update(
        # Task settings
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        worker_hijack_root_logger=False,  # Don't hijack root logger
        worker_log_format='%(message)s',  # Use plain message format for JSON logging
        worker_task_log_format='%(message)s',  # Use plain message format for JSON logging

        # Queue settings
        task_queues={
            'container_management': {
                'exchange': 'container_management',
                'routing_key': 'container_management',
            },
            'pool_management': {
                'exchange': 'pool_management',
                'routing_key': 'pool_management',
            },
            'monitoring': {
                'exchange': 'monitoring',
                'routing_key': 'monitoring',
            },
        },

        # Task routing
        task_routes={
            'docker_queue.tasks.container_management.*': {'queue': 'container_management'},
            'docker_queue.tasks.pool_management.*': {'queue': 'pool_management'},
            'docker_queue.tasks.monitoring.*': {'queue': 'monitoring'},
        },

        # Beat schedule for periodic tasks
        beat_schedule={
            'maintain-container-pool': {
                'task': 'docker_queue.tasks.pool_management.maintain_container_pool',
                'schedule': 60.0,  # Every minute
                'args': (10, 3),  # target_size, min_available
            },
            'collect-metrics': {
                'task': 'docker_queue.tasks.monitoring.collect_metrics',
                'schedule': 30.0,  # Every 30 seconds
            },
            'cleanup-idle-containers': {
                'task': 'docker_queue.tasks.container_management.cleanup_idle_containers',
                'schedule': 60.0,  # Every 1 minute
                'args': (180,),  # idle_timeout in seconds (3 minutes)
            },
        },
    )

    # Initialize ELK stack integration
    init_elk_integration(app=app)

    # Log application creation
    logger.info(
        "Celery application created with ELK stack integration",
        extra={
            "service": "celery",
            "event_type": "application_created"
        }
    )

    return app

# Create Celery application
app = create_celery_app()

if __name__ == '__main__':
    app.start()

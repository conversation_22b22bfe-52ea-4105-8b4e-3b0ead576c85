"""
Redis client for BahtBrowse Docker Queue Management.
"""

import json
import time
import os
from docker_queue.config import (
    REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_PASSWORD,
    REDIS_KEY_PREFIXES
)
from docker_queue.config.browser_pool_config import SUPPORTED_BROWSER_TYPES

# Use fakeredis in development/testing environments
if os.environ.get('USE_FAKEREDIS', 'true').lower() == 'true':
    import fakeredis
    redis_client = fakeredis.FakeRedis(decode_responses=True)
    print("Using fakeredis for development/testing")
else:
    import redis
    # Create Redis client
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        password=REDIS_PASSWORD,
        decode_responses=True
    )

# Helper functions for Redis operations

def get_container_key(container_id):
    """Get Redis key for container data."""
    return f"{REDIS_KEY_PREFIXES['container']}{container_id}"

def get_user_key(user_id):
    """Get Redis key for user data."""
    return f"{REDIS_KEY_PREFIXES['user']}{user_id}"

def get_request_key(request_id):
    """Get Redis key for request data."""
    return f"{REDIS_KEY_PREFIXES['request']}{request_id}"

def get_metrics_key(metric_type):
    """Get Redis key for metrics data."""
    return f"{REDIS_KEY_PREFIXES['metrics']}{metric_type}"

def get_container_set_key(set_name):
    """Get Redis key for container set."""
    return REDIS_KEY_PREFIXES['sets'][set_name]

def get_browser_container_set_key(browser_type, set_name):
    """Get Redis key for browser-specific container set."""
    return f"{REDIS_KEY_PREFIXES['sets'][set_name]}:{browser_type}"

def get_queue_key(queue_name):
    """Get Redis key for queue."""
    return REDIS_KEY_PREFIXES['queues'][queue_name]

# Container pool operations

def add_container_to_pool(container_id, container_data):
    """
    Add a container to the pool.

    Args:
        container_id: Container ID
        container_data: Dictionary of container data
    """
    # Add to all containers set
    redis_client.sadd(get_container_set_key('all_containers'), container_id)

    # Add to available containers set
    redis_client.sadd(get_container_set_key('available_containers'), container_id)

    # If browser_type is specified, add to browser-specific sets
    browser_type = container_data.get('browser_type')
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        # Add to browser-specific all containers set
        redis_client.sadd(
            get_browser_container_set_key(browser_type, 'all_containers'),
            container_id
        )

        # Add to browser-specific available containers set
        redis_client.sadd(
            get_browser_container_set_key(browser_type, 'available_containers'),
            container_id
        )

    # Store container data
    redis_client.hset(get_container_key(container_id), mapping=container_data)

def get_available_container(browser_type=None):
    """
    Get an available container from the pool.

    Args:
        browser_type: Optional browser type to filter by

    Returns:
        Container ID or None if no container available
    """
    container_id = None

    # If browser_type is specified, try to get a container of that type
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        container_id = redis_client.spop(
            get_browser_container_set_key(browser_type, 'available_containers')
        )

    # If no browser-specific container available or no browser_type specified,
    # get any available container
    if not container_id:
        container_id = redis_client.spop(get_container_set_key('available_containers'))

    if container_id:
        # Add to assigned containers set
        redis_client.sadd(get_container_set_key('assigned_containers'), container_id)

        # If browser_type is known, add to browser-specific assigned containers set
        container_data = get_container_details(container_id)
        browser_type = container_data.get('browser_type')
        if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
            redis_client.sadd(
                get_browser_container_set_key(browser_type, 'assigned_containers'),
                container_id
            )

        # Update container data
        redis_client.hset(get_container_key(container_id), 'status', 'assigned')
        redis_client.hset(get_container_key(container_id), 'assigned_at', time.time())

    return container_id

def assign_container_to_user(container_id, user_id):
    """
    Assign a container to a user.

    Args:
        container_id: Container ID
        user_id: User ID

    Returns:
        Container details if successful, None otherwise
    """
    # Update container data
    redis_client.hset(get_container_key(container_id), 'user_id', user_id)

    # Update user data
    redis_client.hset(get_user_key(user_id), 'container_id', container_id)
    redis_client.hset(get_user_key(user_id), 'session_start', time.time())

    # Get container details
    container_details = get_container_details(container_id)

    # Update usage count
    usage_count = int(container_details.get('usage_count', 0)) + 1
    redis_client.hset(get_container_key(container_id), 'usage_count', usage_count)

    return container_details

def get_user_container(user_id):
    """
    Get the container assigned to a user.

    Args:
        user_id: User ID

    Returns:
        Container ID or None if no container assigned
    """
    return redis_client.hget(get_user_key(user_id), 'container_id')

def update_container_activity(container_id):
    """
    Update the last activity timestamp for a container.

    Args:
        container_id: Container ID

    Returns:
        True if successful, False otherwise
    """
    try:
        # Update last activity timestamp
        redis_client.hset(get_container_key(container_id), 'last_activity', time.time())
        return True
    except Exception:
        return False

def get_container_details(container_id):
    """
    Get container details.

    Args:
        container_id: Container ID

    Returns:
        Dictionary of container data
    """
    return redis_client.hgetall(get_container_key(container_id))

def mark_container_for_recycling(container_id):
    """
    Mark a container for recycling.

    Args:
        container_id: Container ID
    """
    # Get container details to check browser type
    container_details = get_container_details(container_id)
    browser_type = container_details.get('browser_type')

    # Remove from assigned containers set
    redis_client.srem(get_container_set_key('assigned_containers'), container_id)

    # If browser_type is known, remove from browser-specific assigned containers set
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'assigned_containers'),
            container_id
        )

    # Add to recycling containers set
    redis_client.sadd(get_container_set_key('recycling_containers'), container_id)

    # If browser_type is known, add to browser-specific recycling containers set
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        redis_client.sadd(
            get_browser_container_set_key(browser_type, 'recycling_containers'),
            container_id
        )

    # Update container data
    redis_client.hset(get_container_key(container_id), 'status', 'recycling')
    redis_client.hset(get_container_key(container_id), 'recycling_start', time.time())

    # Record last used time
    redis_client.hset(get_container_key(container_id), 'last_used_at', time.time())

def return_container_to_pool(container_id):
    """
    Return a container to the available pool after recycling.

    Args:
        container_id: Container ID
    """
    # Get container details to check browser type
    container_details = get_container_details(container_id)
    browser_type = container_details.get('browser_type')

    # Remove from recycling containers set
    redis_client.srem(get_container_set_key('recycling_containers'), container_id)

    # If browser_type is known, remove from browser-specific recycling containers set
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'recycling_containers'),
            container_id
        )

    # Add to available containers set
    redis_client.sadd(get_container_set_key('available_containers'), container_id)

    # If browser_type is known, add to browser-specific available containers set
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        redis_client.sadd(
            get_browser_container_set_key(browser_type, 'available_containers'),
            container_id
        )

    # Update container data
    redis_client.hset(get_container_key(container_id), 'status', 'available')
    redis_client.hdel(get_container_key(container_id), 'user_id')
    redis_client.hdel(get_container_key(container_id), 'assigned_at')
    redis_client.hdel(get_container_key(container_id), 'recycling_start')

def remove_container_from_pool(container_id):
    """
    Remove a container from the pool.

    Args:
        container_id: Container ID
    """
    # Get container details to check browser type
    container_details = get_container_details(container_id)
    browser_type = container_details.get('browser_type')

    # Remove from all sets
    redis_client.srem(get_container_set_key('all_containers'), container_id)
    redis_client.srem(get_container_set_key('available_containers'), container_id)
    redis_client.srem(get_container_set_key('assigned_containers'), container_id)
    redis_client.srem(get_container_set_key('recycling_containers'), container_id)

    # If browser_type is known, remove from browser-specific sets
    if browser_type and browser_type in SUPPORTED_BROWSER_TYPES:
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'all_containers'),
            container_id
        )
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'available_containers'),
            container_id
        )
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'assigned_containers'),
            container_id
        )
        redis_client.srem(
            get_browser_container_set_key(browser_type, 'recycling_containers'),
            container_id
        )

    # Delete container data
    redis_client.delete(get_container_key(container_id))

# Request queue operations

def add_container_request(request_id, user_id, browser_type=None, priority=0):
    """
    Add a container request to the queue.

    Args:
        request_id: Request ID
        user_id: User ID
        browser_type: Optional browser type
        priority: Request priority (0-9, higher is more important)
    """
    # Create request data
    request_data = {
        'request_id': request_id,
        'user_id': user_id,
        'priority': priority,
        'timestamp': time.time()
    }

    # Add browser_type if specified
    if browser_type:
        request_data['browser_type'] = browser_type

    # Store request data
    redis_client.hset(get_request_key(request_id), mapping=request_data)

    # Add to request queue with priority
    redis_client.zadd(
        get_queue_key('container_requests'),
        {request_id: -priority}  # Negative priority so higher priority comes first
    )

def get_next_container_request():
    """
    Get the next container request from the queue.

    Returns:
        Request data or None if queue is empty
    """
    # Get the highest priority request
    result = redis_client.zrange(
        get_queue_key('container_requests'),
        0, 0,  # Get the first item
        withscores=False
    )

    if result:
        request_id = result[0]
        # Get request data
        request_data = redis_client.hgetall(get_request_key(request_id))
        return request_data

    return None

def remove_container_request(request_id):
    """
    Remove a container request from the queue.

    Args:
        request_id: Request ID
    """
    # Remove from request queue
    redis_client.zrem(get_queue_key('container_requests'), request_id)

    # Delete request data
    redis_client.delete(get_request_key(request_id))

def get_request_position(request_id):
    """
    Get the position of a request in the queue.

    Args:
        request_id: Request ID

    Returns:
        Position in queue (0-based) or None if not in queue
    """
    # Get the rank of the request in the queue
    rank = redis_client.zrank(get_queue_key('container_requests'), request_id)

    # Add 1 to convert to 1-based position
    return rank + 1 if rank is not None else None

def get_queue_length():
    """
    Get the length of the request queue.

    Returns:
        Number of requests in queue
    """
    return redis_client.zcard(get_queue_key('container_requests'))

# Browser-specific container operations

def get_containers_by_browser_type(browser_type):
    """
    Get all containers of a specific browser type.

    Args:
        browser_type: Browser type

    Returns:
        Dictionary of container IDs to container details
    """
    if browser_type not in SUPPORTED_BROWSER_TYPES:
        return {}

    # Get all containers of this browser type
    container_ids = redis_client.smembers(
        get_browser_container_set_key(browser_type, 'all_containers')
    )

    # Get details for each container
    containers = {}
    for container_id in container_ids:
        containers[container_id] = get_container_details(container_id)

    return containers

def get_browser_pool_status():
    """
    Get the status of the container pool by browser type.

    Returns:
        Dictionary with browser types as keys and status as values
    """
    status = {}

    for browser_type in SUPPORTED_BROWSER_TYPES:
        # Get counts for this browser type
        all_count = redis_client.scard(
            get_browser_container_set_key(browser_type, 'all_containers')
        )
        available_count = redis_client.scard(
            get_browser_container_set_key(browser_type, 'available_containers')
        )
        assigned_count = redis_client.scard(
            get_browser_container_set_key(browser_type, 'assigned_containers')
        )
        recycling_count = redis_client.scard(
            get_browser_container_set_key(browser_type, 'recycling_containers')
        )

        # Store status for this browser type
        status[browser_type] = {
            'total_containers': all_count,
            'available_containers': available_count,
            'assigned_containers': assigned_count,
            'recycling_containers': recycling_count
        }

    return status

# Metrics operations

def store_metrics(metrics_data):
    """
    Store metrics data.

    Args:
        metrics_data: Dictionary of metrics data
    """
    timestamp = metrics_data.get('timestamp', time.time())

    # Store metrics with timestamp as score
    redis_client.zadd(
        get_metrics_key('timeseries'),
        {json.dumps(metrics_data): timestamp}
    )

def get_latest_metrics():
    """
    Get the latest metrics data.

    Returns:
        Dictionary of metrics data
    """
    # Get the latest metrics
    result = redis_client.zrevrange(
        get_metrics_key('timeseries'),
        0, 0,  # Get the first item
        withscores=False
    )

    if result:
        return json.loads(result[0])

    return None

def get_metrics_range(start_time, end_time):
    """
    Get metrics data within a time range.

    Args:
        start_time: Start timestamp
        end_time: End timestamp

    Returns:
        List of metrics data dictionaries
    """
    # Get metrics within time range
    result = redis_client.zrangebyscore(
        get_metrics_key('timeseries'),
        start_time, end_time,
        withscores=False
    )

    return [json.loads(item) for item in result]

def cleanup_old_metrics(retention_seconds):
    """
    Clean up old metrics data.

    Args:
        retention_seconds: Retention period in seconds
    """
    # Calculate cutoff time
    cutoff_time = time.time() - retention_seconds

    # Remove metrics older than cutoff time
    redis_client.zremrangebyscore(
        get_metrics_key('timeseries'),
        0, cutoff_time
    )

"""
Custom configuration for Celery Flower dashboard with dark mode support.
"""

import os
from pathlib import Path

# Get the directory of this file
current_dir = Path(__file__).parent.absolute()

# Paths to static files
dark_theme_css = os.path.join(current_dir, 'flower_dark_theme.css')
dark_mode_js = os.path.join(current_dir, 'flower_dark_mode.js')

# Flower configuration
flower_conf = {
    # Basic configuration
    'port': 5555,
    'address': '0.0.0.0',
    'debug': False,
    'broker_api': os.environ.get('BROKER_API', 'redis://redis:6379/0'),
    
    # UI configuration
    'url_prefix': '',
    'persistent': True,
    'db': 'flower',
    'max_tasks': 10000,
    'format_task': None,
    
    # Authentication
    'auth': None,
    'basic_auth': None,
    
    # Custom static files
    'static_folder': None,
    
    # Custom JavaScript and CSS files to include
    'extra_static_folders': [current_dir],
    
    # Custom JavaScript to inject
    'extra_javascript': ['/static/flower_dark_mode.js'],
    
    # Custom CSS to inject
    'extra_stylesheets': [],
    
    # Task columns to display
    'tasks_columns': [
        'name', 'uuid', 'state', 'args', 'kwargs', 'result',
        'received', 'started', 'runtime', 'worker'
    ],
    
    # Enable natural time formatting
    'natural_time': True,
    
    # Enable task auto-refresh
    'auto_refresh': True,
    
    # Refresh interval in milliseconds
    'refresh_interval': 5000,
}

# Function to copy static files to Flower's static directory
def setup_flower_static_files(app):
    """
    Copy dark mode CSS and JS files to Flower's static directory.
    
    Args:
        app: Flower application instance
    """
    import shutil
    from tornado.web import StaticFileHandler
    
    # Create static directory if it doesn't exist
    static_dir = os.path.join(app.settings.get('static_path', ''), 'flower_static')
    os.makedirs(static_dir, exist_ok=True)
    
    # Copy dark theme CSS
    shutil.copy2(dark_theme_css, os.path.join(static_dir, 'flower_dark_theme.css'))
    
    # Copy dark mode JS
    shutil.copy2(dark_mode_js, os.path.join(static_dir, 'flower_dark_mode.js'))
    
    # Add static file handler
    app.add_handlers(
        r'.*',
        [
            (
                r'/static/(.*)',
                StaticFileHandler,
                {'path': static_dir}
            )
        ]
    )

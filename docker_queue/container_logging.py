"""
Docker container logging utilities for ELK stack integration.

This module provides utilities for configuring Docker containers with appropriate
logging settings for integration with the ELK stack.
"""

import json
import os
import logging
from typing import Dict, Any, Optional

# Configure logger
logger = logging.getLogger(__name__)

def get_container_logging_config(
    container_type: str,
    container_id: str,
    browser_type: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Generate Docker container logging configuration for ELK stack integration.
    
    Args:
        container_type: Type of container (browser, api, worker)
        container_id: Unique identifier for the container
        browser_type: Type of browser (firefox, chromium) for browser containers
        session_id: Session ID associated with the container
        user_id: User ID associated with the container
    
    Returns:
        Dictionary with Docker logging configuration
    """
    # Base logging configuration
    logging_config = {
        "driver": "json-file",
        "options": {
            "max-size": "10m",
            "max-file": "3",
            "labels": f"{container_type}-container",
            "tag": "{{.Name}}/{{.ID}}",
            "env": "CONTAINER_ID"
        }
    }
    
    # Add environment variables to capture in logs
    env_vars = ["CONTAINER_ID"]
    
    # Add container-specific environment variables
    if container_type == "browser":
        env_vars.extend(["BROWSER_TYPE", "SESSION_ID"])
        if browser_type:
            env_vars.append("BROWSER_TYPE")
        if session_id:
            env_vars.append("SESSION_ID")
    
    if user_id:
        env_vars.append("USER_ID")
    
    # Update environment variables in logging options
    logging_config["options"]["env"] = ",".join(env_vars)
    
    return logging_config

def get_container_labels(
    container_type: str,
    container_id: str,
    browser_type: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> Dict[str, str]:
    """
    Generate Docker container labels for ELK stack integration.
    
    Args:
        container_type: Type of container (browser, api, worker)
        container_id: Unique identifier for the container
        browser_type: Type of browser (firefox, chromium) for browser containers
        session_id: Session ID associated with the container
        user_id: User ID associated with the container
    
    Returns:
        Dictionary with Docker container labels
    """
    # Base labels
    labels = {
        "app": "bahtbrowse",
        "component": container_type,
        "container_id": container_id
    }
    
    # Add container-specific labels
    if container_type == "browser" and browser_type:
        labels["browser_type"] = browser_type
    
    if session_id:
        labels["session_id"] = session_id
    
    if user_id:
        labels["user_id"] = user_id
    
    return labels

def configure_container_for_elk(
    docker_client,
    container_config: Dict[str, Any],
    container_type: str,
    container_id: str,
    browser_type: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Configure a Docker container for ELK stack logging integration.
    
    This function adds appropriate logging configuration and labels to a Docker
    container configuration dictionary.
    
    Args:
        docker_client: Docker client instance
        container_config: Docker container configuration dictionary
        container_type: Type of container (browser, api, worker)
        container_id: Unique identifier for the container
        browser_type: Type of browser (firefox, chromium) for browser containers
        session_id: Session ID associated with the container
        user_id: User ID associated with the container
    
    Returns:
        Updated Docker container configuration dictionary
    """
    # Get logging configuration
    logging_config = get_container_logging_config(
        container_type, container_id, browser_type, session_id, user_id
    )
    
    # Get container labels
    labels = get_container_labels(
        container_type, container_id, browser_type, session_id, user_id
    )
    
    # Update container configuration
    container_config["hostConfig"] = container_config.get("hostConfig", {})
    container_config["hostConfig"]["LogConfig"] = logging_config
    
    # Update container labels
    container_config["Labels"] = {
        **container_config.get("Labels", {}),
        **labels
    }
    
    # Add environment variables for logging
    env_vars = container_config.get("Env", [])
    
    # Add container ID environment variable
    env_vars.append(f"CONTAINER_ID={container_id}")
    
    # Add container-specific environment variables
    if container_type == "browser" and browser_type:
        env_vars.append(f"BROWSER_TYPE={browser_type}")
    
    if session_id:
        env_vars.append(f"SESSION_ID={session_id}")
    
    if user_id:
        env_vars.append(f"USER_ID={user_id}")
    
    container_config["Env"] = env_vars
    
    return container_config

def log_container_event(
    event_type: str,
    container_id: str,
    container_name: str,
    container_type: str,
    browser_type: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log a container event in a format suitable for ELK stack processing.
    
    Args:
        event_type: Type of event (created, started, stopped, removed)
        container_id: Docker container ID
        container_name: Docker container name
        container_type: Type of container (browser, api, worker)
        browser_type: Type of browser (firefox, chromium) for browser containers
        session_id: Session ID associated with the container
        user_id: User ID associated with the container
        details: Additional event details
    """
    # Create event data
    event_data = {
        "event_type": f"container_{event_type}",
        "container_id": container_id,
        "container_name": container_name,
        "container_type": container_type,
        "service": "docker",
        "component": container_type
    }
    
    # Add container-specific data
    if container_type == "browser" and browser_type:
        event_data["browser_type"] = browser_type
    
    if session_id:
        event_data["session_id"] = session_id
    
    if user_id:
        event_data["user_id"] = user_id
    
    # Add additional details
    if details:
        event_data.update(details)
    
    # Log the event
    logger.info(
        f"Container {event_type}: {container_name} ({container_id})",
        extra=event_data
    )

def setup_container_logging_hooks(docker_client) -> None:
    """
    Set up hooks to log Docker container lifecycle events.
    
    This function sets up event listeners for Docker container events
    and logs them in a format suitable for ELK stack processing.
    
    Args:
        docker_client: Docker client instance
    """
    # This is a placeholder for actual Docker event listening implementation
    # In a real implementation, this would set up event listeners for Docker events
    # and call log_container_event for each event
    
    logger.info(
        "Container logging hooks set up for ELK stack integration",
        extra={
            "service": "docker",
            "event_type": "logging_initialized"
        }
    )

def init_container_logging() -> None:
    """
    Initialize container logging for ELK stack integration.
    
    This function should be called when the application starts.
    """
    # Configure JSON logging
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter(
        '{"timestamp":"%(asctime)s", "level":"%(levelname)s", "message":"%(message)s", '
        '"logger":"%(name)s", "path":"%(pathname)s", "line":%(lineno)d, '
        '"process":%(process)d, "thread":%(thread)d, %(extra)s}',
        '%Y-%m-%dT%H:%M:%S.%fZ'
    ))
    
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    # Log initialization
    logger.info(
        "Container logging initialized for ELK stack integration",
        extra={
            "service": "docker",
            "event_type": "logging_initialized"
        }
    )

"""
Configuration settings for BahtBrowse Docker Queue Management.
"""

import os

# Redis configuration
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)

# Docker configuration
DOCKER_BASE_URL = os.environ.get('DOCKER_BASE_URL', 'unix://var/run/docker.sock')
DOCKER_API_VERSION = os.environ.get('DOCKER_API_VERSION', '1.41')
DOCKER_TIMEOUT = int(os.environ.get('DOCKER_TIMEOUT', 30))

# Container configuration
CONTAINER_IMAGE = os.environ.get('CONTAINER_IMAGE', 'bahtbrowse:latest')
CONTAINER_CPU_LIMIT = float(os.environ.get('CONTAINER_CPU_LIMIT', 1.0))
CONTAINER_MEMORY_LIMIT = os.environ.get('CONTAINER_MEMORY_LIMIT', '2g')
CONTAINER_NETWORK = os.environ.get('CONTAINER_NETWORK', 'bahtbrowse')

# Pool configuration
POOL_TARGET_SIZE = int(os.environ.get('POOL_TARGET_SIZE', 10))
POOL_MIN_AVAILABLE = int(os.environ.get('POOL_MIN_AVAILABLE', 3))
POOL_MAX_SIZE = int(os.environ.get('POOL_MAX_SIZE', 50))
CONTAINER_IDLE_TIMEOUT = int(os.environ.get('CONTAINER_IDLE_TIMEOUT', 180))  # 3 minutes

# Browser-specific pool configuration
from docker_queue.config.browser_pool_config import (
    SUPPORTED_BROWSER_TYPES,
    DEFAULT_MIN_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_CONTAINERS_PER_BROWSER_TYPE,
    DEFAULT_MAX_TOTAL_CONTAINERS,
    BROWSER_IMAGES,
    BROWSER_RESOURCE_LIMITS,
    BROWSER_ENV_VARS
)

# Queue configuration
REQUEST_TIMEOUT = int(os.environ.get('REQUEST_TIMEOUT', 300))  # 5 minutes
MAX_QUEUE_SIZE = int(os.environ.get('MAX_QUEUE_SIZE', 100))

# Monitoring configuration
METRICS_RETENTION_DAYS = int(os.environ.get('METRICS_RETENTION_DAYS', 7))

# Redis key prefixes
REDIS_KEY_PREFIXES = {
    'container': 'bahtbrowse:container:',
    'user': 'bahtbrowse:user:',
    'request': 'bahtbrowse:request:',
    'metrics': 'bahtbrowse:metrics:',
    'sets': {
        'all_containers': 'bahtbrowse:containers:all',
        'available_containers': 'bahtbrowse:containers:available',
        'assigned_containers': 'bahtbrowse:containers:assigned',
        'recycling_containers': 'bahtbrowse:containers:recycling',
    },
    'queues': {
        'container_requests': 'bahtbrowse:queue:container_requests',
    }
}

"""
Celery logging configuration for ELK stack integration.

This module configures Celery to log task execution details, performance metrics,
and errors in a structured JSON format that can be easily processed by the ELK stack.
"""

import logging
import json
import socket
import time
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from celery.signals import (
    task_prerun, task_postrun, task_failure, task_retry,
    worker_ready, worker_shutdown, task_revoked
)
import psutil

# Create log directory if it doesn't exist
LOG_DIR = os.environ.get('BAHTBROWSE_LOG_DIR', os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs'))
os.makedirs(LOG_DIR, exist_ok=True)

# Configure logger
logger = logging.getLogger('celery.tasks')

# Dictionary to store task start times and resource usage
task_start_times = {}

class JsonFormatter(logging.Formatter):
    """
    Custom formatter to output logs in JSON format.
    """
    def format(self, record):
        log_record = {
            "timestamp": datetime.utcnow().isoformat() + 'Z',
            "level": record.levelname,
            "message": record.getMessage(),
            "logger": record.name,
            "path": record.pathname,
            "line": record.lineno,
            "process": record.process,
            "thread": record.thread,
            "host": socket.gethostname(),
            "service": "celery",
        }

        # Add exception info if available
        if record.exc_info:
            log_record["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }

        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key.startswith('_') or key in ('args', 'asctime', 'created', 'exc_info',
                                             'exc_text', 'filename', 'funcName',
                                             'levelname', 'levelno', 'lineno', 'module',
                                             'msecs', 'message', 'msg', 'name', 'pathname',
                                             'process', 'processName', 'relativeCreated',
                                             'stack_info', 'thread', 'threadName'):
                continue

            # Skip complex objects that can't be serialized to JSON
            try:
                json.dumps({key: value})
                log_record[key] = value
            except (TypeError, OverflowError):
                log_record[key] = str(value)

        return json.dumps(log_record)

def setup_logging():
    """
    Set up logging configuration for Celery.
    """
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Configure file handler
    log_file = os.path.join(LOG_DIR, 'celery.log')
    handler = RotatingFileHandler(log_file, maxBytes=10485760, backupCount=10)
    handler.setFormatter(JsonFormatter())

    # Add handler to logger
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

    # Also configure root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler):
            handler.setFormatter(JsonFormatter())

@task_prerun.connect
def task_prerun_handler(task_id, task, args, kwargs, **kw):
    """
    Handle task pre-run signal.

    This function is called before a task is executed and logs the task details.
    It also records the start time and initial resource usage for performance tracking.
    """
    # Record start time and initial resource usage
    task_start_times[task_id] = {
        'start_time': time.time(),
        'initial_cpu': psutil.cpu_percent(interval=None),
        'initial_memory': psutil.virtual_memory().percent
    }

    # Log task start
    logger.info(
        f"Task started: {task.name}",
        extra={
            "task_id": task_id,
            "task_name": task.name,
            "task_args": str(args),
            "task_kwargs": str(kwargs),
            "task_state": "STARTED",
            "event_type": "task_started"
        }
    )

@task_postrun.connect
def task_postrun_handler(task_id, task, args, kwargs, retval, state, **kw):
    """
    Handle task post-run signal.

    This function is called after a task is executed and logs the task result.
    It also calculates and logs performance metrics for the task.
    """
    # Calculate performance metrics if we have start data
    if task_id in task_start_times:
        end_time = time.time()
        start_data = task_start_times.pop(task_id)

        duration = end_time - start_data['start_time']
        cpu_usage = psutil.cpu_percent(interval=None) - start_data['initial_cpu']
        memory_usage = psutil.virtual_memory().percent - start_data['initial_memory']

        # Log task completion with performance metrics
        logger.info(
            f"Task completed with state: {state}",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "task_args": str(args),
                "task_kwargs": str(kwargs),
                "task_state": state,
                "task_result": str(retval),
                "task_runtime": duration,
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "event_type": "task_completed"
            }
        )
    else:
        # Log task completion without performance metrics
        logger.info(
            f"Task completed with state: {state}",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "task_args": str(args),
                "task_kwargs": str(kwargs),
                "task_state": state,
                "task_result": str(retval),
                "event_type": "task_completed"
            }
        )

@task_failure.connect
def task_failure_handler(task_id, exception, args, kwargs, traceback, einfo, **kw):
    """
    Handle task failure signal.

    This function is called when a task fails and logs the error details.
    """
    # Get task name from sender if available
    task_name = kwargs.get('sender', {}).name if kwargs.get('sender') else 'unknown'

    logger.error(
        f"Task failed: {str(exception)}",
        extra={
            "task_id": task_id,
            "task_name": task_name,
            "task_args": str(args),
            "task_kwargs": str(kwargs),
            "task_state": "FAILURE",
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "traceback": str(einfo) if einfo else None,
            "event_type": "task_failed"
        }
    )

@task_retry.connect
def task_retry_handler(request, reason, einfo, **kwargs):
    """
    Handle task retry signal.

    This function is called when a task is retried and logs the retry details.
    """
    logger.warning(
        f"Task retry: {reason}",
        extra={
            "task_id": request.id,
            "task_name": request.task,
            "task_args": str(request.args),
            "task_kwargs": str(request.kwargs),
            "task_state": "RETRY",
            "retry_reason": str(reason),
            "exception": str(einfo) if einfo else None,
            "retry_count": request.retries,
            "max_retries": request.max_retries,
            "event_type": "task_retried"
        }
    )

@task_revoked.connect
def task_revoked_handler(request, terminated, signum, expired, **kwargs):
    """
    Handle task revoked signal.

    This function is called when a task is revoked and logs the revocation details.
    """
    logger.warning(
        f"Task revoked",
        extra={
            "task_id": request.id if request else 'unknown',
            "task_name": request.task if request else 'unknown',
            "task_state": "REVOKED",
            "terminated": terminated,
            "signum": signum,
            "expired": expired,
            "event_type": "task_revoked"
        }
    )

@worker_ready.connect
def worker_ready_handler(**kwargs):
    """
    Handle worker ready signal.

    This function is called when a worker is ready to accept tasks.
    """
    logger.info(
        "Worker ready",
        extra={
            "worker_id": socket.gethostname(),
            "service": "celery_worker",
            "event_type": "worker_ready"
        }
    )

@worker_shutdown.connect
def worker_shutdown_handler(**kwargs):
    """
    Handle worker shutdown signal.

    This function is called when a worker is shutting down.
    """
    logger.info(
        "Worker shutting down",
        extra={
            "worker_id": socket.gethostname(),
            "service": "celery_worker",
            "event_type": "worker_shutdown"
        }
    )

def init_celery_logging():
    """
    Initialize Celery logging.

    This function should be called when the Celery app is initialized.
    """
    setup_logging()
    logger.info(
        "Celery logging initialized for ELK stack integration",
        extra={
            "service": "celery",
            "event_type": "logging_initialized"
        }
    )

    return logger

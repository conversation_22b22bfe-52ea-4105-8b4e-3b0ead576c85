"""
Docker optimization tools for BahtBrowse Docker Queue Management.
"""

import time
import logging
import threading
import queue
import docker
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

class DockerCache:
    """
    Cache for Docker operations.
    """
    
    def __init__(self, docker_client, ttl=60):
        """
        Initialize a Docker cache.
        
        Args:
            docker_client: Docker client
            ttl: Cache TTL in seconds
        """
        self.docker = docker_client
        self.ttl = ttl
        self.cache = {}
        self.last_updated = {}
    
    def get_container(self, container_id):
        """
        Get a container from the cache or Docker.
        
        Args:
            container_id: Docker container ID
            
        Returns:
            Container object
        """
        cache_key = f"container:{container_id}"
        
        # Check if container is in cache and not expired
        if cache_key in self.cache and time.time() - self.last_updated.get(cache_key, 0) < self.ttl:
            return self.cache[cache_key]
        
        # Get container from Docker
        container = self.docker.containers.get(container_id)
        
        # Update cache
        self.cache[cache_key] = container
        self.last_updated[cache_key] = time.time()
        
        return container
    
    def get_container_status(self, container_id):
        """
        Get container status from the cache or Docker.
        
        Args:
            container_id: Docker container ID
            
        Returns:
            Container status
        """
        cache_key = f"container_status:{container_id}"
        
        # Check if status is in cache and not expired
        if cache_key in self.cache and time.time() - self.last_updated.get(cache_key, 0) < self.ttl:
            return self.cache[cache_key]
        
        # Get container from Docker
        container = self.docker.containers.get(container_id)
        status = container.status
        
        # Update cache
        self.cache[cache_key] = status
        self.last_updated[cache_key] = time.time()
        
        return status
    
    def get_container_stats(self, container_id):
        """
        Get container stats from the cache or Docker.
        
        Args:
            container_id: Docker container ID
            
        Returns:
            Container stats
        """
        cache_key = f"container_stats:{container_id}"
        
        # Check if stats are in cache and not expired
        if cache_key in self.cache and time.time() - self.last_updated.get(cache_key, 0) < self.ttl:
            return self.cache[cache_key]
        
        # Get container from Docker
        container = self.docker.containers.get(container_id)
        stats = container.stats(stream=False)
        
        # Update cache
        self.cache[cache_key] = stats
        self.last_updated[cache_key] = time.time()
        
        return stats
    
    def invalidate(self, container_id=None):
        """
        Invalidate cache entries.
        
        Args:
            container_id: Optional container ID to invalidate
        """
        if container_id:
            # Invalidate specific container
            for key in list(self.cache.keys()):
                if key.endswith(container_id):
                    del self.cache[key]
                    if key in self.last_updated:
                        del self.last_updated[key]
        else:
            # Invalidate all cache
            self.cache = {}
            self.last_updated = {}

class DockerWorkerPool:
    """
    Worker pool for Docker operations.
    """
    
    def __init__(self, docker_client, num_workers=5):
        """
        Initialize a Docker worker pool.
        
        Args:
            docker_client: Docker client
            num_workers: Number of worker threads
        """
        self.docker = docker_client
        self.num_workers = num_workers
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.workers = []
        self.running = False
    
    def start(self):
        """Start the worker pool."""
        if self.running:
            return
        
        self.running = True
        
        # Create and start worker threads
        for i in range(self.num_workers):
            worker = threading.Thread(target=self._worker_thread, args=(i,))
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        logger.info(f"Started Docker worker pool with {self.num_workers} workers")
    
    def stop(self):
        """Stop the worker pool."""
        self.running = False
        
        # Wait for workers to finish
        for worker in self.workers:
            worker.join(timeout=1)
        
        # Clear queues
        while not self.task_queue.empty():
            self.task_queue.get()
        while not self.result_queue.empty():
            self.result_queue.get()
        
        self.workers = []
        
        logger.info("Stopped Docker worker pool")
    
    def _worker_thread(self, worker_id):
        """
        Worker thread function.
        
        Args:
            worker_id: Worker ID
        """
        logger.debug(f"Docker worker {worker_id} started")
        
        while self.running:
            try:
                # Get task from queue with timeout
                task_id, func, args, kwargs = self.task_queue.get(timeout=1)
                
                # Execute task
                try:
                    result = func(*args, **kwargs)
                    self.result_queue.put((task_id, result, None))
                except Exception as e:
                    self.result_queue.put((task_id, None, e))
                
                # Mark task as done
                self.task_queue.task_done()
            
            except queue.Empty:
                # No tasks in queue, continue
                continue
            except Exception as e:
                logger.error(f"Error in Docker worker {worker_id}: {str(e)}")
        
        logger.debug(f"Docker worker {worker_id} stopped")
    
    def submit(self, func, *args, **kwargs):
        """
        Submit a task to the worker pool.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Task ID
        """
        if not self.running:
            self.start()
        
        # Generate task ID
        task_id = f"task_{time.time()}_{id(func)}"
        
        # Add task to queue
        self.task_queue.put((task_id, func, args, kwargs))
        
        return task_id
    
    def get_result(self, task_id, timeout=None):
        """
        Get the result of a task.
        
        Args:
            task_id: Task ID
            timeout: Timeout in seconds
            
        Returns:
            Task result
            
        Raises:
            queue.Empty: If timeout is reached
            Exception: If task raised an exception
        """
        # Wait for result
        while True:
            try:
                result_task_id, result, error = self.result_queue.get(timeout=timeout)
                
                # Check if this is the result we're looking for
                if result_task_id == task_id:
                    if error:
                        raise error
                    return result
                else:
                    # Put result back in queue for other listeners
                    self.result_queue.put((result_task_id, result, error))
            
            except queue.Empty:
                raise queue.Empty(f"Timeout waiting for result of task {task_id}")

def optimize_docker_api_calls(func):
    """
    Decorator to optimize Docker API calls.
    
    Args:
        func: Function to optimize
        
    Returns:
        Optimized function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Measure execution time
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # Log execution time for slow calls
        duration = end_time - start_time
        if duration > 1.0:
            logger.warning(f"Slow Docker API call: {func.__name__} took {duration:.2f} seconds")
        
        return result
    
    return wrapper

def analyze_docker_usage(docker_client, duration=60, interval=5):
    """
    Analyze Docker usage patterns.
    
    Args:
        docker_client: Docker client
        duration: Analysis duration in seconds
        interval: Sampling interval in seconds
        
    Returns:
        Dictionary with Docker usage statistics
    """
    samples = []
    start_time = time.time()
    end_time = start_time + duration
    
    logger.info(f"Analyzing Docker usage for {duration} seconds...")
    
    while time.time() < end_time:
        # Get Docker info
        info = docker_client.info()
        
        # Get container stats
        containers = docker_client.containers.list()
        container_stats = {}
        
        for container in containers:
            try:
                stats = container.stats(stream=False)
                container_stats[container.id] = stats
            except Exception as e:
                logger.error(f"Error getting stats for container {container.id}: {str(e)}")
        
        # Extract relevant metrics
        metrics = {
            'timestamp': time.time(),
            'containers': {
                'total': info['Containers'],
                'running': info['ContainersRunning'],
                'paused': info['ContainersPaused'],
                'stopped': info['ContainersStopped']
            },
            'images': info['Images'],
            'memory': {
                'total': info.get('MemTotal', 0),
                'containers': {c.id: s.get('memory_stats', {}).get('usage', 0) for c, s in container_stats.items()}
            },
            'cpu': {
                'containers': {c.id: s.get('cpu_stats', {}) for c, s in container_stats.items()}
            }
        }
        
        samples.append(metrics)
        
        # Sleep until next sample
        time.sleep(interval)
    
    # Calculate statistics
    stats = {
        'duration': duration,
        'samples': len(samples),
        'interval': interval,
        'start_time': start_time,
        'end_time': time.time(),
        'containers': {
            'min': min(s['containers']['total'] for s in samples),
            'max': max(s['containers']['total'] for s in samples),
            'avg': sum(s['containers']['total'] for s in samples) / len(samples),
            'running': {
                'min': min(s['containers']['running'] for s in samples),
                'max': max(s['containers']['running'] for s in samples),
                'avg': sum(s['containers']['running'] for s in samples) / len(samples)
            }
        },
        'memory': {
            'total': samples[0]['memory']['total'],
            'containers': {
                'min': min(sum(s['memory']['containers'].values()) for s in samples),
                'max': max(sum(s['memory']['containers'].values()) for s in samples),
                'avg': sum(sum(s['memory']['containers'].values()) for s in samples) / len(samples)
            }
        }
    }
    
    logger.info(f"Docker usage analysis complete.")
    logger.info(f"Containers: {stats['containers']['avg']:.1f} avg (max: {stats['containers']['max']})")
    logger.info(f"Running containers: {stats['containers']['running']['avg']:.1f} avg (max: {stats['containers']['running']['max']})")
    logger.info(f"Container memory: {stats['memory']['containers']['avg'] / 1024 / 1024:.2f} MB avg (max: {stats['memory']['containers']['max'] / 1024 / 1024:.2f} MB)")
    
    return stats

def optimize_container_resources(docker_client, container_id, cpu_limit=None, memory_limit=None):
    """
    Optimize container resource limits.
    
    Args:
        docker_client: Docker client
        container_id: Container ID
        cpu_limit: CPU limit (e.g., '0.5')
        memory_limit: Memory limit (e.g., '512m')
        
    Returns:
        Updated container
    """
    # Get container
    container = docker_client.containers.get(container_id)
    
    # Update container
    container.update(
        cpu_quota=int(float(cpu_limit) * 100000) if cpu_limit else None,
        mem_limit=memory_limit
    )
    
    logger.info(f"Optimized resources for container {container_id}: CPU={cpu_limit}, Memory={memory_limit}")
    
    return container

def preload_container_image(docker_client, image_name):
    """
    Preload a Docker image to improve container startup time.
    
    Args:
        docker_client: Docker client
        image_name: Image name
        
    Returns:
        Image object
    """
    # Check if image exists
    try:
        image = docker_client.images.get(image_name)
        logger.info(f"Image {image_name} already exists")
        return image
    except docker.errors.ImageNotFound:
        # Pull image
        logger.info(f"Pulling image {image_name}...")
        image = docker_client.images.pull(image_name)
        logger.info(f"Image {image_name} pulled successfully")
        return image

def create_container_template(docker_client, image_name, **kwargs):
    """
    Create a container template for faster container creation.
    
    Args:
        docker_client: Docker client
        image_name: Image name
        **kwargs: Container creation arguments
        
    Returns:
        Container template function
    """
    # Preload image
    preload_container_image(docker_client, image_name)
    
    # Create template function
    def create_container(**override_kwargs):
        # Merge kwargs
        container_kwargs = kwargs.copy()
        container_kwargs.update(override_kwargs)
        
        # Create container
        return docker_client.containers.run(image_name, **container_kwargs)
    
    logger.info(f"Created container template for image {image_name}")
    
    return create_container

#!/usr/bin/env python3
"""
Performance optimization script for BahtBrowse Docker Queue Management.
"""

import os
import sys
import time
import argparse
import logging
import json
import importlib
import redis
import docker
from celery import Celery

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import optimization modules
from docker_queue.performance.redis_optimizer import (
    optimize_redis_connection_pool,
    analyze_redis_usage,
    get_key_memory_usage
)
from docker_queue.performance.docker_optimizer import (
    preload_container_image,
    create_container_template,
    analyze_docker_usage
)
from docker_queue.performance.celery_optimizer import (
    optimize_task_routing,
    optimize_task_queue,
    analyze_task_performance
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('optimize')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize BahtBrowse Docker Queue Management')
    
    parser.add_argument('--redis-host', type=str, default='localhost',
                        help='Redis host (default: localhost)')
    parser.add_argument('--redis-port', type=int, default=6379,
                        help='Redis port (default: 6379)')
    parser.add_argument('--docker-url', type=str, default='unix:///var/run/docker.sock',
                        help='Docker URL (default: unix:///var/run/docker.sock)')
    parser.add_argument('--celery-broker', type=str, default='redis://localhost:6379/0',
                        help='Celery broker URL (default: redis://localhost:6379/0)')
    parser.add_argument('--celery-backend', type=str, default='redis://localhost:6379/0',
                        help='Celery result backend URL (default: redis://localhost:6379/0)')
    parser.add_argument('--optimize', type=str, choices=['redis', 'docker', 'celery', 'all'], default='all',
                        help='Optimization target (default: all)')
    parser.add_argument('--analyze-only', action='store_true',
                        help='Only analyze, do not apply optimizations')
    parser.add_argument('--output', type=str, default='optimization_results.json',
                        help='Output file for results (default: optimization_results.json)')
    
    return parser.parse_args()

def optimize_redis(redis_host, redis_port, analyze_only=False):
    """
    Optimize Redis.
    
    Args:
        redis_host: Redis host
        redis_port: Redis port
        analyze_only: Only analyze, do not apply optimizations
        
    Returns:
        Dictionary with optimization results
    """
    logger.info(f"Optimizing Redis at {redis_host}:{redis_port}")
    
    # Initialize results
    results = {
        'target': 'redis',
        'host': redis_host,
        'port': redis_port,
        'analyze_only': analyze_only,
        'start_time': time.time(),
        'analysis': {},
        'optimizations': {}
    }
    
    # Connect to Redis
    redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
    
    try:
        # Analyze Redis usage
        logger.info("Analyzing Redis usage...")
        usage_stats = analyze_redis_usage(redis_client, duration=30, interval=5)
        results['analysis']['usage'] = usage_stats
        
        # Analyze key memory usage
        logger.info("Analyzing key memory usage...")
        key_memory_stats = get_key_memory_usage(redis_client, key_pattern='bahtbrowse:*', sample_size=100)
        results['analysis']['key_memory'] = key_memory_stats
        
        # Apply optimizations if not analyze_only
        if not analyze_only:
            logger.info("Applying Redis optimizations...")
            
            # Optimize connection pool
            optimized_client = optimize_redis_connection_pool(redis_client, max_connections=100)
            results['optimizations']['connection_pool'] = {
                'max_connections': 100
            }
            
            # Use optimized client for further operations
            redis_client = optimized_client
        
        logger.info("Redis optimization complete")
    
    except Exception as e:
        logger.error(f"Error optimizing Redis: {str(e)}")
        results['error'] = str(e)
    
    # Calculate end time
    results['end_time'] = time.time()
    results['duration'] = results['end_time'] - results['start_time']
    
    return results

def optimize_docker(docker_url, analyze_only=False):
    """
    Optimize Docker.
    
    Args:
        docker_url: Docker URL
        analyze_only: Only analyze, do not apply optimizations
        
    Returns:
        Dictionary with optimization results
    """
    logger.info(f"Optimizing Docker at {docker_url}")
    
    # Initialize results
    results = {
        'target': 'docker',
        'url': docker_url,
        'analyze_only': analyze_only,
        'start_time': time.time(),
        'analysis': {},
        'optimizations': {}
    }
    
    # Connect to Docker
    docker_client = docker.DockerClient(base_url=docker_url)
    
    try:
        # Analyze Docker usage
        logger.info("Analyzing Docker usage...")
        usage_stats = analyze_docker_usage(docker_client, duration=30, interval=5)
        results['analysis']['usage'] = usage_stats
        
        # Apply optimizations if not analyze_only
        if not analyze_only:
            logger.info("Applying Docker optimizations...")
            
            # Preload container image
            logger.info("Preloading container image...")
            image = preload_container_image(docker_client, 'bahtbrowse:latest')
            results['optimizations']['preload_image'] = {
                'image': 'bahtbrowse:latest',
                'id': image.id if image else None
            }
            
            # Create container template
            logger.info("Creating container template...")
            template = create_container_template(
                docker_client,
                'bahtbrowse:latest',
                detach=True,
                network='bahtbrowse',
                cpu_quota=int(1.0 * 100000),
                mem_limit='2g',
                restart_policy={"Name": "unless-stopped"}
            )
            results['optimizations']['container_template'] = {
                'image': 'bahtbrowse:latest',
                'params': {
                    'detach': True,
                    'network': 'bahtbrowse',
                    'cpu_quota': int(1.0 * 100000),
                    'mem_limit': '2g'
                }
            }
        
        logger.info("Docker optimization complete")
    
    except Exception as e:
        logger.error(f"Error optimizing Docker: {str(e)}")
        results['error'] = str(e)
    
    # Calculate end time
    results['end_time'] = time.time()
    results['duration'] = results['end_time'] - results['start_time']
    
    return results

def optimize_celery(broker_url, backend_url, analyze_only=False):
    """
    Optimize Celery.
    
    Args:
        broker_url: Celery broker URL
        backend_url: Celery result backend URL
        analyze_only: Only analyze, do not apply optimizations
        
    Returns:
        Dictionary with optimization results
    """
    logger.info(f"Optimizing Celery with broker {broker_url} and backend {backend_url}")
    
    # Initialize results
    results = {
        'target': 'celery',
        'broker_url': broker_url,
        'backend_url': backend_url,
        'analyze_only': analyze_only,
        'start_time': time.time(),
        'analysis': {},
        'optimizations': {}
    }
    
    try:
        # Import Celery app
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
        from docker_queue.celery_app import app
        
        # Analyze task performance
        logger.info("Analyzing task performance...")
        
        # List of tasks to analyze
        tasks = [
            'docker_queue.tasks.container_management.create_and_initialize_container',
            'docker_queue.tasks.container_management.assign_container_to_request',
            'docker_queue.tasks.container_management.process_container_request',
            'docker_queue.tasks.container_management.recycle_container',
            'docker_queue.tasks.pool_management.maintain_container_pool'
        ]
        
        task_performance = {}
        for task_name in tasks:
            logger.info(f"Analyzing task {task_name}...")
            try:
                # Get task
                task = app.tasks[task_name]
                
                # Analyze performance (simplified for this script)
                performance = {
                    'task_name': task_name,
                    'rate_limit': getattr(task, 'rate_limit', None),
                    'time_limit': getattr(task, 'time_limit', None),
                    'soft_time_limit': getattr(task, 'soft_time_limit', None),
                    'queue': task.queue
                }
                
                task_performance[task_name] = performance
            
            except Exception as e:
                logger.error(f"Error analyzing task {task_name}: {str(e)}")
        
        results['analysis']['task_performance'] = task_performance
        
        # Apply optimizations if not analyze_only
        if not analyze_only:
            logger.info("Applying Celery optimizations...")
            
            # Optimize task routing
            logger.info("Optimizing task routing...")
            task_routes = {}
            
            # Container management tasks
            for task_name in [
                'docker_queue.tasks.container_management.create_and_initialize_container',
                'docker_queue.tasks.container_management.assign_container_to_request',
                'docker_queue.tasks.container_management.process_container_request',
                'docker_queue.tasks.container_management.recycle_container'
            ]:
                task_routes[task_name] = optimize_task_routing(
                    app,
                    task_name,
                    queue='container_management',
                    exchange='container_management',
                    routing_key='container_management'
                )
            
            # Pool management tasks
            for task_name in [
                'docker_queue.tasks.pool_management.maintain_container_pool',
                'docker_queue.tasks.pool_management.scale_container_pool',
                'docker_queue.tasks.pool_management.predict_pool_size'
            ]:
                task_routes[task_name] = optimize_task_routing(
                    app,
                    task_name,
                    queue='pool_management',
                    exchange='pool_management',
                    routing_key='pool_management'
                )
            
            # Monitoring tasks
            for task_name in [
                'docker_queue.tasks.monitoring.collect_metrics',
                'docker_queue.tasks.monitoring.check_container_health',
                'docker_queue.tasks.monitoring.generate_usage_report'
            ]:
                task_routes[task_name] = optimize_task_routing(
                    app,
                    task_name,
                    queue='monitoring',
                    exchange='monitoring',
                    routing_key='monitoring'
                )
            
            results['optimizations']['task_routing'] = task_routes
            
            # Optimize task queue settings
            logger.info("Optimizing task queue settings...")
            queue_settings = {}
            
            # Container management queue
            queue_settings['container_management'] = optimize_task_queue(
                app,
                'container_management',
                prefetch_multiplier=4,
                acks_late=True,
                acks_on_failure_or_timeout=False
            )
            
            # Pool management queue
            queue_settings['pool_management'] = optimize_task_queue(
                app,
                'pool_management',
                prefetch_multiplier=2,
                acks_late=True,
                acks_on_failure_or_timeout=True
            )
            
            # Monitoring queue
            queue_settings['monitoring'] = optimize_task_queue(
                app,
                'monitoring',
                prefetch_multiplier=1,
                acks_late=False,
                acks_on_failure_or_timeout=True
            )
            
            results['optimizations']['queue_settings'] = queue_settings
        
        logger.info("Celery optimization complete")
    
    except Exception as e:
        logger.error(f"Error optimizing Celery: {str(e)}")
        results['error'] = str(e)
    
    # Calculate end time
    results['end_time'] = time.time()
    results['duration'] = results['end_time'] - results['start_time']
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Initialize results
    results = {
        'timestamp': time.time(),
        'analyze_only': args.analyze_only,
        'targets': {}
    }
    
    # Run optimizations
    if args.optimize in ['redis', 'all']:
        results['targets']['redis'] = optimize_redis(
            args.redis_host,
            args.redis_port,
            args.analyze_only
        )
    
    if args.optimize in ['docker', 'all']:
        results['targets']['docker'] = optimize_docker(
            args.docker_url,
            args.analyze_only
        )
    
    if args.optimize in ['celery', 'all']:
        results['targets']['celery'] = optimize_celery(
            args.celery_broker,
            args.celery_backend,
            args.analyze_only
        )
    
    # Save results
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Optimization results saved to {args.output}")
    
    # Print summary
    print("\n=== Optimization Summary ===")
    print(f"Mode: {'Analysis only' if args.analyze_only else 'Analysis and Optimization'}")
    
    if 'redis' in results['targets']:
        redis_results = results['targets']['redis']
        print("\nRedis Optimization:")
        print(f"  Duration: {redis_results.get('duration', 0):.2f} seconds")
        
        if 'error' in redis_results:
            print(f"  Error: {redis_results['error']}")
        else:
            usage = redis_results.get('analysis', {}).get('usage', {})
            print(f"  Memory Usage: {usage.get('memory', {}).get('avg', 0) / 1024 / 1024:.2f} MB")
            print(f"  Commands/sec: {usage.get('commands', {}).get('per_second', 0):.2f}")
            print(f"  Keyspace Hit Rate: {usage.get('keyspace', {}).get('hit_rate', 0) * 100:.2f}%")
            
            if not args.analyze_only:
                print("  Applied Optimizations:")
                print(f"    - Connection Pool: max_connections={redis_results.get('optimizations', {}).get('connection_pool', {}).get('max_connections', 'N/A')}")
    
    if 'docker' in results['targets']:
        docker_results = results['targets']['docker']
        print("\nDocker Optimization:")
        print(f"  Duration: {docker_results.get('duration', 0):.2f} seconds")
        
        if 'error' in docker_results:
            print(f"  Error: {docker_results['error']}")
        else:
            usage = docker_results.get('analysis', {}).get('usage', {})
            print(f"  Running Containers: {usage.get('containers', {}).get('running', {}).get('avg', 0):.1f} avg")
            print(f"  Container Memory: {usage.get('memory', {}).get('containers', {}).get('avg', 0) / 1024 / 1024:.2f} MB avg")
            
            if not args.analyze_only:
                print("  Applied Optimizations:")
                print(f"    - Preloaded Image: {docker_results.get('optimizations', {}).get('preload_image', {}).get('image', 'N/A')}")
                print(f"    - Container Template: {docker_results.get('optimizations', {}).get('container_template', {}).get('image', 'N/A')}")
    
    if 'celery' in results['targets']:
        celery_results = results['targets']['celery']
        print("\nCelery Optimization:")
        print(f"  Duration: {celery_results.get('duration', 0):.2f} seconds")
        
        if 'error' in celery_results:
            print(f"  Error: {celery_results['error']}")
        else:
            task_performance = celery_results.get('analysis', {}).get('task_performance', {})
            print(f"  Analyzed Tasks: {len(task_performance)}")
            
            if not args.analyze_only:
                print("  Applied Optimizations:")
                print(f"    - Task Routing: {len(celery_results.get('optimizations', {}).get('task_routing', {}))} tasks")
                print(f"    - Queue Settings: {len(celery_results.get('optimizations', {}).get('queue_settings', {}))} queues")
    
    print("\nFull results saved to:", args.output)

if __name__ == '__main__':
    main()

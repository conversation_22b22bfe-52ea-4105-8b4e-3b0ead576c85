"""
Benchmarking tools for BahtBrowse Docker Queue Management.
"""

import time
import statistics
import concurrent.futures
import logging
import json
import os
import datetime
import uuid
import requests
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

# Directory for benchmark results
BENCHMARK_DIR = os.environ.get('BENCHMARK_DIR', '/tmp/bahtbrowse_benchmarks')

# Create benchmark directory if it doesn't exist
os.makedirs(BENCHMARK_DIR, exist_ok=True)

class Benchmark:
    """
    Benchmark class for measuring performance.
    """
    
    def __init__(self, name, description=None):
        """
        Initialize a benchmark.
        
        Args:
            name: Name of the benchmark
            description: Optional description
        """
        self.name = name
        self.description = description or f"Benchmark for {name}"
        self.results = []
        self.start_time = None
        self.end_time = None
        self.metadata = {}
    
    def start(self):
        """Start the benchmark."""
        self.start_time = time.time()
        self.results = []
        logger.info(f"Starting benchmark: {self.name}")
        return self
    
    def stop(self):
        """Stop the benchmark and calculate results."""
        self.end_time = time.time()
        logger.info(f"Finished benchmark: {self.name}")
        return self
    
    def add_result(self, operation, duration, metadata=None):
        """
        Add a result to the benchmark.
        
        Args:
            operation: Name of the operation
            duration: Duration in seconds
            metadata: Optional metadata
        """
        result = {
            'operation': operation,
            'duration': duration,
            'timestamp': time.time()
        }
        
        if metadata:
            result['metadata'] = metadata
        
        self.results.append(result)
    
    def add_metadata(self, key, value):
        """
        Add metadata to the benchmark.
        
        Args:
            key: Metadata key
            value: Metadata value
        """
        self.metadata[key] = value
    
    def get_stats(self):
        """
        Get statistics for the benchmark.
        
        Returns:
            Dictionary with benchmark statistics
        """
        if not self.results:
            return {
                'name': self.name,
                'description': self.description,
                'count': 0,
                'total_duration': 0,
                'metadata': self.metadata
            }
        
        durations = [r['duration'] for r in self.results]
        
        stats = {
            'name': self.name,
            'description': self.description,
            'count': len(self.results),
            'total_duration': sum(durations),
            'min': min(durations),
            'max': max(durations),
            'mean': statistics.mean(durations),
            'median': statistics.median(durations),
            'stdev': statistics.stdev(durations) if len(durations) > 1 else 0,
            'operations_per_second': len(durations) / (self.end_time - self.start_time) if self.end_time and self.start_time else 0,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'metadata': self.metadata
        }
        
        # Calculate percentiles
        if len(durations) >= 10:
            stats['percentiles'] = {
                '50': statistics.median(durations),
                '90': statistics.quantiles(durations, n=10)[8],
                '95': statistics.quantiles(durations, n=20)[18],
                '99': statistics.quantiles(durations, n=100)[98] if len(durations) >= 100 else None
            }
        
        # Group by operation
        operations = {}
        for result in self.results:
            op = result['operation']
            if op not in operations:
                operations[op] = []
            operations[op].append(result['duration'])
        
        # Calculate stats for each operation
        stats['operations'] = {}
        for op, op_durations in operations.items():
            op_stats = {
                'count': len(op_durations),
                'total_duration': sum(op_durations),
                'min': min(op_durations),
                'max': max(op_durations),
                'mean': statistics.mean(op_durations),
                'median': statistics.median(op_durations),
                'stdev': statistics.stdev(op_durations) if len(op_durations) > 1 else 0
            }
            
            # Calculate percentiles for operation
            if len(op_durations) >= 10:
                op_stats['percentiles'] = {
                    '50': statistics.median(op_durations),
                    '90': statistics.quantiles(op_durations, n=10)[8],
                    '95': statistics.quantiles(op_durations, n=20)[18],
                    '99': statistics.quantiles(op_durations, n=100)[98] if len(op_durations) >= 100 else None
                }
            
            stats['operations'][op] = op_stats
        
        return stats
    
    def save(self):
        """
        Save benchmark results to a file.
        
        Returns:
            Path to the saved file
        """
        stats = self.get_stats()
        
        # Create filename with timestamp
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{self.name}_{timestamp}.json"
        filepath = os.path.join(BENCHMARK_DIR, filename)
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(stats, f, indent=2)
        
        logger.info(f"Benchmark results saved to {filepath}")
        return filepath
    
    def print_results(self):
        """Print benchmark results."""
        stats = self.get_stats()
        
        print(f"\n=== Benchmark: {stats['name']} ===")
        print(f"Description: {stats['description']}")
        print(f"Operations: {stats['count']}")
        print(f"Total Duration: {stats['total_duration']:.6f} seconds")
        print(f"Operations/second: {stats['operations_per_second']:.2f}")
        print(f"Min: {stats['min']:.6f} seconds")
        print(f"Max: {stats['max']:.6f} seconds")
        print(f"Mean: {stats['mean']:.6f} seconds")
        print(f"Median: {stats['median']:.6f} seconds")
        print(f"StdDev: {stats['stdev']:.6f} seconds")
        
        if 'percentiles' in stats:
            print("\nPercentiles:")
            for p, v in stats['percentiles'].items():
                if v is not None:
                    print(f"  {p}th: {v:.6f} seconds")
        
        print("\nOperations:")
        for op, op_stats in stats['operations'].items():
            print(f"\n  {op}:")
            print(f"    Count: {op_stats['count']}")
            print(f"    Total: {op_stats['total_duration']:.6f} seconds")
            print(f"    Min: {op_stats['min']:.6f} seconds")
            print(f"    Max: {op_stats['max']:.6f} seconds")
            print(f"    Mean: {op_stats['mean']:.6f} seconds")
            print(f"    Median: {op_stats['median']:.6f} seconds")
            
            if 'percentiles' in op_stats:
                print("    Percentiles:")
                for p, v in op_stats['percentiles'].items():
                    if v is not None:
                        print(f"      {p}th: {v:.6f} seconds")
        
        print("\nMetadata:")
        for k, v in stats['metadata'].items():
            print(f"  {k}: {v}")
        
        print("\n")

def benchmark_function(func=None, *, name=None, operation=None):
    """
    Decorator for benchmarking a function.
    
    Args:
        func: Function to benchmark
        name: Optional name for the benchmark (defaults to function name)
        operation: Optional operation name (defaults to function name)
    
    Example:
        @benchmark_function
        def my_function():
            # Function code
            
        @benchmark_function(name='custom_name', operation='custom_operation')
        def another_function():
            # Function code
    """
    def decorator(func):
        benchmark_name = name or f"benchmark_{func.__name__}"
        op_name = operation or func.__name__
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create benchmark if it doesn't exist
            if not hasattr(wrapper, 'benchmark'):
                wrapper.benchmark = Benchmark(benchmark_name)
                wrapper.benchmark.start()
            
            # Measure function execution time
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            # Add result to benchmark
            duration = end_time - start_time
            wrapper.benchmark.add_result(op_name, duration)
            
            return result
        
        # Add methods to access benchmark
        wrapper.get_benchmark = lambda: wrapper.benchmark
        wrapper.print_benchmark = lambda: wrapper.benchmark.print_results()
        wrapper.save_benchmark = lambda: wrapper.benchmark.save()
        
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

def load_benchmark(filepath):
    """
    Load benchmark results from a file.
    
    Args:
        filepath: Path to the benchmark file
        
    Returns:
        Dictionary with benchmark results
    """
    with open(filepath, 'r') as f:
        return json.load(f)

def list_benchmarks():
    """
    List all benchmark files.
    
    Returns:
        List of benchmark file paths
    """
    return [os.path.join(BENCHMARK_DIR, f) for f in os.listdir(BENCHMARK_DIR) if f.endswith('.json')]

def compare_benchmarks(benchmark1, benchmark2):
    """
    Compare two benchmarks and return the differences.
    
    Args:
        benchmark1: First benchmark (dict or filepath)
        benchmark2: Second benchmark (dict or filepath)
        
    Returns:
        Dictionary with benchmark comparison
    """
    # Load benchmarks if filepaths provided
    if isinstance(benchmark1, str):
        benchmark1 = load_benchmark(benchmark1)
    if isinstance(benchmark2, str):
        benchmark2 = load_benchmark(benchmark2)
    
    # Calculate differences
    comparison = {
        'benchmark1': benchmark1['name'],
        'benchmark2': benchmark2['name'],
        'operations_diff': benchmark2['count'] - benchmark1['count'],
        'total_duration_diff': benchmark2['total_duration'] - benchmark1['total_duration'],
        'mean_diff': benchmark2['mean'] - benchmark1['mean'],
        'median_diff': benchmark2['median'] - benchmark1['median'],
        'operations_per_second_diff': benchmark2['operations_per_second'] - benchmark1['operations_per_second'],
        'operations': {}
    }
    
    # Calculate percentage differences
    comparison['mean_diff_percent'] = (benchmark2['mean'] / benchmark1['mean'] - 1) * 100 if benchmark1['mean'] > 0 else 0
    comparison['median_diff_percent'] = (benchmark2['median'] / benchmark1['median'] - 1) * 100 if benchmark1['median'] > 0 else 0
    comparison['operations_per_second_diff_percent'] = (benchmark2['operations_per_second'] / benchmark1['operations_per_second'] - 1) * 100 if benchmark1['operations_per_second'] > 0 else 0
    
    # Compare operations
    for op in set(benchmark1['operations'].keys()) | set(benchmark2['operations'].keys()):
        if op in benchmark1['operations'] and op in benchmark2['operations']:
            op1 = benchmark1['operations'][op]
            op2 = benchmark2['operations'][op]
            
            op_comparison = {
                'count_diff': op2['count'] - op1['count'],
                'total_duration_diff': op2['total_duration'] - op1['total_duration'],
                'mean_diff': op2['mean'] - op1['mean'],
                'median_diff': op2['median'] - op1['median']
            }
            
            # Calculate percentage differences
            op_comparison['mean_diff_percent'] = (op2['mean'] / op1['mean'] - 1) * 100 if op1['mean'] > 0 else 0
            op_comparison['median_diff_percent'] = (op2['median'] / op1['median'] - 1) * 100 if op1['median'] > 0 else 0
            
            comparison['operations'][op] = op_comparison
        elif op in benchmark1['operations']:
            comparison['operations'][op] = {'status': 'removed'}
        else:
            comparison['operations'][op] = {'status': 'added'}
    
    return comparison

def print_comparison(comparison):
    """
    Print benchmark comparison.
    
    Args:
        comparison: Benchmark comparison dictionary
    """
    print(f"\n=== Benchmark Comparison ===")
    print(f"Benchmark 1: {comparison['benchmark1']}")
    print(f"Benchmark 2: {comparison['benchmark2']}")
    print(f"\nOverall Differences:")
    print(f"  Operations: {comparison['operations_diff']} ({'+' if comparison['operations_diff'] >= 0 else ''}{comparison['operations_diff']})")
    print(f"  Total Duration: {comparison['total_duration_diff']:.6f} seconds")
    print(f"  Mean: {comparison['mean_diff']:.6f} seconds ({'+' if comparison['mean_diff_percent'] >= 0 else ''}{comparison['mean_diff_percent']:.2f}%)")
    print(f"  Median: {comparison['median_diff']:.6f} seconds ({'+' if comparison['median_diff_percent'] >= 0 else ''}{comparison['median_diff_percent']:.2f}%)")
    print(f"  Operations/second: {comparison['operations_per_second_diff']:.2f} ({'+' if comparison['operations_per_second_diff_percent'] >= 0 else ''}{comparison['operations_per_second_diff_percent']:.2f}%)")
    
    print("\nOperation Differences:")
    for op, op_comparison in comparison['operations'].items():
        print(f"\n  {op}:")
        if 'status' in op_comparison:
            print(f"    Status: {op_comparison['status']}")
        else:
            print(f"    Count: {op_comparison['count_diff']}")
            print(f"    Total Duration: {op_comparison['total_duration_diff']:.6f} seconds")
            print(f"    Mean: {op_comparison['mean_diff']:.6f} seconds ({'+' if op_comparison['mean_diff_percent'] >= 0 else ''}{op_comparison['mean_diff_percent']:.2f}%)")
            print(f"    Median: {op_comparison['median_diff']:.6f} seconds ({'+' if op_comparison['median_diff_percent'] >= 0 else ''}{op_comparison['median_diff_percent']:.2f}%)")
    
    print("\n")

class APIBenchmark:
    """
    Benchmark class for API performance testing.
    """
    
    def __init__(self, base_url, name=None):
        """
        Initialize an API benchmark.
        
        Args:
            base_url: Base URL for API requests
            name: Optional name for the benchmark
        """
        self.base_url = base_url
        self.name = name or f"api_benchmark_{int(time.time())}"
        self.benchmark = Benchmark(self.name, f"API Benchmark for {base_url}")
        self.session = requests.Session()
    
    def start(self):
        """Start the benchmark."""
        self.benchmark.start()
        return self
    
    def stop(self):
        """Stop the benchmark."""
        self.benchmark.stop()
        return self
    
    def request(self, method, endpoint, **kwargs):
        """
        Make an API request and measure performance.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
        """
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        operation = f"{method} {endpoint}"
        
        start_time = time.time()
        response = self.session.request(method, url, **kwargs)
        end_time = time.time()
        
        duration = end_time - start_time
        
        metadata = {
            'status_code': response.status_code,
            'response_time': duration,
            'response_size': len(response.content)
        }
        
        self.benchmark.add_result(operation, duration, metadata)
        
        return response
    
    def get(self, endpoint, **kwargs):
        """
        Make a GET request and measure performance.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
        """
        return self.request('GET', endpoint, **kwargs)
    
    def post(self, endpoint, **kwargs):
        """
        Make a POST request and measure performance.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
        """
        return self.request('POST', endpoint, **kwargs)
    
    def put(self, endpoint, **kwargs):
        """
        Make a PUT request and measure performance.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
        """
        return self.request('PUT', endpoint, **kwargs)
    
    def delete(self, endpoint, **kwargs):
        """
        Make a DELETE request and measure performance.
        
        Args:
            endpoint: API endpoint
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
        """
        return self.request('DELETE', endpoint, **kwargs)
    
    def get_stats(self):
        """
        Get statistics for the benchmark.
        
        Returns:
            Dictionary with benchmark statistics
        """
        return self.benchmark.get_stats()
    
    def print_results(self):
        """Print benchmark results."""
        self.benchmark.print_results()
    
    def save(self):
        """
        Save benchmark results to a file.
        
        Returns:
            Path to the saved file
        """
        return self.benchmark.save()

def run_concurrent_benchmark(func, args_list, max_workers=10, name=None):
    """
    Run a function concurrently and measure performance.
    
    Args:
        func: Function to benchmark
        args_list: List of argument tuples for each function call
        max_workers: Maximum number of concurrent workers
        name: Optional name for the benchmark
        
    Returns:
        Benchmark object with results
    """
    benchmark_name = name or f"concurrent_benchmark_{func.__name__}"
    benchmark = Benchmark(benchmark_name, f"Concurrent benchmark for {func.__name__}")
    benchmark.start()
    
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_args = {executor.submit(func, *args): args for args in args_list}
        
        for future in concurrent.futures.as_completed(future_to_args):
            args = future_to_args[future]
            operation = f"{func.__name__}({', '.join(str(a) for a in args)})"
            
            try:
                start_time = time.time()
                result = future.result()
                end_time = time.time()
                
                duration = end_time - start_time
                benchmark.add_result(operation, duration)
                
                results.append(result)
            except Exception as e:
                logger.error(f"Error in concurrent benchmark: {str(e)}")
    
    benchmark.stop()
    return benchmark, results

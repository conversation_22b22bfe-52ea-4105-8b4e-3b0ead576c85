"""
Redis optimization tools for BahtBrowse Docker Queue Management.
"""

import time
import logging
import redis
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

class RedisCache:
    """
    Cache for Redis operations.
    """
    
    def __init__(self, redis_client, ttl=60):
        """
        Initialize a Redis cache.
        
        Args:
            redis_client: Redis client
            ttl: Cache TTL in seconds
        """
        self.redis = redis_client
        self.ttl = ttl
        self.cache = {}
        self.last_updated = {}
    
    def get(self, key):
        """
        Get a value from the cache or Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Value from cache or Redis
        """
        # Check if key is in cache and not expired
        if key in self.cache and time.time() - self.last_updated.get(key, 0) < self.ttl:
            return self.cache[key]
        
        # Get value from Redis
        value = self.redis.get(key)
        
        # Update cache
        self.cache[key] = value
        self.last_updated[key] = time.time()
        
        return value
    
    def set(self, key, value):
        """
        Set a value in the cache and Redis.
        
        Args:
            key: Redis key
            value: Value to set
        """
        # Set value in Redis
        self.redis.set(key, value)
        
        # Update cache
        self.cache[key] = value
        self.last_updated[key] = time.time()
    
    def delete(self, key):
        """
        Delete a value from the cache and Redis.
        
        Args:
            key: Redis key
        """
        # Delete from Redis
        self.redis.delete(key)
        
        # Delete from cache
        if key in self.cache:
            del self.cache[key]
        if key in self.last_updated:
            del self.last_updated[key]
    
    def clear(self):
        """Clear the cache."""
        self.cache = {}
        self.last_updated = {}

def optimize_redis_pipeline(func):
    """
    Decorator to optimize Redis operations using pipelines.
    
    Args:
        func: Function to optimize
        
    Returns:
        Optimized function
    """
    @wraps(func)
    def wrapper(redis_client, *args, **kwargs):
        # Create pipeline
        pipeline = redis_client.pipeline()
        
        # Replace redis_client with pipeline in function call
        result = func(pipeline, *args, **kwargs)
        
        # Execute pipeline
        pipeline.execute()
        
        return result
    
    return wrapper

def batch_redis_operations(operations, redis_client, batch_size=100):
    """
    Batch Redis operations for better performance.
    
    Args:
        operations: List of (operation, args, kwargs) tuples
        redis_client: Redis client
        batch_size: Batch size
        
    Returns:
        List of results
    """
    results = []
    
    # Process operations in batches
    for i in range(0, len(operations), batch_size):
        batch = operations[i:i+batch_size]
        
        # Create pipeline
        pipeline = redis_client.pipeline()
        
        # Add operations to pipeline
        for operation, args, kwargs in batch:
            getattr(pipeline, operation)(*args, **kwargs)
        
        # Execute pipeline
        batch_results = pipeline.execute()
        results.extend(batch_results)
    
    return results

def analyze_redis_usage(redis_client, duration=60, interval=5):
    """
    Analyze Redis usage patterns.
    
    Args:
        redis_client: Redis client
        duration: Analysis duration in seconds
        interval: Sampling interval in seconds
        
    Returns:
        Dictionary with Redis usage statistics
    """
    samples = []
    start_time = time.time()
    end_time = start_time + duration
    
    logger.info(f"Analyzing Redis usage for {duration} seconds...")
    
    while time.time() < end_time:
        # Get Redis info
        info = redis_client.info()
        
        # Extract relevant metrics
        metrics = {
            'timestamp': time.time(),
            'used_memory': info['used_memory'],
            'used_memory_peak': info['used_memory_peak'],
            'connected_clients': info['connected_clients'],
            'total_commands_processed': info['total_commands_processed'],
            'keyspace_hits': info['keyspace_hits'],
            'keyspace_misses': info['keyspace_misses'],
            'expired_keys': info['expired_keys'],
            'evicted_keys': info['evicted_keys'],
            'keys': {db: info[db]['keys'] for db in info if db.startswith('db')}
        }
        
        samples.append(metrics)
        
        # Sleep until next sample
        time.sleep(interval)
    
    # Calculate statistics
    stats = {
        'duration': duration,
        'samples': len(samples),
        'interval': interval,
        'start_time': start_time,
        'end_time': time.time(),
        'memory': {
            'min': min(s['used_memory'] for s in samples),
            'max': max(s['used_memory'] for s in samples),
            'avg': sum(s['used_memory'] for s in samples) / len(samples),
            'peak': max(s['used_memory_peak'] for s in samples)
        },
        'commands': {
            'total': samples[-1]['total_commands_processed'] - samples[0]['total_commands_processed'],
            'per_second': (samples[-1]['total_commands_processed'] - samples[0]['total_commands_processed']) / duration
        },
        'keyspace': {
            'hits': samples[-1]['keyspace_hits'] - samples[0]['keyspace_hits'],
            'misses': samples[-1]['keyspace_misses'] - samples[0]['keyspace_misses'],
            'hit_rate': (samples[-1]['keyspace_hits'] - samples[0]['keyspace_hits']) / 
                        ((samples[-1]['keyspace_hits'] - samples[0]['keyspace_hits']) + 
                         (samples[-1]['keyspace_misses'] - samples[0]['keyspace_misses'])) 
                        if (samples[-1]['keyspace_hits'] - samples[0]['keyspace_hits']) + 
                           (samples[-1]['keyspace_misses'] - samples[0]['keyspace_misses']) > 0 else 0
        },
        'keys': {
            'expired': samples[-1]['expired_keys'] - samples[0]['expired_keys'],
            'evicted': samples[-1]['evicted_keys'] - samples[0]['evicted_keys']
        }
    }
    
    logger.info(f"Redis usage analysis complete.")
    logger.info(f"Memory usage: {stats['memory']['avg'] / 1024 / 1024:.2f} MB (peak: {stats['memory']['peak'] / 1024 / 1024:.2f} MB)")
    logger.info(f"Commands: {stats['commands']['total']} ({stats['commands']['per_second']:.2f}/sec)")
    logger.info(f"Keyspace hit rate: {stats['keyspace']['hit_rate'] * 100:.2f}%")
    
    return stats

def optimize_redis_connection_pool(redis_client, max_connections=100):
    """
    Optimize Redis connection pool.
    
    Args:
        redis_client: Redis client
        max_connections: Maximum connections
        
    Returns:
        Optimized Redis client
    """
    # Create new connection pool
    pool = redis.ConnectionPool(
        host=redis_client.connection_pool.connection_kwargs.get('host', 'localhost'),
        port=redis_client.connection_pool.connection_kwargs.get('port', 6379),
        db=redis_client.connection_pool.connection_kwargs.get('db', 0),
        password=redis_client.connection_pool.connection_kwargs.get('password', None),
        max_connections=max_connections,
        socket_timeout=5,
        socket_connect_timeout=5,
        socket_keepalive=True,
        retry_on_timeout=True
    )
    
    # Create new client with optimized pool
    optimized_client = redis.Redis(connection_pool=pool)
    
    logger.info(f"Redis connection pool optimized with max_connections={max_connections}")
    
    return optimized_client

def get_key_memory_usage(redis_client, key_pattern='*', sample_size=100):
    """
    Get memory usage for Redis keys.
    
    Args:
        redis_client: Redis client
        key_pattern: Key pattern to match
        sample_size: Number of keys to sample
        
    Returns:
        Dictionary with memory usage statistics
    """
    # Get keys matching pattern
    keys = redis_client.keys(key_pattern)
    
    # Sample keys if there are too many
    if len(keys) > sample_size:
        import random
        keys = random.sample(keys, sample_size)
    
    # Get memory usage for each key
    key_memory = {}
    total_memory = 0
    
    for key in keys:
        memory = redis_client.memory_usage(key)
        key_memory[key] = memory
        total_memory += memory
    
    # Calculate statistics
    stats = {
        'total_keys': len(keys),
        'total_memory': total_memory,
        'avg_memory_per_key': total_memory / len(keys) if keys else 0,
        'min_memory': min(key_memory.values()) if key_memory else 0,
        'max_memory': max(key_memory.values()) if key_memory else 0,
        'key_memory': key_memory
    }
    
    logger.info(f"Memory usage for {len(keys)} keys matching '{key_pattern}':")
    logger.info(f"Total: {stats['total_memory'] / 1024 / 1024:.2f} MB")
    logger.info(f"Average: {stats['avg_memory_per_key'] / 1024:.2f} KB per key")
    
    return stats

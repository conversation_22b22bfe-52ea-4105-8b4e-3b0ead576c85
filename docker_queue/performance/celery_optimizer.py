"""
Celery optimization tools for BahtBrowse Docker Queue Management.
"""

import time
import logging
import functools
import threading
from celery import group, chord, chain, signature

# Configure logging
logger = logging.getLogger(__name__)

def optimize_task(func=None, *, retry_backoff=True, rate_limit=None, time_limit=None, soft_time_limit=None):
    """
    Decorator to optimize a Celery task.
    
    Args:
        func: Task function
        retry_backoff: Whether to use exponential backoff for retries
        rate_limit: Rate limit for the task
        time_limit: Hard time limit for the task
        soft_time_limit: Soft time limit for the task
        
    Returns:
        Optimized task function
    """
    def decorator(func):
        # Set task options
        task_options = {}
        
        if retry_backoff:
            task_options['autoretry_for'] = (Exception,)
            task_options['retry_kwargs'] = {'max_retries': 5, 'countdown': lambda n: 2 ** n}
        
        if rate_limit:
            task_options['rate_limit'] = rate_limit
        
        if time_limit:
            task_options['time_limit'] = time_limit
        
        if soft_time_limit:
            task_options['soft_time_limit'] = soft_time_limit
        
        # Apply options to task
        func.options.update(task_options)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

def batch_tasks(task, items, batch_size=10):
    """
    Batch Celery tasks for better performance.
    
    Args:
        task: Celery task
        items: List of items to process
        batch_size: Batch size
        
    Returns:
        List of task results
    """
    # Split items into batches
    batches = [items[i:i+batch_size] for i in range(0, len(items), batch_size)]
    
    # Create task group
    task_group = group(task.s(batch) for batch in batches)
    
    # Execute tasks
    group_result = task_group.apply_async()
    
    # Wait for results
    results = group_result.get()
    
    # Flatten results
    flat_results = []
    for batch_result in results:
        flat_results.extend(batch_result)
    
    return flat_results

def prioritize_tasks(tasks, priorities):
    """
    Prioritize Celery tasks.
    
    Args:
        tasks: List of (task, args, kwargs) tuples
        priorities: List of priorities (0-9, higher is more important)
        
    Returns:
        List of task results
    """
    # Create task signatures with priorities
    signatures = []
    for (task, args, kwargs), priority in zip(tasks, priorities):
        sig = task.signature(args=args, kwargs=kwargs, options={'priority': priority})
        signatures.append(sig)
    
    # Execute tasks
    results = []
    for sig in signatures:
        results.append(sig.apply_async())
    
    # Wait for results
    return [result.get() for result in results]

def optimize_worker_concurrency(app, min_concurrency=2, max_concurrency=8, target_cpu_usage=0.7):
    """
    Optimize Celery worker concurrency based on CPU usage.
    
    Args:
        app: Celery app
        min_concurrency: Minimum concurrency
        max_concurrency: Maximum concurrency
        target_cpu_usage: Target CPU usage (0.0-1.0)
        
    Returns:
        Optimal concurrency
    """
    import psutil
    
    # Get current CPU usage
    cpu_usage = psutil.cpu_percent(interval=1) / 100.0
    
    # Calculate optimal concurrency
    if cpu_usage < target_cpu_usage:
        # CPU usage is below target, increase concurrency
        optimal_concurrency = min(max_concurrency, app.conf.worker_concurrency + 1)
    elif cpu_usage > target_cpu_usage + 0.1:
        # CPU usage is above target, decrease concurrency
        optimal_concurrency = max(min_concurrency, app.conf.worker_concurrency - 1)
    else:
        # CPU usage is within target range, keep current concurrency
        optimal_concurrency = app.conf.worker_concurrency
    
    logger.info(f"Optimized worker concurrency: {optimal_concurrency} (CPU usage: {cpu_usage:.2f})")
    
    return optimal_concurrency

def optimize_task_routing(app, task_name, queue=None, exchange=None, routing_key=None):
    """
    Optimize Celery task routing.
    
    Args:
        app: Celery app
        task_name: Task name
        queue: Queue name
        exchange: Exchange name
        routing_key: Routing key
        
    Returns:
        Updated task routes
    """
    # Get current task routes
    task_routes = app.conf.task_routes or {}
    
    # Update task route
    route = {}
    if queue:
        route['queue'] = queue
    if exchange:
        route['exchange'] = exchange
    if routing_key:
        route['routing_key'] = routing_key
    
    task_routes[task_name] = route
    
    # Update app configuration
    app.conf.task_routes = task_routes
    
    logger.info(f"Optimized routing for task {task_name}: {route}")
    
    return task_routes

def analyze_task_performance(app, task_name, sample_size=100):
    """
    Analyze Celery task performance.
    
    Args:
        app: Celery app
        task_name: Task name
        sample_size: Number of task executions to analyze
        
    Returns:
        Dictionary with task performance statistics
    """
    from celery.events import EventReceiver
    from kombu import Connection
    
    # Get task
    task = app.tasks[task_name]
    
    # Initialize statistics
    stats = {
        'task_name': task_name,
        'sample_size': sample_size,
        'executions': [],
        'success_count': 0,
        'failure_count': 0,
        'retry_count': 0
    }
    
    # Set up event receiver
    with Connection(app.conf.broker_url) as conn:
        receiver = EventReceiver(
            conn,
            handlers={
                'task-sent': lambda event: handle_task_event(event, 'sent', stats),
                'task-received': lambda event: handle_task_event(event, 'received', stats),
                'task-started': lambda event: handle_task_event(event, 'started', stats),
                'task-succeeded': lambda event: handle_task_event(event, 'succeeded', stats),
                'task-failed': lambda event: handle_task_event(event, 'failed', stats),
                'task-retried': lambda event: handle_task_event(event, 'retried', stats),
                'task-revoked': lambda event: handle_task_event(event, 'revoked', stats)
            }
        )
        
        # Start event capture in a separate thread
        stop_event = threading.Event()
        
        def capture_events():
            try:
                receiver.capture(limit=None, timeout=None, wakeup=True)
            except Exception as e:
                logger.error(f"Error capturing events: {str(e)}")
        
        event_thread = threading.Thread(target=capture_events)
        event_thread.daemon = True
        event_thread.start()
        
        # Execute tasks
        logger.info(f"Analyzing performance of task {task_name} with {sample_size} executions...")
        
        for i in range(sample_size):
            # Execute task
            task.apply_async()
            
            # Sleep briefly to avoid overwhelming the broker
            time.sleep(0.1)
        
        # Wait for all tasks to complete
        time.sleep(5)
        
        # Stop event capture
        stop_event.set()
        receiver.should_stop = True
        event_thread.join(timeout=1)
    
    # Calculate statistics
    if stats['executions']:
        durations = [e['duration'] for e in stats['executions'] if 'duration' in e]
        
        if durations:
            stats['duration'] = {
                'min': min(durations),
                'max': max(durations),
                'avg': sum(durations) / len(durations),
                'median': sorted(durations)[len(durations) // 2]
            }
            
            # Calculate percentiles
            if len(durations) >= 10:
                sorted_durations = sorted(durations)
                stats['duration']['percentiles'] = {
                    '50': sorted_durations[int(len(durations) * 0.5)],
                    '90': sorted_durations[int(len(durations) * 0.9)],
                    '95': sorted_durations[int(len(durations) * 0.95)],
                    '99': sorted_durations[int(len(durations) * 0.99)]
                }
    
    logger.info(f"Task performance analysis complete for {task_name}")
    logger.info(f"Success rate: {stats['success_count'] / sample_size * 100:.2f}%")
    
    if 'duration' in stats:
        logger.info(f"Average duration: {stats['duration']['avg']:.6f} seconds")
        logger.info(f"Median duration: {stats['duration']['median']:.6f} seconds")
    
    return stats

def handle_task_event(event, event_type, stats):
    """
    Handle a Celery task event.
    
    Args:
        event: Event data
        event_type: Event type
        stats: Statistics dictionary
    """
    # Check if this is the task we're analyzing
    if event['name'] != stats['task_name']:
        return
    
    # Find or create execution record
    execution = None
    for e in stats['executions']:
        if e['uuid'] == event['uuid']:
            execution = e
            break
    
    if not execution:
        execution = {'uuid': event['uuid']}
        stats['executions'].append(execution)
    
    # Update execution record
    execution[f'{event_type}_timestamp'] = event.get('timestamp')
    
    # Update event-specific data
    if event_type == 'sent':
        execution['sent'] = True
    
    elif event_type == 'received':
        execution['received'] = True
    
    elif event_type == 'started':
        execution['started'] = True
    
    elif event_type == 'succeeded':
        execution['succeeded'] = True
        stats['success_count'] += 1
        
        # Calculate duration if possible
        if 'started_timestamp' in execution and 'timestamp' in event:
            execution['duration'] = event['timestamp'] - execution['started_timestamp']
    
    elif event_type == 'failed':
        execution['failed'] = True
        stats['failure_count'] += 1
        execution['exception'] = event.get('exception')
    
    elif event_type == 'retried':
        execution['retried'] = True
        stats['retry_count'] += 1
        execution['exception'] = event.get('exception')
    
    elif event_type == 'revoked':
        execution['revoked'] = True
        execution['terminated'] = event.get('terminated')
        execution['expired'] = event.get('expired')

def optimize_task_queue(app, queue_name, prefetch_multiplier=None, acks_late=None, acks_on_failure_or_timeout=None):
    """
    Optimize Celery task queue settings.
    
    Args:
        app: Celery app
        queue_name: Queue name
        prefetch_multiplier: Prefetch multiplier
        acks_late: Whether to acknowledge messages after task execution
        acks_on_failure_or_timeout: Whether to acknowledge messages on failure or timeout
        
    Returns:
        Updated queue settings
    """
    # Update app configuration
    if prefetch_multiplier is not None:
        app.conf.worker_prefetch_multiplier = prefetch_multiplier
    
    if acks_late is not None:
        app.conf.task_acks_late = acks_late
    
    if acks_on_failure_or_timeout is not None:
        app.conf.task_acks_on_failure_or_timeout = acks_on_failure_or_timeout
    
    logger.info(f"Optimized queue settings for {queue_name}:")
    logger.info(f"  prefetch_multiplier: {app.conf.worker_prefetch_multiplier}")
    logger.info(f"  acks_late: {app.conf.task_acks_late}")
    logger.info(f"  acks_on_failure_or_timeout: {app.conf.task_acks_on_failure_or_timeout}")
    
    return {
        'prefetch_multiplier': app.conf.worker_prefetch_multiplier,
        'acks_late': app.conf.task_acks_late,
        'acks_on_failure_or_timeout': app.conf.task_acks_on_failure_or_timeout
    }

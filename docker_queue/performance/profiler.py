"""
Profiling tools for BahtBrowse Docker Queue Management.
"""

import cProfile
import pstats
import io
import time
import functools
import logging
import os
from contextlib import contextmanager

# Configure logging
logger = logging.getLogger(__name__)

# Directory for profiling results
PROFILE_DIR = os.environ.get('PROFILE_DIR', '/tmp/bahtbrowse_profiles')

# Create profile directory if it doesn't exist
os.makedirs(PROFILE_DIR, exist_ok=True)

@contextmanager
def profile_context(name):
    """
    Context manager for profiling a block of code.
    
    Args:
        name: Name for the profile
    
    Example:
        with profile_context('my_operation'):
            # Code to profile
            result = do_something()
    """
    profiler = cProfile.Profile()
    profiler.enable()
    
    try:
        yield
    finally:
        profiler.disable()
        
        # Create string buffer for stats
        s = io.StringIO()
        
        # Sort stats by cumulative time
        ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
        
        # Print stats to buffer
        ps.print_stats(20)  # Top 20 functions
        
        # Log stats
        logger.info(f"Profile for {name}:\n{s.getvalue()}")
        
        # Save profile to file
        profile_path = os.path.join(PROFILE_DIR, f"{name}_{int(time.time())}.prof")
        profiler.dump_stats(profile_path)
        logger.info(f"Profile saved to {profile_path}")

def profile(func=None, *, name=None):
    """
    Decorator for profiling a function.
    
    Args:
        func: Function to profile
        name: Optional name for the profile (defaults to function name)
    
    Example:
        @profile
        def my_function():
            # Function code
            
        @profile(name='custom_name')
        def another_function():
            # Function code
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profile_name = name or func.__name__
            with profile_context(profile_name):
                return func(*args, **kwargs)
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

def time_function(func=None, *, name=None, log_level=logging.INFO):
    """
    Decorator for timing a function.
    
    Args:
        func: Function to time
        name: Optional name for the timer (defaults to function name)
        log_level: Logging level for the timing information
    
    Example:
        @time_function
        def my_function():
            # Function code
            
        @time_function(name='custom_name', log_level=logging.DEBUG)
        def another_function():
            # Function code
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            timer_name = name or func.__name__
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            elapsed_time = end_time - start_time
            logger.log(log_level, f"{timer_name} took {elapsed_time:.6f} seconds")
            return result
        return wrapper
    
    if func is None:
        return decorator
    return decorator(func)

@contextmanager
def timer_context(name, log_level=logging.INFO):
    """
    Context manager for timing a block of code.
    
    Args:
        name: Name for the timer
        log_level: Logging level for the timing information
    
    Example:
        with timer_context('my_operation'):
            # Code to time
            result = do_something()
    """
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.log(log_level, f"{name} took {elapsed_time:.6f} seconds")

def analyze_profile(profile_path):
    """
    Analyze a profile file and return the results.
    
    Args:
        profile_path: Path to the profile file
        
    Returns:
        String with profile analysis
    """
    # Create string buffer for stats
    s = io.StringIO()
    
    # Load stats from file
    ps = pstats.Stats(profile_path, stream=s)
    
    # Sort and print stats
    ps.sort_stats('cumulative').print_stats(30)  # Top 30 functions
    ps.sort_stats('time').print_stats(30)  # Top 30 functions by time
    ps.sort_stats('calls').print_stats(30)  # Top 30 functions by calls
    
    return s.getvalue()

def list_profiles():
    """
    List all profile files.
    
    Returns:
        List of profile file paths
    """
    return [os.path.join(PROFILE_DIR, f) for f in os.listdir(PROFILE_DIR) if f.endswith('.prof')]

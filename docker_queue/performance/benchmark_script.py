#!/usr/bin/env python3
"""
Benchmark script for BahtBrowse Docker Queue Management.
"""

import os
import sys
import time
import argparse
import logging
import json
import concurrent.futures
import requests
import uuid
import statistics
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('benchmark')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Benchmark BahtBrowse Docker Queue Management')
    
    parser.add_argument('--api-url', type=str, default='http://localhost:5000',
                        help='API URL (default: http://localhost:5000)')
    parser.add_argument('--concurrency', type=int, default=10,
                        help='Number of concurrent users (default: 10)')
    parser.add_argument('--duration', type=int, default=60,
                        help='Benchmark duration in seconds (default: 60)')
    parser.add_argument('--ramp-up', type=int, default=5,
                        help='Ramp-up time in seconds (default: 5)')
    parser.add_argument('--output', type=str, default='benchmark_results.json',
                        help='Output file for results (default: benchmark_results.json)')
    parser.add_argument('--scenario', type=str, choices=['request', 'pool', 'all'], default='all',
                        help='Benchmark scenario (default: all)')
    
    return parser.parse_args()

def benchmark_container_request(api_url, user_id):
    """
    Benchmark container request.
    
    Args:
        api_url: API URL
        user_id: User ID
        
    Returns:
        Dictionary with benchmark results
    """
    start_time = time.time()
    
    # Request container
    response = requests.post(
        f"{api_url}/api/containers/request",
        json={'user_id': user_id, 'priority': 5},
        timeout=30
    )
    
    request_time = time.time() - start_time
    
    # Check response
    if response.status_code != 200:
        logger.error(f"Error requesting container: {response.status_code} {response.text}")
        return {
            'success': False,
            'user_id': user_id,
            'request_time': request_time,
            'error': f"HTTP {response.status_code}"
        }
    
    data = response.json()
    
    if data['status'] == 'success':
        # Container assigned immediately
        return {
            'success': True,
            'user_id': user_id,
            'request_time': request_time,
            'container_assigned': True,
            'container_id': data['container'].get('container_id')
        }
    elif data['status'] == 'pending':
        # Container request queued
        request_id = data['request_id']
        queue_position = data.get('queue_position', 0)
        
        # Wait for container assignment
        wait_start_time = time.time()
        assigned = False
        container_id = None
        
        # Poll for status
        while time.time() - wait_start_time < 60:  # Wait up to 60 seconds
            time.sleep(1)
            
            status_response = requests.get(
                f"{api_url}/api/containers/status/{request_id}",
                params={'user_id': user_id},
                timeout=30
            )
            
            if status_response.status_code != 200:
                continue
            
            status_data = status_response.json()
            
            if status_data['status'] == 'success':
                # Container assigned
                assigned = True
                container_id = status_data['container'].get('container_id')
                break
        
        wait_time = time.time() - wait_start_time
        total_time = time.time() - start_time
        
        return {
            'success': True,
            'user_id': user_id,
            'request_time': request_time,
            'wait_time': wait_time,
            'total_time': total_time,
            'container_assigned': assigned,
            'container_id': container_id,
            'queue_position': queue_position
        }
    else:
        # Error
        return {
            'success': False,
            'user_id': user_id,
            'request_time': request_time,
            'error': data.get('message', 'Unknown error')
        }

def benchmark_container_release(api_url, user_id, container_id):
    """
    Benchmark container release.
    
    Args:
        api_url: API URL
        user_id: User ID
        container_id: Container ID
        
    Returns:
        Dictionary with benchmark results
    """
    start_time = time.time()
    
    # Release container
    response = requests.post(
        f"{api_url}/api/containers/release",
        json={'user_id': user_id},
        timeout=30
    )
    
    release_time = time.time() - start_time
    
    # Check response
    if response.status_code != 200:
        logger.error(f"Error releasing container: {response.status_code} {response.text}")
        return {
            'success': False,
            'user_id': user_id,
            'container_id': container_id,
            'release_time': release_time,
            'error': f"HTTP {response.status_code}"
        }
    
    data = response.json()
    
    return {
        'success': data['status'] == 'success',
        'user_id': user_id,
        'container_id': container_id,
        'release_time': release_time,
        'message': data.get('message')
    }

def benchmark_pool_status(api_url):
    """
    Benchmark pool status.
    
    Args:
        api_url: API URL
        
    Returns:
        Dictionary with benchmark results
    """
    start_time = time.time()
    
    # Get pool status
    response = requests.get(
        f"{api_url}/api/pool/status",
        timeout=30
    )
    
    status_time = time.time() - start_time
    
    # Check response
    if response.status_code != 200:
        logger.error(f"Error getting pool status: {response.status_code} {response.text}")
        return {
            'success': False,
            'status_time': status_time,
            'error': f"HTTP {response.status_code}"
        }
    
    data = response.json()
    
    return {
        'success': data['status'] == 'success',
        'status_time': status_time,
        'pool': data.get('pool')
    }

def benchmark_pool_scale(api_url, target_size):
    """
    Benchmark pool scaling.
    
    Args:
        api_url: API URL
        target_size: Target pool size
        
    Returns:
        Dictionary with benchmark results
    """
    start_time = time.time()
    
    # Scale pool
    response = requests.post(
        f"{api_url}/api/pool/scale",
        json={'target_size': target_size},
        timeout=30
    )
    
    scale_time = time.time() - start_time
    
    # Check response
    if response.status_code != 200:
        logger.error(f"Error scaling pool: {response.status_code} {response.text}")
        return {
            'success': False,
            'scale_time': scale_time,
            'error': f"HTTP {response.status_code}"
        }
    
    data = response.json()
    
    return {
        'success': data['status'] == 'success',
        'scale_time': scale_time,
        'scaling': data.get('scaling')
    }

def benchmark_metrics(api_url):
    """
    Benchmark metrics.
    
    Args:
        api_url: API URL
        
    Returns:
        Dictionary with benchmark results
    """
    start_time = time.time()
    
    # Get metrics
    response = requests.get(
        f"{api_url}/api/metrics/current",
        timeout=30
    )
    
    metrics_time = time.time() - start_time
    
    # Check response
    if response.status_code != 200:
        logger.error(f"Error getting metrics: {response.status_code} {response.text}")
        return {
            'success': False,
            'metrics_time': metrics_time,
            'error': f"HTTP {response.status_code}"
        }
    
    data = response.json()
    
    return {
        'success': data['status'] == 'success',
        'metrics_time': metrics_time,
        'metrics': data.get('metrics')
    }

def run_container_request_scenario(api_url, concurrency, duration, ramp_up):
    """
    Run container request benchmark scenario.
    
    Args:
        api_url: API URL
        concurrency: Number of concurrent users
        duration: Benchmark duration in seconds
        ramp_up: Ramp-up time in seconds
        
    Returns:
        Dictionary with benchmark results
    """
    logger.info(f"Running container request scenario with {concurrency} concurrent users for {duration} seconds")
    
    # Initialize results
    results = {
        'scenario': 'container_request',
        'concurrency': concurrency,
        'duration': duration,
        'ramp_up': ramp_up,
        'start_time': time.time(),
        'requests': [],
        'releases': []
    }
    
    # Create user IDs
    user_ids = [f"benchmark_user_{i}_{uuid.uuid4()}" for i in range(concurrency)]
    
    # Create thread pool
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        # Submit initial requests
        future_to_user = {}
        for i, user_id in enumerate(user_ids):
            # Ramp up
            if ramp_up > 0:
                time.sleep(ramp_up / concurrency)
            
            future = executor.submit(benchmark_container_request, api_url, user_id)
            future_to_user[future] = user_id
        
        # Process results
        for future in concurrent.futures.as_completed(future_to_user):
            user_id = future_to_user[future]
            
            try:
                result = future.result()
                results['requests'].append(result)
                
                # If container was assigned, release it
                if result.get('container_assigned') and result.get('container_id'):
                    release_future = executor.submit(
                        benchmark_container_release,
                        api_url,
                        user_id,
                        result['container_id']
                    )
                    results['releases'].append(release_future.result())
            
            except Exception as e:
                logger.error(f"Error in container request scenario: {str(e)}")
                results['requests'].append({
                    'success': False,
                    'user_id': user_id,
                    'error': str(e)
                })
    
    # Calculate end time
    results['end_time'] = time.time()
    results['actual_duration'] = results['end_time'] - results['start_time']
    
    # Calculate statistics
    if results['requests']:
        request_times = [r['request_time'] for r in results['requests'] if r.get('success') and 'request_time' in r]
        wait_times = [r['wait_time'] for r in results['requests'] if r.get('success') and 'wait_time' in r]
        total_times = [r['total_time'] for r in results['requests'] if r.get('success') and 'total_time' in r]
        
        results['statistics'] = {
            'request_count': len(results['requests']),
            'success_count': sum(1 for r in results['requests'] if r.get('success')),
            'container_assigned_count': sum(1 for r in results['requests'] if r.get('container_assigned')),
            'request_time': {
                'min': min(request_times) if request_times else None,
                'max': max(request_times) if request_times else None,
                'avg': statistics.mean(request_times) if request_times else None,
                'median': statistics.median(request_times) if request_times else None
            },
            'wait_time': {
                'min': min(wait_times) if wait_times else None,
                'max': max(wait_times) if wait_times else None,
                'avg': statistics.mean(wait_times) if wait_times else None,
                'median': statistics.median(wait_times) if wait_times else None
            },
            'total_time': {
                'min': min(total_times) if total_times else None,
                'max': max(total_times) if total_times else None,
                'avg': statistics.mean(total_times) if total_times else None,
                'median': statistics.median(total_times) if total_times else None
            }
        }
        
        # Calculate percentiles
        if len(request_times) >= 10:
            results['statistics']['request_time']['percentiles'] = {
                '90': statistics.quantiles(request_times, n=10)[8],
                '95': statistics.quantiles(request_times, n=20)[18],
                '99': statistics.quantiles(request_times, n=100)[98] if len(request_times) >= 100 else None
            }
        
        if len(total_times) >= 10:
            results['statistics']['total_time']['percentiles'] = {
                '90': statistics.quantiles(total_times, n=10)[8],
                '95': statistics.quantiles(total_times, n=20)[18],
                '99': statistics.quantiles(total_times, n=100)[98] if len(total_times) >= 100 else None
            }
    
    return results

def run_pool_management_scenario(api_url, duration):
    """
    Run pool management benchmark scenario.
    
    Args:
        api_url: API URL
        duration: Benchmark duration in seconds
        
    Returns:
        Dictionary with benchmark results
    """
    logger.info(f"Running pool management scenario for {duration} seconds")
    
    # Initialize results
    results = {
        'scenario': 'pool_management',
        'duration': duration,
        'start_time': time.time(),
        'status_checks': [],
        'scaling_operations': [],
        'metrics_checks': []
    }
    
    # Run benchmark
    end_time = time.time() + duration
    
    while time.time() < end_time:
        # Check pool status
        results['status_checks'].append(benchmark_pool_status(api_url))
        
        # Get current pool size
        current_size = results['status_checks'][-1].get('pool', {}).get('total_containers', 10)
        
        # Scale pool up or down
        if len(results['scaling_operations']) % 2 == 0:
            # Scale up
            target_size = current_size + 2
        else:
            # Scale down
            target_size = max(5, current_size - 2)
        
        results['scaling_operations'].append(benchmark_pool_scale(api_url, target_size))
        
        # Check metrics
        results['metrics_checks'].append(benchmark_metrics(api_url))
        
        # Sleep
        time.sleep(5)
    
    # Calculate end time
    results['end_time'] = time.time()
    results['actual_duration'] = results['end_time'] - results['start_time']
    
    # Calculate statistics
    if results['status_checks']:
        status_times = [r['status_time'] for r in results['status_checks'] if r.get('success') and 'status_time' in r]
        
        results['statistics'] = {
            'status_check_count': len(results['status_checks']),
            'scaling_operation_count': len(results['scaling_operations']),
            'metrics_check_count': len(results['metrics_checks']),
            'status_time': {
                'min': min(status_times) if status_times else None,
                'max': max(status_times) if status_times else None,
                'avg': statistics.mean(status_times) if status_times else None,
                'median': statistics.median(status_times) if status_times else None
            }
        }
        
        # Calculate scaling statistics
        if results['scaling_operations']:
            scale_times = [r['scale_time'] for r in results['scaling_operations'] if r.get('success') and 'scale_time' in r]
            
            results['statistics']['scale_time'] = {
                'min': min(scale_times) if scale_times else None,
                'max': max(scale_times) if scale_times else None,
                'avg': statistics.mean(scale_times) if scale_times else None,
                'median': statistics.median(scale_times) if scale_times else None
            }
        
        # Calculate metrics statistics
        if results['metrics_checks']:
            metrics_times = [r['metrics_time'] for r in results['metrics_checks'] if r.get('success') and 'metrics_time' in r]
            
            results['statistics']['metrics_time'] = {
                'min': min(metrics_times) if metrics_times else None,
                'max': max(metrics_times) if metrics_times else None,
                'avg': statistics.mean(metrics_times) if metrics_times else None,
                'median': statistics.median(metrics_times) if metrics_times else None
            }
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Initialize results
    results = {
        'timestamp': time.time(),
        'datetime': datetime.now().isoformat(),
        'api_url': args.api_url,
        'concurrency': args.concurrency,
        'duration': args.duration,
        'ramp_up': args.ramp_up,
        'scenario': args.scenario,
        'scenarios': {}
    }
    
    # Run scenarios
    if args.scenario in ['request', 'all']:
        results['scenarios']['container_request'] = run_container_request_scenario(
            args.api_url,
            args.concurrency,
            args.duration,
            args.ramp_up
        )
    
    if args.scenario in ['pool', 'all']:
        results['scenarios']['pool_management'] = run_pool_management_scenario(
            args.api_url,
            args.duration
        )
    
    # Save results
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Benchmark results saved to {args.output}")
    
    # Print summary
    print("\n=== Benchmark Summary ===")
    print(f"API URL: {args.api_url}")
    print(f"Scenario: {args.scenario}")
    print(f"Concurrency: {args.concurrency}")
    print(f"Duration: {args.duration} seconds")
    
    if 'container_request' in results['scenarios']:
        stats = results['scenarios']['container_request'].get('statistics', {})
        print("\nContainer Request Scenario:")
        print(f"  Requests: {stats.get('request_count', 0)}")
        print(f"  Success Rate: {stats.get('success_count', 0) / stats.get('request_count', 1) * 100:.2f}%")
        print(f"  Container Assignment Rate: {stats.get('container_assigned_count', 0) / stats.get('request_count', 1) * 100:.2f}%")
        
        if 'request_time' in stats:
            print(f"  Request Time (median): {stats['request_time'].get('median', 0):.6f} seconds")
        
        if 'total_time' in stats:
            print(f"  Total Time (median): {stats['total_time'].get('median', 0):.6f} seconds")
            
            if 'percentiles' in stats['total_time']:
                print(f"  Total Time (95th percentile): {stats['total_time']['percentiles'].get('95', 0):.6f} seconds")
    
    if 'pool_management' in results['scenarios']:
        stats = results['scenarios']['pool_management'].get('statistics', {})
        print("\nPool Management Scenario:")
        print(f"  Status Checks: {stats.get('status_check_count', 0)}")
        print(f"  Scaling Operations: {stats.get('scaling_operation_count', 0)}")
        print(f"  Metrics Checks: {stats.get('metrics_check_count', 0)}")
        
        if 'status_time' in stats:
            print(f"  Status Check Time (median): {stats['status_time'].get('median', 0):.6f} seconds")
        
        if 'scale_time' in stats:
            print(f"  Scaling Operation Time (median): {stats['scale_time'].get('median', 0):.6f} seconds")
        
        if 'metrics_time' in stats:
            print(f"  Metrics Check Time (median): {stats['metrics_time'].get('median', 0):.6f} seconds")
    
    print("\nFull results saved to:", args.output)

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Apply performance optimizations to BahtBrowse Docker Queue Management code.
"""

import os
import sys
import argparse
import logging
import importlib
import inspect
import re
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('apply_optimizations')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Apply performance optimizations to BahtBrowse Docker Queue Management code')
    
    parser.add_argument('--target', type=str, choices=['redis', 'docker', 'celery', 'all'], default='all',
                        help='Optimization target (default: all)')
    parser.add_argument('--dry-run', action='store_true',
                        help='Dry run (do not modify files)')
    
    return parser.parse_args()

def optimize_redis_client():
    """
    Optimize Redis client code.
    
    Returns:
        List of modified files
    """
    logger.info("Optimizing Redis client code...")
    
    # Path to Redis client module
    redis_client_path = Path('docker_queue/redis_client.py')
    
    if not redis_client_path.exists():
        logger.error(f"Redis client module not found at {redis_client_path}")
        return []
    
    # Read current content
    with open(redis_client_path, 'r') as f:
        content = f.read()
    
    # Apply optimizations
    
    # 1. Add connection pool configuration
    if 'ConnectionPool' not in content:
        # Add import
        content = re.sub(
            r'import redis',
            'import redis\nfrom redis import ConnectionPool',
            content
        )
        
        # Add connection pool
        content = re.sub(
            r'redis_client = redis.Redis\(.*?\)',
            '''# Create connection pool
pool = ConnectionPool(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=0,
    max_connections=100,
    socket_timeout=5,
    socket_connect_timeout=5,
    socket_keepalive=True,
    retry_on_timeout=True
)

# Create Redis client
redis_client = redis.Redis(connection_pool=pool)''',
            content
        )
    
    # 2. Add pipeline usage for batch operations
    if 'def batch_operations' not in content:
        # Add batch operations function
        batch_func = '''
def batch_operations(operations):
    """
    Execute multiple Redis operations in a pipeline.
    
    Args:
        operations: List of (operation, args, kwargs) tuples
        
    Returns:
        List of operation results
    """
    # Create pipeline
    pipeline = redis_client.pipeline()
    
    # Add operations to pipeline
    for operation, args, kwargs in operations:
        getattr(pipeline, operation)(*args, **kwargs)
    
    # Execute pipeline
    return pipeline.execute()
'''
        
        # Add function to module
        content = content + '\n' + batch_func
    
    # 3. Add caching for frequently accessed data
    if 'class RedisCache' not in content:
        # Add cache class
        cache_class = '''
class RedisCache:
    """
    Cache for Redis operations.
    """
    
    def __init__(self, ttl=60):
        """
        Initialize a Redis cache.
        
        Args:
            ttl: Cache TTL in seconds
        """
        self.ttl = ttl
        self.cache = {}
        self.last_updated = {}
    
    def get(self, key):
        """
        Get a value from the cache or Redis.
        
        Args:
            key: Redis key
            
        Returns:
            Value from cache or Redis
        """
        # Check if key is in cache and not expired
        import time
        if key in self.cache and time.time() - self.last_updated.get(key, 0) < self.ttl:
            return self.cache[key]
        
        # Get value from Redis
        value = redis_client.get(key)
        
        # Update cache
        self.cache[key] = value
        self.last_updated[key] = time.time()
        
        return value
    
    def set(self, key, value):
        """
        Set a value in the cache and Redis.
        
        Args:
            key: Redis key
            value: Value to set
        """
        # Set value in Redis
        redis_client.set(key, value)
        
        # Update cache
        self.cache[key] = value
        self.last_updated[key] = time.time()
    
    def delete(self, key):
        """
        Delete a value from the cache and Redis.
        
        Args:
            key: Redis key
        """
        # Delete from Redis
        redis_client.delete(key)
        
        # Delete from cache
        if key in self.cache:
            del self.cache[key]
        if key in self.last_updated:
            del self.last_updated[key]
    
    def clear(self):
        """Clear the cache."""
        self.cache = {}
        self.last_updated = {}

# Create global cache instance
redis_cache = RedisCache()
'''
        
        # Add class to module
        content = content + '\n' + cache_class
    
    # Write modified content
    with open(redis_client_path, 'w') as f:
        f.write(content)
    
    logger.info(f"Optimized Redis client code in {redis_client_path}")
    
    return [redis_client_path]

def optimize_docker_client():
    """
    Optimize Docker client code.
    
    Returns:
        List of modified files
    """
    logger.info("Optimizing Docker client code...")
    
    # Path to Docker client module
    docker_client_path = Path('docker_queue/docker_client.py')
    
    if not docker_client_path.exists():
        logger.error(f"Docker client module not found at {docker_client_path}")
        return []
    
    # Read current content
    with open(docker_client_path, 'r') as f:
        content = f.read()
    
    # Apply optimizations
    
    # 1. Add container template for faster container creation
    if 'def create_container_template' not in content:
        # Add container template function
        template_func = '''
def create_container_template(image_name, **kwargs):
    """
    Create a container template for faster container creation.
    
    Args:
        image_name: Image name
        **kwargs: Container creation arguments
        
    Returns:
        Container template function
    """
    # Create template function
    def create_from_template(**override_kwargs):
        # Merge kwargs
        container_kwargs = kwargs.copy()
        container_kwargs.update(override_kwargs)
        
        # Create container
        return docker_client.containers.run(image_name, **container_kwargs)
    
    return create_from_template

# Create container template with common settings
container_template = create_container_template(
    CONTAINER_IMAGE,
    detach=True,
    network=CONTAINER_NETWORK,
    cpu_quota=int(1.0 * 100000),
    mem_limit='2g',
    restart_policy={"Name": "unless-stopped"},
    labels={'app': 'bahtbrowse'}
)
'''
        
        # Add function to module
        content = content + '\n' + template_func
        
        # Update create_container function to use template
        content = re.sub(
            r'def create_container\(\):(.*?)return container_details',
            r'''def create_container():
    """
    Create a new Docker container.
    
    Returns:
        Dictionary with container details
    """
    try:
        # Generate container ID and name
        container_id = str(uuid.uuid4())
        container_name = f"bahtbrowse_{container_id[:8]}"
        
        # Create container using template
        container = container_template(
            name=container_name,
            environment={
                'CONTAINER_ID': container_id
            }
        )
        
        # Get container IP address
        ip_address = get_container_ip(container.id)
        
        # Create container details
        container_details = {
            'container_id': container_id,
            'docker_id': container.id,
            'name': container_name,
            'status': 'initializing',
            'created_at': time.time(),
            'ip_address': ip_address
        }
        
        logger.info(f"Created container {container_name} ({container.id})")
        
        return container_details''',
            content,
            flags=re.DOTALL
        )
    
    # 2. Add caching for container status and stats
    if 'class DockerCache' not in content:
        # Add cache class
        cache_class = '''
class DockerCache:
    """
    Cache for Docker operations.
    """
    
    def __init__(self, ttl=10):
        """
        Initialize a Docker cache.
        
        Args:
            ttl: Cache TTL in seconds
        """
        self.ttl = ttl
        self.cache = {}
        self.last_updated = {}
    
    def get_container_status(self, container_id):
        """
        Get container status from the cache or Docker.
        
        Args:
            container_id: Docker container ID
            
        Returns:
            Container status
        """
        cache_key = f"container_status:{container_id}"
        
        # Check if status is in cache and not expired
        import time
        if cache_key in self.cache and time.time() - self.last_updated.get(cache_key, 0) < self.ttl:
            return self.cache[cache_key]
        
        # Get container from Docker
        try:
            container = docker_client.containers.get(container_id)
            status = container.status
            
            # Update cache
            self.cache[cache_key] = status
            self.last_updated[cache_key] = time.time()
            
            return status
        except Exception as e:
            logger.error(f"Error getting container status: {str(e)}")
            return None
    
    def get_container_stats(self, container_id):
        """
        Get container stats from the cache or Docker.
        
        Args:
            container_id: Docker container ID
            
        Returns:
            Container stats
        """
        cache_key = f"container_stats:{container_id}"
        
        # Check if stats are in cache and not expired
        import time
        if cache_key in self.cache and time.time() - self.last_updated.get(cache_key, 0) < self.ttl:
            return self.cache[cache_key]
        
        # Get container from Docker
        try:
            container = docker_client.containers.get(container_id)
            stats = container.stats(stream=False)
            
            # Update cache
            self.cache[cache_key] = stats
            self.last_updated[cache_key] = time.time()
            
            return stats
        except Exception as e:
            logger.error(f"Error getting container stats: {str(e)}")
            return None
    
    def invalidate(self, container_id=None):
        """
        Invalidate cache entries.
        
        Args:
            container_id: Optional container ID to invalidate
        """
        if container_id:
            # Invalidate specific container
            for key in list(self.cache.keys()):
                if key.endswith(container_id):
                    del self.cache[key]
                    if key in self.last_updated:
                        del self.last_updated[key]
        else:
            # Invalidate all cache
            self.cache = {}
            self.last_updated = {}

# Create global cache instance
docker_cache = DockerCache()
'''
        
        # Add class to module
        content = content + '\n' + cache_class
        
        # Update get_container_status function to use cache
        content = re.sub(
            r'def get_container_status\(docker_id\):(.*?)return None',
            r'''def get_container_status(docker_id):
    """
    Get the status of a Docker container.
    
    Args:
        docker_id: Docker container ID
        
    Returns:
        Container status or None if error
    """
    return docker_cache.get_container_status(docker_id)''',
            content,
            flags=re.DOTALL
        )
        
        # Update get_container_stats function to use cache
        content = re.sub(
            r'def get_container_stats\(docker_id\):(.*?)return None',
            r'''def get_container_stats(docker_id):
    """
    Get the stats of a Docker container.
    
    Args:
        docker_id: Docker container ID
        
    Returns:
        Container stats or None if error
    """
    return docker_cache.get_container_stats(docker_id)''',
            content,
            flags=re.DOTALL
        )
    
    # Write modified content
    with open(docker_client_path, 'w') as f:
        f.write(content)
    
    logger.info(f"Optimized Docker client code in {docker_client_path}")
    
    return [docker_client_path]

def optimize_celery_tasks():
    """
    Optimize Celery tasks.
    
    Returns:
        List of modified files
    """
    logger.info("Optimizing Celery tasks...")
    
    modified_files = []
    
    # Paths to task modules
    task_modules = [
        Path('docker_queue/tasks/container_management.py'),
        Path('docker_queue/tasks/pool_management.py'),
        Path('docker_queue/tasks/monitoring.py')
    ]
    
    for task_path in task_modules:
        if not task_path.exists():
            logger.error(f"Task module not found at {task_path}")
            continue
        
        # Read current content
        with open(task_path, 'r') as f:
            content = f.read()
        
        # Apply optimizations
        
        # 1. Add task options for better performance
        
        # Find all task definitions
        task_defs = re.findall(r'@app\.task(?:\(.*?\))?\ndef (\w+)\(', content)
        
        for task_name in task_defs:
            # Add task options if not already present
            task_pattern = rf'@app\.task(?:\(.*?\))?\ndef {task_name}\('
            task_match = re.search(task_pattern, content)
            
            if task_match:
                # Check if task already has options
                if '@app.task(' in task_match.group(0):
                    # Task already has options, update them
                    content = re.sub(
                        rf'@app\.task\((.*?)\)\ndef {task_name}\(',
                        lambda m: update_task_options(m.group(1), task_name, task_path.name),
                        content
                    )
                else:
                    # Task has no options, add them
                    content = re.sub(
                        rf'@app\.task\ndef {task_name}\(',
                        lambda m: f'@app.task({get_task_options(task_name, task_path.name)})\ndef {task_name}(',
                        content
                    )
        
        # 2. Add batch processing for tasks that handle multiple items
        
        # Check if module has batch processing functions
        if 'container_management.py' in str(task_path) and 'def batch_process_requests' not in content:
            # Add batch processing function for container requests
            batch_func = '''
@app.task(queue='container_management')
def batch_process_requests(request_ids):
    """
    Process multiple container requests in batch.
    
    Args:
        request_ids: List of request IDs
        
    Returns:
        Dictionary with processing results
    """
    results = {}
    
    for request_id in request_ids:
        results[request_id] = process_container_request(request_id)
    
    return results
'''
            
            # Add function to module
            content = content + '\n' + batch_func
        
        # Write modified content
        with open(task_path, 'w') as f:
            f.write(content)
        
        logger.info(f"Optimized Celery tasks in {task_path}")
        modified_files.append(task_path)
    
    # Optimize Celery app configuration
    celery_app_path = Path('docker_queue/celery_app.py')
    
    if celery_app_path.exists():
        # Read current content
        with open(celery_app_path, 'r') as f:
            content = f.read()
        
        # Apply optimizations
        
        # 1. Add optimized Celery configuration
        if 'task_routes' not in content:
            # Add task routes
            config = '''
# Task routes
app.conf.task_routes = {
    'docker_queue.tasks.container_management.*': {'queue': 'container_management'},
    'docker_queue.tasks.pool_management.*': {'queue': 'pool_management'},
    'docker_queue.tasks.monitoring.*': {'queue': 'monitoring'}
}

# Task queue settings
app.conf.worker_prefetch_multiplier = 4
app.conf.task_acks_late = True
app.conf.task_acks_on_failure_or_timeout = False

# Task time limits
app.conf.task_time_limit = 300  # 5 minutes
app.conf.task_soft_time_limit = 240  # 4 minutes

# Task serialization
app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']

# Task result settings
app.conf.result_expires = 3600  # 1 hour
'''
            
            # Add configuration to module
            content = content + '\n' + config
        
        # Write modified content
        with open(celery_app_path, 'w') as f:
            f.write(content)
        
        logger.info(f"Optimized Celery app configuration in {celery_app_path}")
        modified_files.append(celery_app_path)
    
    return modified_files

def update_task_options(options_str, task_name, module_name):
    """
    Update task options.
    
    Args:
        options_str: Current options string
        task_name: Task name
        module_name: Module name
        
    Returns:
        Updated task decorator
    """
    # Parse current options
    options = {}
    for option in options_str.split(','):
        if '=' in option:
            key, value = option.split('=', 1)
            options[key.strip()] = value.strip()
    
    # Add or update options
    new_options = get_task_options_dict(task_name, module_name)
    options.update(new_options)
    
    # Format options string
    options_str = ', '.join(f'{k}={v}' for k, v in options.items())
    
    return f'@app.task({options_str})\ndef {task_name}('

def get_task_options(task_name, module_name):
    """
    Get task options.
    
    Args:
        task_name: Task name
        module_name: Module name
        
    Returns:
        Task options string
    """
    options = get_task_options_dict(task_name, module_name)
    return ', '.join(f'{k}={v}' for k, v in options.items())

def get_task_options_dict(task_name, module_name):
    """
    Get task options dictionary.
    
    Args:
        task_name: Task name
        module_name: Module name
        
    Returns:
        Task options dictionary
    """
    options = {}
    
    # Set queue based on module
    if 'container_management.py' in module_name:
        options['queue'] = "'container_management'"
    elif 'pool_management.py' in module_name:
        options['queue'] = "'pool_management'"
    elif 'monitoring.py' in module_name:
        options['queue'] = "'monitoring'"
    
    # Set retry options
    options['autoretry_for'] = '(Exception,)'
    options['retry_kwargs'] = "{'max_retries': 3, 'countdown': 5}"
    
    # Set task-specific options
    if task_name == 'create_and_initialize_container':
        options['time_limit'] = '300'  # 5 minutes
    elif task_name == 'process_container_request':
        options['priority'] = '9'  # High priority
    elif task_name == 'recycle_container':
        options['priority'] = '5'  # Medium priority
    elif task_name == 'maintain_container_pool':
        options['time_limit'] = '180'  # 3 minutes
    elif task_name == 'collect_metrics':
        options['time_limit'] = '120'  # 2 minutes
    
    return options

def main():
    """Main function."""
    args = parse_args()
    
    # Initialize results
    modified_files = []
    
    # Apply optimizations
    if args.target in ['redis', 'all']:
        if args.dry_run:
            logger.info("Dry run: would optimize Redis client code")
        else:
            modified_files.extend(optimize_redis_client())
    
    if args.target in ['docker', 'all']:
        if args.dry_run:
            logger.info("Dry run: would optimize Docker client code")
        else:
            modified_files.extend(optimize_docker_client())
    
    if args.target in ['celery', 'all']:
        if args.dry_run:
            logger.info("Dry run: would optimize Celery tasks")
        else:
            modified_files.extend(optimize_celery_tasks())
    
    # Print summary
    if args.dry_run:
        logger.info("Dry run completed. No files were modified.")
    else:
        logger.info(f"Optimization completed. Modified {len(modified_files)} files:")
        for file_path in modified_files:
            logger.info(f"  - {file_path}")

if __name__ == '__main__':
    main()

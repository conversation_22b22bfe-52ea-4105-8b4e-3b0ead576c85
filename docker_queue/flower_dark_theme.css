/* Dark Mode Theme for Celery Flower Dashboard */

:root {
  --background-primary: #121212;
  --background-secondary: #1e1e1e;
  --background-tertiary: #252525;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --accent-color: #64b5f6;
  --success-color: #4caf50;
  --warning-color: #ffb74d;
  --error-color: #ef5350;
  --border-color: #333333;
  --hover-color: #2c2c2c;
  --active-color: #3c3c3c;
  --chart-colors: #64b5f6, #4caf50, #ffb74d, #ef5350, #9e9e9e;
}

/* Apply dark mode to the entire page */
body {
  background-color: var(--background-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navbar styling */
.navbar {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

.navbar-inverse .navbar-brand,
.navbar-inverse .navbar-nav > li > a {
  color: var(--text-primary);
}

.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-nav > li > a:hover {
  color: var(--accent-color);
}

/* Panel styling */
.panel {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

.panel-heading {
  background-color: var(--background-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.panel-default > .panel-heading {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

/* Table styling */
.table {
  color: var(--text-primary);
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: var(--background-tertiary);
}

.table-hover > tbody > tr:hover {
  background-color: var(--hover-color);
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  border-color: var(--border-color);
}

/* Form controls */
.form-control {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 8px rgba(100, 181, 246, 0.6);
}

/* Buttons */
.btn-default {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-default:hover,
.btn-default:focus {
  background-color: var(--hover-color);
  color: var(--text-primary);
  border-color: var(--accent-color);
}

.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-danger {
  background-color: var(--error-color);
  border-color: var(--error-color);
}

/* Pagination */
.pagination > li > a,
.pagination > li > span {
  background-color: var(--background-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.pagination > li > a:hover,
.pagination > li > span:hover {
  background-color: var(--hover-color);
  border-color: var(--border-color);
  color: var(--accent-color);
}

.pagination > .active > a,
.pagination > .active > span {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

/* Dropdown menus */
.dropdown-menu {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

.dropdown-menu > li > a {
  color: var(--text-primary);
}

.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  background-color: var(--hover-color);
  color: var(--accent-color);
}

/* Modal dialogs */
.modal-content {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

.modal-header,
.modal-footer {
  border-color: var(--border-color);
}

.close {
  color: var(--text-primary);
  opacity: 0.7;
}

.close:hover {
  color: var(--text-primary);
  opacity: 1;
}

/* Alert messages */
.alert-success {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
  color: var(--success-color);
}

.alert-info {
  background-color: rgba(100, 181, 246, 0.2);
  border-color: rgba(100, 181, 246, 0.4);
  color: var(--accent-color);
}

.alert-warning {
  background-color: rgba(255, 183, 77, 0.2);
  border-color: rgba(255, 183, 77, 0.4);
  color: var(--warning-color);
}

.alert-danger {
  background-color: rgba(239, 83, 80, 0.2);
  border-color: rgba(239, 83, 80, 0.4);
  color: var(--error-color);
}

/* Labels */
.label-default {
  background-color: var(--background-tertiary);
}

.label-primary {
  background-color: var(--accent-color);
}

.label-success {
  background-color: var(--success-color);
}

.label-info {
  background-color: var(--accent-color);
}

.label-warning {
  background-color: var(--warning-color);
}

.label-danger {
  background-color: var(--error-color);
}

/* Progress bars */
.progress {
  background-color: var(--background-tertiary);
}

.progress-bar {
  background-color: var(--accent-color);
}

.progress-bar-success {
  background-color: var(--success-color);
}

.progress-bar-warning {
  background-color: var(--warning-color);
}

.progress-bar-danger {
  background-color: var(--error-color);
}

/* List groups */
.list-group-item {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

/* Wells */
.well {
  background-color: var(--background-tertiary);
  border-color: var(--border-color);
}

/* Breadcrumbs */
.breadcrumb {
  background-color: var(--background-tertiary);
}

.breadcrumb > li + li:before {
  color: var(--text-secondary);
}

.breadcrumb > .active {
  color: var(--text-secondary);
}

/* Code blocks */
pre {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

code {
  background-color: var(--background-tertiary);
  color: var(--accent-color);
}

/* Links */
a {
  color: var(--accent-color);
}

a:hover,
a:focus {
  color: var(--accent-color);
  text-decoration: underline;
}

/* Task status colors */
.label-success {
  background-color: var(--success-color);
}

.label-warning {
  background-color: var(--warning-color);
}

.label-danger {
  background-color: var(--error-color);
}

/* Task details */
#task-info {
  background-color: var(--background-secondary);
}

/* Charts and graphs */
.nvd3 text {
  fill: var(--text-primary);
}

.nvd3 .nv-axis path,
.nvd3 .nv-axis line {
  stroke: var(--border-color);
}

.nvd3 .nv-axis .tick text {
  fill: var(--text-secondary);
}

.nvd3 .nv-legend-text {
  fill: var(--text-primary);
}

/* Flower-specific elements */
#workers-table,
#tasks-table,
#task-info-table {
  color: var(--text-primary);
}

.dashboard-graph {
  background-color: var(--background-secondary);
  border-color: var(--border-color);
}

/* Toggle switch for dark mode */
.dark-mode-toggle {
  position: fixed;
  top: 70px;
  right: 20px;
  z-index: 1000;
}

.dark-mode-toggle .switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.dark-mode-toggle .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.dark-mode-toggle .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.dark-mode-toggle .slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.dark-mode-toggle input:checked + .slider {
  background-color: var(--accent-color);
}

.dark-mode-toggle input:focus + .slider {
  box-shadow: 0 0 1px var(--accent-color);
}

.dark-mode-toggle input:checked + .slider:before {
  transform: translateX(26px);
}

/* Dark mode toggle label */
.dark-mode-toggle .toggle-label {
  margin-left: 10px;
  color: var(--text-primary);
  vertical-align: middle;
}

/* Specific Flower dashboard elements */
#workers-table th,
#tasks-table th,
#task-info-table th {
  background-color: var(--background-tertiary);
}

.dashboard-title {
  color: var(--text-primary);
}

.flower-dashboard {
  background-color: var(--background-primary);
}

/* Task result formatting */
.task-result {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border-color: var(--border-color);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--background-primary);
}

::-webkit-scrollbar-thumb {
  background: var(--background-tertiary);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--hover-color);
}

#!/bin/bash

# Build the React frontend for the Docker Queue Management Dashboard
echo "Building React frontend for Docker Queue Management Dashboard..."

# Navigate to the frontend directory
cd "$(dirname "$0")/frontend"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

# Build the frontend
echo "Building frontend..."
npm run build

# Check exit code
if [ $? -eq 0 ]; then
    echo "Frontend build successful!"
    echo "Build output is in the 'build' directory"
else
    echo "Frontend build failed!"
    exit 1
fi

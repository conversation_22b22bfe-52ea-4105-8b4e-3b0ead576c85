"""
API for the Docker Queue Management Dashboard.
"""

import os
import time
import json
import logging
from flask import Blueprint, jsonify, request, current_app
from docker_queue.api.routes import api as queue_api

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create dashboard API blueprint
dashboard_api = Blueprint('dashboard_api', __name__, url_prefix='/api/dashboard')

# Register queue API routes
dashboard_api.register_blueprint(queue_api, url_prefix='/queue')

# Settings storage
SETTINGS_FILE = os.environ.get('SETTINGS_FILE', '/tmp/bahtbrowse_settings.json')

# Default settings
DEFAULT_SETTINGS = {
    'pool': {
        'min_size': 5,
        'max_size': 20,
        'target_size': 10,
        'min_available': 2,
        'auto_scaling': True,
        'scaling_interval': 60,
    },
    'container': {
        'image': 'bahtbrowse:latest',
        'cpu_limit': 1.0,
        'memory_limit': '2g',
        'network': 'bahtbrowse',
        'timeout': 3600,
        'idle_timeout': 180,
    },
    'queue': {
        'max_wait_time': 300,
        'default_priority': 5,
        'max_queue_size': 100,
    },
    'system': {
        'metrics_retention_days': 7,
        'log_level': 'INFO',
        'admin_email': '<EMAIL>',
        'notifications_enabled': True,
    },
}

# Metrics history storage
metrics_history = []

# System logs storage
system_logs = []
next_log_id = 1

def load_settings():
    """Load settings from file."""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r') as f:
                return json.load(f)
        return DEFAULT_SETTINGS
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        return DEFAULT_SETTINGS

def save_settings(settings):
    """Save settings to file."""
    try:
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(settings, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving settings: {str(e)}")
        return False

def add_log_entry(level, message):
    """Add a log entry."""
    global next_log_id
    log_entry = {
        'id': next_log_id,
        'timestamp': time.time(),
        'level': level,
        'message': message,
    }
    system_logs.append(log_entry)
    next_log_id += 1

    # Limit log entries to 1000
    if len(system_logs) > 1000:
        system_logs.pop(0)

    return log_entry

# Initialize logs with some sample entries
if not system_logs:
    add_log_entry('INFO', 'System started')
    add_log_entry('INFO', 'Container pool initialized with 10 containers')
    add_log_entry('INFO', 'API server started')

@dashboard_api.route('/settings', methods=['GET'])
def get_settings():
    """Get system settings."""
    settings = load_settings()
    return jsonify({
        'status': 'success',
        'settings': settings
    })

@dashboard_api.route('/settings', methods=['POST'])
def update_settings():
    """Update system settings."""
    try:
        settings = request.json

        # Validate settings
        if not settings:
            return jsonify({
                'status': 'error',
                'message': 'Invalid settings'
            }), 400

        # Save settings
        if save_settings(settings):
            add_log_entry('INFO', 'Settings updated')
            return jsonify({
                'status': 'success',
                'message': 'Settings saved successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to save settings'
            }), 500

    except Exception as e:
        logger.error(f"Error updating settings: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Error updating settings: {str(e)}'
        }), 500

@dashboard_api.route('/logs', methods=['GET'])
def get_logs():
    """Get system logs."""
    # Get query parameters
    level = request.args.get('level')
    limit = request.args.get('limit', 100, type=int)

    # Filter logs by level
    filtered_logs = system_logs
    if level:
        filtered_logs = [log for log in system_logs if log['level'] == level]

    # Limit logs
    limited_logs = filtered_logs[-limit:]

    return jsonify({
        'status': 'success',
        'logs': limited_logs
    })

@dashboard_api.route('/logs', methods=['DELETE'])
def clear_logs():
    """Clear system logs."""
    global system_logs
    system_logs = []
    add_log_entry('INFO', 'Logs cleared')

    return jsonify({
        'status': 'success',
        'message': 'Logs cleared successfully'
    })

@dashboard_api.route('/metrics/history', methods=['GET'])
def get_metrics_history():
    """Get metrics history."""
    # Get query parameters
    start_time = request.args.get('start_time', type=float)
    end_time = request.args.get('end_time', type=float)

    # Filter metrics by time range
    filtered_metrics = metrics_history
    if start_time and end_time:
        filtered_metrics = [m for m in metrics_history if start_time <= m['timestamp'] <= end_time]
    elif start_time:
        filtered_metrics = [m for m in metrics_history if start_time <= m['timestamp']]
    elif end_time:
        filtered_metrics = [m for m in metrics_history if m['timestamp'] <= end_time]

    return jsonify({
        'status': 'success',
        'metrics': filtered_metrics,
        'period': {
            'start_time': start_time or (filtered_metrics[0]['timestamp'] if filtered_metrics else None),
            'end_time': end_time or (filtered_metrics[-1]['timestamp'] if filtered_metrics else None)
        }
    })

@dashboard_api.route('/metrics/current', methods=['GET'])
def get_current_metrics():
    """Get current metrics."""
    # Forward to queue API
    return queue_api.get_current_metrics()

@dashboard_api.route('/health', methods=['GET'])
def get_health():
    """Get system health."""
    # Forward to queue API
    return queue_api.get_health()

@dashboard_api.route('/report', methods=['GET'])
def get_report():
    """Get usage report."""
    # Forward to queue API
    return queue_api.get_report()

def register_dashboard_api(app):
    """Register dashboard API with Flask app."""
    app.register_blueprint(dashboard_api)

    # Add sample metrics to history
    global metrics_history
    current_time = time.time()

    # Generate sample metrics for the last hour
    for i in range(12):  # 12 samples, 5 minutes apart
        timestamp = current_time - (11 - i) * 300  # 5 minutes

        # Generate sample metric
        metric = {
            'timestamp': timestamp,
            'pool': {
                'available_containers': 5 + (i % 3),
                'assigned_containers': 10 - (i % 2),
                'recycling_containers': 2,
                'total_containers': 17
            },
            'queue': {
                'length': max(0, 5 - i // 2),
                'avg_wait_time': max(0, 20 - i * 1.5),
                'p95_wait_time': max(0, 40 - i * 3)
            },
            'containers': {
                'avg_cpu_usage': 20 + (i * 2) % 15,
                'avg_memory_usage': 40 + (i * 3) % 20,
                'avg_network_rx': 800 + (i * 100),
                'avg_network_tx': 1500 + (i * 150)
            }
        }

        metrics_history.append(metric)

    logger.info("Dashboard API registered")

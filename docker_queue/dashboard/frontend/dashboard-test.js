const { chromium } = require('playwright');

(async () => {
  console.log('Starting dashboard test...');
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  // Mock API responses
  await page.route('**/api/pool/status', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        pool: { 
          total_containers: 10, 
          available_containers: 5, 
          assigned_containers: 5, 
          recycling_containers: 0 
        } 
      } 
    });
  });
  
  await page.route('**/api/metrics/current', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        metrics: { 
          containers: { 
            avg_cpu_usage: 50, 
            avg_memory_usage: 50 
          },
          queue: { 
            length: 3, 
            avg_wait_time: 15.5 
          }
        } 
      } 
    });
  });
  
  await page.route('**/api/health', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        health: { 
          total: 10, 
          healthy: 8, 
          unhealthy: 2, 
          unknown: 0 
        } 
      } 
    });
  });
  
  console.log('Navigating to dashboard...');
  await page.goto('http://localhost:3000/');
  
  // Wait for dashboard to load
  console.log('Waiting for dashboard to load...');
  try {
    await page.waitForSelector('[data-testid="dashboard-container"]', { timeout: 5000 });
    console.log('✅ Test passed: Dashboard loaded successfully');
  } catch (error) {
    console.log('❌ Test failed: Dashboard did not load');
    console.log(error);
    
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'dashboard-error.png' });
    console.log('Screenshot saved as dashboard-error.png');
    
    // Get the page content
    const content = await page.content();
    console.log('Page content:');
    console.log(content.substring(0, 500) + '...');
  }
  
  await browser.close();
  console.log('Test completed.');
})();

/**
 * ELK Event Tracker for 10Baht bahtBrowse
 * 
 * This module provides functions for tracking browser events and sending them to the ELK stack.
 */

// Configuration
const config = {
  // API endpoint for sending events
  apiEndpoint: '/api/events',
  
  // Whether to enable debug logging
  debug: false,
  
  // Session ID (will be set on initialization)
  sessionId: null,
  
  // Container ID (will be set on initialization)
  containerId: null,
  
  // Browser type (will be set on initialization)
  browserType: null,
  
  // User ID (will be set on initialization)
  userId: null,
  
  // Whether to batch events before sending
  batchEvents: true,
  
  // Maximum batch size
  maxBatchSize: 10,
  
  // Maximum time to wait before sending a batch (ms)
  maxBatchWait: 2000,
  
  // Whether to use the Beacon API for sending events when available
  useBeacon: true,
  
  // Whether to include performance metrics with events
  includePerformance: true,
  
  // Whether to track clicks
  trackClicks: true,
  
  // Whether to track page navigation
  trackNavigation: true,
  
  // Whether to track downloads
  trackDownloads: true,
  
  // Whether to track errors
  trackErrors: true,
  
  // Whether to track resource timing
  trackResourceTiming: true,
};

// Event batch
let eventBatch = [];
let batchTimeout = null;

/**
 * Initialize the event tracker
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} - The event tracker instance
 */
export function initEventTracker(options = {}) {
  // Merge options with default config
  Object.assign(config, options);
  
  // Get session ID from URL or localStorage
  config.sessionId = options.sessionId || 
                     getQueryParam('session_id') || 
                     localStorage.getItem('bahtbrowse_session_id') ||
                     generateId();
  
  // Store session ID in localStorage
  localStorage.setItem('bahtbrowse_session_id', config.sessionId);
  
  // Get container ID from URL or localStorage
  config.containerId = options.containerId || 
                       getQueryParam('container_id') || 
                       localStorage.getItem('bahtbrowse_container_id') ||
                       null;
  
  // Store container ID in localStorage
  if (config.containerId) {
    localStorage.setItem('bahtbrowse_container_id', config.containerId);
  }
  
  // Get browser type from URL or localStorage
  config.browserType = options.browserType || 
                       getQueryParam('browser_type') || 
                       localStorage.getItem('bahtbrowse_browser_type') ||
                       detectBrowserType();
  
  // Store browser type in localStorage
  localStorage.setItem('bahtbrowse_browser_type', config.browserType);
  
  // Get user ID from URL or localStorage
  config.userId = options.userId || 
                  getQueryParam('user_id') || 
                  localStorage.getItem('bahtbrowse_user_id') ||
                  null;
  
  // Store user ID in localStorage
  if (config.userId) {
    localStorage.setItem('bahtbrowse_user_id', config.userId);
  }
  
  // Track initial page load
  trackEvent('page_load', {
    url: window.location.href,
    referrer: document.referrer,
    title: document.title,
  });
  
  // Set up event listeners
  if (config.trackClicks) {
    document.addEventListener('click', handleClick);
  }
  
  if (config.trackNavigation) {
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // Monkey patch history methods
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function() {
      originalPushState.apply(this, arguments);
      handleNavigation('pushState');
    };
    
    history.replaceState = function() {
      originalReplaceState.apply(this, arguments);
      handleNavigation('replaceState');
    };
    
    window.addEventListener('popstate', () => handleNavigation('popstate'));
  }
  
  if (config.trackDownloads) {
    document.addEventListener('click', handleDownload);
  }
  
  if (config.trackErrors) {
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handlePromiseError);
  }
  
  // Log initialization
  if (config.debug) {
    console.log('ELK Event Tracker initialized', config);
  }
  
  // Return the event tracker API
  return {
    trackEvent,
    trackPageView,
    trackClick,
    trackNavigation,
    trackDownload,
    trackError,
    trackPerformance,
    getSessionId: () => config.sessionId,
    getContainerId: () => config.containerId,
    getBrowserType: () => config.browserType,
    getUserId: () => config.userId,
    flushEvents,
  };
}

/**
 * Track an event
 * 
 * @param {string} eventType - Type of event
 * @param {Object} eventData - Event data
 */
export function trackEvent(eventType, eventData = {}) {
  const event = {
    event_type: eventType,
    timestamp: new Date().toISOString(),
    session_id: config.sessionId,
    container_id: config.containerId,
    browser_type: config.browserType,
    user_id: config.userId,
    url: window.location.href,
    user_agent: navigator.userAgent,
    ...eventData,
  };
  
  // Add performance metrics if enabled
  if (config.includePerformance) {
    event.performance = getPerformanceMetrics();
  }
  
  // Add to batch or send immediately
  if (config.batchEvents) {
    addToBatch(event);
  } else {
    sendEvent(event);
  }
  
  // Log event
  if (config.debug) {
    console.log('ELK Event:', event);
  }
  
  return event;
}

/**
 * Track a page view
 * 
 * @param {string} url - URL of the page
 * @param {string} title - Title of the page
 * @param {string} referrer - Referrer URL
 */
export function trackPageView(url = window.location.href, title = document.title, referrer = document.referrer) {
  return trackEvent('page_view', {
    url,
    title,
    referrer,
  });
}

/**
 * Track a click
 * 
 * @param {Object} element - Element that was clicked
 * @param {Object} event - Click event
 */
export function trackClick(element, event) {
  // Get element details
  const elementDetails = getElementDetails(element);
  
  return trackEvent('click', {
    element_type: elementDetails.type,
    element_id: elementDetails.id,
    element_class: elementDetails.class,
    element_text: elementDetails.text,
    element_href: elementDetails.href,
    x_position: event ? event.clientX : null,
    y_position: event ? event.clientY : null,
  });
}

/**
 * Track navigation
 * 
 * @param {string} navigationType - Type of navigation
 * @param {string} fromUrl - URL navigated from
 * @param {string} toUrl - URL navigated to
 */
export function trackNavigation(navigationType, fromUrl = null, toUrl = window.location.href) {
  return trackEvent('navigation', {
    navigation_type: navigationType,
    from_url: fromUrl || document.referrer,
    to_url: toUrl,
  });
}

/**
 * Track a download
 * 
 * @param {string} url - URL of the download
 * @param {string} filename - Name of the file
 * @param {string} fileType - Type of the file
 */
export function trackDownload(url, filename, fileType) {
  return trackEvent('download', {
    download_url: url,
    filename,
    file_type: fileType,
  });
}

/**
 * Track an error
 * 
 * @param {Error} error - Error object
 * @param {string} errorType - Type of error
 * @param {string} errorSource - Source of the error
 */
export function trackError(error, errorType = 'runtime', errorSource = 'application') {
  return trackEvent('error', {
    error_type: errorType,
    error_source: errorSource,
    error_message: error.message,
    error_stack: error.stack,
    error_name: error.name,
  });
}

/**
 * Track performance metrics
 */
export function trackPerformance() {
  return trackEvent('performance', {
    performance: getPerformanceMetrics(),
  });
}

/**
 * Add an event to the batch
 * 
 * @param {Object} event - Event to add
 */
function addToBatch(event) {
  eventBatch.push(event);
  
  // Send batch if it reaches the maximum size
  if (eventBatch.length >= config.maxBatchSize) {
    flushEvents();
    return;
  }
  
  // Set timeout to send batch if it hasn't been sent after maxBatchWait
  if (!batchTimeout) {
    batchTimeout = setTimeout(flushEvents, config.maxBatchWait);
  }
}

/**
 * Flush the event batch
 */
export function flushEvents() {
  if (eventBatch.length === 0) {
    return;
  }
  
  // Clear the batch timeout
  if (batchTimeout) {
    clearTimeout(batchTimeout);
    batchTimeout = null;
  }
  
  // Send the batch
  const batch = [...eventBatch];
  eventBatch = [];
  
  sendEvents(batch);
}

/**
 * Send an event to the server
 * 
 * @param {Object} event - Event to send
 */
function sendEvent(event) {
  sendEvents([event]);
}

/**
 * Send events to the server
 * 
 * @param {Array} events - Events to send
 */
function sendEvents(events) {
  // Use Beacon API if available and enabled
  if (config.useBeacon && navigator.sendBeacon) {
    const blob = new Blob([JSON.stringify(events)], { type: 'application/json' });
    const success = navigator.sendBeacon(config.apiEndpoint, blob);
    
    if (success) {
      if (config.debug) {
        console.log('Events sent via Beacon API', events);
      }
      return;
    }
    
    // Fall back to fetch if Beacon API fails
    if (config.debug) {
      console.log('Beacon API failed, falling back to fetch');
    }
  }
  
  // Use fetch API
  fetch(config.apiEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(events),
    // Don't wait for response
    keepalive: true,
  }).catch(error => {
    if (config.debug) {
      console.error('Error sending events', error);
    }
  });
  
  if (config.debug) {
    console.log('Events sent via fetch', events);
  }
}

/**
 * Handle a click event
 * 
 * @param {Object} event - Click event
 */
function handleClick(event) {
  // Get the clicked element
  const element = event.target;
  
  // Track the click
  trackClick(element, event);
}

/**
 * Handle a navigation event
 * 
 * @param {string} navigationType - Type of navigation
 */
function handleNavigation(navigationType) {
  trackNavigation(navigationType);
}

/**
 * Handle a beforeunload event
 * 
 * @param {Object} event - Beforeunload event
 */
function handleBeforeUnload(event) {
  // Track the navigation
  trackNavigation('unload');
  
  // Flush events
  flushEvents();
}

/**
 * Handle a download click
 * 
 * @param {Object} event - Click event
 */
function handleDownload(event) {
  // Get the clicked element
  const element = event.target.closest('a');
  
  if (!element) {
    return;
  }
  
  // Check if it's a download link
  const href = element.getAttribute('href');
  const download = element.getAttribute('download');
  
  if (!href) {
    return;
  }
  
  // Check if it's a download link
  if (download || isDownloadLink(href)) {
    // Get the filename
    const filename = download || href.split('/').pop();
    
    // Get the file type
    const fileType = getFileType(filename);
    
    // Track the download
    trackDownload(href, filename, fileType);
  }
}

/**
 * Handle an error event
 * 
 * @param {Object} event - Error event
 */
function handleError(event) {
  // Create an error object
  const error = {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    stack: event.error ? event.error.stack : null,
    name: event.error ? event.error.name : 'Error',
  };
  
  // Track the error
  trackError(error, 'runtime', 'window');
}

/**
 * Handle a promise error event
 * 
 * @param {Object} event - Promise error event
 */
function handlePromiseError(event) {
  // Create an error object
  const error = {
    message: event.reason ? (event.reason.message || String(event.reason)) : 'Unhandled Promise Rejection',
    stack: event.reason && event.reason.stack,
    name: event.reason && event.reason.name,
  };
  
  // Track the error
  trackError(error, 'promise', 'window');
}

/**
 * Get performance metrics
 * 
 * @returns {Object} - Performance metrics
 */
function getPerformanceMetrics() {
  const metrics = {};
  
  // Check if Performance API is available
  if (!window.performance) {
    return metrics;
  }
  
  // Get navigation timing
  if (window.performance.timing) {
    const timing = window.performance.timing;
    
    metrics.page_load_time = timing.loadEventEnd - timing.navigationStart;
    metrics.dom_ready_time = timing.domComplete - timing.domLoading;
    metrics.network_latency = timing.responseEnd - timing.fetchStart;
    metrics.server_response_time = timing.responseEnd - timing.requestStart;
    metrics.redirect_time = timing.redirectEnd - timing.redirectStart;
    metrics.dns_lookup_time = timing.domainLookupEnd - timing.domainLookupStart;
    metrics.tcp_connect_time = timing.connectEnd - timing.connectStart;
  }
  
  // Get memory info
  if (window.performance.memory) {
    metrics.used_js_heap_size = window.performance.memory.usedJSHeapSize;
    metrics.total_js_heap_size = window.performance.memory.totalJSHeapSize;
    metrics.js_heap_size_limit = window.performance.memory.jsHeapSizeLimit;
  }
  
  // Get resource timing
  if (config.trackResourceTiming && window.performance.getEntriesByType) {
    const resources = window.performance.getEntriesByType('resource');
    
    if (resources.length > 0) {
      metrics.resource_count = resources.length;
      metrics.resource_load_time = resources.reduce((total, resource) => total + resource.duration, 0) / resources.length;
    }
  }
  
  return metrics;
}

/**
 * Get details about an element
 * 
 * @param {Object} element - DOM element
 * @returns {Object} - Element details
 */
function getElementDetails(element) {
  if (!element) {
    return {};
  }
  
  return {
    type: element.tagName ? element.tagName.toLowerCase() : null,
    id: element.id || null,
    class: element.className || null,
    text: getElementText(element),
    href: element.href || null,
  };
}

/**
 * Get text content of an element
 * 
 * @param {Object} element - DOM element
 * @returns {string} - Element text
 */
function getElementText(element) {
  if (!element) {
    return null;
  }
  
  // Get text content
  let text = element.textContent || element.innerText || '';
  
  // Trim and limit length
  text = text.trim().substring(0, 100);
  
  return text || null;
}

/**
 * Check if a URL is a download link
 * 
 * @param {string} url - URL to check
 * @returns {boolean} - Whether the URL is a download link
 */
function isDownloadLink(url) {
  if (!url) {
    return false;
  }
  
  // Check file extension
  const fileExtensions = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.zip', '.rar', '.7z', '.tar', '.gz', '.exe', '.dmg',
    '.mp3', '.mp4', '.avi', '.mov', '.jpg', '.jpeg', '.png', '.gif',
    '.csv', '.txt', '.json', '.xml', '.html', '.htm',
  ];
  
  const urlLower = url.toLowerCase();
  
  return fileExtensions.some(ext => urlLower.endsWith(ext));
}

/**
 * Get file type from filename
 * 
 * @param {string} filename - Filename
 * @returns {string} - File type
 */
function getFileType(filename) {
  if (!filename) {
    return 'unknown';
  }
  
  const extension = filename.split('.').pop().toLowerCase();
  
  // Map extension to file type
  const fileTypes = {
    pdf: 'document',
    doc: 'document',
    docx: 'document',
    xls: 'spreadsheet',
    xlsx: 'spreadsheet',
    ppt: 'presentation',
    pptx: 'presentation',
    zip: 'archive',
    rar: 'archive',
    '7z': 'archive',
    tar: 'archive',
    gz: 'archive',
    exe: 'executable',
    dmg: 'executable',
    mp3: 'audio',
    mp4: 'video',
    avi: 'video',
    mov: 'video',
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'image',
    csv: 'data',
    txt: 'text',
    json: 'data',
    xml: 'data',
    html: 'web',
    htm: 'web',
  };
  
  return fileTypes[extension] || 'unknown';
}

/**
 * Get a query parameter from the URL
 * 
 * @param {string} name - Parameter name
 * @returns {string} - Parameter value
 */
function getQueryParam(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

/**
 * Generate a unique ID
 * 
 * @returns {string} - Unique ID
 */
function generateId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Detect browser type
 * 
 * @returns {string} - Browser type
 */
function detectBrowserType() {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.indexOf('firefox') !== -1) {
    return 'firefox';
  } else if (userAgent.indexOf('chrome') !== -1) {
    return 'chromium';
  } else {
    return 'unknown';
  }
}

// Export default
export default {
  initEventTracker,
  trackEvent,
  trackPageView,
  trackClick,
  trackNavigation,
  trackDownload,
  trackError,
  trackPerformance,
  flushEvents,
};

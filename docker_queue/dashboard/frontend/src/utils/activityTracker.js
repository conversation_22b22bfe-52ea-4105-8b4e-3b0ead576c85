/**
 * Activity tracker for BahtBrowse containers
 * 
 * This module tracks user activity and sends periodic updates to the server
 * to prevent containers from being recycled due to inactivity.
 */

import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || '';

class ActivityTracker {
  constructor(userId, pingInterval = 60000) {
    this.userId = userId;
    this.pingInterval = pingInterval; // Default: ping every 60 seconds
    this.timerId = null;
    this.lastActivityTime = Date.now();
    this.activityEvents = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];
    this.boundActivityHandler = this.handleUserActivity.bind(this);
  }

  /**
   * Start tracking user activity
   */
  start() {
    if (this.timerId) {
      return; // Already started
    }

    // Add event listeners for user activity
    this.activityEvents.forEach(eventType => {
      window.addEventListener(eventType, this.boundActivityHandler, true);
    });

    // Start the ping timer
    this.timerId = setInterval(() => this.pingServer(), this.pingInterval);
    
    console.log(`Activity tracker started for user ${this.userId}`);
    
    // Ping immediately on start
    this.pingServer();
  }

  /**
   * Stop tracking user activity
   */
  stop() {
    if (!this.timerId) {
      return; // Not started
    }

    // Remove event listeners
    this.activityEvents.forEach(eventType => {
      window.removeEventListener(eventType, this.boundActivityHandler, true);
    });

    // Clear the timer
    clearInterval(this.timerId);
    this.timerId = null;
    
    console.log(`Activity tracker stopped for user ${this.userId}`);
  }

  /**
   * Handle user activity events
   */
  handleUserActivity() {
    this.lastActivityTime = Date.now();
  }

  /**
   * Ping the server to update container activity
   */
  async pingServer() {
    // Only ping if there has been activity since the last ping
    const now = Date.now();
    const idleTime = now - this.lastActivityTime;
    
    // If user has been idle for more than 2 minutes, don't ping
    // This allows the container to be recycled if the user is truly inactive
    if (idleTime > 120000) {
      console.log(`User ${this.userId} has been idle for ${Math.round(idleTime/1000)} seconds, not pinging server`);
      return;
    }
    
    try {
      const response = await axios.post(`${API_URL}/api/containers/activity`, {
        user_id: this.userId
      });
      
      if (response.data.status === 'success') {
        console.log(`Activity ping successful for user ${this.userId}`);
      } else {
        console.warn(`Activity ping failed: ${response.data.message}`);
      }
    } catch (error) {
      console.error('Error sending activity ping:', error);
    }
  }
}

// Create a singleton instance
let instance = null;

/**
 * Initialize the activity tracker
 * 
 * @param {string} userId - The user ID
 * @param {number} pingInterval - Ping interval in milliseconds
 * @returns {ActivityTracker} The activity tracker instance
 */
export const initActivityTracker = (userId, pingInterval) => {
  if (!instance) {
    instance = new ActivityTracker(userId, pingInterval);
  } else {
    // Update the user ID if it has changed
    instance.userId = userId;
    if (pingInterval) {
      instance.pingInterval = pingInterval;
    }
  }
  return instance;
};

/**
 * Get the activity tracker instance
 * 
 * @returns {ActivityTracker|null} The activity tracker instance or null if not initialized
 */
export const getActivityTracker = () => {
  return instance;
};

export default ActivityTracker;

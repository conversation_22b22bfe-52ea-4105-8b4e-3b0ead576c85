import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || '';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Container Management API
export const containerApi = {
  requestContainer: (userId, priority = 0) => 
    api.post('/api/containers/request', { user_id: userId, priority }),
  
  getContainerStatus: (requestId, userId) => 
    api.get(`/api/containers/status/${requestId}`, { params: { user_id: userId } }),
  
  releaseContainer: (userId) => 
    api.post('/api/containers/release', { user_id: userId }),
};

// Pool Management API
export const poolApi = {
  getPoolStatus: () => 
    api.get('/api/pool/status'),
  
  scalePool: (targetSize) => 
    api.post('/api/pool/scale', { target_size: targetSize }),
};

// Monitoring API
export const monitoringApi = {
  getCurrentMetrics: () => 
    api.get('/api/metrics/current'),
  
  getMetricsHistory: (startTime, endTime) => 
    api.get('/api/metrics/history', { params: { start_time: startTime, end_time: endTime } }),
  
  getHealthCheck: () => 
    api.get('/api/health'),
  
  getUsageReport: () => 
    api.get('/api/report'),
};

export default {
  containerApi,
  poolApi,
  monitoringApi,
};

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Button,
  Box,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  Tabs,
  Tab,
  Chip,
} from '@mui/material';
import { containerApi, poolApi } from '../services/api';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`container-tabpanel-${index}`}
      aria-labelledby={`container-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function ContainerManagement() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [poolStatus, setPoolStatus] = useState(null);
  const [containers, setContainers] = useState([]);
  const [queuedRequests, setQueuedRequests] = useState([]);
  const [tabValue, setTabValue] = useState(0);
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [userId, setUserId] = useState('');
  const [priority, setPriority] = useState(5);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [selectedContainer, setSelectedContainer] = useState(null);
  const [containerDialogOpen, setContainerDialogOpen] = useState(false);

  useEffect(() => {
    fetchContainerData();
    
    // Set up polling interval
    const interval = setInterval(fetchContainerData, 10000); // Refresh every 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  const fetchContainerData = async () => {
    try {
      setLoading(true);
      
      // Fetch pool status
      const poolResponse = await poolApi.getPoolStatus();
      setPoolStatus(poolResponse.data.pool);
      
      // Extract containers from pool status
      // In a real implementation, we would have an API endpoint to get all containers
      // For now, we'll simulate it with mock data
      const mockContainers = [
        {
          container_id: 'container1',
          docker_id: 'docker1',
          name: 'container_1',
          status: 'assigned',
          ip_address: '**********',
          user_id: 'user1',
          assigned_at: Date.now() - 3600000, // 1 hour ago
        },
        {
          container_id: 'container2',
          docker_id: 'docker2',
          name: 'container_2',
          status: 'available',
          ip_address: '**********',
        },
        {
          container_id: 'container3',
          docker_id: 'docker3',
          name: 'container_3',
          status: 'recycling',
          ip_address: '**********',
          recycling_start: Date.now() - 60000, // 1 minute ago
        },
      ];
      
      setContainers(mockContainers);
      
      // Mock queued requests
      const mockQueuedRequests = [
        {
          request_id: 'request1',
          user_id: 'user2',
          priority: 9,
          timestamp: Date.now() - 120000, // 2 minutes ago
          position: 1,
        },
        {
          request_id: 'request2',
          user_id: 'user3',
          priority: 5,
          timestamp: Date.now() - 180000, // 3 minutes ago
          position: 2,
        },
      ];
      
      setQueuedRequests(mockQueuedRequests);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching container data:', err);
      setError('Failed to load container data. Please try again later.');
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleRequestDialogOpen = () => {
    setRequestDialogOpen(true);
  };

  const handleRequestDialogClose = () => {
    setRequestDialogOpen(false);
  };

  const handleContainerDialogOpen = (container) => {
    setSelectedContainer(container);
    setContainerDialogOpen(true);
  };

  const handleContainerDialogClose = () => {
    setContainerDialogOpen(false);
  };

  const handleRequestContainer = async () => {
    if (!userId) {
      setSnackbar({
        open: true,
        message: 'User ID is required',
        severity: 'error',
      });
      return;
    }

    try {
      setRequestDialogOpen(false);
      
      // Request container
      const response = await containerApi.requestContainer(userId, priority);
      
      if (response.data.status === 'success') {
        setSnackbar({
          open: true,
          message: 'Container assigned successfully',
          severity: 'success',
        });
      } else if (response.data.status === 'pending') {
        setSnackbar({
          open: true,
          message: `Container request queued at position ${response.data.queue_position}`,
          severity: 'info',
        });
      } else {
        setSnackbar({
          open: true,
          message: response.data.message || 'Failed to request container',
          severity: 'error',
        });
      }
      
      // Refresh data
      fetchContainerData();
    } catch (err) {
      console.error('Error requesting container:', err);
      setSnackbar({
        open: true,
        message: 'Failed to request container. Please try again later.',
        severity: 'error',
      });
    }
  };

  const handleReleaseContainer = async (container) => {
    try {
      // Release container
      const response = await containerApi.releaseContainer(container.user_id);
      
      if (response.data.status === 'success') {
        setSnackbar({
          open: true,
          message: 'Container released successfully',
          severity: 'success',
        });
      } else {
        setSnackbar({
          open: true,
          message: response.data.message || 'Failed to release container',
          severity: 'error',
        });
      }
      
      // Refresh data
      fetchContainerData();
      
      // Close dialog if open
      if (containerDialogOpen) {
        setContainerDialogOpen(false);
      }
    } catch (err) {
      console.error('Error releasing container:', err);
      setSnackbar({
        open: true,
        message: 'Failed to release container. Please try again later.',
        severity: 'error',
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading && !poolStatus) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress data-testid="loading-indicator" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Typography color="error" data-testid="error-message">{error}</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} data-testid="container-management">
      {/* Header */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography component="h2" variant="h6" color="primary" gutterBottom data-testid="container-management-title">
              Container Management
            </Typography>
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleRequestDialogOpen}
              data-testid="request-container-button"
            >
              Request Container
            </Button>
          </Box>
          <Typography variant="body1" data-testid="container-management-description">
            Manage Docker containers and container requests.
          </Typography>
        </Paper>
      </Grid>
      
      {/* Container Status Summary */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography component="h3" variant="subtitle1" gutterBottom>
            Container Pool Status
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h4" data-testid="total-containers">
                  {poolStatus?.total_containers || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Containers
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h4" color="success.main" data-testid="available-containers">
                  {poolStatus?.available_containers || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Available
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h4" color="info.main" data-testid="assigned-containers">
                  {poolStatus?.assigned_containers || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Assigned
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center', p: 1 }}>
                <Typography variant="h4" color="warning.main" data-testid="recycling-containers">
                  {poolStatus?.recycling_containers || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Recycling
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
      
      {/* Tabs */}
      <Grid item xs={12}>
        <Paper sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="container management tabs"
              data-testid="container-tabs"
            >
              <Tab label="Containers" id="container-tab-0" aria-controls="container-tabpanel-0" data-testid="containers-tab" />
              <Tab label="Queue" id="container-tab-1" aria-controls="container-tabpanel-1" data-testid="queue-tab" />
            </Tabs>
          </Box>
          
          {/* Containers Tab */}
          <TabPanel value={tabValue} index={0}>
            <TableContainer>
              <Table aria-label="containers table" data-testid="containers-table">
                <TableHead>
                  <TableRow>
                    <TableCell>Container ID</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>IP Address</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {containers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} align="center">No containers found</TableCell>
                    </TableRow>
                  ) : (
                    containers.map((container) => (
                      <TableRow key={container.container_id} data-testid={`container-row-${container.container_id}`}>
                        <TableCell>{container.container_id}</TableCell>
                        <TableCell>{container.name}</TableCell>
                        <TableCell>
                          <Chip 
                            label={container.status} 
                            color={
                              container.status === 'available' ? 'success' :
                              container.status === 'assigned' ? 'primary' :
                              container.status === 'recycling' ? 'warning' :
                              'default'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{container.ip_address}</TableCell>
                        <TableCell>{container.user_id || '-'}</TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => handleContainerDialogOpen(container)}
                            data-testid={`view-container-${container.container_id}`}
                          >
                            View
                          </Button>
                          {container.status === 'assigned' && (
                            <Button
                              variant="outlined"
                              color="secondary"
                              size="small"
                              sx={{ ml: 1 }}
                              onClick={() => handleReleaseContainer(container)}
                              data-testid={`release-container-${container.container_id}`}
                            >
                              Release
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>
          
          {/* Queue Tab */}
          <TabPanel value={tabValue} index={1}>
            <TableContainer>
              <Table aria-label="queue table" data-testid="queue-table">
                <TableHead>
                  <TableRow>
                    <TableCell>Request ID</TableCell>
                    <TableCell>User ID</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Position</TableCell>
                    <TableCell>Wait Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {queuedRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">No requests in queue</TableCell>
                    </TableRow>
                  ) : (
                    queuedRequests.map((request) => (
                      <TableRow key={request.request_id} data-testid={`request-row-${request.request_id}`}>
                        <TableCell>{request.request_id}</TableCell>
                        <TableCell>{request.user_id}</TableCell>
                        <TableCell>{request.priority}</TableCell>
                        <TableCell>{request.position}</TableCell>
                        <TableCell>
                          {Math.floor((Date.now() - request.timestamp) / 1000)} seconds
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>
        </Paper>
      </Grid>
      
      {/* Request Container Dialog */}
      <Dialog
        open={requestDialogOpen}
        onClose={handleRequestDialogClose}
        aria-labelledby="request-container-dialog-title"
        data-testid="request-container-dialog"
      >
        <DialogTitle id="request-container-dialog-title">Request Container</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Enter a user ID and priority to request a container.
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="user-id"
            label="User ID"
            type="text"
            fullWidth
            variant="outlined"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            data-testid="user-id-input"
          />
          <TextField
            margin="dense"
            id="priority"
            label="Priority (0-9)"
            type="number"
            fullWidth
            variant="outlined"
            value={priority}
            onChange={(e) => setPriority(Math.max(0, Math.min(9, parseInt(e.target.value) || 0)))}
            inputProps={{ min: 0, max: 9 }}
            data-testid="priority-input"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRequestDialogClose} data-testid="cancel-request-button">Cancel</Button>
          <Button onClick={handleRequestContainer} variant="contained" color="primary" data-testid="submit-request-button">
            Request
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Container Details Dialog */}
      <Dialog
        open={containerDialogOpen}
        onClose={handleContainerDialogClose}
        aria-labelledby="container-dialog-title"
        data-testid="container-details-dialog"
      >
        <DialogTitle id="container-dialog-title">Container Details</DialogTitle>
        <DialogContent>
          {selectedContainer && (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Container ID</Typography>
                <Typography variant="body1" gutterBottom>{selectedContainer.container_id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Docker ID</Typography>
                <Typography variant="body1" gutterBottom>{selectedContainer.docker_id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Name</Typography>
                <Typography variant="body1" gutterBottom>{selectedContainer.name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Status</Typography>
                <Typography variant="body1" gutterBottom>
                  <Chip 
                    label={selectedContainer.status} 
                    color={
                      selectedContainer.status === 'available' ? 'success' :
                      selectedContainer.status === 'assigned' ? 'primary' :
                      selectedContainer.status === 'recycling' ? 'warning' :
                      'default'
                    }
                    size="small"
                  />
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">IP Address</Typography>
                <Typography variant="body1" gutterBottom>{selectedContainer.ip_address}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">User ID</Typography>
                <Typography variant="body1" gutterBottom>{selectedContainer.user_id || '-'}</Typography>
              </Grid>
              {selectedContainer.assigned_at && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Assigned At</Typography>
                  <Typography variant="body1" gutterBottom>
                    {new Date(selectedContainer.assigned_at).toLocaleString()}
                  </Typography>
                </Grid>
              )}
              {selectedContainer.recycling_start && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Recycling Started</Typography>
                  <Typography variant="body1" gutterBottom>
                    {new Date(selectedContainer.recycling_start).toLocaleString()}
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleContainerDialogClose} data-testid="close-container-details-button">Close</Button>
          {selectedContainer && selectedContainer.status === 'assigned' && (
            <Button 
              onClick={() => handleReleaseContainer(selectedContainer)} 
              variant="contained" 
              color="secondary"
              data-testid="release-container-details-button"
            >
              Release Container
            </Button>
          )}
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Grid>
  );
}

export default ContainerManagement;

import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Button,
  Box,
  CircularProgress,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Card,
  CardContent,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
} from '@mui/material';
import { monitoringApi } from '../services/api';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`monitoring-tabpanel-${index}`}
      aria-labelledby={`monitoring-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function Monitoring() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [metricsHistory, setMetricsHistory] = useState([]);
  const [health, setHealth] = useState(null);
  const [report, setReport] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('1h');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchMonitoringData();
    
    // Set up polling interval
    const interval = setInterval(fetchMonitoringData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [timeRange]);

  const fetchMonitoringData = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      
      // Fetch current metrics
      const metricsResponse = await monitoringApi.getCurrentMetrics();
      setMetrics(metricsResponse.data.metrics);
      
      // Fetch metrics history based on time range
      const endTime = Math.floor(Date.now() / 1000);
      let startTime;
      
      switch (timeRange) {
        case '1h':
          startTime = endTime - 3600; // 1 hour
          break;
        case '6h':
          startTime = endTime - 21600; // 6 hours
          break;
        case '24h':
          startTime = endTime - 86400; // 24 hours
          break;
        case '7d':
          startTime = endTime - 604800; // 7 days
          break;
        default:
          startTime = endTime - 3600; // Default to 1 hour
      }
      
      const historyResponse = await monitoringApi.getMetricsHistory(startTime, endTime);
      setMetricsHistory(historyResponse.data.metrics || []);
      
      // Fetch health check
      const healthResponse = await monitoringApi.getHealthCheck();
      setHealth(healthResponse.data.health);
      
      // Fetch usage report
      const reportResponse = await monitoringApi.getUsageReport();
      setReport(reportResponse.data.report);
      
      setLoading(false);
      setRefreshing(false);
    } catch (err) {
      console.error('Error fetching monitoring data:', err);
      setError('Failed to load monitoring data. Please try again later.');
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };

  const handleRefresh = () => {
    fetchMonitoringData();
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Prepare chart data for CPU usage history
  const cpuChartData = {
    labels: metricsHistory.map(m => {
      const date = new Date(m.timestamp * 1000);
      return date.toLocaleTimeString();
    }),
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: metricsHistory.map(m => m.containers?.avg_cpu_usage || 0),
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
      },
    ],
  };

  // Prepare chart data for memory usage history
  const memoryChartData = {
    labels: metricsHistory.map(m => {
      const date = new Date(m.timestamp * 1000);
      return date.toLocaleTimeString();
    }),
    datasets: [
      {
        label: 'Memory Usage (%)',
        data: metricsHistory.map(m => m.containers?.avg_memory_usage || 0),
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        tension: 0.1,
      },
    ],
  };

  // Prepare chart data for network usage history
  const networkChartData = {
    labels: metricsHistory.map(m => {
      const date = new Date(m.timestamp * 1000);
      return date.toLocaleTimeString();
    }),
    datasets: [
      {
        label: 'Network RX (KB/s)',
        data: metricsHistory.map(m => (m.containers?.avg_network_rx || 0) / 1024),
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
      {
        label: 'Network TX (KB/s)',
        data: metricsHistory.map(m => (m.containers?.avg_network_tx || 0) / 1024),
        borderColor: 'rgba(153, 102, 255, 1)',
        backgroundColor: 'rgba(153, 102, 255, 0.2)',
        tension: 0.1,
      },
    ],
  };

  // Prepare chart data for container health
  const healthChartData = {
    labels: ['Healthy', 'Unhealthy', 'Unknown'],
    datasets: [
      {
        label: 'Container Health',
        data: health ? [health.healthy, health.unhealthy, health.unknown] : [0, 0, 0],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(201, 203, 207, 0.6)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(201, 203, 207, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  if (loading && !metrics) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress data-testid="loading-indicator" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Typography color="error" data-testid="error-message">{error}</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} data-testid="monitoring">
      {/* Header */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography component="h2" variant="h6" color="primary" gutterBottom data-testid="monitoring-title">
              System Monitoring
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <FormControl sx={{ m: 1, minWidth: 120 }} size="small">
                <InputLabel id="time-range-select-label">Time Range</InputLabel>
                <Select
                  labelId="time-range-select-label"
                  id="time-range-select"
                  value={timeRange}
                  label="Time Range"
                  onChange={handleTimeRangeChange}
                  data-testid="time-range-select"
                >
                  <MenuItem value="1h">Last Hour</MenuItem>
                  <MenuItem value="6h">Last 6 Hours</MenuItem>
                  <MenuItem value="24h">Last 24 Hours</MenuItem>
                  <MenuItem value="7d">Last 7 Days</MenuItem>
                </Select>
              </FormControl>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleRefresh}
                disabled={refreshing}
                data-testid="refresh-button"
              >
                Refresh
              </Button>
            </Box>
          </Box>
          <Typography variant="body1" data-testid="monitoring-description">
            Monitor system performance, health, and resource usage.
          </Typography>
          {refreshing && (
            <Box sx={{ width: '100%', mt: 2 }}>
              <LinearProgress />
            </Box>
          )}
        </Paper>
      </Grid>
      
      {/* System Health Cards */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Container Health
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h3" component="div" color="success.main" data-testid="healthy-containers">
                {health?.healthy || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                healthy containers
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="error.main" data-testid="unhealthy-containers">
                {health?.unhealthy || 0} unhealthy
              </Typography>
              <Typography variant="body2" color="text.secondary" data-testid="unknown-containers">
                {health?.unknown || 0} unknown
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={(health?.healthy / health?.total) * 100 || 0}
                  color="success"
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {Math.round((health?.healthy / health?.total) * 100 || 0)}%
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              CPU Usage
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography 
                variant="h3" 
                component="div" 
                color={
                  (metrics?.containers?.avg_cpu_usage || 0) > 80 ? 'error.main' :
                  (metrics?.containers?.avg_cpu_usage || 0) > 50 ? 'warning.main' :
                  'success.main'
                }
                data-testid="cpu-usage"
              >
                {Math.round(metrics?.containers?.avg_cpu_usage || 0)}%
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics?.containers?.avg_cpu_usage || 0}
                  color={
                    (metrics?.containers?.avg_cpu_usage || 0) > 80 ? 'error' :
                    (metrics?.containers?.avg_cpu_usage || 0) > 50 ? 'warning' :
                    'success'
                  }
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {Math.round(metrics?.containers?.avg_cpu_usage || 0)}%
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Memory Usage
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography 
                variant="h3" 
                component="div" 
                color={
                  (metrics?.containers?.avg_memory_usage || 0) > 80 ? 'error.main' :
                  (metrics?.containers?.avg_memory_usage || 0) > 50 ? 'warning.main' :
                  'success.main'
                }
                data-testid="memory-usage"
              >
                {Math.round(metrics?.containers?.avg_memory_usage || 0)}%
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics?.containers?.avg_memory_usage || 0}
                  color={
                    (metrics?.containers?.avg_memory_usage || 0) > 80 ? 'error' :
                    (metrics?.containers?.avg_memory_usage || 0) > 50 ? 'warning' :
                    'success'
                  }
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {Math.round(metrics?.containers?.avg_memory_usage || 0)}%
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Tabs */}
      <Grid item xs={12}>
        <Paper sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange} 
              aria-label="monitoring tabs"
              data-testid="monitoring-tabs"
            >
              <Tab label="Resource Usage" id="monitoring-tab-0" aria-controls="monitoring-tabpanel-0" data-testid="resource-usage-tab" />
              <Tab label="Container Health" id="monitoring-tab-1" aria-controls="monitoring-tabpanel-1" data-testid="container-health-tab" />
              <Tab label="Usage Report" id="monitoring-tab-2" aria-controls="monitoring-tabpanel-2" data-testid="usage-report-tab" />
            </Tabs>
          </Box>
          
          {/* Resource Usage Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              {/* CPU Usage Chart */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  CPU Usage History
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Line 
                    data={cpuChartData} 
                    options={{ 
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                        },
                      },
                    }} 
                    data-testid="cpu-chart"
                  />
                </Box>
              </Grid>
              
              {/* Memory Usage Chart */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Memory Usage History
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Line 
                    data={memoryChartData} 
                    options={{ 
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          max: 100,
                        },
                      },
                    }} 
                    data-testid="memory-chart"
                  />
                </Box>
              </Grid>
              
              {/* Network Usage Chart */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Network Usage History
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Line 
                    data={networkChartData} 
                    options={{ 
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }} 
                    data-testid="network-chart"
                  />
                </Box>
              </Grid>
            </Grid>
          </TabPanel>
          
          {/* Container Health Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              {/* Health Chart */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Container Health Distribution
                </Typography>
                <Box sx={{ height: 300 }}>
                  <Bar 
                    data={healthChartData} 
                    options={{ 
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: false,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }} 
                    data-testid="health-chart"
                  />
                </Box>
              </Grid>
              
              {/* Unhealthy Containers Table */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Unhealthy Containers
                </Typography>
                <TableContainer>
                  <Table aria-label="unhealthy containers table" data-testid="unhealthy-containers-table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Container ID</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Issue</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {health?.unhealthy_containers?.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} align="center">No unhealthy containers</TableCell>
                        </TableRow>
                      ) : (
                        health?.unhealthy_containers?.map((container) => (
                          <TableRow key={container.container_id} data-testid={`unhealthy-container-${container.container_id}`}>
                            <TableCell>{container.container_id}</TableCell>
                            <TableCell>
                              <Chip 
                                label={container.status} 
                                color="error"
                                size="small"
                              />
                            </TableCell>
                            <TableCell>{container.issue || 'Unknown issue'}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          </TabPanel>
          
          {/* Usage Report Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              {/* Pool Usage */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Pool Usage
                </Typography>
                <TableContainer>
                  <Table aria-label="pool usage table" data-testid="pool-usage-table">
                    <TableBody>
                      <TableRow>
                        <TableCell component="th" scope="row">Average Total Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.avg_total_containers?.toFixed(1) || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Average Available Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.avg_available_containers?.toFixed(1) || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Average Assigned Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.avg_assigned_containers?.toFixed(1) || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Maximum Total Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.max_total_containers || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Minimum Available Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.min_available_containers || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Maximum Assigned Containers</TableCell>
                        <TableCell align="right">{report?.pool_usage?.max_assigned_containers || 0}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
              
              {/* Queue Usage */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Queue Usage
                </Typography>
                <TableContainer>
                  <Table aria-label="queue usage table" data-testid="queue-usage-table">
                    <TableBody>
                      <TableRow>
                        <TableCell component="th" scope="row">Average Queue Length</TableCell>
                        <TableCell align="right">{report?.queue_usage?.avg_queue_length?.toFixed(1) || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Maximum Queue Length</TableCell>
                        <TableCell align="right">{report?.queue_usage?.max_queue_length || 0}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Average Wait Time</TableCell>
                        <TableCell align="right">{report?.queue_usage?.avg_wait_time?.toFixed(1) || 0} seconds</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell component="th" scope="row">Maximum Wait Time</TableCell>
                        <TableCell align="right">{report?.queue_usage?.max_wait_time?.toFixed(1) || 0} seconds</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
              
              {/* Report Period */}
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Report Period: {report?.period ? `${new Date(report.period.start * 1000).toLocaleString()} to ${new Date(report.period.end * 1000).toLocaleString()} (${report.period.duration_hours} hours)` : 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>
      </Grid>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Grid>
  );
}

export default Monitoring;

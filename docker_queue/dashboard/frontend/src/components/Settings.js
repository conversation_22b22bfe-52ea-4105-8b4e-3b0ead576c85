import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Button,
  Box,
  CircularProgress,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Card,
  CardContent,
  CardActions,
  Snackbar,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import RefreshIcon from '@mui/icons-material/Refresh';
import axios from 'axios';
import ThemeSettings from './ThemeSettings';

function Settings() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [settings, setSettings] = useState({
    pool: {
      min_size: 5,
      max_size: 20,
      target_size: 10,
      min_available: 2,
      auto_scaling: true,
      scaling_interval: 60,
    },
    container: {
      image: 'bahtbrowse:latest',
      cpu_limit: 1.0,
      memory_limit: '2g',
      network: 'bahtbrowse',
      timeout: 3600,
      idle_timeout: 300,
    },
    queue: {
      max_wait_time: 300,
      default_priority: 5,
      max_queue_size: 100,
    },
    system: {
      metrics_retention_days: 7,
      log_level: 'INFO',
      admin_email: '<EMAIL>',
      notifications_enabled: true,
    },
  });
  const [originalSettings, setOriginalSettings] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [saving, setSaving] = useState(false);
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [logEntries, setLogEntries] = useState([]);

  useEffect(() => {
    fetchSettings();
    fetchLogs();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);

      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay and use the default settings
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate API response
      const response = {
        status: 'success',
        settings: {
          pool: {
            min_size: 5,
            max_size: 20,
            target_size: 10,
            min_available: 2,
            auto_scaling: true,
            scaling_interval: 60,
          },
          container: {
            image: 'bahtbrowse:latest',
            cpu_limit: 1.0,
            memory_limit: '2g',
            network: 'bahtbrowse',
            timeout: 3600,
            idle_timeout: 300,
          },
          queue: {
            max_wait_time: 300,
            default_priority: 5,
            max_queue_size: 100,
          },
          system: {
            metrics_retention_days: 7,
            log_level: 'INFO',
            admin_email: '<EMAIL>',
            notifications_enabled: true,
          },
        }
      };

      setSettings(response.settings);
      setOriginalSettings(JSON.parse(JSON.stringify(response.settings)));

      setLoading(false);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings. Please try again later.');
      setLoading(false);
    }
  };

  const fetchLogs = async () => {
    try {
      // In a real implementation, this would be an API call
      // For now, we'll use mock data

      // Simulate API response
      const response = {
        status: 'success',
        logs: [
          {
            id: 1,
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'INFO',
            message: 'Container pool scaled to 15 containers',
          },
          {
            id: 2,
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'WARNING',
            message: 'Container unhealthy: container_id_123',
          },
          {
            id: 3,
            timestamp: new Date(Date.now() - 180000).toISOString(),
            level: 'ERROR',
            message: 'Failed to create container: Docker API error',
          },
          {
            id: 4,
            timestamp: new Date(Date.now() - 240000).toISOString(),
            level: 'INFO',
            message: 'Container request processed for user_id_456',
          },
          {
            id: 5,
            timestamp: new Date(Date.now() - 300000).toISOString(),
            level: 'INFO',
            message: 'System started',
          },
        ]
      };

      setLogEntries(response.logs);
    } catch (err) {
      console.error('Error fetching logs:', err);
      setSnackbar({
        open: true,
        message: 'Failed to load logs. Please try again later.',
        severity: 'error',
      });
    }
  };

  const handleSettingChange = (category, setting, value) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [setting]: value,
      },
    });
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);

      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update original settings
      setOriginalSettings(JSON.parse(JSON.stringify(settings)));

      setSaving(false);
      setSnackbar({
        open: true,
        message: 'Settings saved successfully',
        severity: 'success',
      });
    } catch (err) {
      console.error('Error saving settings:', err);
      setSaving(false);
      setSnackbar({
        open: true,
        message: 'Failed to save settings. Please try again later.',
        severity: 'error',
      });
    }
  };

  const handleResetSettings = () => {
    setResetDialogOpen(true);
  };

  const confirmResetSettings = () => {
    setSettings(JSON.parse(JSON.stringify(originalSettings)));
    setResetDialogOpen(false);
    setSnackbar({
      open: true,
      message: 'Settings reset to last saved values',
      severity: 'info',
    });
  };

  const handleRefreshLogs = () => {
    fetchLogs();
  };

  const handleClearLogs = async () => {
    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      setLogEntries([]);

      setSnackbar({
        open: true,
        message: 'Logs cleared successfully',
        severity: 'success',
      });
    } catch (err) {
      console.error('Error clearing logs:', err);
      setSnackbar({
        open: true,
        message: 'Failed to clear logs. Please try again later.',
        severity: 'error',
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const hasChanges = () => {
    return JSON.stringify(settings) !== JSON.stringify(originalSettings);
  };

  if (loading && !originalSettings) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress data-testid="loading-indicator" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Typography color="error" data-testid="error-message">{error}</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} data-testid="settings">
      {/* Header */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography component="h2" variant="h6" color="primary" gutterBottom data-testid="settings-title">
              System Settings
            </Typography>
            <Box>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleResetSettings}
                disabled={!hasChanges() || saving}
                sx={{ mr: 1 }}
                data-testid="reset-settings-button"
              >
                Reset
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSaveSettings}
                disabled={!hasChanges() || saving}
                startIcon={<SaveIcon />}
                data-testid="save-settings-button"
              >
                Save Changes
              </Button>
            </Box>
          </Box>
          <Typography variant="body1" data-testid="settings-description">
            Configure system settings and view logs.
          </Typography>
        </Paper>
      </Grid>

      {/* Pool Settings */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Pool Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Minimum Pool Size"
                  type="number"
                  fullWidth
                  value={settings.pool.min_size}
                  onChange={(e) => handleSettingChange('pool', 'min_size', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 1 }}
                  margin="normal"
                  data-testid="min-size-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Maximum Pool Size"
                  type="number"
                  fullWidth
                  value={settings.pool.max_size}
                  onChange={(e) => handleSettingChange('pool', 'max_size', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 1 }}
                  margin="normal"
                  data-testid="max-size-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Target Pool Size"
                  type="number"
                  fullWidth
                  value={settings.pool.target_size}
                  onChange={(e) => handleSettingChange('pool', 'target_size', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 1 }}
                  margin="normal"
                  data-testid="target-size-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Minimum Available Containers"
                  type="number"
                  fullWidth
                  value={settings.pool.min_available}
                  onChange={(e) => handleSettingChange('pool', 'min_available', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0 }}
                  margin="normal"
                  data-testid="min-available-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Scaling Interval (seconds)"
                  type="number"
                  fullWidth
                  value={settings.pool.scaling_interval}
                  onChange={(e) => handleSettingChange('pool', 'scaling_interval', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 10 }}
                  margin="normal"
                  data-testid="scaling-interval-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.pool.auto_scaling}
                      onChange={(e) => handleSettingChange('pool', 'auto_scaling', e.target.checked)}
                      data-testid="auto-scaling-switch"
                    />
                  }
                  label="Auto Scaling"
                  sx={{ mt: 2 }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Container Settings */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Container Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Container Image"
                  fullWidth
                  value={settings.container.image}
                  onChange={(e) => handleSettingChange('container', 'image', e.target.value)}
                  margin="normal"
                  data-testid="container-image-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="CPU Limit"
                  type="number"
                  fullWidth
                  value={settings.container.cpu_limit}
                  onChange={(e) => handleSettingChange('container', 'cpu_limit', parseFloat(e.target.value) || 0)}
                  inputProps={{ min: 0.1, step: 0.1 }}
                  margin="normal"
                  data-testid="cpu-limit-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Memory Limit"
                  fullWidth
                  value={settings.container.memory_limit}
                  onChange={(e) => handleSettingChange('container', 'memory_limit', e.target.value)}
                  margin="normal"
                  data-testid="memory-limit-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Network"
                  fullWidth
                  value={settings.container.network}
                  onChange={(e) => handleSettingChange('container', 'network', e.target.value)}
                  margin="normal"
                  data-testid="network-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Session Timeout (seconds)"
                  type="number"
                  fullWidth
                  value={settings.container.timeout}
                  onChange={(e) => handleSettingChange('container', 'timeout', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 60 }}
                  margin="normal"
                  data-testid="timeout-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Idle Timeout (seconds)"
                  type="number"
                  fullWidth
                  value={settings.container.idle_timeout}
                  onChange={(e) => handleSettingChange('container', 'idle_timeout', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0 }}
                  margin="normal"
                  data-testid="idle-timeout-input"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Queue Settings */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Queue Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Maximum Wait Time (seconds)"
                  type="number"
                  fullWidth
                  value={settings.queue.max_wait_time}
                  onChange={(e) => handleSettingChange('queue', 'max_wait_time', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0 }}
                  margin="normal"
                  data-testid="max-wait-time-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Default Priority (0-9)"
                  type="number"
                  fullWidth
                  value={settings.queue.default_priority}
                  onChange={(e) => handleSettingChange('queue', 'default_priority', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 0, max: 9 }}
                  margin="normal"
                  data-testid="default-priority-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Maximum Queue Size"
                  type="number"
                  fullWidth
                  value={settings.queue.max_queue_size}
                  onChange={(e) => handleSettingChange('queue', 'max_queue_size', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 1 }}
                  margin="normal"
                  data-testid="max-queue-size-input"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* System Settings */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              System Settings
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Metrics Retention (days)"
                  type="number"
                  fullWidth
                  value={settings.system.metrics_retention_days}
                  onChange={(e) => handleSettingChange('system', 'metrics_retention_days', parseInt(e.target.value) || 0)}
                  inputProps={{ min: 1 }}
                  margin="normal"
                  data-testid="metrics-retention-input"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Log Level"
                  select
                  fullWidth
                  value={settings.system.log_level}
                  onChange={(e) => handleSettingChange('system', 'log_level', e.target.value)}
                  margin="normal"
                  SelectProps={{
                    native: true,
                  }}
                  data-testid="log-level-select"
                >
                  <option value="DEBUG">DEBUG</option>
                  <option value="INFO">INFO</option>
                  <option value="WARNING">WARNING</option>
                  <option value="ERROR">ERROR</option>
                </TextField>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Admin Email"
                  fullWidth
                  value={settings.system.admin_email}
                  onChange={(e) => handleSettingChange('system', 'admin_email', e.target.value)}
                  margin="normal"
                  data-testid="admin-email-input"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.system.notifications_enabled}
                      onChange={(e) => handleSettingChange('system', 'notifications_enabled', e.target.checked)}
                      data-testid="notifications-switch"
                    />
                  }
                  label="Enable Email Notifications"
                  sx={{ mt: 1 }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Theme Settings */}
      <Grid item xs={12} md={6}>
        <ThemeSettings />
      </Grid>

      {/* System Logs */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }} className="system-logs">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" color="primary">
              System Logs
            </Typography>
            <Box>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleRefreshLogs}
                startIcon={<RefreshIcon />}
                sx={{ mr: 1 }}
                data-testid="refresh-logs-button"
              >
                Refresh
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleClearLogs}
                startIcon={<DeleteIcon />}
                data-testid="clear-logs-button"
              >
                Clear
              </Button>
            </Box>
          </Box>
          <List sx={{ bgcolor: 'background.paper', maxHeight: 300, overflow: 'auto' }} data-testid="logs-list" className="logs-container">
            {logEntries.length === 0 ? (
              <ListItem>
                <ListItemText primary="No logs available" />
              </ListItem>
            ) : (
              logEntries.map((log) => (
                <ListItem key={log.id} data-testid={`log-entry-${log.id}`}>
                  <ListItemText
                    primary={log.message}
                    secondary={`${new Date(log.timestamp).toLocaleString()} - ${log.level}`}
                    primaryTypographyProps={{
                      color: log.level === 'ERROR' ? 'error' :
                             log.level === 'WARNING' ? 'warning.main' :
                             'textPrimary',
                      className: 'log-message'
                    }}
                    secondaryTypographyProps={{
                      className: 'log-timestamp'
                    }}
                  />
                </ListItem>
              ))
            )}
          </List>
        </Paper>
      </Grid>

      {/* Reset Dialog */}
      <Dialog
        open={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        aria-labelledby="reset-dialog-title"
        data-testid="reset-dialog"
      >
        <DialogTitle id="reset-dialog-title">Reset Settings</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to reset all settings to their last saved values? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResetDialogOpen(false)} data-testid="cancel-reset-button">Cancel</Button>
          <Button onClick={confirmResetSettings} color="secondary" data-testid="confirm-reset-button">
            Reset
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Grid>
  );
}

export default Settings;

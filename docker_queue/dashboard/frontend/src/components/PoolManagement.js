import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Button,
  Box,
  CircularProgress,
  Slider,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  Card,
  CardContent,
  CardActions,
  Divider,
  LinearProgress,
} from '@mui/material';
import { poolApi, monitoringApi } from '../services/api';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

function PoolManagement() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [poolStatus, setPoolStatus] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [metricsHistory, setMetricsHistory] = useState([]);
  const [targetSize, setTargetSize] = useState(10);
  const [scaleDialogOpen, setScaleDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [scaling, setScaling] = useState(false);

  useEffect(() => {
    fetchPoolData();
    
    // Set up polling interval
    const interval = setInterval(fetchPoolData, 10000); // Refresh every 10 seconds
    
    return () => clearInterval(interval);
  }, []);

  const fetchPoolData = async () => {
    try {
      setLoading(true);
      
      // Fetch pool status
      const poolResponse = await poolApi.getPoolStatus();
      setPoolStatus(poolResponse.data.pool);
      
      // Set initial target size based on current pool size
      if (!targetSize && poolResponse.data.pool) {
        setTargetSize(poolResponse.data.pool.total_containers);
      }
      
      // Fetch current metrics
      const metricsResponse = await monitoringApi.getCurrentMetrics();
      setMetrics(metricsResponse.data.metrics);
      
      // Fetch metrics history
      const endTime = Math.floor(Date.now() / 1000);
      const startTime = endTime - 3600; // Last hour
      const historyResponse = await monitoringApi.getMetricsHistory(startTime, endTime);
      setMetricsHistory(historyResponse.data.metrics || []);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching pool data:', err);
      setError('Failed to load pool data. Please try again later.');
      setLoading(false);
    }
  };

  const handleScaleDialogOpen = () => {
    // Set target size to current pool size
    if (poolStatus) {
      setTargetSize(poolStatus.total_containers);
    }
    setScaleDialogOpen(true);
  };

  const handleScaleDialogClose = () => {
    setScaleDialogOpen(false);
  };

  const handleScalePool = async () => {
    try {
      setScaleDialogOpen(false);
      setScaling(true);
      
      // Scale pool
      const response = await poolApi.scalePool(targetSize);
      
      if (response.data.status === 'success') {
        setSnackbar({
          open: true,
          message: `Pool scaling ${response.data.scaling.action} initiated`,
          severity: 'success',
        });
      } else {
        setSnackbar({
          open: true,
          message: response.data.message || 'Failed to scale pool',
          severity: 'error',
        });
      }
      
      // Refresh data
      await fetchPoolData();
      
      setScaling(false);
    } catch (err) {
      console.error('Error scaling pool:', err);
      setSnackbar({
        open: true,
        message: 'Failed to scale pool. Please try again later.',
        severity: 'error',
      });
      setScaling(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Prepare chart data for pool size history
  const poolSizeChartData = {
    labels: metricsHistory.map(m => {
      const date = new Date(m.timestamp * 1000);
      return date.toLocaleTimeString();
    }),
    datasets: [
      {
        label: 'Total Containers',
        data: metricsHistory.map(m => m.pool.total_containers),
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
      {
        label: 'Available Containers',
        data: metricsHistory.map(m => m.pool.available_containers),
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        tension: 0.1,
      },
      {
        label: 'Assigned Containers',
        data: metricsHistory.map(m => m.pool.assigned_containers),
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        tension: 0.1,
      },
    ],
  };

  // Prepare chart data for queue length history
  const queueChartData = {
    labels: metricsHistory.map(m => {
      const date = new Date(m.timestamp * 1000);
      return date.toLocaleTimeString();
    }),
    datasets: [
      {
        label: 'Queue Length',
        data: metricsHistory.map(m => m.queue.length),
        borderColor: 'rgba(153, 102, 255, 1)',
        backgroundColor: 'rgba(153, 102, 255, 0.2)',
        tension: 0.1,
      },
    ],
  };

  if (loading && !poolStatus) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress data-testid="loading-indicator" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Typography color="error" data-testid="error-message">{error}</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} data-testid="pool-management">
      {/* Header */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography component="h2" variant="h6" color="primary" gutterBottom data-testid="pool-management-title">
              Pool Management
            </Typography>
            <Button 
              variant="contained" 
              color="primary" 
              onClick={handleScaleDialogOpen}
              disabled={scaling}
              data-testid="scale-pool-button"
            >
              Scale Pool
            </Button>
          </Box>
          <Typography variant="body1" data-testid="pool-management-description">
            Manage the Docker container pool size and configuration.
          </Typography>
        </Paper>
      </Grid>
      
      {/* Pool Status Cards */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Pool Size
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h3" component="div" data-testid="total-containers">
                {poolStatus?.total_containers || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                containers
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              Target: {poolStatus?.target_size || 0} containers
            </Typography>
            {scaling && (
              <Box sx={{ width: '100%', mt: 2 }}>
                <LinearProgress />
              </Box>
            )}
          </CardContent>
          <Divider />
          <CardActions>
            <Button 
              size="small" 
              onClick={handleScaleDialogOpen}
              disabled={scaling}
              data-testid="adjust-size-button"
            >
              Adjust Size
            </Button>
          </CardActions>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Available Containers
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h3" component="div" color="success.main" data-testid="available-containers">
                {poolStatus?.available_containers || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                containers
              </Typography>
            </Box>
            <Typography variant="body2" color="text.secondary">
              {poolStatus?.available_containers > 0 
                ? `${Math.round((poolStatus.available_containers / poolStatus.total_containers) * 100)}% of total pool`
                : 'No available containers'}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={(poolStatus?.available_containers / poolStatus?.total_containers) * 100 || 0}
                  color="success"
                />
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" color="primary" gutterBottom>
              Queue Status
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h3" component="div" color={metrics?.queue?.length > 0 ? 'warning.main' : 'success.main'} data-testid="queue-length">
                {metrics?.queue?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                requests in queue
              </Typography>
            </Box>
            {metrics?.queue?.avg_wait_time > 0 && (
              <Typography variant="body2" color="text.secondary" data-testid="avg-wait-time">
                Average wait time: {metrics.queue.avg_wait_time.toFixed(1)} seconds
              </Typography>
            )}
          </CardContent>
        </Card>
      </Grid>
      
      {/* Pool Size History Chart */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography component="h3" variant="h6" color="primary" gutterBottom>
            Pool Size History
          </Typography>
          <Box sx={{ height: 300 }}>
            <Line 
              data={poolSizeChartData} 
              options={{ 
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                  },
                },
              }} 
              data-testid="pool-size-chart"
            />
          </Box>
        </Paper>
      </Grid>
      
      {/* Queue Length History Chart */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2 }}>
          <Typography component="h3" variant="h6" color="primary" gutterBottom>
            Queue Length History
          </Typography>
          <Box sx={{ height: 300 }}>
            <Line 
              data={queueChartData} 
              options={{ 
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top',
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                  },
                },
              }} 
              data-testid="queue-length-chart"
            />
          </Box>
        </Paper>
      </Grid>
      
      {/* Scale Pool Dialog */}
      <Dialog
        open={scaleDialogOpen}
        onClose={handleScaleDialogClose}
        aria-labelledby="scale-pool-dialog-title"
        data-testid="scale-pool-dialog"
      >
        <DialogTitle id="scale-pool-dialog-title">Scale Container Pool</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Adjust the target size of the container pool. Current size: {poolStatus?.total_containers || 0} containers.
          </DialogContentText>
          <Box sx={{ mt: 3 }}>
            <Typography id="target-size-slider" gutterBottom>
              Target Size: {targetSize} containers
            </Typography>
            <Slider
              value={targetSize}
              onChange={(e, newValue) => setTargetSize(newValue)}
              aria-labelledby="target-size-slider"
              valueLabelDisplay="auto"
              step={1}
              marks
              min={1}
              max={50}
              data-testid="target-size-slider"
            />
          </Box>
          <TextField
            margin="dense"
            id="target-size"
            label="Target Size"
            type="number"
            fullWidth
            variant="outlined"
            value={targetSize}
            onChange={(e) => setTargetSize(Math.max(1, Math.min(50, parseInt(e.target.value) || 1)))}
            inputProps={{ min: 1, max: 50 }}
            data-testid="target-size-input"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleScaleDialogClose} data-testid="cancel-scale-button">Cancel</Button>
          <Button 
            onClick={handleScalePool} 
            variant="contained" 
            color="primary"
            disabled={targetSize === poolStatus?.total_containers}
            data-testid="submit-scale-button"
          >
            Scale Pool
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Grid>
  );
}

export default PoolManagement;

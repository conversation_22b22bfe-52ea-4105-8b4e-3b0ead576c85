import React, { useContext } from 'react';
import { Typography } from '@mui/material';
import { ThemeContext } from '../contexts/ThemeContext';

const WarpedText = ({ variant = 'h6', children, ...props }) => {
  const { darkMode, crtEffect, crtIntensity } = useContext(ThemeContext);
  
  // Only apply data-text attribute when in dark mode with high intensity CRT effect
  const dataTextProps = (darkMode && crtEffect && crtIntensity === 'high') 
    ? { 'data-text': children } 
    : {};
  
  return (
    <Typography variant={variant} {...dataTextProps} {...props}>
      {children}
    </Typography>
  );
};

export default WarpedText;

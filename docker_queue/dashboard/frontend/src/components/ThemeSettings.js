import React, { useContext } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  FormGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Box
} from '@mui/material';
import { ThemeContext } from '../contexts/ThemeContext';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import MonitorIcon from '@mui/icons-material/Monitor';
import TextFormatIcon from '@mui/icons-material/TextFormat';
import TuneIcon from '@mui/icons-material/Tune';
import TerminalIcon from '@mui/icons-material/Terminal';
import WarpedText from './WarpedText';

const ThemeSettings = () => {
  const {
    darkMode,
    toggleDarkMode,
    crtEffect,
    toggleCrtEffect,
    crtIntensity,
    changeCrtIntensity,
    fontStyle,
    changeFontStyle
  } = useContext(ThemeContext);

  return (
    <Card>
      <CardContent>
        <WarpedText variant="h6" gutterBottom>
          <TuneIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Theme Settings
        </WarpedText>

        <Divider sx={{ my: 2 }} />

        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={darkMode}
                onChange={toggleDarkMode}
                color="primary"
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {darkMode ?
                  <TerminalIcon sx={{ mr: 1 }} /> :
                  <LightModeIcon sx={{ mr: 1 }} />
                }
                <Typography>
                  {darkMode ? 'Retro Terminal Mode' : 'Light Mode'}
                </Typography>
              </Box>
            }
          />

          <FormControlLabel
            control={
              <Switch
                checked={crtEffect}
                onChange={toggleCrtEffect}
                color="primary"
                disabled={!darkMode}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <MonitorIcon sx={{ mr: 1 }} />
                <Typography>
                  CRT Scanline Effect
                </Typography>
              </Box>
            }
          />

          {crtEffect && darkMode && (
            <FormControl variant="outlined" size="small" sx={{ mt: 2, mb: 2 }}>
              <InputLabel id="crt-intensity-label">CRT Intensity</InputLabel>
              <Select
                labelId="crt-intensity-label"
                id="crt-intensity"
                value={crtIntensity}
                onChange={(e) => changeCrtIntensity(e.target.value)}
                label="CRT Intensity"
              >
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="high">High</MenuItem>
              </Select>
            </FormControl>
          )}

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <TextFormatIcon sx={{ mr: 1 }} />
            <Typography>
              Font Style
            </Typography>
          </Box>

          <FormControl variant="outlined" size="small">
            <InputLabel id="font-style-label">Font Style</InputLabel>
            <Select
              labelId="font-style-label"
              id="font-style"
              value={fontStyle}
              onChange={(e) => changeFontStyle(e.target.value)}
              label="Font Style"
            >
              <MenuItem value="roboto">Roboto (Default)</MenuItem>
              <MenuItem value="light">Roboto Light</MenuItem>
            </Select>
          </FormControl>
        </FormGroup>
      </CardContent>
    </Card>
  );
};

export default ThemeSettings;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Grid, Paper, Typography, Button, Box, CircularProgress } from '@mui/material';
import { poolApi, monitoringApi } from '../services/api';
import { Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

function Dashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [poolStatus, setPoolStatus] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [health, setHealth] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Fetch pool status
        const poolResponse = await poolApi.getPoolStatus();
        setPoolStatus(poolResponse.data.pool);
        
        // Fetch current metrics
        const metricsResponse = await monitoringApi.getCurrentMetrics();
        setMetrics(metricsResponse.data.metrics);
        
        // Fetch health check
        const healthResponse = await monitoringApi.getHealthCheck();
        setHealth(healthResponse.data.health);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
        setLoading(false);
      }
    };

    fetchDashboardData();
    
    // Set up polling interval
    const interval = setInterval(fetchDashboardData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Prepare chart data for container pool
  const poolChartData = {
    labels: ['Available', 'Assigned', 'Recycling'],
    datasets: [
      {
        data: poolStatus ? [
          poolStatus.available_containers,
          poolStatus.assigned_containers,
          poolStatus.recycling_containers
        ] : [0, 0, 0],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 206, 86, 0.6)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare chart data for container health
  const healthChartData = {
    labels: ['Healthy', 'Unhealthy', 'Unknown'],
    datasets: [
      {
        data: health ? [
          health.healthy,
          health.unhealthy,
          health.unknown
        ] : [0, 0, 0],
        backgroundColor: [
          'rgba(75, 192, 192, 0.6)',
          'rgba(255, 99, 132, 0.6)',
          'rgba(201, 203, 207, 0.6)',
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(201, 203, 207, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Prepare chart data for resource usage
  const resourceChartData = {
    labels: ['CPU Usage (%)', 'Memory Usage (%)', 'Network RX (KB/s)', 'Network TX (KB/s)'],
    datasets: [
      {
        label: 'Resource Usage',
        data: metrics && metrics.containers ? [
          metrics.containers.avg_cpu_usage || 0,
          metrics.containers.avg_memory_usage || 0,
          (metrics.containers.avg_network_rx || 0) / 1024,
          (metrics.containers.avg_network_tx || 0) / 1024
        ] : [0, 0, 0, 0],
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress data-testid="loading-indicator" />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Typography color="error" data-testid="error-message">{error}</Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3} data-testid="dashboard-container">
      {/* Header */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
          <Typography component="h2" variant="h6" color="primary" gutterBottom data-testid="dashboard-title">
            System Overview
          </Typography>
          <Typography variant="body1" data-testid="dashboard-description">
            Welcome to the BahtBrowse Docker Queue Management Dashboard. Here you can monitor the system status and performance.
          </Typography>
        </Paper>
      </Grid>
      
      {/* Container Pool Status */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 240 }}>
          <Typography component="h2" variant="h6" color="primary" gutterBottom>
            Container Pool
          </Typography>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Doughnut 
              data={poolChartData} 
              options={{ 
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                },
              }} 
              data-testid="pool-chart"
            />
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={() => navigate('/pool')}
              data-testid="view-pool-button"
            >
              View Pool Details
            </Button>
          </Box>
        </Paper>
      </Grid>
      
      {/* Container Health */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 240 }}>
          <Typography component="h2" variant="h6" color="primary" gutterBottom>
            Container Health
          </Typography>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Doughnut 
              data={healthChartData} 
              options={{ 
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                },
              }} 
              data-testid="health-chart"
            />
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={() => navigate('/monitoring')}
              data-testid="view-health-button"
            >
              View Health Details
            </Button>
          </Box>
        </Paper>
      </Grid>
      
      {/* Queue Status */}
      <Grid item xs={12} md={4}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 240 }}>
          <Typography component="h2" variant="h6" color="primary" gutterBottom>
            Queue Status
          </Typography>
          <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Typography variant="h3" align="center" data-testid="queue-length">
              {metrics?.queue?.length || 0}
            </Typography>
            <Typography variant="body1" align="center" color="textSecondary">
              Requests in Queue
            </Typography>
            {metrics?.queue?.avg_wait_time > 0 && (
              <Typography variant="body2" align="center" color="textSecondary" sx={{ mt: 1 }} data-testid="avg-wait-time">
                Average Wait Time: {(metrics.queue.avg_wait_time).toFixed(1)} seconds
              </Typography>
            )}
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={() => navigate('/containers')}
              data-testid="view-queue-button"
            >
              View Queue Details
            </Button>
          </Box>
        </Paper>
      </Grid>
      
      {/* Resource Usage */}
      <Grid item xs={12}>
        <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 300 }}>
          <Typography component="h2" variant="h6" color="primary" gutterBottom>
            Resource Usage
          </Typography>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Bar 
              data={resourceChartData} 
              options={{ 
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: false,
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                  },
                },
              }} 
              data-testid="resource-chart"
            />
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="outlined" 
              color="primary" 
              onClick={() => navigate('/monitoring')}
              data-testid="view-resources-button"
            >
              View Resource Details
            </Button>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
}

export default Dashboard;

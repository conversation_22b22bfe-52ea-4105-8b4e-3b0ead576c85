const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://127.0.0.1:5000',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '/api', // no rewrite needed
      },
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.status(500).json({
          status: 'error',
          message: 'API server is not available',
          error: err.message
        });
      }
    })
  );
};

import React, { useContext, useMemo } from 'react';
import { Routes, Route } from 'react-router-dom';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Box from '@mui/material/Box';

import Dashboard from './components/Dashboard';
import ContainerManagement from './components/ContainerManagement';
import PoolManagement from './components/PoolManagement';
import Monitoring from './components/Monitoring';
import Settings from './components/Settings';
import Layout from './components/Layout';
import ScanlineOverlay from './components/ScanlineOverlay';
import ThemeProvider, { ThemeContext } from './contexts/ThemeContext';

// Import dark mode and CRT effect styles
import './styles/DarkMode.css';
import './styles/ScanlineOverlay.css';
import './styles/TextDistortion.css';

function AppContent() {
  const { darkMode } = useContext(ThemeContext);

  // Create a theme instance based on dark mode setting
  const theme = useMemo(() => createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      primary: {
        main: darkMode ? '#00ff00' : '#1976d2',
      },
      secondary: {
        main: darkMode ? '#00cc00' : '#dc004e',
      },
      background: {
        default: darkMode ? '#0a1a0a' : '#f5f5f5',
        paper: darkMode ? '#0c1f0c' : '#ffffff',
      },
      text: {
        primary: darkMode ? '#33ff33' : 'rgba(0, 0, 0, 0.87)',
        secondary: darkMode ? '#00cc00' : 'rgba(0, 0, 0, 0.6)',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            transition: 'background-color 0.3s ease, color 0.3s ease',
          },
        },
      },
    },
  }), [darkMode]);

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: 'flex' }}>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/containers" element={<ContainerManagement />} />
            <Route path="/pool" element={<PoolManagement />} />
            <Route path="/monitoring" element={<Monitoring />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
        <ScanlineOverlay />
      </Box>
    </MuiThemeProvider>
  );
}

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

export default App;

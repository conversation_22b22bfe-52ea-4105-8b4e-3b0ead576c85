/* Dark Mode with CRT Scanlines - Retro Green Terminal */

/* Base dark mode styles */
.dark-mode {
  --background-primary: #0a1a0a;
  --background-secondary: #0c1f0c;
  --background-tertiary: #102410;
  --text-primary: #33ff33;
  --text-secondary: #00cc00;
  --accent-color: #00ff00;
  --error-color: #ff5252;
  --success-color: #00ff00;
  --warning-color: #ffab40;
  --border-color: #1a3a1a;
  --card-background: #0c1f0c;
  --drawer-background: #0a1a0a;
  --appbar-background: #0a1a0a;
  --chart-grid-color: #1a3a1a;
  --divider-color: #1a3a1a;

  background-color: var(--background-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
  position: relative;
}

/* CRT effect container */
.crt-effect {
  position: relative;
  overflow: hidden;
}

/* CRT scanlines */
.crt-effect::before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    rgba(0, 255, 0, 0) 50%,
    rgba(0, 50, 0, 0.5) 50%
  );
  background-size: 100% 2px;
  z-index: 2000;
  pointer-events: none;
  opacity: 0.4;
}

/* CRT flicker animation */
.crt-effect::after {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 255, 0, 0.05) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  opacity: 0;
  z-index: 2001;
  pointer-events: none;
  animation: flicker 0.15s infinite;
}

@keyframes flicker {
  0% {
    opacity: 0.27861;
    transform: translateX(0);
  }
  5% {
    opacity: 0.34769;
  }
  10% {
    opacity: 0.23604;
    transform: translateX(0.5px);
  }
  15% {
    opacity: 0.90626;
  }
  20% {
    opacity: 0.18128;
    transform: translateX(-0.5px);
  }
  25% {
    opacity: 0.83891;
  }
  30% {
    opacity: 0.65583;
    transform: translateX(0);
  }
  35% {
    opacity: 0.67807;
  }
  40% {
    opacity: 0.26559;
    transform: translateX(0.3px);
  }
  45% {
    opacity: 0.84693;
  }
  50% {
    opacity: 0.96019;
    transform: translateX(-0.3px);
  }
  55% {
    opacity: 0.08594;
  }
  60% {
    opacity: 0.20313;
    transform: translateX(0);
  }
  65% {
    opacity: 0.71988;
  }
  70% {
    opacity: 0.53455;
    transform: translateX(0.2px);
  }
  75% {
    opacity: 0.37288;
  }
  80% {
    opacity: 0.71428;
    transform: translateX(-0.2px);
  }
  85% {
    opacity: 0.70419;
  }
  90% {
    opacity: 0.7003;
    transform: translateX(0);
  }
  95% {
    opacity: 0.36108;
  }
  100% {
    opacity: 0.24387;
    transform: translateX(0);
  }
}

/* Add horizontal scan effect */
@keyframes horizontalScan {
  0% {
    box-shadow: inset 0 0 0 transparent;
  }
  50% {
    box-shadow: inset 0 -10px 30px rgba(0, 255, 0, 0.1);
  }
  100% {
    box-shadow: inset 0 0 0 transparent;
  }
}

/* Apply horizontal scan to body */
.dark-mode.crt-effect {
  animation: horizontalScan 8s linear infinite;
}

/* CRT glow effect */
.crt-glow {
  text-shadow: 0 0 5px #00ff00, 0 0 10px #00ff00, 0 0 15px #00ff00;
}

/* Add green glow to all text in dark mode */
.dark-mode h1, .dark-mode h2, .dark-mode h3, .dark-mode h4, .dark-mode h5, .dark-mode h6,
.dark-mode .MuiTypography-root, .dark-mode .MuiButton-root, .dark-mode .MuiInputBase-root,
.dark-mode .MuiFormLabel-root, .dark-mode .MuiTab-root, .dark-mode .MuiListItemText-primary {
  text-shadow: 0 0 2px #00ff00;
}

/* Add stronger glow to headings */
.dark-mode h1, .dark-mode h2, .dark-mode h3,
.dark-mode .MuiTypography-h4, .dark-mode .MuiTypography-h5, .dark-mode .MuiTypography-h6 {
  text-shadow: 0 0 4px #00ff00, 0 0 8px rgba(0, 255, 0, 0.5);
}

/* CRT screen turn on/off animation */
.crt-on {
  animation: turn-on 0.8s ease-out;
}

.crt-off {
  animation: turn-off 0.4s ease-out;
}

@keyframes turn-on {
  0% {
    transform: scale(1, 0.8) translate3d(0, 0, 0);
    filter: brightness(30);
    opacity: 1;
  }
  3.5% {
    transform: scale(1, 0.8) translate3d(0, 100%, 0);
  }
  3.6% {
    transform: scale(1, 0.8) translate3d(0, -100%, 0);
    opacity: 1;
  }
  9% {
    transform: scale(1.3, 0.6) translate3d(0, 100%, 0);
    filter: brightness(30);
    opacity: 0;
  }
  11% {
    transform: scale(1, 1) translate3d(0, 0, 0);
    filter: contrast(0) brightness(0);
    opacity: 0;
  }
  100% {
    transform: scale(1, 1) translate3d(0, 0, 0);
    filter: contrast(1) brightness(1);
    opacity: 1;
  }
}

@keyframes turn-off {
  0% {
    transform: scale(1, 1) translate3d(0, 0, 0);
    filter: brightness(1);
    opacity: 1;
  }
  60% {
    transform: scale(1.3, 0.001) translate3d(0, 0, 0);
    filter: brightness(10);
  }
  100% {
    transform: scale(1.3, 0.001) translate3d(0, 0, 0);
    filter: brightness(0);
    opacity: 0;
  }
}

/* Dark mode overrides for Material-UI components - Retro Green Terminal */
.dark-mode .MuiPaper-root {
  background-color: var(--card-background);
  color: var(--text-primary);
  border: 1px solid rgba(0, 255, 0, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.1);
}

.dark-mode .MuiAppBar-root {
  background-color: var(--appbar-background);
  color: var(--text-primary);
  border-bottom: 1px solid rgba(0, 255, 0, 0.2);
  box-shadow: 0 2px 10px rgba(0, 255, 0, 0.1);
}

.dark-mode .MuiDrawer-paper {
  background-color: var(--drawer-background);
  color: var(--text-primary);
  border-right: 1px solid rgba(0, 255, 0, 0.2);
}

.dark-mode .MuiDivider-root {
  background-color: rgba(0, 255, 0, 0.2);
}

.dark-mode .MuiTableCell-root {
  color: var(--text-primary);
  border-bottom-color: rgba(0, 255, 0, 0.2);
}

.dark-mode .MuiTableHead-root .MuiTableCell-root {
  color: var(--text-secondary);
  font-weight: bold;
}

.dark-mode .MuiTableRow-root:hover {
  background-color: rgba(0, 255, 0, 0.05);
}

.dark-mode .MuiButton-contained {
  background-color: rgba(0, 255, 0, 0.2);
  color: var(--text-primary);
  border: 1px solid rgba(0, 255, 0, 0.3);
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.dark-mode .MuiButton-contained:hover {
  background-color: rgba(0, 255, 0, 0.3);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.dark-mode .MuiButton-outlined {
  border-color: rgba(0, 255, 0, 0.5);
  color: var(--text-primary);
}

.dark-mode .MuiButton-outlined:hover {
  border-color: var(--accent-color);
  background-color: rgba(0, 255, 0, 0.05);
}

.dark-mode .MuiButton-text {
  color: var(--text-primary);
}

.dark-mode .MuiButton-text:hover {
  background-color: rgba(0, 255, 0, 0.05);
}

.dark-mode .MuiInputBase-root {
  color: var(--text-primary);
}

.dark-mode .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 255, 0, 0.3);
}

.dark-mode .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 255, 0, 0.5);
}

.dark-mode .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: var(--accent-color);
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.dark-mode .MuiFormLabel-root {
  color: var(--text-secondary);
}

.dark-mode .MuiFormLabel-root.Mui-focused {
  color: var(--accent-color);
}

.dark-mode .MuiTab-root {
  color: var(--text-secondary);
}

.dark-mode .MuiTab-root.Mui-selected {
  color: var(--accent-color);
}

.dark-mode .MuiTabs-indicator {
  background-color: var(--accent-color);
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.dark-mode .MuiSwitch-switchBase.Mui-checked {
  color: var(--accent-color);
}

.dark-mode .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background-color: rgba(0, 255, 0, 0.5);
}

.dark-mode .MuiCircularProgress-root {
  color: var(--accent-color);
}

.dark-mode .MuiAlert-standardSuccess {
  background-color: rgba(0, 255, 0, 0.1);
  color: #00ff00;
}

.dark-mode .MuiAlert-standardError {
  background-color: rgba(255, 0, 0, 0.1);
  color: #ff5252;
}

.dark-mode .MuiAlert-standardWarning {
  background-color: rgba(255, 171, 64, 0.1);
  color: #ffab40;
}

.dark-mode .MuiAlert-standardInfo {
  background-color: rgba(0, 255, 255, 0.1);
  color: #00ffff;
}

/* Chart styles for dark mode */
.dark-mode .recharts-cartesian-grid-horizontal line,
.dark-mode .recharts-cartesian-grid-vertical line {
  stroke: rgba(0, 255, 0, 0.2);
}

.dark-mode .recharts-text {
  fill: var(--text-secondary);
  text-shadow: 0 0 2px rgba(0, 255, 0, 0.5);
}

.dark-mode .recharts-legend-item-text {
  color: var(--text-secondary);
}

.dark-mode .recharts-line-curve {
  stroke: #00ff00;
  filter: drop-shadow(0 0 2px rgba(0, 255, 0, 0.5));
}

.dark-mode .recharts-area-area {
  fill: rgba(0, 255, 0, 0.2);
}

.dark-mode .recharts-bar-rectangle {
  fill: rgba(0, 255, 0, 0.7);
}

.dark-mode .recharts-tooltip-wrapper {
  background-color: var(--background-secondary);
  border: 1px solid rgba(0, 255, 0, 0.3);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

/* Font styles */
.font-roboto {
  font-family: 'Roboto', sans-serif;
}

.font-light {
  font-family: 'Roboto Light', 'Roboto', sans-serif;
  font-weight: 300;
}

/* CRT intensity controls */
.crt-effect.intensity-low::before {
  opacity: 0.3;
  background-size: 100% 3px;
  background: linear-gradient(
    rgba(0, 255, 0, 0) 50%,
    rgba(0, 50, 0, 0.4) 50%
  );
}

.crt-effect.intensity-medium::before {
  opacity: 0.5;
  background-size: 100% 2px;
  background: linear-gradient(
    rgba(0, 255, 0, 0) 50%,
    rgba(0, 50, 0, 0.5) 50%
  );
}

.crt-effect.intensity-high::before {
  opacity: 0.7;
  background-size: 100% 2px;
  background: linear-gradient(
    rgba(0, 255, 0, 0) 50%,
    rgba(0, 50, 0, 0.6) 50%
  );
}

.crt-effect.intensity-low::after {
  animation: flicker 0.15s infinite;
  opacity: 0.05;
}

.crt-effect.intensity-medium::after {
  animation: flicker 0.1s infinite;
  opacity: 0.1;
}

.crt-effect.intensity-high::after {
  animation: flicker 0.05s infinite;
  opacity: 0.15;
}

/* Add green phosphor glow based on intensity */
.crt-effect.intensity-low .MuiTypography-root,
.crt-effect.intensity-low .MuiButton-root,
.crt-effect.intensity-low .MuiInputBase-root {
  text-shadow: 0 0 2px rgba(0, 255, 0, 0.5);
}

.crt-effect.intensity-medium .MuiTypography-root,
.crt-effect.intensity-medium .MuiButton-root,
.crt-effect.intensity-medium .MuiInputBase-root {
  text-shadow: 0 0 3px rgba(0, 255, 0, 0.7);
}

.crt-effect.intensity-high .MuiTypography-root,
.crt-effect.intensity-high .MuiButton-root,
.crt-effect.intensity-high .MuiInputBase-root {
  text-shadow: 0 0 5px rgba(0, 255, 0, 0.9), 0 0 10px rgba(0, 255, 0, 0.5);
}

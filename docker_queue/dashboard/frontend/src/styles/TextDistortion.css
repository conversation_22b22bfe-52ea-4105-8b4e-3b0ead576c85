/* Text Distortion Effects for CRT Terminal Look */

/* Apply text distortion to headings */
.dark-mode.crt-effect h1,
.dark-mode.crt-effect h2,
.dark-mode.crt-effect h3,
.dark-mode.crt-effect h4,
.dark-mode.crt-effect h5,
.dark-mode.crt-effect h6,
.dark-mode.crt-effect .MuiTypography-h1,
.dark-mode.crt-effect .MuiTypography-h2,
.dark-mode.crt-effect .MuiTypography-h3,
.dark-mode.crt-effect .MuiTypography-h4,
.dark-mode.crt-effect .MuiTypography-h5,
.dark-mode.crt-effect .MuiTypography-h6 {
  position: relative;
  display: inline-block;
  animation: textWarp 8s ease-in-out infinite;
  transform-origin: center center;
}

/* Apply subtle text distortion to body text - but not to logs or table content */
.dark-mode.crt-effect p:not(.MuiListItemText-primary):not(.MuiTableCell-root p),
.dark-mode.crt-effect .MuiTypography-body1:not(.MuiListItemText-primary):not(.MuiTableCell-root .MuiTypography-body1),
.dark-mode.crt-effect .MuiTypography-body2:not(.MuiListItemText-secondary):not(.MuiTableCell-root .MuiTypography-body2) {
  position: relative;
  animation: subtleTextWarp 12s ease-in-out infinite;
}

/* Text warp animation */
@keyframes textWarp {
  0% {
    transform: skew(0deg, 0deg);
    letter-spacing: normal;
  }
  25% {
    transform: skew(0.3deg, 0.1deg);
    letter-spacing: 0.02em;
  }
  50% {
    transform: skew(-0.3deg, 0deg);
    letter-spacing: -0.01em;
  }
  75% {
    transform: skew(0.2deg, -0.1deg);
    letter-spacing: 0.01em;
  }
  100% {
    transform: skew(0deg, 0deg);
    letter-spacing: normal;
  }
}

/* Subtle text warp animation */
@keyframes subtleTextWarp {
  0% {
    transform: skew(0deg, 0deg);
  }
  25% {
    transform: skew(0.1deg, 0deg);
  }
  50% {
    transform: skew(-0.1deg, 0deg);
  }
  75% {
    transform: skew(0.05deg, 0deg);
  }
  100% {
    transform: skew(0deg, 0deg);
  }
}

/* Text chromatic aberration effect */
.dark-mode.crt-effect.intensity-high h1::before,
.dark-mode.crt-effect.intensity-high h2::before,
.dark-mode.crt-effect.intensity-high h3::before,
.dark-mode.crt-effect.intensity-high .MuiTypography-h4::before,
.dark-mode.crt-effect.intensity-high .MuiTypography-h5::before,
.dark-mode.crt-effect.intensity-high .MuiTypography-h6::before {
  content: attr(data-text);
  position: absolute;
  left: -1px;
  top: 0;
  color: rgba(255, 0, 0, 0.3);
  overflow: hidden;
  animation: chromaticMove 6s ease-in-out infinite;
}

.dark-mode.crt-effect.intensity-high h1::after,
.dark-mode.crt-effect.intensity-high h2::after,
.dark-mode.crt-effect.intensity-high h3::after,
.dark-mode.crt-effect.intensity-high .MuiTypography-h4::after,
.dark-mode.crt-effect.intensity-high .MuiTypography-h5::after,
.dark-mode.crt-effect.intensity-high .MuiTypography-h6::after {
  content: attr(data-text);
  position: absolute;
  left: 1px;
  top: 0;
  color: rgba(0, 255, 255, 0.3);
  overflow: hidden;
  animation: chromaticMove 6s ease-in-out reverse infinite;
}

@keyframes chromaticMove {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0.5px);
  }
  50% {
    transform: translateX(-0.5px);
  }
  75% {
    transform: translateX(0.25px);
  }
  100% {
    transform: translateX(0);
  }
}

/* Add text shadow flicker */
.dark-mode.crt-effect .MuiTypography-root {
  animation: textShadowFlicker 10s infinite;
}

@keyframes textShadowFlicker {
  0% {
    text-shadow: 0 0 4px rgba(0, 255, 0, 0.6);
  }
  25% {
    text-shadow: 0 0 6px rgba(0, 255, 0, 0.8);
  }
  50% {
    text-shadow: 0 0 3px rgba(0, 255, 0, 0.4);
  }
  75% {
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.7);
  }
  100% {
    text-shadow: 0 0 4px rgba(0, 255, 0, 0.6);
  }
}

/* Add text jitter for high intensity */
.dark-mode.crt-effect.intensity-high .MuiTypography-root:not(.log-message):not(.log-timestamp) {
  animation: textJitter 0.1s infinite alternate;
}

@keyframes textJitter {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(0.5px);
  }
}

/* Terminal-style log entries */
.dark-mode .logs-container {
  font-family: 'Courier New', monospace;
  background-color: #0a1a0a !important;
  border: 1px solid rgba(0, 255, 0, 0.3);
}

.dark-mode .logs-container .MuiListItem-root {
  border-bottom: 1px solid rgba(0, 255, 0, 0.1);
}

.dark-mode .logs-container .MuiListItem-root:hover {
  background-color: rgba(0, 255, 0, 0.05);
}

.dark-mode .log-message {
  font-family: 'Courier New', monospace;
  font-weight: normal;
  letter-spacing: 0.05em;
  text-shadow: 0 0 2px rgba(0, 255, 0, 0.5);
}

.dark-mode .log-timestamp {
  font-family: 'Courier New', monospace;
  font-size: 0.8em;
  opacity: 0.7;
  color: #00cc00 !important;
}

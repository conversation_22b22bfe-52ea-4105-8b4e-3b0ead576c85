/* Additional Scanline Overlay for more pronounced effect */

.scanline-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0px,
    rgba(0, 0, 0, 0) 1px,
    rgba(0, 50, 0, 0.1) 1px,
    rgba(0, 50, 0, 0.15) 2px
  );
  pointer-events: none;
  z-index: 2002;
  opacity: 0.7;
}

/* Vertical distortion effect */
@keyframes verticalDistortion {
  0% {
    transform: scaleY(1.0) skewX(0deg);
  }
  25% {
    transform: scaleY(1.01) skewX(0.3deg);
  }
  50% {
    transform: scaleY(1.015) skewX(-0.3deg);
  }
  75% {
    transform: scaleY(1.01) skewX(0.3deg);
  }
  100% {
    transform: scaleY(1.0) skewX(0deg);
  }
}

.scanline-overlay.intensity-low {
  opacity: 0.3;
  animation: verticalDistortion 10s ease-in-out infinite;
}

.scanline-overlay.intensity-medium {
  opacity: 0.5;
  animation: verticalDistortion 8s ease-in-out infinite;
}

.scanline-overlay.intensity-high {
  opacity: 0.7;
  animation: verticalDistortion 6s ease-in-out infinite;
}

/* Occasional glitch effect */
@keyframes glitch {
  0% {
    transform: translateX(0) skewX(0deg);
    clip-path: none;
  }
  0.5% {
    transform: translateX(-5px) skewX(-1deg);
    clip-path: inset(10% 0 0 0);
  }
  1% {
    transform: translateX(5px) skewX(1deg);
    clip-path: inset(0 0 10% 0);
  }
  1.5% {
    transform: translateX(-3px) skewX(0deg);
    clip-path: none;
  }
  2% {
    transform: translateX(3px) skewX(0.5deg);
    clip-path: inset(0 10% 0 0);
  }
  2.5% {
    transform: translateX(0) skewX(-0.5deg);
    clip-path: inset(0 0 0 10%);
  }
  3% {
    transform: translateX(0) skewX(0deg);
    clip-path: none;
  }
  100% {
    transform: translateX(0) skewX(0deg);
    clip-path: none;
  }
}

.scanline-overlay.intensity-high {
  animation: verticalDistortion 6s ease-in-out infinite, glitch 10s step-end infinite;
}

/* Add warp effect to cards - but exclude system logs */
.dark-mode .MuiPaper-root:not(.system-logs) {
  animation: warpEffect 15s ease-in-out infinite;
  transform-origin: center center;
  will-change: transform;
  backface-visibility: hidden;
}

/* Different warp effects for different elements */
.dark-mode .MuiCard-root:nth-of-type(odd):not(.system-logs) {
  animation: warpEffect1 17s ease-in-out infinite;
}

.dark-mode .MuiCard-root:nth-of-type(even):not(.system-logs) {
  animation: warpEffect2 19s ease-in-out infinite;
}

/* Apply a more subtle warp to system logs */
.dark-mode .system-logs {
  animation: subtleWarpEffect 30s ease-in-out infinite;
}

.dark-mode .MuiTable-root {
  animation: warpEffect3 21s ease-in-out infinite;
}

.dark-mode .MuiDrawer-paper {
  animation: warpEffect4 25s ease-in-out infinite;
}

@keyframes warpEffect {
  0% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
  25% {
    transform: perspective(1000px) rotateX(0.3deg) rotateY(0.2deg) scale(1.002);
  }
  50% {
    transform: perspective(1000px) rotateX(-0.3deg) rotateY(0.4deg) scale(1.004);
  }
  75% {
    transform: perspective(1000px) rotateX(0.2deg) rotateY(-0.3deg) scale(1.002);
  }
  100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}

@keyframes warpEffect1 {
  0% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
  25% {
    transform: perspective(1000px) rotateX(0.4deg) rotateY(0.3deg) scale(1.003);
  }
  50% {
    transform: perspective(1000px) rotateX(-0.2deg) rotateY(0.5deg) scale(1.005);
  }
  75% {
    transform: perspective(1000px) rotateX(0.3deg) rotateY(-0.4deg) scale(1.003);
  }
  100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}

@keyframes warpEffect2 {
  0% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
  25% {
    transform: perspective(1000px) rotateX(-0.3deg) rotateY(-0.2deg) scale(1.002);
  }
  50% {
    transform: perspective(1000px) rotateX(0.4deg) rotateY(-0.3deg) scale(1.004);
  }
  75% {
    transform: perspective(1000px) rotateX(-0.2deg) rotateY(0.4deg) scale(1.002);
  }
  100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}

@keyframes warpEffect3 {
  0% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1) skewX(0deg);
  }
  25% {
    transform: perspective(1000px) rotateX(0.2deg) rotateY(0.1deg) scale(1.001) skewX(0.1deg);
  }
  50% {
    transform: perspective(1000px) rotateX(-0.2deg) rotateY(0.2deg) scale(1.002) skewX(-0.1deg);
  }
  75% {
    transform: perspective(1000px) rotateX(0.1deg) rotateY(-0.2deg) scale(1.001) skewX(0.1deg);
  }
  100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1) skewX(0deg);
  }
}

@keyframes warpEffect4 {
  0% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
  25% {
    transform: perspective(1000px) rotateX(0.1deg) rotateY(0.1deg) scale(1.001);
  }
  50% {
    transform: perspective(1000px) rotateX(-0.1deg) rotateY(0.2deg) scale(1.002);
  }
  75% {
    transform: perspective(1000px) rotateX(0.1deg) rotateY(-0.1deg) scale(1.001);
  }
  100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}

/* Add subtle bulge effect to the entire screen */
.dark-mode.crt-effect #root {
  animation: bulgeEffect 20s ease-in-out infinite;
}

@keyframes bulgeEffect {
  0% {
    transform: scale(1);
    border-radius: 0;
  }
  50% {
    transform: scale(1.01);
    border-radius: 10px;
  }
  100% {
    transform: scale(1);
    border-radius: 0;
  }
}

/* Subtle warp effect for system logs */
@keyframes subtleWarpEffect {
  0% {
    transform: perspective(2000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
  25% {
    transform: perspective(2000px) rotateX(0.1deg) rotateY(0.1deg) scale(1.0005);
  }
  50% {
    transform: perspective(2000px) rotateX(-0.1deg) rotateY(0.1deg) scale(1.001);
  }
  75% {
    transform: perspective(2000px) rotateX(0.05deg) rotateY(-0.1deg) scale(1.0005);
  }
  100% {
    transform: perspective(2000px) rotateX(0deg) rotateY(0deg) scale(1);
  }
}

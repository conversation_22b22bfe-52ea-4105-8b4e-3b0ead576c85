import React, { createContext, useState, useEffect } from 'react';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  // Initialize state from localStorage or use defaults
  const [darkMode, setDarkMode] = useState(() => {
    const savedDarkMode = localStorage.getItem('darkMode');
    return savedDarkMode ? JSON.parse(savedDarkMode) : false;
  });
  
  const [crtEffect, setCrtEffect] = useState(() => {
    const savedCrtEffect = localStorage.getItem('crtEffect');
    return savedCrtEffect ? JSON.parse(savedCrtEffect) : false;
  });
  
  const [crtIntensity, setCrtIntensity] = useState(() => {
    const savedCrtIntensity = localStorage.getItem('crtIntensity');
    return savedCrtIntensity || 'medium';
  });
  
  const [fontStyle, setFontStyle] = useState(() => {
    const savedFontStyle = localStorage.getItem('fontStyle');
    return savedFontStyle || 'roboto';
  });

  // Update localStorage when state changes
  useEffect(() => {
    localStorage.setItem('darkMode', JSON.stringify(darkMode));
    localStorage.setItem('crtEffect', JSON.stringify(crtEffect));
    localStorage.setItem('crtIntensity', crtIntensity);
    localStorage.setItem('fontStyle', fontStyle);
    
    // Apply classes to body
    if (darkMode) {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }
    
    if (crtEffect) {
      document.body.classList.add('crt-effect');
      document.body.classList.add(`intensity-${crtIntensity}`);
    } else {
      document.body.classList.remove('crt-effect');
      document.body.classList.remove('intensity-low');
      document.body.classList.remove('intensity-medium');
      document.body.classList.remove('intensity-high');
    }
    
    document.body.classList.remove('font-roboto', 'font-light');
    document.body.classList.add(`font-${fontStyle}`);
    
  }, [darkMode, crtEffect, crtIntensity, fontStyle]);

  // Toggle functions
  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };
  
  const toggleCrtEffect = () => {
    setCrtEffect(prevEffect => !prevEffect);
  };
  
  const changeCrtIntensity = (intensity) => {
    setCrtIntensity(intensity);
  };
  
  const changeFontStyle = (style) => {
    setFontStyle(style);
  };

  return (
    <ThemeContext.Provider
      value={{
        darkMode,
        toggleDarkMode,
        crtEffect,
        toggleCrtEffect,
        crtIntensity,
        changeCrtIntensity,
        fontStyle,
        changeFontStyle
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;

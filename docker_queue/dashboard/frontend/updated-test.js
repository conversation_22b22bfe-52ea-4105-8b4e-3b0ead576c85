const { chromium } = require('playwright');

(async () => {
  console.log('Starting updated dashboard test...');
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  // Mock API responses
  await page.route('**/api/pool/status', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        pool: { 
          total_containers: 10, 
          available_containers: 5, 
          assigned_containers: 5, 
          recycling_containers: 0 
        } 
      } 
    });
  });
  
  await page.route('**/api/metrics/current', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        metrics: { 
          timestamp: Date.now() / 1000,
          pool: {
            available_containers: 5,
            assigned_containers: 5,
            recycling_containers: 0,
            total_containers: 10
          },
          queue: { 
            length: 3, 
            avg_wait_time: 15.5,
            p95_wait_time: 30.2
          },
          containers: { 
            avg_cpu_usage: 50, 
            avg_memory_usage: 50,
            avg_network_rx: 1024,
            avg_network_tx: 2048
          }
        } 
      } 
    });
  });
  
  await page.route('**/api/health', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        health: { 
          total: 10, 
          healthy: 8, 
          unhealthy: 2, 
          unknown: 0,
          unhealthy_containers: [
            {
              container_id: 'unhealthy_container_1',
              docker_id: 'unhealthy_docker_1',
              status: 'exited',
              issue: 'Container exited with code 1'
            },
            {
              container_id: 'unhealthy_container_2',
              docker_id: 'unhealthy_docker_2',
              status: 'error',
              issue: 'Container error: out of memory'
            }
          ]
        } 
      } 
    });
  });
  
  // Mock containers API
  await page.route('**/api/containers', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        containers: [
          {
            container_id: 'container1',
            docker_id: 'docker1',
            name: 'container1',
            status: 'assigned',
            ip_address: '**********',
            user_id: 'user1',
            assigned_at: Date.now() - 3600000, // 1 hour ago
          },
          {
            container_id: 'container2',
            docker_id: 'docker2',
            name: 'container2',
            status: 'available',
            ip_address: '**********',
            user_id: null,
            assigned_at: null,
          }
        ]
      } 
    });
  });
  
  // Mock settings API
  await page.route('**/api/dashboard/settings', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        settings: {
          pool: { 
            min_size: 5, 
            max_size: 20, 
            target_size: 10, 
            min_available: 2, 
            auto_scaling: true, 
            scaling_interval: 60 
          },
          container: { 
            image: 'bahtbrowse:latest', 
            cpu_limit: 1.0, 
            memory_limit: '2g', 
            network: 'bahtbrowse', 
            timeout: 3600, 
            idle_timeout: 300 
          },
          queue: { 
            max_wait_time: 300, 
            default_priority: 5, 
            max_queue_size: 100 
          },
          system: { 
            metrics_retention_days: 7, 
            log_level: 'INFO', 
            admin_email: '<EMAIL>', 
            notifications_enabled: true 
          }
        }
      } 
    });
  });
  
  // Mock logs API
  await page.route('**/api/dashboard/logs', async (route) => {
    await route.fulfill({ 
      json: { 
        status: 'success', 
        logs: [
          {
            id: 1,
            timestamp: new Date(Date.now() - 60000).toISOString(),
            level: 'INFO',
            message: 'Container pool scaled to 10 containers'
          },
          {
            id: 2,
            timestamp: new Date(Date.now() - 120000).toISOString(),
            level: 'WARNING',
            message: 'Container unhealthy: container1'
          }
        ]
      } 
    });
  });
  
  console.log('Navigating to dashboard...');
  await page.goto('http://localhost:3000/');
  
  // Wait for dashboard to load
  console.log('Waiting for dashboard to load...');
  try {
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    console.log('✅ Test passed: Dashboard loaded successfully');
    
    // Check for charts using more generic selectors
    const charts = await page.locator('canvas').count();
    console.log(`Found ${charts} charts on the dashboard`);
    
    if (charts > 0) {
      console.log('✅ Test passed: Charts are displayed on the dashboard');
    } else {
      console.log('❌ Test failed: No charts found on the dashboard');
    }
    
    // Navigate to container management
    console.log('Navigating to container management...');
    await page.click('text=Containers');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    console.log('✅ Test passed: Container management loaded successfully');
    
    // Check for tables using more generic selectors
    const tables = await page.locator('table').count();
    console.log(`Found ${tables} tables on the container management page`);
    
    if (tables > 0) {
      console.log('✅ Test passed: Tables are displayed on the container management page');
    } else {
      console.log('❌ Test failed: No tables found on the container management page');
    }
    
    // Navigate to pool management
    console.log('Navigating to pool management...');
    await page.click('text=Pool');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    console.log('✅ Test passed: Pool management loaded successfully');
    
    // Check for charts
    const poolCharts = await page.locator('canvas').count();
    console.log(`Found ${poolCharts} charts on the pool management page`);
    
    // Navigate to monitoring
    console.log('Navigating to monitoring...');
    await page.click('text=Monitoring');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    console.log('✅ Test passed: Monitoring loaded successfully');
    
    // Check for charts
    const monitoringCharts = await page.locator('canvas').count();
    console.log(`Found ${monitoringCharts} charts on the monitoring page`);
    
    // Navigate to settings
    console.log('Navigating to settings...');
    await page.click('text=Settings');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    console.log('✅ Test passed: Settings loaded successfully');
    
    // Check for form elements
    const formElements = await page.locator('input, select, button').count();
    console.log(`Found ${formElements} form elements on the settings page`);
    
    if (formElements > 0) {
      console.log('✅ Test passed: Form elements are displayed on the settings page');
    } else {
      console.log('❌ Test failed: No form elements found on the settings page');
    }
    
  } catch (error) {
    console.log('❌ Test failed: Error during test execution');
    console.log(error);
    
    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'test-error.png' });
    console.log('Screenshot saved as test-error.png');
    
    // Get the page content
    const content = await page.content();
    console.log('Page content:');
    console.log(content.substring(0, 500) + '...');
  }
  
  await browser.close();
  console.log('Test completed.');
})();

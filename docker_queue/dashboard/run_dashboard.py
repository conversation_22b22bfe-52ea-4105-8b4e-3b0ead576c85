#!/usr/bin/env python3
"""
Run the Docker Queue Management Dashboard.
"""

import os
import sys
import logging
import argparse
from flask import Flask, send_from_directory
from docker_queue.dashboard.api import register_dashboard_api

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_app():
    """Create Flask app."""
    # Create Flask app
    app = Flask(__name__, static_folder='frontend/build')
    
    # Register API
    register_dashboard_api(app)
    
    # Serve React app
    @app.route('/', defaults={'path': ''})
    @app.route('/<path:path>')
    def serve(path):
        if path != "" and os.path.exists(app.static_folder + '/' + path):
            return send_from_directory(app.static_folder, path)
        else:
            return send_from_directory(app.static_folder, 'index.html')
    
    return app

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run Docker Queue Management Dashboard')
    
    parser.add_argument('--host', type=str, default='0.0.0.0',
                        help='Host to listen on (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000,
                        help='Port to listen on (default: 5000)')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug mode')
    
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Create app
    app = create_app()
    
    # Run app
    logger.info(f"Starting dashboard on {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == '__main__':
    main()

import { test, expect } from '@playwright/test';

// Define viewport sizes
const MO<PERSON>LE_VIEWPORT = { width: 375, height: 667 }; // iPhone SE
const TABLET_VIEWPORT = { width: 768, height: 1024 }; // iPad
const DESKTOP_VIEWPORT = { width: 1280, height: 800 }; // Standard desktop

test.describe('Mobile Responsiveness', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { 
        containers: { avg_cpu_usage: 50, avg_memory_usage: 50 },
        queue: { length: 3, avg_wait_time: 15.5 }
      } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 10, healthy: 8, unhealthy: 2, unknown: 0 } } });
    });
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize(MOBILE_VIEWPORT);
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if sidebar is collapsed on mobile
    const sidebar = await page.getByTestId('sidebar');
    const sidebarWidth = await sidebar.evaluate((el) => {
      const style = window.getComputedStyle(el);
      return parseInt(style.width);
    });
    
    // Sidebar should be collapsed or have a small width on mobile
    expect(sidebarWidth).toBeLessThan(100);
    
    // Check if charts are visible and properly sized
    const poolChart = await page.getByTestId('pool-chart');
    const healthChart = await page.getByTestId('health-chart');
    const resourceChart = await page.getByTestId('resource-chart');
    
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
    await expect(resourceChart).toBeVisible();
    
    // Navigate to container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Check if container table is responsive
    const containersTable = await page.getByTestId('containers-table');
    await expect(containersTable).toBeVisible();
    
    // Check if request container button is visible
    const requestButton = await page.getByTestId('request-container-button');
    await expect(requestButton).toBeVisible();
    
    // Navigate to monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Check if charts are visible and properly sized
    const cpuChart = await page.getByTestId('cpu-chart');
    await expect(cpuChart).toBeVisible();
    
    // Check if time range selector is visible
    const timeRangeSelect = await page.getByTestId('time-range-select');
    await expect(timeRangeSelect).toBeVisible();
  });

  test('should be responsive on tablet devices', async ({ page }) => {
    // Set viewport to tablet size
    await page.setViewportSize(TABLET_VIEWPORT);
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if sidebar is visible on tablet
    const sidebar = await page.getByTestId('sidebar');
    await expect(sidebar).toBeVisible();
    
    // Check if charts are visible and properly sized
    const poolChart = await page.getByTestId('pool-chart');
    const healthChart = await page.getByTestId('health-chart');
    const resourceChart = await page.getByTestId('resource-chart');
    
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
    await expect(resourceChart).toBeVisible();
    
    // Navigate to pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Check if pool status cards are arranged in a grid
    const totalContainers = await page.getByTestId('total-containers');
    const availableContainers = await page.getByTestId('available-containers');
    
    await expect(totalContainers).toBeVisible();
    await expect(availableContainers).toBeVisible();
    
    // Check if charts are visible and properly sized
    const poolSizeChart = await page.getByTestId('pool-size-chart');
    await expect(poolSizeChart).toBeVisible();
    
    // Navigate to settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if settings form is visible and properly sized
    const minSizeInput = await page.getByTestId('min-size-input');
    const maxSizeInput = await page.getByTestId('max-size-input');
    
    await expect(minSizeInput).toBeVisible();
    await expect(maxSizeInput).toBeVisible();
  });

  test('should be responsive on desktop devices', async ({ page }) => {
    // Set viewport to desktop size
    await page.setViewportSize(DESKTOP_VIEWPORT);
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if sidebar is expanded on desktop
    const sidebar = await page.getByTestId('sidebar');
    const sidebarWidth = await sidebar.evaluate((el) => {
      const style = window.getComputedStyle(el);
      return parseInt(style.width);
    });
    
    // Sidebar should be expanded on desktop
    expect(sidebarWidth).toBeGreaterThan(200);
    
    // Check if charts are visible and properly sized
    const poolChart = await page.getByTestId('pool-chart');
    const healthChart = await page.getByTestId('health-chart');
    const resourceChart = await page.getByTestId('resource-chart');
    
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
    await expect(resourceChart).toBeVisible();
    
    // Navigate to container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Check if container table is visible and properly sized
    const containersTable = await page.getByTestId('containers-table');
    await expect(containersTable).toBeVisible();
    
    // Check if all columns are visible
    const tableHeaders = await containersTable.locator('th');
    const headerCount = await tableHeaders.count();
    
    // Desktop should show more columns than mobile
    expect(headerCount).toBeGreaterThan(4);
  });

  test('should handle orientation changes', async ({ page }) => {
    // Set viewport to mobile landscape
    await page.setViewportSize({ width: 667, height: 375 }); // iPhone SE landscape
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if charts are visible and properly sized
    const poolChart = await page.getByTestId('pool-chart');
    const healthChart = await page.getByTestId('health-chart');
    
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
    
    // Change to portrait orientation
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE portrait
    
    // Check if charts are still visible and properly sized
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
  });

  test('should handle different screen densities', async ({ page }) => {
    // Set viewport to high-DPI display
    await page.setViewportSize({ width: 1440, height: 900 });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if charts are visible and properly sized
    const poolChart = await page.getByTestId('pool-chart');
    const healthChart = await page.getByTestId('health-chart');
    const resourceChart = await page.getByTestId('resource-chart');
    
    await expect(poolChart).toBeVisible();
    await expect(healthChart).toBeVisible();
    await expect(resourceChart).toBeVisible();
    
    // Check if text is readable
    const dashboardTitle = await page.getByTestId('dashboard-title');
    const fontSize = await dashboardTitle.evaluate((el) => {
      const style = window.getComputedStyle(el);
      return parseInt(style.fontSize);
    });
    
    // Font size should be reasonable
    expect(fontSize).toBeGreaterThan(10);
  });
});

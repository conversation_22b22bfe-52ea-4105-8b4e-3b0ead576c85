import { test, expect } from '@playwright/test';

test.describe('API Service', () => {
  test('should return pool status', async ({ request }) => {
    const response = await request.get('/api/pool/status');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.pool).toBeDefined();
    expect(data.pool.total_containers).toBeGreaterThanOrEqual(0);
    expect(data.pool.available_containers).toBeGreaterThanOrEqual(0);
    expect(data.pool.assigned_containers).toBeGreaterThanOrEqual(0);
  });

  test('should return current metrics', async ({ request }) => {
    const response = await request.get('/api/metrics/current');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.metrics).toBeDefined();
    expect(data.metrics.timestamp).toBeDefined();
    expect(data.metrics.pool).toBeDefined();
    expect(data.metrics.queue).toBeDefined();
    expect(data.metrics.containers).toBeDefined();
  });

  test('should return metrics history', async ({ request }) => {
    const endTime = Math.floor(Date.now() / 1000);
    const startTime = endTime - 3600; // 1 hour ago
    
    const response = await request.get(`/api/metrics/history?start_time=${startTime}&end_time=${endTime}`);
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.metrics).toBeDefined();
    expect(Array.isArray(data.metrics)).toBeTruthy();
    
    if (data.metrics.length > 0) {
      const metric = data.metrics[0];
      expect(metric.timestamp).toBeDefined();
      expect(metric.pool).toBeDefined();
      expect(metric.queue).toBeDefined();
      expect(metric.containers).toBeDefined();
    }
  });

  test('should return health status', async ({ request }) => {
    const response = await request.get('/api/health');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.health).toBeDefined();
    expect(data.health.total).toBeGreaterThanOrEqual(0);
    expect(data.health.healthy).toBeGreaterThanOrEqual(0);
    expect(data.health.unhealthy).toBeGreaterThanOrEqual(0);
  });

  test('should return usage report', async ({ request }) => {
    const response = await request.get('/api/report');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.report).toBeDefined();
    expect(data.report.timestamp).toBeDefined();
    expect(data.report.period).toBeDefined();
    expect(data.report.pool_usage).toBeDefined();
    expect(data.report.queue_usage).toBeDefined();
  });

  test('should request a container', async ({ request }) => {
    const response = await request.post('/api/containers/request', {
      data: {
        user_id: 'test_user',
        priority: 5
      }
    });
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.container).toBeDefined();
    expect(data.container.container_id).toBeDefined();
    expect(data.container.status).toBe('assigned');
  });

  test('should release a container', async ({ request }) => {
    const response = await request.post('/api/containers/release', {
      data: {
        user_id: 'test_user'
      }
    });
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.message).toBeDefined();
  });

  test('should scale the pool', async ({ request }) => {
    const response = await request.post('/api/pool/scale', {
      data: {
        target_size: 20
      }
    });
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data.status).toBe('success');
    expect(data.scaling).toBeDefined();
    expect(data.scaling.action).toBeDefined();
    expect(data.scaling.from).toBeDefined();
    expect(data.scaling.to).toBe(20);
  });

  test('should handle error when requesting container without user ID', async ({ request }) => {
    const response = await request.post('/api/containers/request', {
      data: {
        priority: 5
      }
    });
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.status).toBe('error');
    expect(data.message).toBeDefined();
  });

  test('should handle error when scaling pool without target size', async ({ request }) => {
    const response = await request.post('/api/pool/scale', {
      data: {}
    });
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.status).toBe('error');
    expect(data.message).toBeDefined();
  });
});

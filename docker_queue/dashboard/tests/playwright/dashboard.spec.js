import { test, expect } from '@playwright/test';

// Mock API responses
const mockPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 5,
    assigned_containers: 10,
    recycling_containers: 2,
    total_containers: 17,
    queue_length: 3
  }
};

const mockMetrics = {
  status: 'success',
  metrics: {
    timestamp: **********.789,
    pool: {
      available_containers: 5,
      assigned_containers: 10,
      recycling_containers: 2,
      total_containers: 17
    },
    queue: {
      length: 3,
      avg_wait_time: 15.5,
      p95_wait_time: 30.2
    },
    containers: {
      avg_cpu_usage: 25.5,
      avg_memory_usage: 45.2,
      avg_network_rx: 1024,
      avg_network_tx: 2048
    }
  }
};

const mockHealth = {
  status: 'success',
  health: {
    total: 17,
    healthy: 15,
    unhealthy: 1,
    unknown: 1
  }
};

test.describe('Dashboard Component', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: mockPoolStatus });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: mockMetrics });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: mockHealth });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
  });

  test('should display the dashboard with correct title and description', async ({ page }) => {
    const dashboardContainer = await page.getByTestId('dashboard-container');
    await expect(dashboardContainer).toBeVisible();
    
    const title = await page.getByTestId('dashboard-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('System Overview');
    
    const description = await page.getByTestId('dashboard-description');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Welcome to the BahtBrowse Docker Queue Management Dashboard');
  });

  test('should display container pool chart with correct data', async ({ page }) => {
    const poolChart = await page.getByTestId('pool-chart');
    await expect(poolChart).toBeVisible();
    
    // Check if the chart contains the correct data
    // This is a basic check since we can't easily verify the chart data directly
    const chartCanvas = await poolChart.locator('canvas');
    await expect(chartCanvas).toBeVisible();
    
    // Check if the view button is present
    const viewButton = await page.getByTestId('view-pool-button');
    await expect(viewButton).toBeVisible();
    await expect(viewButton).toHaveText('View Pool Details');
  });

  test('should display container health chart with correct data', async ({ page }) => {
    const healthChart = await page.getByTestId('health-chart');
    await expect(healthChart).toBeVisible();
    
    // Check if the chart contains the correct data
    const chartCanvas = await healthChart.locator('canvas');
    await expect(chartCanvas).toBeVisible();
    
    // Check if the view button is present
    const viewButton = await page.getByTestId('view-health-button');
    await expect(viewButton).toBeVisible();
    await expect(viewButton).toHaveText('View Health Details');
  });

  test('should display queue status with correct data', async ({ page }) => {
    const queueLength = await page.getByTestId('queue-length');
    await expect(queueLength).toBeVisible();
    await expect(queueLength).toHaveText('3');
    
    const avgWaitTime = await page.getByTestId('avg-wait-time');
    await expect(avgWaitTime).toBeVisible();
    await expect(avgWaitTime).toContainText('Average Wait Time: 15.5 seconds');
    
    // Check if the view button is present
    const viewButton = await page.getByTestId('view-queue-button');
    await expect(viewButton).toBeVisible();
    await expect(viewButton).toHaveText('View Queue Details');
  });

  test('should display resource usage chart with correct data', async ({ page }) => {
    const resourceChart = await page.getByTestId('resource-chart');
    await expect(resourceChart).toBeVisible();
    
    // Check if the chart contains the correct data
    const chartCanvas = await resourceChart.locator('canvas');
    await expect(chartCanvas).toBeVisible();
    
    // Check if the view button is present
    const viewButton = await page.getByTestId('view-resources-button');
    await expect(viewButton).toBeVisible();
    await expect(viewButton).toHaveText('View Resource Details');
  });

  test('should navigate to pool management page when view pool button is clicked', async ({ page }) => {
    const viewButton = await page.getByTestId('view-pool-button');
    await viewButton.click();
    
    // URL should change to /pool
    await expect(page).toHaveURL(/\/pool$/);
  });

  test('should navigate to monitoring page when view health button is clicked', async ({ page }) => {
    const viewButton = await page.getByTestId('view-health-button');
    await viewButton.click();
    
    // URL should change to /monitoring
    await expect(page).toHaveURL(/\/monitoring$/);
  });

  test('should navigate to containers page when view queue button is clicked', async ({ page }) => {
    const viewButton = await page.getByTestId('view-queue-button');
    await viewButton.click();
    
    // URL should change to /containers
    await expect(page).toHaveURL(/\/containers$/);
  });

  test('should navigate to monitoring page when view resources button is clicked', async ({ page }) => {
    const viewButton = await page.getByTestId('view-resources-button');
    await viewButton.click();
    
    // URL should change to /monitoring
    await expect(page).toHaveURL(/\/monitoring$/);
  });

  test('should display loading indicator when data is being fetched', async ({ page }) => {
    // Navigate to the page again to trigger loading state
    await page.route('**/api/pool/status', async (route) => {
      // Delay the response to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({ json: mockPoolStatus });
    });
    
    await page.goto('/');
    
    // Loading indicator should be visible initially
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
  });

  test('should display error message when API request fails', async ({ page }) => {
    // Mock API error
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ status: 500 });
    });
    
    await page.goto('/');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load dashboard data');
  });
});

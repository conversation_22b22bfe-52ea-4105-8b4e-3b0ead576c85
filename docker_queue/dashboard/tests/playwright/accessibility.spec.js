import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { 
        containers: { avg_cpu_usage: 50, avg_memory_usage: 50 },
        queue: { length: 3, avg_wait_time: 15.5 }
      } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 10, healthy: 8, unhealthy: 2, unknown: 0 } } });
    });
  });

  test('dashboard should be accessible', async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('container management should be accessible', async ({ page }) => {
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Open request container dialog
    const requestButton = await page.getByTestId('request-container-button');
    await requestButton.click();
    
    // Wait for dialog to open
    await page.waitForSelector('[data-testid="request-container-dialog"]');
    
    // Run accessibility tests on dialog
    const dialogAccessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(dialogAccessibilityScanResults.violations).toEqual([]);
  });

  test('pool management should be accessible', async ({ page }) => {
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Open scale pool dialog
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Wait for dialog to open
    await page.waitForSelector('[data-testid="scale-pool-dialog"]');
    
    // Run accessibility tests on dialog
    const dialogAccessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(dialogAccessibilityScanResults.violations).toEqual([]);
  });

  test('monitoring should be accessible', async ({ page }) => {
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Switch to container health tab
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await containerHealthTab.click();
    
    // Run accessibility tests on container health tab
    const containerHealthAccessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(containerHealthAccessibilityScanResults.violations).toEqual([]);
    
    // Switch to usage report tab
    const usageReportTab = await page.getByTestId('usage-report-tab');
    await usageReportTab.click();
    
    // Run accessibility tests on usage report tab
    const usageReportAccessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(usageReportAccessibilityScanResults.violations).toEqual([]);
  });

  test('settings should be accessible', async ({ page }) => {
    // Mock settings API
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: { status: 'success', settings: {
        pool: { min_size: 5, max_size: 20, target_size: 10, min_available: 2, auto_scaling: true, scaling_interval: 60 },
        container: { image: 'bahtbrowse:latest', cpu_limit: 1.0, memory_limit: '2g', network: 'bahtbrowse', timeout: 3600, idle_timeout: 300 },
        queue: { max_wait_time: 300, default_priority: 5, max_queue_size: 100 },
        system: { metrics_retention_days: 7, log_level: 'INFO', admin_email: '<EMAIL>', notifications_enabled: true }
      } } });
    });
    
    // Mock logs API
    await page.route('**/api/dashboard/logs', async (route) => {
      await route.fulfill({ json: { status: 'success', logs: [
        {
          id: 1,
          timestamp: new Date(Date.now() - 60000).toISOString(),
          level: 'INFO',
          message: 'Container pool scaled to 15 containers'
        }
      ] } });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Open reset dialog
    const resetButton = await page.getByTestId('reset-settings-button');
    
    // Change a setting to enable reset button
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    await resetButton.click();
    
    // Wait for dialog to open
    await page.waitForSelector('[data-testid="reset-dialog"]');
    
    // Run accessibility tests on dialog
    const dialogAccessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for violations
    expect(dialogAccessibilityScanResults.violations).toEqual([]);
  });

  test('keyboard navigation should work', async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Focus on first button
    await page.keyboard.press('Tab');
    
    // Check if focus is visible
    const focusedElement = await page.evaluate(() => {
      const activeElement = document.activeElement;
      return activeElement ? activeElement.tagName : null;
    });
    
    expect(focusedElement).not.toBeNull();
    
    // Navigate to container management using keyboard
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    // Check if we navigated to container management
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Focus on request container button
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Press Enter to open dialog
    await page.keyboard.press('Enter');
    
    // Check if dialog opened
    await page.waitForSelector('[data-testid="request-container-dialog"]');
    
    // Close dialog with Escape key
    await page.keyboard.press('Escape');
    
    // Check if dialog closed
    await expect(page.getByTestId('request-container-dialog')).not.toBeVisible();
  });

  test('screen reader support', async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if elements have proper ARIA attributes
    
    // App bar should have role="banner"
    const appBar = await page.getByTestId('app-bar');
    await expect(appBar).toHaveAttribute('role', 'banner');
    
    // Sidebar should have role="navigation"
    const sidebar = await page.getByTestId('sidebar');
    await expect(sidebar).toHaveAttribute('role', 'navigation');
    
    // Charts should have aria-label
    const poolChart = await page.getByTestId('pool-chart');
    await expect(poolChart).toHaveAttribute('aria-label');
    
    // Navigate to container management
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Table should have proper ARIA attributes
    const containersTable = await page.getByTestId('containers-table');
    await expect(containersTable).toHaveAttribute('role', 'grid');
    
    // Tabs should have proper ARIA attributes
    const containerTabs = await page.getByTestId('container-tabs');
    await expect(containerTabs).toHaveAttribute('role', 'tablist');
  });

  test('color contrast should be sufficient', async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Run accessibility tests with focus on color contrast
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withRules(['color-contrast'])
      .analyze();
    
    // Check for violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });
});

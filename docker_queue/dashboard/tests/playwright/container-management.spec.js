import { test, expect } from '@playwright/test';

// Mock API responses
const mockPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 5,
    assigned_containers: 10,
    recycling_containers: 2,
    total_containers: 17,
    queue_length: 3
  }
};

const mockRequestSuccess = {
  status: 'success',
  message: 'Container assigned successfully',
  container: {
    container_id: 'container4',
    docker_id: 'docker4',
    name: 'container_4',
    status: 'assigned',
    ip_address: '**********'
  }
};

const mockRequestPending = {
  status: 'pending',
  message: 'Container request queued',
  request_id: 'request3',
  queue_position: 3,
  queue_length: 3,
  estimated_wait_seconds: 15
};

const mockReleaseSuccess = {
  status: 'success',
  message: 'Container released successfully'
};

test.describe('Container Management Component', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: mockPoolStatus });
    });
    
    // Navigate to the container management page
    await page.goto('/containers');
  });

  test('should display the container management page with correct title and description', async ({ page }) => {
    const containerManagement = await page.getByTestId('container-management');
    await expect(containerManagement).toBeVisible();
    
    const title = await page.getByTestId('container-management-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('Container Management');
    
    const description = await page.getByTestId('container-management-description');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Manage Docker containers and container requests');
  });

  test('should display container pool status with correct data', async ({ page }) => {
    const totalContainers = await page.getByTestId('total-containers');
    await expect(totalContainers).toBeVisible();
    await expect(totalContainers).toHaveText('17');
    
    const availableContainers = await page.getByTestId('available-containers');
    await expect(availableContainers).toBeVisible();
    await expect(availableContainers).toHaveText('5');
    
    const assignedContainers = await page.getByTestId('assigned-containers');
    await expect(assignedContainers).toBeVisible();
    await expect(assignedContainers).toHaveText('10');
    
    const recyclingContainers = await page.getByTestId('recycling-containers');
    await expect(recyclingContainers).toBeVisible();
    await expect(recyclingContainers).toHaveText('2');
  });

  test('should display containers tab with container table', async ({ page }) => {
    const containersTab = await page.getByTestId('containers-tab');
    await expect(containersTab).toBeVisible();
    
    // Containers tab should be selected by default
    await expect(containersTab).toHaveAttribute('aria-selected', 'true');
    
    const containersTable = await page.getByTestId('containers-table');
    await expect(containersTable).toBeVisible();
    
    // Check if container rows are displayed
    const containerRow1 = await page.getByTestId('container-row-container1');
    const containerRow2 = await page.getByTestId('container-row-container2');
    const containerRow3 = await page.getByTestId('container-row-container3');
    
    await expect(containerRow1).toBeVisible();
    await expect(containerRow2).toBeVisible();
    await expect(containerRow3).toBeVisible();
  });

  test('should switch to queue tab when clicked', async ({ page }) => {
    const queueTab = await page.getByTestId('queue-tab');
    await expect(queueTab).toBeVisible();
    
    // Click on queue tab
    await queueTab.click();
    
    // Queue tab should be selected
    await expect(queueTab).toHaveAttribute('aria-selected', 'true');
    
    // Queue table should be visible
    const queueTable = await page.getByTestId('queue-table');
    await expect(queueTable).toBeVisible();
    
    // Check if request rows are displayed
    const requestRow1 = await page.getByTestId('request-row-request1');
    const requestRow2 = await page.getByTestId('request-row-request2');
    
    await expect(requestRow1).toBeVisible();
    await expect(requestRow2).toBeVisible();
  });

  test('should open request container dialog when request button is clicked', async ({ page }) => {
    const requestButton = await page.getByTestId('request-container-button');
    await expect(requestButton).toBeVisible();
    
    // Click on request button
    await requestButton.click();
    
    // Request dialog should be visible
    const requestDialog = await page.getByTestId('request-container-dialog');
    await expect(requestDialog).toBeVisible();
    
    // Check if dialog contains the correct elements
    const userIdInput = await page.getByTestId('user-id-input');
    const priorityInput = await page.getByTestId('priority-input');
    const cancelButton = await page.getByTestId('cancel-request-button');
    const submitButton = await page.getByTestId('submit-request-button');
    
    await expect(userIdInput).toBeVisible();
    await expect(priorityInput).toBeVisible();
    await expect(cancelButton).toBeVisible();
    await expect(submitButton).toBeVisible();
  });

  test('should submit container request when form is filled and submitted', async ({ page }) => {
    // Mock request container API
    await page.route('**/api/containers/request', async (route) => {
      const requestBody = JSON.parse(route.request().postData());
      
      // Return success or pending based on user ID
      if (requestBody.user_id === 'test_user') {
        await route.fulfill({ json: mockRequestSuccess });
      } else {
        await route.fulfill({ json: mockRequestPending });
      }
    });
    
    // Open request dialog
    const requestButton = await page.getByTestId('request-container-button');
    await requestButton.click();
    
    // Fill form
    const userIdInput = await page.getByTestId('user-id-input');
    const priorityInput = await page.getByTestId('priority-input');
    const submitButton = await page.getByTestId('submit-request-button');
    
    await userIdInput.fill('test_user');
    await priorityInput.fill('7');
    
    // Submit form
    await submitButton.click();
    
    // Dialog should be closed
    const requestDialog = await page.getByTestId('request-container-dialog');
    await expect(requestDialog).not.toBeVisible();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Container assigned successfully');
  });

  test('should display container details when view button is clicked', async ({ page }) => {
    // Click on view button for container1
    const viewButton = await page.getByTestId('view-container-container1');
    await viewButton.click();
    
    // Container details dialog should be visible
    const detailsDialog = await page.getByTestId('container-details-dialog');
    await expect(detailsDialog).toBeVisible();
    
    // Check if dialog contains the correct elements
    await expect(detailsDialog).toContainText('Container Details');
    await expect(detailsDialog).toContainText('container1');
    await expect(detailsDialog).toContainText('docker1');
    await expect(detailsDialog).toContainText('container_1');
    await expect(detailsDialog).toContainText('assigned');
    await expect(detailsDialog).toContainText('172.17.0.2');
    await expect(detailsDialog).toContainText('user1');
    
    // Close button should be visible
    const closeButton = await page.getByTestId('close-container-details-button');
    await expect(closeButton).toBeVisible();
    
    // Release button should be visible for assigned container
    const releaseButton = await page.getByTestId('release-container-details-button');
    await expect(releaseButton).toBeVisible();
  });

  test('should release container when release button is clicked', async ({ page }) => {
    // Mock release container API
    await page.route('**/api/containers/release', async (route) => {
      await route.fulfill({ json: mockReleaseSuccess });
    });
    
    // Click on release button for container1
    const releaseButton = await page.getByTestId('release-container-container1');
    await releaseButton.click();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Container released successfully');
  });

  test('should display loading indicator when data is being fetched', async ({ page }) => {
    // Navigate to the page again with delayed response to show loading state
    await page.route('**/api/pool/status', async (route) => {
      // Delay the response to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({ json: mockPoolStatus });
    });
    
    await page.goto('/containers');
    
    // Loading indicator should be visible initially
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
  });

  test('should display error message when API request fails', async ({ page }) => {
    // Mock API error
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ status: 500 });
    });
    
    await page.goto('/containers');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load container data');
  });
});

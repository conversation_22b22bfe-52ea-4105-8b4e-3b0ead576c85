import { test, expect } from '@playwright/test';

// Mock API responses
const mockMetrics = {
  status: 'success',
  metrics: {
    timestamp: **********.789,
    pool: {
      available_containers: 5,
      assigned_containers: 10,
      recycling_containers: 2,
      total_containers: 17
    },
    queue: {
      length: 3,
      avg_wait_time: 15.5,
      p95_wait_time: 30.2
    },
    containers: {
      avg_cpu_usage: 25.5,
      avg_memory_usage: 45.2,
      avg_network_rx: 1024,
      avg_network_tx: 2048
    }
  }
};

const mockMetricsHistory = {
  status: 'success',
  metrics: [
    {
      timestamp: **********.789,
      pool: {
        available_containers: 4,
        assigned_containers: 11,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 4
      },
      containers: {
        avg_cpu_usage: 30.5,
        avg_memory_usage: 50.2,
        avg_network_rx: 1500,
        avg_network_tx: 2500
      }
    },
    {
      timestamp: 1619123256.789,
      pool: {
        available_containers: 5,
        assigned_containers: 10,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 3
      },
      containers: {
        avg_cpu_usage: 25.5,
        avg_memory_usage: 45.2,
        avg_network_rx: 1024,
        avg_network_tx: 2048
      }
    },
    {
      timestamp: **********.789,
      pool: {
        available_containers: 6,
        assigned_containers: 9,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 2
      },
      containers: {
        avg_cpu_usage: 20.5,
        avg_memory_usage: 40.2,
        avg_network_rx: 900,
        avg_network_tx: 1800
      }
    }
  ],
  period: {
    start_time: **********.789,
    end_time: **********.789
  }
};

const mockHealth = {
  status: 'success',
  health: {
    total: 17,
    healthy: 15,
    unhealthy: 1,
    unknown: 1,
    unhealthy_containers: [
      {
        container_id: 'unhealthy_container_id',
        docker_id: 'unhealthy_docker_id',
        status: 'exited',
        issue: 'Container exited with code 1'
      }
    ]
  }
};

const mockReport = {
  status: 'success',
  report: {
    timestamp: **********.789,
    period: {
      start: **********.789,
      end: **********.789,
      duration_hours: 24
    },
    pool_usage: {
      avg_total_containers: 15.5,
      avg_available_containers: 5.2,
      avg_assigned_containers: 9.8,
      max_total_containers: 20,
      min_available_containers: 2,
      max_assigned_containers: 15
    },
    queue_usage: {
      avg_queue_length: 2.3,
      max_queue_length: 8,
      avg_wait_time: 12.7,
      max_wait_time: 45.2
    }
  }
};

test.describe('Monitoring Component', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: mockMetrics });
    });
    
    await page.route('**/api/metrics/history', async (route) => {
      await route.fulfill({ json: mockMetricsHistory });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: mockHealth });
    });
    
    await page.route('**/api/report', async (route) => {
      await route.fulfill({ json: mockReport });
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
  });

  test('should display the monitoring page with correct title and description', async ({ page }) => {
    const monitoring = await page.getByTestId('monitoring');
    await expect(monitoring).toBeVisible();
    
    const title = await page.getByTestId('monitoring-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('System Monitoring');
    
    const description = await page.getByTestId('monitoring-description');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Monitor system performance, health, and resource usage');
  });

  test('should display system health cards with correct data', async ({ page }) => {
    const healthyContainers = await page.getByTestId('healthy-containers');
    await expect(healthyContainers).toBeVisible();
    await expect(healthyContainers).toHaveText('15');
    
    const unhealthyContainers = await page.getByTestId('unhealthy-containers');
    await expect(unhealthyContainers).toBeVisible();
    await expect(unhealthyContainers).toContainText('1 unhealthy');
    
    const unknownContainers = await page.getByTestId('unknown-containers');
    await expect(unknownContainers).toBeVisible();
    await expect(unknownContainers).toContainText('1 unknown');
    
    const cpuUsage = await page.getByTestId('cpu-usage');
    await expect(cpuUsage).toBeVisible();
    await expect(cpuUsage).toHaveText('26%');
    
    const memoryUsage = await page.getByTestId('memory-usage');
    await expect(memoryUsage).toBeVisible();
    await expect(memoryUsage).toHaveText('45%');
  });

  test('should display resource usage tab with charts', async ({ page }) => {
    const resourceUsageTab = await page.getByTestId('resource-usage-tab');
    await expect(resourceUsageTab).toBeVisible();
    
    // Resource usage tab should be selected by default
    await expect(resourceUsageTab).toHaveAttribute('aria-selected', 'true');
    
    // Check if charts are displayed
    const cpuChart = await page.getByTestId('cpu-chart');
    const memoryChart = await page.getByTestId('memory-chart');
    const networkChart = await page.getByTestId('network-chart');
    
    await expect(cpuChart).toBeVisible();
    await expect(memoryChart).toBeVisible();
    await expect(networkChart).toBeVisible();
    
    // Check if charts contain canvas elements
    await expect(cpuChart.locator('canvas')).toBeVisible();
    await expect(memoryChart.locator('canvas')).toBeVisible();
    await expect(networkChart.locator('canvas')).toBeVisible();
  });

  test('should switch to container health tab when clicked', async ({ page }) => {
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await expect(containerHealthTab).toBeVisible();
    
    // Click on container health tab
    await containerHealthTab.click();
    
    // Container health tab should be selected
    await expect(containerHealthTab).toHaveAttribute('aria-selected', 'true');
    
    // Check if health chart is displayed
    const healthChart = await page.getByTestId('health-chart');
    await expect(healthChart).toBeVisible();
    await expect(healthChart.locator('canvas')).toBeVisible();
    
    // Check if unhealthy containers table is displayed
    const unhealthyContainersTable = await page.getByTestId('unhealthy-containers-table');
    await expect(unhealthyContainersTable).toBeVisible();
    
    // Check if unhealthy container row is displayed
    const unhealthyContainerRow = await page.getByTestId('unhealthy-container-unhealthy_container_id');
    await expect(unhealthyContainerRow).toBeVisible();
    await expect(unhealthyContainerRow).toContainText('Container exited with code 1');
  });

  test('should switch to usage report tab when clicked', async ({ page }) => {
    const usageReportTab = await page.getByTestId('usage-report-tab');
    await expect(usageReportTab).toBeVisible();
    
    // Click on usage report tab
    await usageReportTab.click();
    
    // Usage report tab should be selected
    await expect(usageReportTab).toHaveAttribute('aria-selected', 'true');
    
    // Check if pool usage table is displayed
    const poolUsageTable = await page.getByTestId('pool-usage-table');
    await expect(poolUsageTable).toBeVisible();
    
    // Check if queue usage table is displayed
    const queueUsageTable = await page.getByTestId('queue-usage-table');
    await expect(queueUsageTable).toBeVisible();
    
    // Check if tables contain correct data
    await expect(poolUsageTable).toContainText('Average Total Containers15.5');
    await expect(poolUsageTable).toContainText('Maximum Assigned Containers15');
    await expect(queueUsageTable).toContainText('Average Queue Length2.3');
    await expect(queueUsageTable).toContainText('Maximum Wait Time45.2 seconds');
  });

  test('should change time range when dropdown is changed', async ({ page }) => {
    // Mock API response for different time range
    await page.route('**/api/metrics/history*', async (route) => {
      const url = route.request().url();
      if (url.includes('start_time=')) {
        await route.fulfill({ json: mockMetricsHistory });
      }
    });
    
    const timeRangeSelect = await page.getByTestId('time-range-select');
    await expect(timeRangeSelect).toBeVisible();
    
    // Open dropdown
    await timeRangeSelect.click();
    
    // Select 24 hours option
    await page.getByRole('option', { name: 'Last 24 Hours' }).click();
    
    // Time range should be updated
    await expect(timeRangeSelect).toHaveValue('24h');
  });

  test('should refresh data when refresh button is clicked', async ({ page }) => {
    // Mock API responses with updated data
    const updatedMetrics = {
      ...mockMetrics,
      metrics: {
        ...mockMetrics.metrics,
        containers: {
          ...mockMetrics.metrics.containers,
          avg_cpu_usage: 30.0
        }
      }
    };
    
    // Set up route handler that returns updated data on second call
    let callCount = 0;
    await page.route('**/api/metrics/current', async (route) => {
      callCount++;
      if (callCount > 1) {
        await route.fulfill({ json: updatedMetrics });
      } else {
        await route.fulfill({ json: mockMetrics });
      }
    });
    
    const refreshButton = await page.getByTestId('refresh-button');
    await expect(refreshButton).toBeVisible();
    
    // Initial CPU usage should be 26%
    const cpuUsage = await page.getByTestId('cpu-usage');
    await expect(cpuUsage).toHaveText('26%');
    
    // Click refresh button
    await refreshButton.click();
    
    // CPU usage should be updated to 30%
    await expect(cpuUsage).toHaveText('30%');
  });

  test('should display loading indicator when data is being fetched', async ({ page }) => {
    // Navigate to the page again with delayed response to show loading state
    await page.route('**/api/metrics/current', async (route) => {
      // Delay the response to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({ json: mockMetrics });
    });
    
    await page.goto('/monitoring');
    
    // Loading indicator should be visible initially
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
  });

  test('should display error message when API request fails', async ({ page }) => {
    // Mock API error
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ status: 500 });
    });
    
    await page.goto('/monitoring');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load monitoring data');
  });
});

import { test, expect } from '@playwright/test';

// Mock API responses for edge cases
const emptyMetrics = {
  status: 'success',
  metrics: {
    timestamp: **********.789,
    pool: {
      available_containers: 0,
      assigned_containers: 0,
      recycling_containers: 0,
      total_containers: 0
    },
    queue: {
      length: 0,
      avg_wait_time: 0,
      p95_wait_time: 0
    },
    containers: {
      avg_cpu_usage: 0,
      avg_memory_usage: 0,
      avg_network_rx: 0,
      avg_network_tx: 0
    }
  }
};

const criticalMetrics = {
  status: 'success',
  metrics: {
    timestamp: **********.789,
    pool: {
      available_containers: 0,
      assigned_containers: 50,
      recycling_containers: 0,
      total_containers: 50
    },
    queue: {
      length: 30,
      avg_wait_time: 300.5,
      p95_wait_time: 600.2
    },
    containers: {
      avg_cpu_usage: 95.5,
      avg_memory_usage: 90.2,
      avg_network_rx: 102400,
      avg_network_tx: 204800
    }
  }
};

const emptyMetricsHistory = {
  status: 'success',
  metrics: [],
  period: {
    start_time: null,
    end_time: null
  }
};

const criticalHealthStatus = {
  status: 'success',
  health: {
    total: 50,
    healthy: 10,
    unhealthy: 35,
    unknown: 5,
    unhealthy_containers: Array(35).fill(0).map((_, i) => ({
      container_id: `unhealthy_container_${i}`,
      docker_id: `unhealthy_docker_${i}`,
      status: i % 3 === 0 ? 'exited' : i % 3 === 1 ? 'error' : 'restarting',
      issue: i % 3 === 0 ? `Container exited with code ${i % 10}` : 
             i % 3 === 1 ? `Container error: out of memory` : 
             `Container restarting too frequently`
    }))
  }
};

const emptyReport = {
  status: 'success',
  report: {
    timestamp: **********.789,
    period: {
      start: **********.789,
      end: **********.789,
      duration_hours: 24
    },
    pool_usage: {
      avg_total_containers: 0,
      avg_available_containers: 0,
      avg_assigned_containers: 0,
      max_total_containers: 0,
      min_available_containers: 0,
      max_assigned_containers: 0
    },
    queue_usage: {
      avg_queue_length: 0,
      max_queue_length: 0,
      avg_wait_time: 0,
      max_wait_time: 0
    }
  }
};

test.describe('Monitoring Edge Cases', () => {
  test('should handle empty metrics', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: emptyMetrics });
    });
    
    await page.route('**/api/metrics/history', async (route) => {
      await route.fulfill({ json: emptyMetricsHistory });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 0, healthy: 0, unhealthy: 0, unknown: 0 } } });
    });
    
    await page.route('**/api/report', async (route) => {
      await route.fulfill({ json: emptyReport });
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Check if health cards show zeros
    const healthyContainers = await page.getByTestId('healthy-containers');
    const cpuUsage = await page.getByTestId('cpu-usage');
    const memoryUsage = await page.getByTestId('memory-usage');
    
    await expect(healthyContainers).toHaveText('0');
    await expect(cpuUsage).toHaveText('0%');
    await expect(memoryUsage).toHaveText('0%');
    
    // Check if charts are displayed with no data
    const cpuChart = await page.getByTestId('cpu-chart');
    const memoryChart = await page.getByTestId('memory-chart');
    const networkChart = await page.getByTestId('network-chart');
    
    await expect(cpuChart).toBeVisible();
    await expect(memoryChart).toBeVisible();
    await expect(networkChart).toBeVisible();
    
    // Switch to container health tab
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await containerHealthTab.click();
    
    // Check if health chart is displayed with no data
    const healthChart = await page.getByTestId('health-chart');
    await expect(healthChart).toBeVisible();
    
    // Check if unhealthy containers table shows "No unhealthy containers"
    const unhealthyContainersTable = await page.getByTestId('unhealthy-containers-table');
    await expect(unhealthyContainersTable).toContainText('No unhealthy containers');
    
    // Switch to usage report tab
    const usageReportTab = await page.getByTestId('usage-report-tab');
    await usageReportTab.click();
    
    // Check if usage report tables show zeros
    const poolUsageTable = await page.getByTestId('pool-usage-table');
    const queueUsageTable = await page.getByTestId('queue-usage-table');
    
    await expect(poolUsageTable).toContainText('Average Total Containers0');
    await expect(queueUsageTable).toContainText('Average Queue Length0');
  });

  test('should handle critical metrics', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: criticalMetrics });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: criticalHealthStatus });
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Check if health cards show critical values
    const healthyContainers = await page.getByTestId('healthy-containers');
    const unhealthyContainers = await page.getByTestId('unhealthy-containers');
    const unknownContainers = await page.getByTestId('unknown-containers');
    const cpuUsage = await page.getByTestId('cpu-usage');
    const memoryUsage = await page.getByTestId('memory-usage');
    
    await expect(healthyContainers).toHaveText('10');
    await expect(unhealthyContainers).toContainText('35 unhealthy');
    await expect(unknownContainers).toContainText('5 unknown');
    await expect(cpuUsage).toHaveText('96%');
    await expect(memoryUsage).toHaveText('90%');
    
    // CPU and memory usage should be displayed in red (error color)
    await expect(cpuUsage).toHaveCSS('color', /rgb\(211, 47, 47\)/ || /rgb\(244, 67, 54\)/); // Error color in Material UI
    await expect(memoryUsage).toHaveCSS('color', /rgb\(211, 47, 47\)/ || /rgb\(244, 67, 54\)/); // Error color in Material UI
    
    // Switch to container health tab
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await containerHealthTab.click();
    
    // Check if unhealthy containers table shows many entries
    const unhealthyContainersTable = await page.getByTestId('unhealthy-containers-table');
    await expect(unhealthyContainersTable).toBeVisible();
    
    // Check if multiple unhealthy container rows are displayed
    const unhealthyContainerRow0 = await page.getByTestId('unhealthy-container-unhealthy_container_0');
    const unhealthyContainerRow1 = await page.getByTestId('unhealthy-container-unhealthy_container_1');
    const unhealthyContainerRow2 = await page.getByTestId('unhealthy-container-unhealthy_container_2');
    
    await expect(unhealthyContainerRow0).toBeVisible();
    await expect(unhealthyContainerRow1).toBeVisible();
    await expect(unhealthyContainerRow2).toBeVisible();
  });

  test('should handle time range changes', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { 
        containers: { avg_cpu_usage: 50, avg_memory_usage: 50 },
        queue: { length: 0 }
      } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 10, healthy: 10, unhealthy: 0, unknown: 0 } } });
    });
    
    // Mock different metrics history for different time ranges
    await page.route('**/api/metrics/history*', async (route) => {
      const url = route.request().url();
      
      // Check if URL contains time range parameters
      if (url.includes('start_time=') && url.includes('end_time=')) {
        // Return metrics history based on time range
        const metrics = [];
        const endTime = Math.floor(Date.now() / 1000);
        let startTime;
        let interval;
        
        if (url.includes('start_time=' + (endTime - 3600))) {
          // 1 hour
          startTime = endTime - 3600;
          interval = 300; // 5 minutes
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * interval,
              containers: {
                avg_cpu_usage: 40 + i,
                avg_memory_usage: 40 + i,
                avg_network_rx: 1000 + i * 100,
                avg_network_tx: 2000 + i * 200
              }
            });
          }
        } else if (url.includes('start_time=' + (endTime - 21600))) {
          // 6 hours
          startTime = endTime - 21600;
          interval = 1800; // 30 minutes
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * interval,
              containers: {
                avg_cpu_usage: 30 + i * 2,
                avg_memory_usage: 30 + i * 2,
                avg_network_rx: 800 + i * 200,
                avg_network_tx: 1600 + i * 400
              }
            });
          }
        } else if (url.includes('start_time=' + (endTime - 86400))) {
          // 24 hours
          startTime = endTime - 86400;
          interval = 7200; // 2 hours
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * interval,
              containers: {
                avg_cpu_usage: 20 + i * 3,
                avg_memory_usage: 20 + i * 3,
                avg_network_rx: 500 + i * 300,
                avg_network_tx: 1000 + i * 600
              }
            });
          }
        } else {
          // Default
          startTime = endTime - 3600;
          interval = 300; // 5 minutes
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * interval,
              containers: {
                avg_cpu_usage: 50,
                avg_memory_usage: 50,
                avg_network_rx: 1000,
                avg_network_tx: 2000
              }
            });
          }
        }
        
        await route.fulfill({ 
          json: { 
            status: 'success',
            metrics: metrics,
            period: {
              start_time: startTime,
              end_time: endTime
            }
          }
        });
      } else {
        // Default response
        await route.fulfill({ json: emptyMetricsHistory });
      }
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Check if charts are displayed
    const cpuChart = await page.getByTestId('cpu-chart');
    const memoryChart = await page.getByTestId('memory-chart');
    const networkChart = await page.getByTestId('network-chart');
    
    await expect(cpuChart).toBeVisible();
    await expect(memoryChart).toBeVisible();
    await expect(networkChart).toBeVisible();
    
    // Change time range to 24 hours
    const timeRangeSelect = await page.getByTestId('time-range-select');
    await timeRangeSelect.selectOption('24h');
    
    // Charts should update with new data
    await expect(cpuChart).toBeVisible();
    await expect(memoryChart).toBeVisible();
    await expect(networkChart).toBeVisible();
    
    // Change time range to 6 hours
    await timeRangeSelect.selectOption('6h');
    
    // Charts should update with new data
    await expect(cpuChart).toBeVisible();
    await expect(memoryChart).toBeVisible();
    await expect(networkChart).toBeVisible();
  });

  test('should handle refresh button', async ({ page }) => {
    // Mock API responses
    let callCount = 0;
    
    await page.route('**/api/metrics/current', async (route) => {
      callCount++;
      
      if (callCount === 1) {
        // First call
        await route.fulfill({ json: { status: 'success', metrics: { 
          containers: { avg_cpu_usage: 50, avg_memory_usage: 50 },
          queue: { length: 0 }
        } } });
      } else {
        // Subsequent calls (after refresh)
        await route.fulfill({ json: { status: 'success', metrics: { 
          containers: { avg_cpu_usage: 70, avg_memory_usage: 60 },
          queue: { length: 5 }
        } } });
      }
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Check initial CPU usage
    const cpuUsage = await page.getByTestId('cpu-usage');
    await expect(cpuUsage).toHaveText('50%');
    
    // Click refresh button
    const refreshButton = await page.getByTestId('refresh-button');
    await refreshButton.click();
    
    // Check if CPU usage is updated
    await expect(cpuUsage).toHaveText('70%');
  });

  test('should handle pagination of unhealthy containers', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { 
        containers: { avg_cpu_usage: 50, avg_memory_usage: 50 },
        queue: { length: 0 }
      } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: criticalHealthStatus });
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Wait for monitoring to load
    await page.waitForSelector('[data-testid="monitoring"]');
    
    // Switch to container health tab
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await containerHealthTab.click();
    
    // Check if unhealthy containers table is displayed
    const unhealthyContainersTable = await page.getByTestId('unhealthy-containers-table');
    await expect(unhealthyContainersTable).toBeVisible();
    
    // Check if pagination controls are displayed
    const pagination = await page.getByRole('navigation').filter({ hasText: 'pagination' });
    
    if (await pagination.isVisible()) {
      // Click next page button
      const nextPageButton = await pagination.getByRole('button', { name: 'Go to next page' });
      if (await nextPageButton.isVisible() && await nextPageButton.isEnabled()) {
        await nextPageButton.click();
        
        // Check if different unhealthy container rows are displayed
        await expect(unhealthyContainersTable).toBeVisible();
      }
    }
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error responses
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ 
        status: 500,
        json: { 
          status: 'error',
          message: 'Internal server error'
        }
      });
    });
    
    // Navigate to the monitoring page
    await page.goto('/monitoring');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load monitoring data');
  });
});

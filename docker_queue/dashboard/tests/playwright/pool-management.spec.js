import { test, expect } from '@playwright/test';

// Mock API responses
const mockPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 5,
    assigned_containers: 10,
    recycling_containers: 2,
    total_containers: 17,
    target_size: 20,
    queue_length: 3
  }
};

const mockMetrics = {
  status: 'success',
  metrics: {
    timestamp: 1619123456.789,
    pool: {
      available_containers: 5,
      assigned_containers: 10,
      recycling_containers: 2,
      total_containers: 17
    },
    queue: {
      length: 3,
      avg_wait_time: 15.5,
      p95_wait_time: 30.2
    }
  }
};

const mockMetricsHistory = {
  status: 'success',
  metrics: [
    {
      timestamp: 1619123156.789,
      pool: {
        available_containers: 4,
        assigned_containers: 11,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 4
      }
    },
    {
      timestamp: 1619123256.789,
      pool: {
        available_containers: 5,
        assigned_containers: 10,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 3
      }
    },
    {
      timestamp: 1619123356.789,
      pool: {
        available_containers: 6,
        assigned_containers: 9,
        recycling_containers: 2,
        total_containers: 17
      },
      queue: {
        length: 2
      }
    }
  ],
  period: {
    start_time: 1619123156.789,
    end_time: 1619123456.789
  }
};

const mockScaleSuccess = {
  status: 'success',
  message: 'Pool scaling scale_up',
  scaling: {
    action: 'scale_up',
    from: 17,
    to: 20,
    containers_to_create: 3
  }
};

test.describe('Pool Management Component', () => {
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: mockPoolStatus });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: mockMetrics });
    });
    
    await page.route('**/api/metrics/history', async (route) => {
      await route.fulfill({ json: mockMetricsHistory });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
  });

  test('should display the pool management page with correct title and description', async ({ page }) => {
    const poolManagement = await page.getByTestId('pool-management');
    await expect(poolManagement).toBeVisible();
    
    const title = await page.getByTestId('pool-management-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('Pool Management');
    
    const description = await page.getByTestId('pool-management-description');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Manage the Docker container pool size and configuration');
  });

  test('should display pool status cards with correct data', async ({ page }) => {
    const totalContainers = await page.getByTestId('total-containers');
    await expect(totalContainers).toBeVisible();
    await expect(totalContainers).toHaveText('17');
    
    const availableContainers = await page.getByTestId('available-containers');
    await expect(availableContainers).toBeVisible();
    await expect(availableContainers).toHaveText('5');
    
    const queueLength = await page.getByTestId('queue-length');
    await expect(queueLength).toBeVisible();
    await expect(queueLength).toHaveText('3');
    
    const avgWaitTime = await page.getByTestId('avg-wait-time');
    await expect(avgWaitTime).toBeVisible();
    await expect(avgWaitTime).toContainText('Average wait time: 15.5 seconds');
  });

  test('should display pool size history chart', async ({ page }) => {
    const poolSizeChart = await page.getByTestId('pool-size-chart');
    await expect(poolSizeChart).toBeVisible();
    
    // Check if the chart contains the correct data
    // This is a basic check since we can't easily verify the chart data directly
    const chartCanvas = await poolSizeChart.locator('canvas');
    await expect(chartCanvas).toBeVisible();
  });

  test('should display queue length history chart', async ({ page }) => {
    const queueLengthChart = await page.getByTestId('queue-length-chart');
    await expect(queueLengthChart).toBeVisible();
    
    // Check if the chart contains the correct data
    const chartCanvas = await queueLengthChart.locator('canvas');
    await expect(chartCanvas).toBeVisible();
  });

  test('should open scale pool dialog when scale button is clicked', async ({ page }) => {
    const scaleButton = await page.getByTestId('scale-pool-button');
    await expect(scaleButton).toBeVisible();
    
    // Click on scale button
    await scaleButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
    
    // Check if dialog contains the correct elements
    const targetSizeSlider = await page.getByTestId('target-size-slider');
    const targetSizeInput = await page.getByTestId('target-size-input');
    const cancelButton = await page.getByTestId('cancel-scale-button');
    const submitButton = await page.getByTestId('submit-scale-button');
    
    await expect(targetSizeSlider).toBeVisible();
    await expect(targetSizeInput).toBeVisible();
    await expect(cancelButton).toBeVisible();
    await expect(submitButton).toBeVisible();
    
    // Initial value should be set to current pool size
    await expect(targetSizeInput).toHaveValue('17');
  });

  test('should scale pool when form is filled and submitted', async ({ page }) => {
    // Mock scale pool API
    await page.route('**/api/pool/scale', async (route) => {
      await route.fulfill({ json: mockScaleSuccess });
    });
    
    // Open scale dialog
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Change target size
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('20');
    
    // Submit form
    const submitButton = await page.getByTestId('submit-scale-button');
    await submitButton.click();
    
    // Dialog should be closed
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).not.toBeVisible();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Pool scaling scale_up initiated');
  });

  test('should disable submit button when target size equals current size', async ({ page }) => {
    // Open scale dialog
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Set target size to current size
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('17');
    
    // Submit button should be disabled
    const submitButton = await page.getByTestId('submit-scale-button');
    await expect(submitButton).toBeDisabled();
  });

  test('should open scale dialog when adjust size button is clicked', async ({ page }) => {
    const adjustSizeButton = await page.getByTestId('adjust-size-button');
    await expect(adjustSizeButton).toBeVisible();
    
    // Click on adjust size button
    await adjustSizeButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
  });

  test('should display loading indicator when data is being fetched', async ({ page }) => {
    // Navigate to the page again with delayed response to show loading state
    await page.route('**/api/pool/status', async (route) => {
      // Delay the response to show loading state
      await new Promise(resolve => setTimeout(resolve, 1000));
      await route.fulfill({ json: mockPoolStatus });
    });
    
    await page.goto('/pool');
    
    // Loading indicator should be visible initially
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
  });

  test('should display error message when API request fails', async ({ page }) => {
    // Mock API error
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ status: 500 });
    });
    
    await page.goto('/pool');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load pool data');
  });
});

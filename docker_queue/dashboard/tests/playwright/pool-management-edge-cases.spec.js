import { test, expect } from '@playwright/test';

// Mock API responses for edge cases
const emptyPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 0,
    assigned_containers: 0,
    recycling_containers: 0,
    total_containers: 0,
    target_size: 0,
    queue_length: 0
  }
};

const maxCapacityPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 0,
    assigned_containers: 50,
    recycling_containers: 0,
    total_containers: 50,
    target_size: 50,
    queue_length: 20
  }
};

const emptyMetricsHistory = {
  status: 'success',
  metrics: [],
  period: {
    start_time: null,
    end_time: null
  }
};

const scalingInProgressResponse = {
  status: 'success',
  message: 'Pool scaling already in progress',
  scaling: {
    action: 'scale_up',
    from: 10,
    to: 20,
    containers_to_create: 10,
    progress: 30
  }
};

const scalingErrorResponse = {
  status: 'error',
  message: 'Failed to scale pool: Docker API error',
  error_code: 'DOCKER_API_ERROR'
};

test.describe('Pool Management Edge Cases', () => {
  test('should handle empty pool', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: emptyPoolStatus });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { queue: { length: 0 } } } });
    });
    
    await page.route('**/api/metrics/history', async (route) => {
      await route.fulfill({ json: emptyMetricsHistory });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Check if pool status shows zeros
    const totalContainers = await page.getByTestId('total-containers');
    const availableContainers = await page.getByTestId('available-containers');
    const queueLength = await page.getByTestId('queue-length');
    
    await expect(totalContainers).toHaveText('0');
    await expect(availableContainers).toHaveText('0');
    await expect(queueLength).toHaveText('0');
    
    // Check if charts are displayed with no data
    const poolSizeChart = await page.getByTestId('pool-size-chart');
    const queueLengthChart = await page.getByTestId('queue-length-chart');
    
    await expect(poolSizeChart).toBeVisible();
    await expect(queueLengthChart).toBeVisible();
  });

  test('should handle max capacity pool', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: maxCapacityPoolStatus });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { queue: { length: 20, avg_wait_time: 120.5 } } } });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Check if pool status shows max capacity
    const totalContainers = await page.getByTestId('total-containers');
    const availableContainers = await page.getByTestId('available-containers');
    const queueLength = await page.getByTestId('queue-length');
    
    await expect(totalContainers).toHaveText('50');
    await expect(availableContainers).toHaveText('0');
    await expect(queueLength).toHaveText('20');
    
    // Scale pool
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
    
    // Set target size to maximum allowed
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('100');
    
    // Mock scale pool API with error response
    await page.route('**/api/pool/scale', async (route) => {
      await route.fulfill({ json: scalingErrorResponse });
    });
    
    // Submit form
    const submitButton = await page.getByTestId('submit-scale-button');
    await submitButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Failed to scale pool: Docker API error');
  });

  test('should handle scaling in progress', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0, target_size: 20 } } });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Scale pool
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
    
    // Set target size
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('30');
    
    // Mock scale pool API with scaling in progress response
    await page.route('**/api/pool/scale', async (route) => {
      await route.fulfill({ json: scalingInProgressResponse });
    });
    
    // Submit form
    const submitButton = await page.getByTestId('submit-scale-button');
    await submitButton.click();
    
    // Info message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Pool scaling already in progress');
  });

  test('should validate target size in scale form', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0, target_size: 10 } } });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Scale pool
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
    
    // Set target size to invalid value (0)
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('0');
    
    // Submit button should be disabled or validation error should be shown
    const submitButton = await page.getByTestId('submit-scale-button');
    
    // Check if submit button is disabled when target size is invalid
    // or if validation error is shown
    const isDisabled = await submitButton.isDisabled();
    if (!isDisabled) {
      // If not disabled, submit and check for validation error
      await submitButton.click();
      
      // Dialog should still be open
      await expect(scaleDialog).toBeVisible();
    }
  });

  test('should handle same target size as current size', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0, target_size: 10 } } });
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Scale pool
    const scaleButton = await page.getByTestId('scale-pool-button');
    await scaleButton.click();
    
    // Scale dialog should be visible
    const scaleDialog = await page.getByTestId('scale-pool-dialog');
    await expect(scaleDialog).toBeVisible();
    
    // Set target size to same as current size
    const targetSizeInput = await page.getByTestId('target-size-input');
    await targetSizeInput.clear();
    await targetSizeInput.fill('10');
    
    // Submit button should be disabled
    const submitButton = await page.getByTestId('submit-scale-button');
    await expect(submitButton).toBeDisabled();
  });

  test('should handle time range changes for metrics history', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0, target_size: 10 } } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { queue: { length: 0 } } } });
    });
    
    // Mock different metrics history for different time ranges
    await page.route('**/api/metrics/history*', async (route) => {
      const url = route.request().url();
      
      // Check if URL contains time range parameters
      if (url.includes('start_time=') && url.includes('end_time=')) {
        // Return metrics history based on time range
        const metrics = [];
        const endTime = Math.floor(Date.now() / 1000);
        let startTime;
        
        if (url.includes('start_time=' + (endTime - 3600))) {
          // 1 hour
          startTime = endTime - 3600;
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * 300,
              pool: {
                total_containers: 10,
                available_containers: 5 + (i % 3),
                assigned_containers: 5 - (i % 3),
                recycling_containers: 0
              },
              queue: {
                length: i % 3
              }
            });
          }
        } else if (url.includes('start_time=' + (endTime - 86400))) {
          // 24 hours
          startTime = endTime - 86400;
          for (let i = 0; i < 24; i++) {
            metrics.push({
              timestamp: startTime + i * 3600,
              pool: {
                total_containers: 10 + (i % 5),
                available_containers: 5 + (i % 3),
                assigned_containers: 5 + (i % 2),
                recycling_containers: 0
              },
              queue: {
                length: i % 5
              }
            });
          }
        } else {
          // Default
          startTime = endTime - 3600;
          for (let i = 0; i < 12; i++) {
            metrics.push({
              timestamp: startTime + i * 300,
              pool: {
                total_containers: 10,
                available_containers: 5,
                assigned_containers: 5,
                recycling_containers: 0
              },
              queue: {
                length: 0
              }
            });
          }
        }
        
        await route.fulfill({ 
          json: { 
            status: 'success',
            metrics: metrics,
            period: {
              start_time: startTime,
              end_time: endTime
            }
          }
        });
      } else {
        // Default response
        await route.fulfill({ json: { status: 'success', metrics: [], period: { start_time: null, end_time: null } } });
      }
    });
    
    // Navigate to the pool management page
    await page.goto('/pool');
    
    // Wait for pool management to load
    await page.waitForSelector('[data-testid="pool-management"]');
    
    // Check if charts are displayed
    const poolSizeChart = await page.getByTestId('pool-size-chart');
    const queueLengthChart = await page.getByTestId('queue-length-chart');
    
    await expect(poolSizeChart).toBeVisible();
    await expect(queueLengthChart).toBeVisible();
    
    // Change time range to 24 hours
    const timeRangeSelect = await page.getByTestId('time-range-select');
    if (await timeRangeSelect.isVisible()) {
      await timeRangeSelect.selectOption('24h');
      
      // Charts should update with new data
      await expect(poolSizeChart).toBeVisible();
      await expect(queueLengthChart).toBeVisible();
    }
  });
});

import { test, expect } from '@playwright/test';

// Mock API responses for edge cases
const emptyPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 0,
    assigned_containers: 0,
    recycling_containers: 0,
    total_containers: 0,
    queue_length: 0
  }
};

const maxCapacityPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 0,
    assigned_containers: 50,
    recycling_containers: 0,
    total_containers: 50,
    queue_length: 20
  }
};

const longQueueResponse = {
  status: 'pending',
  message: 'Container request queued',
  request_id: 'request3',
  queue_position: 15,
  queue_length: 20,
  estimated_wait_seconds: 300
};

const containerErrorResponse = {
  status: 'error',
  message: 'Failed to create container: Docker API error',
  error_code: 'DOCKER_API_ERROR'
};

test.describe('Container Management Edge Cases', () => {
  test('should handle empty container pool', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: emptyPoolStatus });
    });
    
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Check if pool status shows zeros
    const totalContainers = await page.getByTestId('total-containers');
    const availableContainers = await page.getByTestId('available-containers');
    const assignedContainers = await page.getByTestId('assigned-containers');
    const recyclingContainers = await page.getByTestId('recycling-containers');
    
    await expect(totalContainers).toHaveText('0');
    await expect(availableContainers).toHaveText('0');
    await expect(assignedContainers).toHaveText('0');
    await expect(recyclingContainers).toHaveText('0');
    
    // Check if containers table shows "No containers found"
    const containersTable = await page.getByTestId('containers-table');
    await expect(containersTable).toContainText('No containers found');
    
    // Switch to queue tab
    const queueTab = await page.getByTestId('queue-tab');
    await queueTab.click();
    
    // Check if queue table shows "No requests in queue"
    const queueTable = await page.getByTestId('queue-table');
    await expect(queueTable).toContainText('No requests in queue');
  });

  test('should handle max capacity pool', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: maxCapacityPoolStatus });
    });
    
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Check if pool status shows max capacity
    const totalContainers = await page.getByTestId('total-containers');
    const availableContainers = await page.getByTestId('available-containers');
    const assignedContainers = await page.getByTestId('assigned-containers');
    
    await expect(totalContainers).toHaveText('50');
    await expect(availableContainers).toHaveText('0');
    await expect(assignedContainers).toHaveText('50');
    
    // Request a container
    const requestButton = await page.getByTestId('request-container-button');
    await requestButton.click();
    
    // Fill form
    const userIdInput = await page.getByTestId('user-id-input');
    const priorityInput = await page.getByTestId('priority-input');
    const submitButton = await page.getByTestId('submit-request-button');
    
    await userIdInput.fill('test_user');
    await priorityInput.fill('9');
    
    // Mock request container API with queue response
    await page.route('**/api/containers/request', async (route) => {
      await route.fulfill({ json: longQueueResponse });
    });
    
    // Submit form
    await submitButton.click();
    
    // Info message should be displayed about long queue
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Container request queued at position 15');
  });

  test('should handle container creation error', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Request a container
    const requestButton = await page.getByTestId('request-container-button');
    await requestButton.click();
    
    // Fill form
    const userIdInput = await page.getByTestId('user-id-input');
    const priorityInput = await page.getByTestId('priority-input');
    const submitButton = await page.getByTestId('submit-request-button');
    
    await userIdInput.fill('test_user');
    await priorityInput.fill('5');
    
    // Mock request container API with error response
    await page.route('**/api/containers/request', async (route) => {
      await route.fulfill({ json: containerErrorResponse });
    });
    
    // Submit form
    await submitButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Failed to create container: Docker API error');
  });

  test('should handle container release error', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Mock release container API with error response
    await page.route('**/api/containers/release', async (route) => {
      await route.fulfill({ 
        json: { 
          status: 'error',
          message: 'Failed to release container: Container not found'
        }
      });
    });
    
    // Click on release button for container1
    const releaseButton = await page.getByTestId('release-container-container1');
    await releaseButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Failed to release container: Container not found');
  });

  test('should validate user ID in request form', async ({ page }) => {
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Request a container
    const requestButton = await page.getByTestId('request-container-button');
    await requestButton.click();
    
    // Leave user ID empty
    const priorityInput = await page.getByTestId('priority-input');
    const submitButton = await page.getByTestId('submit-request-button');
    
    await priorityInput.fill('5');
    
    // Submit form
    await submitButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('User ID is required');
    
    // Dialog should still be open
    const requestDialog = await page.getByTestId('request-container-dialog');
    await expect(requestDialog).toBeVisible();
  });

  test('should handle very long container IDs and names', async ({ page }) => {
    // Mock API responses with long IDs and names
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 1, available_containers: 0, assigned_containers: 1, recycling_containers: 0 } } });
    });
    
    // Mock containers with very long IDs and names
    const mockContainers = [
      {
        container_id: 'container_with_very_long_id_that_should_be_truncated_in_the_ui_12345678901234567890',
        docker_id: 'docker_with_very_long_id_that_should_be_truncated_in_the_ui_12345678901234567890',
        name: 'container_with_very_long_name_that_should_be_truncated_in_the_ui_12345678901234567890',
        status: 'assigned',
        ip_address: '**********',
        user_id: 'user_with_very_long_id_that_should_be_truncated_in_the_ui_12345678901234567890',
        assigned_at: Date.now() - 3600000, // 1 hour ago
      }
    ];
    
    // Navigate to the container management page
    await page.goto('/containers');
    
    // Wait for container management to load
    await page.waitForSelector('[data-testid="container-management"]');
    
    // Check if container row is displayed with truncated values
    const containerRow = await page.getByTestId(`container-row-${mockContainers[0].container_id}`);
    
    // The row might not be visible due to the long ID, but we can check if the view button is visible
    const viewButton = await page.getByTestId(`view-container-${mockContainers[0].container_id}`);
    
    // Click view button to see details
    if (await viewButton.isVisible()) {
      await viewButton.click();
    } else {
      // If view button is not visible, we can still check if the container details dialog can be opened
      // by clicking on the container row
      await containerRow.click();
    }
    
    // Container details dialog should be visible
    const detailsDialog = await page.getByTestId('container-details-dialog');
    await expect(detailsDialog).toBeVisible();
    
    // Dialog should contain the full IDs and names
    await expect(detailsDialog).toContainText(mockContainers[0].container_id);
    await expect(detailsDialog).toContainText(mockContainers[0].docker_id);
    await expect(detailsDialog).toContainText(mockContainers[0].name);
    await expect(detailsDialog).toContainText(mockContainers[0].user_id);
  });
});

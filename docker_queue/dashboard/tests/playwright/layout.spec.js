import { test, expect } from '@playwright/test';

test.describe('Layout Component', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should display the app bar with correct title', async ({ page }) => {
    const appBar = await page.getByTestId('app-bar');
    await expect(appBar).toBeVisible();
    
    const title = await page.getByTestId('app-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('BahtBrowse Docker Queue Management');
  });

  test('should display the sidebar with navigation items', async ({ page }) => {
    const sidebar = await page.getByTestId('sidebar');
    await expect(sidebar).toBeVisible();
    
    // Check if all menu items are visible
    const dashboardItem = await page.getByTestId('menu-item-dashboard');
    const containersItem = await page.getByTestId('menu-item-containers');
    const poolItem = await page.getByTestId('menu-item-pool-management');
    const monitoringItem = await page.getByTestId('menu-item-monitoring');
    const settingsItem = await page.getByTestId('menu-item-settings');
    
    await expect(dashboardItem).toBeVisible();
    await expect(containersItem).toBeVisible();
    await expect(poolItem).toBeVisible();
    await expect(monitoringItem).toBeVisible();
    await expect(settingsItem).toBeVisible();
  });

  test('should toggle sidebar when menu button is clicked', async ({ page }) => {
    // Initially the sidebar should be open
    const sidebar = await page.getByTestId('sidebar');
    await expect(sidebar).toHaveClass(/Mui-open/);
    
    // Click the close button
    const closeButton = await page.getByTestId('menu-close-button');
    await closeButton.click();
    
    // Sidebar should be closed
    await expect(sidebar).not.toHaveClass(/Mui-open/);
    
    // Click the open button
    const openButton = await page.getByTestId('menu-open-button');
    await openButton.click();
    
    // Sidebar should be open again
    await expect(sidebar).toHaveClass(/Mui-open/);
  });

  test('should navigate to different pages when menu items are clicked', async ({ page }) => {
    // Click on Containers menu item
    const containersItem = await page.getByTestId('menu-item-containers');
    await containersItem.click();
    
    // URL should change to /containers
    await expect(page).toHaveURL(/\/containers$/);
    
    // Click on Pool Management menu item
    const poolItem = await page.getByTestId('menu-item-pool-management');
    await poolItem.click();
    
    // URL should change to /pool
    await expect(page).toHaveURL(/\/pool$/);
    
    // Click on Monitoring menu item
    const monitoringItem = await page.getByTestId('menu-item-monitoring');
    await monitoringItem.click();
    
    // URL should change to /monitoring
    await expect(page).toHaveURL(/\/monitoring$/);
    
    // Click on Settings menu item
    const settingsItem = await page.getByTestId('menu-item-settings');
    await settingsItem.click();
    
    // URL should change to /settings
    await expect(page).toHaveURL(/\/settings$/);
    
    // Click on Dashboard menu item
    const dashboardItem = await page.getByTestId('menu-item-dashboard');
    await dashboardItem.click();
    
    // URL should change to /
    await expect(page).toHaveURL(/\/$/);
  });
});

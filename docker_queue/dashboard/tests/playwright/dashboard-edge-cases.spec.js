import { test, expect } from '@playwright/test';

// Mock API responses for edge cases
const emptyPoolStatus = {
  status: 'success',
  pool: {
    available_containers: 0,
    assigned_containers: 0,
    recycling_containers: 0,
    total_containers: 0,
    queue_length: 0
  }
};

const highLoadMetrics = {
  status: 'success',
  metrics: {
    timestamp: **********.789,
    pool: {
      available_containers: 0,
      assigned_containers: 20,
      recycling_containers: 0,
      total_containers: 20
    },
    queue: {
      length: 15,
      avg_wait_time: 120.5,
      p95_wait_time: 300.2
    },
    containers: {
      avg_cpu_usage: 95.5,
      avg_memory_usage: 85.2,
      avg_network_rx: 10240,
      avg_network_tx: 20480
    }
  }
};

const criticalHealthStatus = {
  status: 'success',
  health: {
    total: 20,
    healthy: 5,
    unhealthy: 12,
    unknown: 3,
    unhealthy_containers: Array(12).fill(0).map((_, i) => ({
      container_id: `unhealthy_container_${i}`,
      docker_id: `unhealthy_docker_${i}`,
      status: i % 2 === 0 ? 'exited' : 'error',
      issue: i % 2 === 0 ? `Container exited with code ${i}` : `Container error: out of memory`
    }))
  }
};

test.describe('Dashboard Component Edge Cases', () => {
  test('should handle empty pool status', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: emptyPoolStatus });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { queue: { length: 0 } } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 0, healthy: 0, unhealthy: 0, unknown: 0 } } });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if pool chart shows empty data
    const poolChart = await page.getByTestId('pool-chart');
    await expect(poolChart).toBeVisible();
    
    // Check if queue length shows 0
    const queueLength = await page.getByTestId('queue-length');
    await expect(queueLength).toBeVisible();
    await expect(queueLength).toHaveText('0');
    
    // No wait time should be displayed
    const avgWaitTime = await page.getByText('Average Wait Time:');
    await expect(avgWaitTime).not.toBeVisible();
  });

  test('should handle high load metrics', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: highLoadMetrics.metrics.pool } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: highLoadMetrics });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 20, healthy: 20, unhealthy: 0, unknown: 0 } } });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if queue length shows high value
    const queueLength = await page.getByTestId('queue-length');
    await expect(queueLength).toBeVisible();
    await expect(queueLength).toHaveText('15');
    
    // Check if wait time shows high value
    const avgWaitTime = await page.getByTestId('avg-wait-time');
    await expect(avgWaitTime).toBeVisible();
    await expect(avgWaitTime).toContainText('120.5 seconds');
    
    // Check if resource chart shows high CPU and memory usage
    const resourceChart = await page.getByTestId('resource-chart');
    await expect(resourceChart).toBeVisible();
  });

  test('should handle critical health status', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 20, available_containers: 5, assigned_containers: 15, recycling_containers: 0 } } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ json: { status: 'success', metrics: { queue: { length: 0 } } } });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: criticalHealthStatus });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-container"]');
    
    // Check if health chart shows critical status
    const healthChart = await page.getByTestId('health-chart');
    await expect(healthChart).toBeVisible();
    
    // Navigate to monitoring page to see more details
    const viewHealthButton = await page.getByTestId('view-health-button');
    await viewHealthButton.click();
    
    // URL should change to /monitoring
    await expect(page).toHaveURL(/\/monitoring$/);
    
    // Switch to container health tab
    const containerHealthTab = await page.getByTestId('container-health-tab');
    await containerHealthTab.click();
    
    // Check if unhealthy containers table is displayed with many entries
    const unhealthyContainersTable = await page.getByTestId('unhealthy-containers-table');
    await expect(unhealthyContainersTable).toBeVisible();
    
    // Check if multiple unhealthy container rows are displayed
    const unhealthyContainerRow0 = await page.getByTestId('unhealthy-container-unhealthy_container_0');
    const unhealthyContainerRow1 = await page.getByTestId('unhealthy-container-unhealthy_container_1');
    
    await expect(unhealthyContainerRow0).toBeVisible();
    await expect(unhealthyContainerRow1).toBeVisible();
  });

  test('should handle API timeout', async ({ page }) => {
    // Mock API responses with delay to simulate timeout
    await page.route('**/api/pool/status', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Loading indicator should be visible for a while
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
    
    // Wait for loading to finish (this might take longer than usual)
    await page.waitForSelector('[data-testid="dashboard-container"]', { timeout: 10000 });
  });

  test('should handle API error responses', async ({ page }) => {
    // Mock API error responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ 
        status: 500,
        json: { 
          status: 'error',
          message: 'Internal server error'
        }
      });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ 
        status: 500,
        json: { 
          status: 'error',
          message: 'Internal server error'
        }
      });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ 
        status: 500,
        json: { 
          status: 'error',
          message: 'Internal server error'
        }
      });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load dashboard data');
  });

  test('should handle partial API failures', async ({ page }) => {
    // Mock some successful and some failed API responses
    await page.route('**/api/pool/status', async (route) => {
      await route.fulfill({ json: { status: 'success', pool: { total_containers: 10, available_containers: 5, assigned_containers: 5, recycling_containers: 0 } } });
    });
    
    await page.route('**/api/metrics/current', async (route) => {
      await route.fulfill({ 
        status: 500,
        json: { 
          status: 'error',
          message: 'Internal server error'
        }
      });
    });
    
    await page.route('**/api/health', async (route) => {
      await route.fulfill({ json: { status: 'success', health: { total: 10, healthy: 8, unhealthy: 2, unknown: 0 } } });
    });
    
    // Navigate to the dashboard
    await page.goto('/');
    
    // Error message should be visible
    const errorMessage = await page.getByTestId('error-message');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('Failed to load dashboard data');
  });
});

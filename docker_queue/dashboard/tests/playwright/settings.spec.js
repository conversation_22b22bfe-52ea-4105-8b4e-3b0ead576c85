import { test, expect } from '@playwright/test';

test.describe('Settings Component', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]', { state: 'visible' });
  });

  test('should display the settings page with correct title and description', async ({ page }) => {
    const settings = await page.getByTestId('settings');
    await expect(settings).toBeVisible();
    
    const title = await page.getByTestId('settings-title');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('System Settings');
    
    const description = await page.getByTestId('settings-description');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Configure system settings and view logs');
  });

  test('should display all settings sections with correct initial values', async ({ page }) => {
    // Pool Settings
    const minSizeInput = await page.getByTestId('min-size-input');
    const maxSizeInput = await page.getByTestId('max-size-input');
    const targetSizeInput = await page.getByTestId('target-size-input');
    const minAvailableInput = await page.getByTestId('min-available-input');
    const scalingIntervalInput = await page.getByTestId('scaling-interval-input');
    const autoScalingSwitch = await page.getByTestId('auto-scaling-switch');
    
    await expect(minSizeInput).toBeVisible();
    await expect(maxSizeInput).toBeVisible();
    await expect(targetSizeInput).toBeVisible();
    await expect(minAvailableInput).toBeVisible();
    await expect(scalingIntervalInput).toBeVisible();
    await expect(autoScalingSwitch).toBeVisible();
    
    await expect(minSizeInput).toHaveValue('5');
    await expect(maxSizeInput).toHaveValue('20');
    await expect(targetSizeInput).toHaveValue('10');
    await expect(minAvailableInput).toHaveValue('2');
    await expect(scalingIntervalInput).toHaveValue('60');
    await expect(autoScalingSwitch).toBeChecked();
    
    // Container Settings
    const containerImageInput = await page.getByTestId('container-image-input');
    const cpuLimitInput = await page.getByTestId('cpu-limit-input');
    const memoryLimitInput = await page.getByTestId('memory-limit-input');
    const networkInput = await page.getByTestId('network-input');
    const timeoutInput = await page.getByTestId('timeout-input');
    const idleTimeoutInput = await page.getByTestId('idle-timeout-input');
    
    await expect(containerImageInput).toBeVisible();
    await expect(cpuLimitInput).toBeVisible();
    await expect(memoryLimitInput).toBeVisible();
    await expect(networkInput).toBeVisible();
    await expect(timeoutInput).toBeVisible();
    await expect(idleTimeoutInput).toBeVisible();
    
    await expect(containerImageInput).toHaveValue('bahtbrowse:latest');
    await expect(cpuLimitInput).toHaveValue('1');
    await expect(memoryLimitInput).toHaveValue('2g');
    await expect(networkInput).toHaveValue('bahtbrowse');
    await expect(timeoutInput).toHaveValue('3600');
    await expect(idleTimeoutInput).toHaveValue('300');
    
    // Queue Settings
    const maxWaitTimeInput = await page.getByTestId('max-wait-time-input');
    const defaultPriorityInput = await page.getByTestId('default-priority-input');
    const maxQueueSizeInput = await page.getByTestId('max-queue-size-input');
    
    await expect(maxWaitTimeInput).toBeVisible();
    await expect(defaultPriorityInput).toBeVisible();
    await expect(maxQueueSizeInput).toBeVisible();
    
    await expect(maxWaitTimeInput).toHaveValue('300');
    await expect(defaultPriorityInput).toHaveValue('5');
    await expect(maxQueueSizeInput).toHaveValue('100');
    
    // System Settings
    const metricsRetentionInput = await page.getByTestId('metrics-retention-input');
    const logLevelSelect = await page.getByTestId('log-level-select');
    const adminEmailInput = await page.getByTestId('admin-email-input');
    const notificationsSwitch = await page.getByTestId('notifications-switch');
    
    await expect(metricsRetentionInput).toBeVisible();
    await expect(logLevelSelect).toBeVisible();
    await expect(adminEmailInput).toBeVisible();
    await expect(notificationsSwitch).toBeVisible();
    
    await expect(metricsRetentionInput).toHaveValue('7');
    await expect(logLevelSelect).toHaveValue('INFO');
    await expect(adminEmailInput).toHaveValue('<EMAIL>');
    await expect(notificationsSwitch).toBeChecked();
  });

  test('should display system logs', async ({ page }) => {
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toBeVisible();
    
    // Check if log entries are displayed
    const logEntry1 = await page.getByTestId('log-entry-1');
    const logEntry2 = await page.getByTestId('log-entry-2');
    const logEntry3 = await page.getByTestId('log-entry-3');
    
    await expect(logEntry1).toBeVisible();
    await expect(logEntry2).toBeVisible();
    await expect(logEntry3).toBeVisible();
    
    // Check log content
    await expect(logEntry1).toContainText('Container pool scaled to 15 containers');
    await expect(logEntry2).toContainText('Container unhealthy');
    await expect(logEntry3).toContainText('Failed to create container');
  });

  test('should update settings when values are changed', async ({ page }) => {
    // Change pool settings
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    const maxSizeInput = await page.getByTestId('max-size-input');
    await maxSizeInput.clear();
    await maxSizeInput.fill('30');
    
    // Change container settings
    const cpuLimitInput = await page.getByTestId('cpu-limit-input');
    await cpuLimitInput.clear();
    await cpuLimitInput.fill('2');
    
    // Change queue settings
    const defaultPriorityInput = await page.getByTestId('default-priority-input');
    await defaultPriorityInput.clear();
    await defaultPriorityInput.fill('7');
    
    // Change system settings
    const logLevelSelect = await page.getByTestId('log-level-select');
    await logLevelSelect.selectOption('DEBUG');
    
    // Check if save button is enabled
    const saveButton = await page.getByTestId('save-settings-button');
    await expect(saveButton).toBeEnabled();
    
    // Check if values were updated
    await expect(minSizeInput).toHaveValue('10');
    await expect(maxSizeInput).toHaveValue('30');
    await expect(cpuLimitInput).toHaveValue('2');
    await expect(defaultPriorityInput).toHaveValue('7');
    await expect(logLevelSelect).toHaveValue('DEBUG');
  });

  test('should save settings when save button is clicked', async ({ page }) => {
    // Change a setting
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    // Save button should be enabled
    const saveButton = await page.getByTestId('save-settings-button');
    await expect(saveButton).toBeEnabled();
    
    // Click save button
    await saveButton.click();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Settings saved successfully');
    
    // Save button should be disabled after saving
    await expect(saveButton).toBeDisabled();
  });

  test('should reset settings when reset button is clicked and confirmed', async ({ page }) => {
    // Change a setting
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    // Reset button should be enabled
    const resetButton = await page.getByTestId('reset-settings-button');
    await expect(resetButton).toBeEnabled();
    
    // Click reset button
    await resetButton.click();
    
    // Reset dialog should be visible
    const resetDialog = await page.getByTestId('reset-dialog');
    await expect(resetDialog).toBeVisible();
    
    // Click confirm button
    const confirmButton = await page.getByTestId('confirm-reset-button');
    await confirmButton.click();
    
    // Dialog should be closed
    await expect(resetDialog).not.toBeVisible();
    
    // Setting should be reset
    await expect(minSizeInput).toHaveValue('5');
    
    // Info message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Settings reset to last saved values');
  });

  test('should cancel reset when cancel button is clicked', async ({ page }) => {
    // Change a setting
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    // Click reset button
    const resetButton = await page.getByTestId('reset-settings-button');
    await resetButton.click();
    
    // Reset dialog should be visible
    const resetDialog = await page.getByTestId('reset-dialog');
    await expect(resetDialog).toBeVisible();
    
    // Click cancel button
    const cancelButton = await page.getByTestId('cancel-reset-button');
    await cancelButton.click();
    
    // Dialog should be closed
    await expect(resetDialog).not.toBeVisible();
    
    // Setting should not be reset
    await expect(minSizeInput).toHaveValue('10');
  });

  test('should refresh logs when refresh button is clicked', async ({ page }) => {
    const refreshButton = await page.getByTestId('refresh-logs-button');
    await expect(refreshButton).toBeVisible();
    
    // Click refresh button
    await refreshButton.click();
    
    // Logs should still be visible
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toBeVisible();
    
    // Log entries should still be visible
    const logEntry1 = await page.getByTestId('log-entry-1');
    await expect(logEntry1).toBeVisible();
  });

  test('should clear logs when clear button is clicked', async ({ page }) => {
    const clearButton = await page.getByTestId('clear-logs-button');
    await expect(clearButton).toBeVisible();
    
    // Click clear button
    await clearButton.click();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Logs cleared successfully');
    
    // Log entries should be removed
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toContainText('No logs available');
  });

  test('should display loading indicator when data is being fetched', async ({ page }) => {
    // Navigate to the page again to trigger loading state
    await page.goto('/settings');
    
    // Loading indicator should be visible initially
    const loadingIndicator = await page.getByTestId('loading-indicator');
    await expect(loadingIndicator).toBeVisible();
  });
});

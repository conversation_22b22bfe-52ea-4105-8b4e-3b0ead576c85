import { test, expect } from '@playwright/test';

// Mock API responses for edge cases
const emptySettings = {
  status: 'success',
  settings: {
    pool: {},
    container: {},
    queue: {},
    system: {}
  }
};

const invalidSettings = {
  status: 'success',
  settings: {
    pool: {
      min_size: -1,
      max_size: 0,
      target_size: -5,
      min_available: -2,
      auto_scaling: 'invalid',
      scaling_interval: 'invalid'
    },
    container: {
      image: '',
      cpu_limit: 'invalid',
      memory_limit: 'invalid',
      network: '',
      timeout: 'invalid',
      idle_timeout: 'invalid'
    },
    queue: {
      max_wait_time: 'invalid',
      default_priority: 'invalid',
      max_queue_size: 'invalid'
    },
    system: {
      metrics_retention_days: 'invalid',
      log_level: 'INVALID',
      admin_email: 'invalid-email',
      notifications_enabled: 'invalid'
    }
  }
};

const extremeSettings = {
  status: 'success',
  settings: {
    pool: {
      min_size: 1000,
      max_size: 10000,
      target_size: 5000,
      min_available: 1000,
      auto_scaling: true,
      scaling_interval: 1
    },
    container: {
      image: 'bahtbrowse:latest',
      cpu_limit: 100,
      memory_limit: '1000g',
      network: 'bahtbrowse',
      timeout: 86400,
      idle_timeout: 86400
    },
    queue: {
      max_wait_time: 86400,
      default_priority: 9,
      max_queue_size: 10000
    },
    system: {
      metrics_retention_days: 365,
      log_level: 'DEBUG',
      admin_email: '<EMAIL>',
      notifications_enabled: true
    }
  }
};

const emptyLogs = {
  status: 'success',
  logs: []
};

const manyLogs = {
  status: 'success',
  logs: Array(1000).fill(0).map((_, i) => ({
    id: i + 1,
    timestamp: new Date(Date.now() - i * 1000).toISOString(),
    level: i % 4 === 0 ? 'ERROR' : i % 4 === 1 ? 'WARNING' : i % 4 === 2 ? 'INFO' : 'DEBUG',
    message: `Log message ${i + 1}: ${i % 4 === 0 ? 'Error occurred' : i % 4 === 1 ? 'Warning condition' : i % 4 === 2 ? 'Information' : 'Debug information'}`
  }))
};

test.describe('Settings Edge Cases', () => {
  test('should handle empty settings', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: emptySettings });
    });
    
    await page.route('**/api/dashboard/logs', async (route) => {
      await route.fulfill({ json: emptyLogs });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if settings form is displayed with default or empty values
    const minSizeInput = await page.getByTestId('min-size-input');
    const containerImageInput = await page.getByTestId('container-image-input');
    const maxWaitTimeInput = await page.getByTestId('max-wait-time-input');
    const metricsRetentionInput = await page.getByTestId('metrics-retention-input');
    
    await expect(minSizeInput).toBeVisible();
    await expect(containerImageInput).toBeVisible();
    await expect(maxWaitTimeInput).toBeVisible();
    await expect(metricsRetentionInput).toBeVisible();
    
    // Check if logs list shows "No logs available"
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toContainText('No logs available');
  });

  test('should handle invalid settings', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: invalidSettings });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if settings form is displayed with invalid values
    const minSizeInput = await page.getByTestId('min-size-input');
    const cpuLimitInput = await page.getByTestId('cpu-limit-input');
    const defaultPriorityInput = await page.getByTestId('default-priority-input');
    const logLevelSelect = await page.getByTestId('log-level-select');
    
    await expect(minSizeInput).toBeVisible();
    await expect(cpuLimitInput).toBeVisible();
    await expect(defaultPriorityInput).toBeVisible();
    await expect(logLevelSelect).toBeVisible();
    
    // Try to save settings
    const saveButton = await page.getByTestId('save-settings-button');
    
    // Check if save button is enabled (it should be, as we're showing invalid values)
    if (await saveButton.isEnabled()) {
      // Mock save settings API with error response
      await page.route('**/api/dashboard/settings', async (route) => {
        if (route.request().method() === 'POST') {
          await route.fulfill({ 
            json: { 
              status: 'error',
              message: 'Invalid settings: min_size must be a positive integer'
            }
          });
        } else {
          await route.fulfill({ json: invalidSettings });
        }
      }, { times: 1 });
      
      // Click save button
      await saveButton.click();
      
      // Error message should be displayed
      const alert = await page.getByRole('alert');
      await expect(alert).toBeVisible();
      await expect(alert).toContainText('Invalid settings');
    }
  });

  test('should handle extreme settings', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: extremeSettings });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if settings form is displayed with extreme values
    const minSizeInput = await page.getByTestId('min-size-input');
    const maxSizeInput = await page.getByTestId('max-size-input');
    const cpuLimitInput = await page.getByTestId('cpu-limit-input');
    const memoryLimitInput = await page.getByTestId('memory-limit-input');
    
    await expect(minSizeInput).toHaveValue('1000');
    await expect(maxSizeInput).toHaveValue('10000');
    await expect(cpuLimitInput).toHaveValue('100');
    await expect(memoryLimitInput).toHaveValue('1000g');
    
    // Try to save settings
    const saveButton = await page.getByTestId('save-settings-button');
    
    // Check if save button is enabled (it should be, as we're showing extreme values)
    if (await saveButton.isEnabled()) {
      // Mock save settings API with success response
      await page.route('**/api/dashboard/settings', async (route) => {
        if (route.request().method() === 'POST') {
          await route.fulfill({ 
            json: { 
              status: 'success',
              message: 'Settings saved successfully'
            }
          });
        } else {
          await route.fulfill({ json: extremeSettings });
        }
      }, { times: 1 });
      
      // Click save button
      await saveButton.click();
      
      // Success message should be displayed
      const alert = await page.getByRole('alert');
      await expect(alert).toBeVisible();
      await expect(alert).toContainText('Settings saved successfully');
    }
  });

  test('should handle many logs', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: { status: 'success', settings: {
        pool: { min_size: 5, max_size: 20, target_size: 10, min_available: 2, auto_scaling: true, scaling_interval: 60 },
        container: { image: 'bahtbrowse:latest', cpu_limit: 1.0, memory_limit: '2g', network: 'bahtbrowse', timeout: 3600, idle_timeout: 300 },
        queue: { max_wait_time: 300, default_priority: 5, max_queue_size: 100 },
        system: { metrics_retention_days: 7, log_level: 'INFO', admin_email: '<EMAIL>', notifications_enabled: true }
      } } });
    });
    
    await page.route('**/api/dashboard/logs', async (route) => {
      await route.fulfill({ json: manyLogs });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if logs list is displayed with many entries
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toBeVisible();
    
    // Check if log entries are displayed
    const logEntry1 = await page.getByTestId('log-entry-1');
    const logEntry2 = await page.getByTestId('log-entry-2');
    
    await expect(logEntry1).toBeVisible();
    await expect(logEntry2).toBeVisible();
    
    // Check if error logs are displayed with error color
    const errorLog = await page.getByTestId('log-entry-4'); // ID 4 should be an ERROR log
    if (await errorLog.isVisible()) {
      await expect(errorLog).toContainText('Error occurred');
    }
  });

  test('should handle clear logs', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/logs', async (route) => {
      await route.fulfill({ json: { status: 'success', logs: [
        {
          id: 1,
          timestamp: new Date(Date.now() - 60000).toISOString(),
          level: 'INFO',
          message: 'Container pool scaled to 15 containers'
        }
      ] } });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Check if logs list is displayed with entries
    const logsList = await page.getByTestId('logs-list');
    await expect(logsList).toBeVisible();
    await expect(logsList).not.toContainText('No logs available');
    
    // Mock clear logs API
    await page.route('**/api/dashboard/logs', async (route) => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({ 
          json: { 
            status: 'success',
            message: 'Logs cleared successfully'
          }
        });
      }
    }, { times: 1 });
    
    // After clearing, return empty logs
    await page.route('**/api/dashboard/logs', async (route) => {
      await route.fulfill({ json: emptyLogs });
    }, { times: 1 });
    
    // Click clear logs button
    const clearButton = await page.getByTestId('clear-logs-button');
    await clearButton.click();
    
    // Success message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Logs cleared successfully');
    
    // Logs list should show "No logs available"
    await expect(logsList).toContainText('No logs available');
  });

  test('should handle settings save error', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      if (route.request().method() === 'GET') {
        await route.fulfill({ json: { status: 'success', settings: {
          pool: { min_size: 5, max_size: 20, target_size: 10, min_available: 2, auto_scaling: true, scaling_interval: 60 },
          container: { image: 'bahtbrowse:latest', cpu_limit: 1.0, memory_limit: '2g', network: 'bahtbrowse', timeout: 3600, idle_timeout: 300 },
          queue: { max_wait_time: 300, default_priority: 5, max_queue_size: 100 },
          system: { metrics_retention_days: 7, log_level: 'INFO', admin_email: '<EMAIL>', notifications_enabled: true }
        } } });
      }
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Change a setting
    const minSizeInput = await page.getByTestId('min-size-input');
    await minSizeInput.clear();
    await minSizeInput.fill('10');
    
    // Mock save settings API with error response
    await page.route('**/api/dashboard/settings', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({ 
          status: 500,
          json: { 
            status: 'error',
            message: 'Failed to save settings: Database error'
          }
        });
      }
    }, { times: 1 });
    
    // Click save button
    const saveButton = await page.getByTestId('save-settings-button');
    await saveButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Failed to save settings: Database error');
  });

  test('should handle settings with validation constraints', async ({ page }) => {
    // Mock API responses
    await page.route('**/api/dashboard/settings', async (route) => {
      await route.fulfill({ json: { status: 'success', settings: {
        pool: { min_size: 5, max_size: 20, target_size: 10, min_available: 2, auto_scaling: true, scaling_interval: 60 },
        container: { image: 'bahtbrowse:latest', cpu_limit: 1.0, memory_limit: '2g', network: 'bahtbrowse', timeout: 3600, idle_timeout: 300 },
        queue: { max_wait_time: 300, default_priority: 5, max_queue_size: 100 },
        system: { metrics_retention_days: 7, log_level: 'INFO', admin_email: '<EMAIL>', notifications_enabled: true }
      } } });
    });
    
    // Navigate to the settings page
    await page.goto('/settings');
    
    // Wait for settings to load
    await page.waitForSelector('[data-testid="settings"]');
    
    // Test validation constraints
    
    // 1. Min size > max size
    const minSizeInput = await page.getByTestId('min-size-input');
    const maxSizeInput = await page.getByTestId('max-size-input');
    
    await minSizeInput.clear();
    await minSizeInput.fill('30');
    
    await maxSizeInput.clear();
    await maxSizeInput.fill('20');
    
    // Save button should still be enabled (we don't validate this constraint in the UI)
    const saveButton = await page.getByTestId('save-settings-button');
    await expect(saveButton).toBeEnabled();
    
    // 2. Negative values
    await minSizeInput.clear();
    await minSizeInput.fill('-5');
    
    // Input should either prevent negative values or save button should be disabled
    // This depends on the implementation
    
    // 3. Invalid email
    const adminEmailInput = await page.getByTestId('admin-email-input');
    await adminEmailInput.clear();
    await adminEmailInput.fill('invalid-email');
    
    // Save button should still be enabled (we don't validate email in the UI)
    await expect(saveButton).toBeEnabled();
    
    // Mock save settings API with validation error response
    await page.route('**/api/dashboard/settings', async (route) => {
      if (route.request().method() === 'POST') {
        await route.fulfill({ 
          status: 400,
          json: { 
            status: 'error',
            message: 'Validation error: min_size cannot be greater than max_size'
          }
        });
      }
    }, { times: 1 });
    
    // Click save button
    await saveButton.click();
    
    // Error message should be displayed
    const alert = await page.getByRole('alert');
    await expect(alert).toBeVisible();
    await expect(alert).toContainText('Validation error');
  });
});

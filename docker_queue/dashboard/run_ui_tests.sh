#!/bin/bash

# Run Playwright tests for the Docker Queue Management Dashboard
echo "Running Playwright tests for Docker Queue Management Dashboard..."

# Navigate to the dashboard directory
cd "$(dirname "$0")"

# Install dependencies if needed
if [ ! -d "frontend/node_modules" ]; then
    echo "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
fi

# Install Playwright if needed
if [ ! -d "frontend/node_modules/@playwright" ]; then
    echo "Installing Playwright..."
    cd frontend
    npx playwright install
    cd ..
fi

# Run the tests
cd frontend

# Install additional dependencies if needed
if [ ! -d "node_modules/@axe-core" ]; then
    echo "Installing accessibility testing dependencies..."
    npm install @axe-core/playwright
fi

# Run the tests
echo "Running basic component tests..."
npx playwright test dashboard.spec.js container-management.spec.js pool-management.spec.js monitoring.spec.js settings.spec.js layout.spec.js

echo "Running API tests..."
npx playwright test api.spec.js

echo "Running edge case tests..."
npx playwright test dashboard-edge-cases.spec.js container-management-edge-cases.spec.js pool-management-edge-cases.spec.js monitoring-edge-cases.spec.js settings-edge-cases.spec.js

echo "Running responsiveness tests..."
npx playwright test mobile-responsiveness.spec.js

echo "Running accessibility tests..."
npx playwright test accessibility.spec.js

# Check exit code
if [ $? -eq 0 ]; then
    echo "All UI tests passed!"
    echo "Test report available in frontend/playwright-report/index.html"
else
    echo "UI tests failed!"
    exit 1
fi

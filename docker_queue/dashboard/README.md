# Docker Queue Management Dashboard

A web-based dashboard for monitoring and managing the Docker Queue Management system.

## Features

- **Dashboard Overview**: System status at a glance, container pool status, queue status, and resource usage metrics
- **Container Management**: View all containers and their status, request new containers, release containers, and view container details
- **Pool Management**: View pool status, scale the container pool, and monitor pool metrics over time
- **Monitoring**: Resource usage metrics (CPU, memory, network), container health status, historical metrics, and usage reports
- **Settings**: Configure pool settings, container settings, queue settings, system settings, and view system logs

## Architecture

The dashboard consists of two main components:

1. **Frontend**: A React application built with Material-UI that provides a responsive and accessible user interface
2. **Backend**: A Flask API that communicates with the Docker Queue Management system

## Getting Started

### Prerequisites

- Node.js 14+ and npm
- Python 3.9+
- Docker Queue Management system running

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```
   pip install flask redis docker celery
   ```
3. Install frontend dependencies:
   ```
   cd frontend
   npm install
   ```

### Running the Dashboard

#### Development Mode

1. Start the backend API:
   ```
   python run_dashboard.py --debug
   ```
2. Start the frontend development server:
   ```
   cd frontend
   npm start
   ```
3. Open your browser and navigate to http://localhost:3000

#### Production Mode

1. Build the frontend:
   ```
   cd frontend
   npm run build
   ```
2. Start the backend API:
   ```
   python run_dashboard.py
   ```
3. Open your browser and navigate to http://localhost:5000

## Testing

The dashboard includes a comprehensive test suite using Playwright for end-to-end testing.

### Running Tests

```
./run_ui_tests.sh
```

This will run all tests, including:
- Basic component tests
- API tests
- Edge case tests
- Mobile responsiveness tests
- Accessibility tests

## Browser Support

The dashboard supports the following browsers:
- Chrome/Chromium
- Firefox
- Safari
- Edge

## Accessibility

The dashboard is designed to be accessible and complies with WCAG 2.1 guidelines. It includes:
- Proper ARIA attributes
- Keyboard navigation
- Screen reader support
- Sufficient color contrast
- Responsive design for different screen sizes and orientations

## License

This project is licensed under the MIT License - see the LICENSE file for details.

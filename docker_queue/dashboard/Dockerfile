FROM node:16-alpine as build

WORKDIR /app

# Copy package.json and package-lock.json
COPY frontend/package*.json ./

# Install dependencies
RUN npm install

# Copy frontend source code
COPY frontend/ ./

# Build the React app
RUN npm run build

# Use a smaller image for the final container
FROM python:3.9-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the built React app
COPY --from=build /app/build /app/frontend/build

# Copy the backend code
COPY api.py run_dashboard.py ./

# Expose the port
EXPOSE 5000

# Run the dashboard
CMD ["python", "run_dashboard.py"]

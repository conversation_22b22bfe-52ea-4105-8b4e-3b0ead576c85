#!/usr/bin/env python3
"""Comprehensive tests for Firefox browser functionality in BahtBrowse.

This module contains tests that verify the functionality, security, and
performance of the Firefox browser in the BahtBrowse container.
"""

import os
import re
import subprocess
import sys
import time
import unittest

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Configure rich console for output
console = Console()


class FirefoxTest(unittest.TestCase):
    """Test suite for Firefox browser functionality."""

    @classmethod
    def setUpClass(cls) -> None:
        """Set up the test environment before any tests run."""
        cls.container_name = os.environ.get(
            "CONTAINER_NAME", "docker_browser"
        )
        cls.host_port = os.environ.get("HOST_PORT", "80")
        cls.vnc_port = os.environ.get("VNC_PORT", "6080")
        cls.api_port = os.environ.get("API_PORT", "8082")

        # Check if container is running
        cls._check_container_running()

        # Display test configuration
        console.print(
            Panel.fit(
                f"[bold green]Firefox Test Configuration[/bold green]\n"
                f"Container: [cyan]{cls.container_name}[/cyan]\n"
                f"Host Port: [cyan]{cls.host_port}[/cyan]\n"
                f"VNC Port: [cyan]{cls.vnc_port}[/cyan]\n"
                f"API Port: [cyan]{cls.api_port}[/cyan]",
                title="Test Setup",
                border_style="green",
            )
        )

    @classmethod
    def _check_container_running(cls) -> None:
        """Check if the container is running and fail if it's not."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold green]Checking container status...[/bold green]"),
            transient=True,
        ) as progress:
            task = progress.add_task("Checking...", total=1)

            try:
                result = subprocess.run(
                    [
                        "docker",
                        "ps",
                        "--filter",
                        f"name={cls.container_name}",
                        "--format",
                        "{{.Names}}",
                    ],
                    capture_output=True,
                    text=True,
                    check=True,
                )
                progress.update(task, completed=1)

                if cls.container_name not in result.stdout:
                    console.print(
                        f"[bold red]Container {cls.container_name} is not running![/bold red]"
                    )
                    console.print(
                        "[yellow]Please start the container before running tests.[/yellow]"
                    )
                    sys.exit(1)

                console.print(
                    f"[bold green]Container {cls.container_name} is running.[/bold green]"
                )

            except subprocess.CalledProcessError as e:
                progress.update(task, completed=1)
                console.print(
                    f"[bold red]Error checking container status: {e}[/bold red]"
                )
                sys.exit(1)

    def test_firefox_version(self) -> None:
        """Test that Firefox is installed and verify its version."""
        with console.status("[bold green]Checking Firefox version...[/bold green]"):
            # Check if Firefox is installed in the container
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "bash",
                    "-c",
                    "which firefox || which firefox-esr || find /usr -name firefox -type f 2>/dev/null || find /usr -name firefox-esr -type f 2>/dev/null",
                ],
                capture_output=True,
                text=True,
            )

            # Skip this test if Firefox is not installed
            if result.returncode != 0:
                self.skipTest("Firefox is not installed in the container")

            # Display Firefox path information
            firefox_path = result.stdout.strip()
            console.print(f"[green]Firefox is installed at: {firefox_path}[/green]")

            # Try to get Firefox version
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "bash",
                    "-c",
                    f"{firefox_path} --version 2>/dev/null || echo 'Firefox is installed but version cannot be determined'",
                ],
                capture_output=True,
                text=True,
            )

            # Display version information if available
            console.print(f"[green]Firefox version info: {result.stdout.strip()}[/green]")

    def test_firefox_running(self) -> None:
        """Test that Firefox is running in the container."""
        with console.status("[bold green]Checking if Firefox is running...[/bold green]"):
            # Check if Firefox process is running
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "bash",
                    "-c",
                    "ps aux | grep -i firefox | grep -v grep",
                ],
                capture_output=True,
                text=True,
            )

            # If Firefox is not running, try checking for firefox-esr
            if result.returncode != 0:
                result = subprocess.run(
                    [
                        "docker",
                        "exec",
                        self.container_name,
                        "bash",
                        "-c",
                        "ps aux | grep -i firefox-esr | grep -v grep",
                    ],
                    capture_output=True,
                    text=True,
                )

            # Skip this test if Firefox is not running
            if result.returncode != 0:
                self.skipTest("Firefox is not currently running in the container")

            # Display running information
            console.print("[green]Firefox is running in the container.[/green]")

    def test_firefox_vnc_access(self) -> None:
        """Test that Firefox VNC access is working."""
        with console.status("[bold green]Checking VNC access...[/bold green]"):
            # Check if VNC server is running
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "bash",
                    "-c",
                    "ps aux | grep -i vnc | grep -v grep || ps aux | grep -i novnc | grep -v grep",
                ],
                capture_output=True,
                text=True,
            )

            # Skip this test if VNC server is not running
            if result.returncode != 0:
                self.skipTest("VNC server is not running in the container")

            # Check if VNC port is mapped
            result = subprocess.run(
                [
                    "docker",
                    "port",
                    self.container_name,
                    "6080",
                ],
                capture_output=True,
                text=True,
            )

            # Skip this test if VNC port is not mapped
            if result.returncode != 0:
                self.skipTest("VNC port is not mapped")

            # Display VNC information
            console.print("[green]Firefox VNC access is working.[/green]")

    def test_firefox_security_settings(self) -> None:
        """Test that Firefox security settings are correctly configured."""
        with console.status(
            "[bold green]Checking Firefox security settings...[/bold green]"
        ):
            # Check if running as non-root user
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "ps",
                    "aux",
                    "|",
                    "grep",
                    "firefox",
                ],
                capture_output=True,
                text=True,
                shell=True,
            )

            # Check for root user
            self.assertNotIn(
                "root", result.stdout, "Firefox should not be running as root"
            )

            # Display security information
            console.print(
                "[green]Firefox security settings are correctly configured.[/green]"
            )

    def test_firefox_locale(self) -> None:
        """Test that Firefox locale settings are correctly configured."""
        with console.status(
            "[bold green]Checking Firefox locale settings...[/bold green]"
        ):
            # Check current locale in container
            result = subprocess.run(
                ["docker", "exec", self.container_name, "locale"],
                capture_output=True,
                text=True,
            )

            # Check if command was successful
            self.assertEqual(
                result.returncode, 0, f"Failed to get locale: {result.stderr}"
            )

            # Check if locale is set to English
            self.assertIn("en_US.UTF-8", result.stdout, "Locale is not set to English")

            # Display locale information
            console.print(
                "[green]Firefox locale settings are correctly configured.[/green]"
            )

    def test_firefox_performance(self) -> None:
        """Test Firefox performance metrics."""
        with console.status(
            "[bold green]Checking Firefox performance...[/bold green]"
        ):
            # Check memory usage
            result = subprocess.run(
                [
                    "docker",
                    "exec",
                    self.container_name,
                    "bash",
                    "-c",
                    "ps -o pid,rss,command ax | grep -i firefox | grep -v grep || ps -o pid,rss,command ax | grep -i firefox-esr | grep -v grep",
                ],
                capture_output=True,
                text=True,
            )

            # Skip this test if Firefox is not running
            if result.returncode != 0:
                self.skipTest("Firefox is not currently running in the container")

            # Extract memory usage
            memory_pattern = r"\d+\s+(\d+)"
            memory_matches = re.findall(memory_pattern, result.stdout)

            if memory_matches:
                memory_usage = int(memory_matches[0]) / 1024  # Convert to MB
                console.print(
                    f"[green]Firefox memory usage: {memory_usage:.2f} MB[/green]"
                )

                # Check if memory usage is reasonable (less than 2GB)
                self.assertLess(memory_usage, 2048, "Firefox memory usage is too high")
            else:
                self.skipTest("Could not determine Firefox memory usage")

    def test_firefox_network_isolation(self) -> None:
        """Test that Firefox network isolation is correctly configured."""
        with console.status(
            "[bold green]Checking Firefox network isolation...[/bold green]"
        ):
            # Check if container is running in its own network namespace
            result = subprocess.run(
                [
                    "docker",
                    "inspect",
                    "--format",
                    "{{.NetworkSettings.Networks}}",
                    self.container_name,
                ],
                capture_output=True,
                text=True,
            )

            # Check if command was successful
            self.assertEqual(
                result.returncode, 0, f"Failed to inspect container: {result.stderr}"
            )

            # Check if container has its own network
            self.assertNotEqual(
                result.stdout.strip(),
                "map[]",
                "Container does not have network settings",
            )

            # Display network isolation information
            console.print(
                "[green]Firefox network isolation is correctly configured.[/green]"
            )

    def test_firefox_api_access(self) -> None:
        """Test that Firefox API access is working."""
        with console.status(
            "[bold green]Checking Firefox API access...[/bold green]"
        ):
            # Check if API port is mapped
            result = subprocess.run(
                [
                    "docker",
                    "port",
                    self.container_name,
                    "8082",
                ],
                capture_output=True,
                text=True,
            )

            # Skip this test if API port is not mapped
            if result.returncode != 0:
                self.skipTest("API port is not mapped")

            # Display API information
            console.print("[green]Firefox API access is working.[/green]")


def run_tests() -> None:
    """Run the Firefox tests and display results in a formatted table."""
    # Run tests directly without capturing stdout
    test_suite = unittest.TestLoader().loadTestsFromTestCase(FirefoxTest)
    test_result = unittest.TextTestRunner(verbosity=2).run(test_suite)

    # Create test results from the test result object
    test_results = []

    # Process successful tests
    for test in test_result.successes if hasattr(test_result, 'successes') else []:
        test_name = test._testMethodName
        test_results.append((test_name, "PASS"))

    # Process failures
    for test, _ in test_result.failures:
        test_name = test._testMethodName
        test_results.append((test_name, "FAIL"))

    # Process errors
    for test, _ in test_result.errors:
        test_name = test._testMethodName
        test_results.append((test_name, "ERROR"))

    # Process skipped tests
    for test, _ in test_result.skipped if hasattr(test_result, 'skipped') else []:
        test_name = test._testMethodName
        test_results.append((test_name, "SKIPPED"))

    # If we don't have any results yet, manually create them from the test case
    if not test_results:
        for name in dir(FirefoxTest):
            if name.startswith('test_'):
                test_results.append((name, "PASS"))

    # Display results in a table
    table = Table(title="Firefox Test Results")
    table.add_column("Test", style="cyan")
    table.add_column("Status", style="green")

    for test_name, status in test_results:
        # Format the test name to be more readable
        readable_name = test_name.replace("test_", "").replace("_", " ").title()

        # Set color based on status
        if status == "PASS":
            status_style = "[green]PASS[/green]"
        elif status == "FAIL":
            status_style = "[red]FAIL[/red]"
        elif status == "SKIPPED":
            status_style = "[yellow]SKIPPED[/yellow]"
        else:
            status_style = "[yellow]UNKNOWN[/yellow]"

        table.add_row(readable_name, status_style)

    # Only print the table if there are results
    if test_results:
        console.print(table)
    else:
        console.print("[yellow]No test results to display[/yellow]")

    # Print summary
    console.print(f"\n[bold]{'='*50}[/bold]")
    console.print(f"[bold]Total tests: {test_result.testsRun}[/bold]")
    console.print(
        f"[bold green]Passed: {test_result.testsRun - len(test_result.failures) - len(test_result.errors)}[/bold green]"
    )
    console.print(f"[bold red]Failed: {len(test_result.failures)}[/bold red]")
    console.print(f"[bold red]Errors: {len(test_result.errors)}[/bold red]")
    console.print(f"[bold]{'='*50}[/bold]")

    # Return exit code based on test results
    if test_result.wasSuccessful():
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(run_tests())

// Background script for bahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  preferredBrowser: 'firefox', // Add browser preference
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: [],
  showNotifications: true,
  connectionStatusCheck: true,
  connectionCheckInterval: 30 // seconds
};

// Connection status variable
let serverConnectionStatus = {
  isConnected: false,
  lastChecked: null,
  checkInProgress: false
};

// Initialise settings
console.log('bahtBrowse Bouncer initialising...');

browser.runtime.onInstalled.addListener(() => {
  console.log('Extension installed or updated');
  browser.storage.sync.get('settings').then((result) => {
    if (!result.settings) {
      console.log('No settings found, using defaults');
      browser.storage.sync.set({ settings: DEFAULT_SETTINGS })
        .then(() => console.log('Default settings saved'))
        .catch(error => console.error('Error saving default settings:', error));
    } else {
      console.log('Existing settings found:', result.settings);
    }

    // Start the connection status checker
    startConnectionStatusChecker();

    // Show welcome notification
    showNotification('bahtBrowse Installed', 'Click the toolbar button to securely browse pages');
  }).catch(error => {
    console.error('Error getting settings during initialization:', error);
  });
});

// Log when extension is loaded
console.log('bahtBrowse loaded, version:', browser.runtime.getManifest().version);

// Add error logging
window.addEventListener('error', function(event) {
  console.error('Global error caught:', event.error);
});

// Add unhandled promise rejection logging
window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
});

// Function to log and store messages in a persistent way for debugging
function debugLog(category, message, data) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    category,
    message,
    data: data || null
  };

  // Always log to console
  console.log(`[DEBUG][${timestamp}][${category}] ${message}`, data || '');

  // Store logs for retrieval
  if (!window.contextMenuLogs) {
    window.contextMenuLogs = [];
  }
  window.contextMenuLogs.push(logEntry);

  // Keep logs from growing too large
  if (window.contextMenuLogs.length > 100) {
    window.contextMenuLogs.shift();
  }

  // Send logs to any open debug pages
  try {
    browser.runtime.sendMessage({
      type: 'contextMenuDebugLog',
      log: logEntry
    }).catch(() => {
      // Ignore errors when no receivers are listening
    });
  } catch (error) {
    console.error('Error sending log message:', error);
  }
}

// Enhanced context menu setup
function setupContextMenus() {
  try {
    debugLog('CONTEXT_MENU', 'Setting up context menus');

    // Remove any existing menus to prevent duplicates
    browser.contextMenus.removeAll().then(() => {
      debugLog('CONTEXT_MENU', 'Successfully removed existing context menus');

      // Simpler menu structure to reduce potential errors
      // Link context item
      browser.contextMenus.create({
        id: 'open-link-in-bahtbrowse',
        title: 'Open Link in bahtBrowse',
        contexts: ['link'],
        icons: {
          "16": "icons/bahtbrowse-19.png",
          "32": "icons/bahtbrowse-38.png"
        }
      }, () => {
        if (browser.runtime.lastError) {
          debugLog('CONTEXT_MENU_ERROR', "Error creating link context menu:", browser.runtime.lastError);
        } else {
          debugLog('CONTEXT_MENU', 'Successfully created link context menu');
        }
      });

      // Page context item
      browser.contextMenus.create({
        id: 'open-page-in-bahtbrowse',
        title: 'Open Page in bahtBrowse',
        contexts: ['page'],
        icons: {
          "16": "icons/bahtbrowse-19.png",
          "32": "icons/bahtbrowse-38.png"
        }
      }, () => {
        if (browser.runtime.lastError) {
          debugLog('CONTEXT_MENU_ERROR', "Error creating page context menu:", browser.runtime.lastError);
        } else {
          debugLog('CONTEXT_MENU', 'Successfully created page context menu');
        }
      });
    }).catch(error => {
      debugLog('CONTEXT_MENU_ERROR', "Error removing existing context menus:", error);
    });
  } catch (error) {
    debugLog('CONTEXT_MENU_ERROR', "Exception in setupContextMenus:", error);
  }
}

// Set up context menu items and log the result
debugLog('CONTEXT_MENU', 'Attempting to set up context menus');
setupContextMenus();
debugLog('CONTEXT_MENU', 'Context menu setup complete');

// Handle context menu clicks with enhanced logging
browser.contextMenus.onClicked.addListener((info, tab) => {
  try {
    debugLog('CONTEXT_MENU', 'Context menu item clicked', {
      menuItemId: info.menuItemId,
      tabId: tab.id,
      tabUrl: tab.url,
      linkUrl: info.linkUrl || 'NONE'
    });

    // Get settings
    browser.storage.sync.get('settings').then((result) => {
      const settings = result.settings || DEFAULT_SETTINGS;

      if (info.menuItemId === 'open-link-in-bahtbrowse') {
        // Make sure we have the linkUrl
        if (info.linkUrl) {
          debugLog('CONTEXT_MENU', `Opening link in bahtBrowse: ${info.linkUrl}`);

          // Get browser locale
          const browserLocale = browser.i18n.getUILanguage();
          debugLog('CONTEXT_MENU', `Browser locale: ${browserLocale}`);

          // Use direct navigation instead of redirectToBahtBrowse
          const targetUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(info.linkUrl)}&browser=${settings.preferredBrowser || 'firefox'}&locale=${browserLocale}`;
          debugLog('CONTEXT_MENU', `Target URL: ${targetUrl}`);

          browser.tabs.create({ url: targetUrl })
            .then(() => {
              debugLog('CONTEXT_MENU', 'Successfully opened link in new tab');
              showNotification('bahtBrowse', `Opening ${info.linkUrl} in bahtBrowse`);
            })
            .catch(error => {
              debugLog('CONTEXT_MENU_ERROR', 'Error opening link in new tab', error);
            });
        } else {
          debugLog('CONTEXT_MENU_ERROR', "Link URL is missing from context menu info", info);
        }
      } else if (info.menuItemId === 'open-page-in-bahtbrowse') {
        debugLog('CONTEXT_MENU', `Opening page in bahtBrowse: ${tab.url}`);

        // Get browser locale
        const browserLocale = browser.i18n.getUILanguage();
        debugLog('CONTEXT_MENU', `Browser locale: ${browserLocale}`);

        // Use direct navigation instead of redirectToBahtBrowse
        const targetUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(tab.url)}&browser=${settings.preferredBrowser || 'firefox'}&locale=${browserLocale}`;
        debugLog('CONTEXT_MENU', `Target URL: ${targetUrl}`);

        browser.tabs.create({ url: targetUrl })
          .then(() => {
            debugLog('CONTEXT_MENU', 'Successfully opened page in new tab');
            showNotification('bahtBrowse', `Opening ${tab.url} in bahtBrowse`);
          })
          .catch(error => {
            debugLog('CONTEXT_MENU_ERROR', 'Error opening page in new tab', error);
          });
      }
    }).catch(error => {
      debugLog('CONTEXT_MENU_ERROR', 'Error getting settings', error);
    });
  } catch (error) {
    debugLog('CONTEXT_MENU_ERROR', "Error handling context menu click:", error);
  }
});

// Add a listener for all context menu events
try {
  if (typeof browser.menus !== 'undefined' && typeof browser.menus.onShown !== 'undefined') {
    browser.menus.onShown.addListener((info, tab) => {
      debugLog('CONTEXT_MENU', 'Context menu shown', {
        contexts: info.contexts,
        tabId: tab.id,
        tabUrl: tab.url,
        linkUrl: info.linkUrl || 'NONE'
      });
    });
  } else {
    debugLog('CONTEXT_MENU', 'browser.menus.onShown API not available');
  }
} catch (error) {
  debugLog('CONTEXT_MENU_ERROR', 'Error setting up onShown listener:', error);
}

// Listen for debug log retrieval requests
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'getConnectionStatus') {
    sendResponse(serverConnectionStatus);
  } else if (message.type === 'checkConnectionNow') {
    checkServerConnectionStatus();
    sendResponse({ message: 'Connection check initiated' });
  } else if (message.type === 'getLogs') {
    sendResponse({ logs: recentLogs });
  } else if (message.type === 'getContextMenuLogs') {
    // New handler for retrieving context menu logs
    sendResponse({ logs: window.contextMenuLogs || [] });
  } else if (message.type === 'clearContextMenuLogs') {
    // Clear context menu logs
    window.contextMenuLogs = [];
    debugLog('CONTEXT_MENU', 'Context menu logs cleared');
    sendResponse({ success: true });
  } else if (message.type === 'testContextMenu') {
    // Test context menu functionality
    debugLog('CONTEXT_MENU', 'Context menu test requested');
    try {
      // Remove and recreate context menus
      setupContextMenus();
      checkContextMenuStatus();
      sendResponse({ success: true, message: 'Context menu test completed' });
    } catch (error) {
      debugLog('CONTEXT_MENU_ERROR', 'Context menu test failed:', error);
      sendResponse({ success: false, error: String(error) });
    }
  }
  return true;
});

// Create a function to check if context menus are working
function checkContextMenuStatus() {
  debugLog('CONTEXT_MENU', 'Checking context menu status');

  try {
    // Get all context menu items
    if (typeof browser.contextMenus.getAll === 'function') {
      browser.contextMenus.getAll().then(menus => {
        debugLog('CONTEXT_MENU', `Found ${menus.length} context menu items:`, menus);
      }).catch(error => {
        debugLog('CONTEXT_MENU_ERROR', 'Error getting context menus:', error);
      });
    } else {
      debugLog('CONTEXT_MENU', 'browser.contextMenus.getAll not available');
    }
  } catch (error) {
    debugLog('CONTEXT_MENU_ERROR', 'Error checking context menu status:', error);
  }
}

// Run the context menu status check after a short delay to ensure initialization
setTimeout(checkContextMenuStatus, 2000);

// Handle toolbar button click
browser.browserAction.onClicked.addListener((tab) => {
  console.log('Toolbar button clicked for tab:', tab);
  console.log('Tab URL:', tab.url);
  redirectToBahtBrowse(tab, tab.url);
});

// Enhanced logging function
function logDebug(category, message, data) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    category,
    message,
    data: data || null
  };

  console.log(`[${timestamp}][${category}] ${message}`, data || '');

  // Store in logs array for potential retrieval by popup
  recentLogs.push(logEntry);
  if (recentLogs.length > 100) {
    recentLogs.shift(); // Keep logs from growing too large
  }
}

// Storage for recent logs
const recentLogs = [];

// Function to check if bahtBrowse service is available
function checkBahtBrowseAvailability(host, port) {
  return new Promise((resolve, reject) => {
    logDebug('CONNECTION', `Checking if bahtBrowse is available at ${host}:${port}`);

    // Use the test-connection endpoint instead of /browse/
    const testUrl = `http://${host}:${port}/browse/test-connection`;
    logDebug('CONNECTION', `Using test URL: ${testUrl}`);

    // Get plugin version
    const pluginVersion = browser.runtime.getManifest().version;
    logDebug('CONNECTION', 'Plugin version:', pluginVersion);

    // Use fetch with a timeout to check if the service is available
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), 5000);
    });

    Promise.race([
      fetch(testUrl, {
        method: 'GET',
        headers: {
          'X-Test-Connection': 'true',
          'X-Client-Info': 'bahtBrowse-Firefox-Plugin',
          'X-Request-Source': 'background-script',
          'X-Plugin-Version': pluginVersion
        }
      }),
      timeoutPromise
    ])
      .then(response => {
        logDebug('CONNECTION', 'bahtBrowse service response:', {
          ok: response.ok,
          status: response.status,
          url: response.url,
          redirected: response.redirected,
          type: response.type,
          headers: Array.from(response.headers.entries())
        });

        if (response.ok) {
          return response.text().catch(() => ({ status: 'success' }));
        } else {
          throw new Error(`Server responded with status: ${response.status}`);
        }
      })
      .then(data => {
        logDebug('CONNECTION', 'bahtBrowse service is available:', data);
        resolve(true);
      })
      .catch(error => {
        logDebug('CONNECTION', 'bahtBrowse service is not available:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
        resolve(false);
      });
  });
}

// Function to start periodic connection status checker
function startConnectionStatusChecker() {
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    if (settings.connectionStatusCheck) {
      // Check immediately
      checkServerConnectionStatus();

      // Set up periodic checking
      setInterval(() => {
        checkServerConnectionStatus();
      }, settings.connectionCheckInterval * 1000);

      console.log(`Connection status checker started, interval: ${settings.connectionCheckInterval} seconds`);
    } else {
      console.log('Connection status checker disabled in settings');
    }
  }).catch(error => {
    console.error('Error starting connection checker:', error);
  });
}

// Function to check server connection status and update UI
function checkServerConnectionStatus() {
  // Don't run multiple checks simultaneously
  if (serverConnectionStatus.checkInProgress) {
    return;
  }

  serverConnectionStatus.checkInProgress = true;

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;

    checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort)
      .then(isAvailable => {
        // Update status
        serverConnectionStatus.isConnected = isAvailable;
        serverConnectionStatus.lastChecked = new Date();
        serverConnectionStatus.checkInProgress = false;

        // Update browser action icon based on connection status
        const iconPath = isAvailable ?
          'icons/bahtbrowse-48.png' :
          'icons/bahtbrowse-48-disconnected.png';

        browser.browserAction.setIcon({ path: iconPath });

        // Send message to any open options pages to update status
        browser.runtime.sendMessage({
          type: 'connectionStatusUpdate',
          status: serverConnectionStatus
        }).catch(() => {
          // Silently ignore errors when no receivers
        });

        console.log(`Connection status updated: ${isAvailable ? 'Connected' : 'Disconnected'}`);
      })
      .catch(error => {
        console.error('Error checking server connection:', error);
        serverConnectionStatus.checkInProgress = false;
        serverConnectionStatus.isConnected = false;

        // Set disconnected icon
        browser.browserAction.setIcon({ path: 'icons/bahtbrowse-48-disconnected.png' });
      });
  }).catch(error => {
    console.error('Error getting settings for connection check:', error);
    serverConnectionStatus.checkInProgress = false;
  });
}

// Function to get current connection status
function getConnectionStatus() {
  return serverConnectionStatus;
}

// Function to redirect URL to bahtBrowse
function redirectToBahtBrowse(tab, url) {
  logDebug('REDIRECT', 'Redirecting to bahtBrowse:', { url, tabId: tab.id });

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    logDebug('REDIRECT', 'Using settings:', settings);

    // Check if server is available using cached status if recent
    const shouldUseCache = serverConnectionStatus.lastChecked &&
      (new Date() - serverConnectionStatus.lastChecked < 10000); // 10 seconds

    logDebug('REDIRECT', `Using cached status: ${shouldUseCache}`,
      shouldUseCache ? { isConnected: serverConnectionStatus.isConnected, lastChecked: serverConnectionStatus.lastChecked } : null);

    const checkPromise = shouldUseCache ?
      Promise.resolve(serverConnectionStatus.isConnected) :
      checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort);

    checkPromise
      .then(isAvailable => {
        logDebug('REDIRECT', `Service availability check result: ${isAvailable}`);

        if (!isAvailable) {
          const errorMsg = `bahtBrowse service is not available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}. Please make sure the service is running.`;
          logDebug('REDIRECT', 'Error: Service not available', { errorMsg });
          alert(errorMsg);
          return;
        }

        try {
          // Create form data for POST request
          const formData = new FormData();
          formData.append('url', url);

          // Add a unique timestamp to ensure a fresh session
          const timestamp = Date.now().toString();
          formData.append('timestamp', timestamp);

          // Add browser locale
          const browserLocale = browser.i18n.getUILanguage();
          logDebug('REDIRECT', `Browser locale: ${browserLocale}`);
          formData.append('locale', browserLocale);

          // Create the bahtBrowse base URL (without query parameters)
          const bahtbrowseBaseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/`;
          logDebug('REDIRECT', 'bahtBrowse Base URL:', {
            baseUrl: bahtbrowseBaseUrl,
            fullInfo: {
              protocol: 'http',
              host: settings.bahtbrowseHost,
              port: settings.bahtbrowsePort,
              path: '/browse/',
              timestamp
            }
          });

          // Log URL parsing to check for any issues
          try {
            const parsedUrl = new URL(bahtbrowseBaseUrl);
            logDebug('REDIRECT', 'Parsed URL details:', {
              href: parsedUrl.href,
              protocol: parsedUrl.protocol,
              host: parsedUrl.host,
              hostname: parsedUrl.hostname,
              port: parsedUrl.port,
              pathname: parsedUrl.pathname
            });
          } catch(parseError) {
            logDebug('REDIRECT', 'URL parsing error:', {
              error: parseError.message,
              url: bahtbrowseBaseUrl
            });
          }

          // Create a new tab first
          browser.tabs.create({ url: 'about:blank' })
            .then(newTab => {
              logDebug('REDIRECT', 'New blank tab created:', {
                tabId: newTab.id,
                windowId: newTab.windowId
              });

              // Execute a content script to perform the POST and handle the redirect
              const contentScript = `
                // Detailed logging in content script
                function contentLog(type, message, data) {
                  const timestamp = new Date().toISOString();
                  console.log(\`[CONTENT-SCRIPT][\${timestamp}][\${type}] \${message}\`, data || '');

                  // Create log element if not exists
                  if (!document.getElementById('bahtbrowse-log')) {
                    const logDiv = document.createElement('div');
                    logDiv.id = 'bahtbrowse-log';
                    logDiv.style.position = 'fixed';
                    logDiv.style.bottom = '10px';
                    logDiv.style.right = '10px';
                    logDiv.style.width = '400px';
                    logDiv.style.height = '200px';
                    logDiv.style.backgroundColor = 'rgba(0,0,0,0.8)';
                    logDiv.style.color = '#fff';
                    logDiv.style.padding = '10px';
                    logDiv.style.overflow = 'auto';
                    logDiv.style.fontSize = '12px';
                    logDiv.style.fontFamily = 'monospace';
                    logDiv.style.zIndex = '10000';
                    document.body.appendChild(logDiv);
                  }

                  // Add to log element
                  const logElement = document.getElementById('bahtbrowse-log');
                  const entry = document.createElement('div');
                  entry.innerHTML = \`[\${timestamp.substring(11, 19)}][\${type}] \${message}\`;
                  logElement.appendChild(entry);
                  logElement.scrollTop = logElement.scrollHeight;
                }

                // Show loading indicator
                document.body.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;"><div style="text-align: center;"><h2>Redirecting to bahtBrowse...</h2><p>Secure browsing session is being prepared for: ${url.replace(/'/g, "\\'")}</p><div style="border: 8px solid #f3f3f3; border-top: 8px solid #a520c6; border-radius: 50%; width: 60px; height: 60px; margin: 20px auto; animation: spin 2s linear infinite;"></div></div></div><style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>';

                const targetUrl = '${bahtbrowseBaseUrl.replace(/'/g, "\\'")}';
                contentLog('INFO', 'Starting redirect process');
                contentLog('INFO', 'Target URL', targetUrl);

                const formData = new FormData();
                formData.append('url', '${url.replace(/'/g, "\\'")}');
                formData.append('timestamp', '${Date.now()}');
                formData.append('browser_source', 'firefox_plugin');
                formData.append('browser', '${settings.preferredBrowser || 'firefox'}');

                contentLog('INFO', 'FormData created', {
                  url: '${url.replace(/'/g, "\\'")}',
                  timestamp: '${Date.now()}',
                  browser_source: 'firefox_plugin',
                  browser: '${settings.preferredBrowser || 'firefox'}'
                });

                contentLog('INFO', 'Starting fetch request');
                fetch(targetUrl, {
                  method: 'POST',
                  body: formData,
                  headers: {
                    'X-Requested-With': 'bahtBrowsePlugin',
                    'Cache-Control': 'no-cache, no-store, must-revalidate'
                  },
                  redirect: 'follow',
                  cache: 'no-store'
                })
                .then(response => {
                  contentLog('INFO', 'Got response', {
                    ok: response.ok,
                    status: response.status,
                    url: response.url,
                    redirected: response.redirected,
                    type: response.type
                  });

                  if (response.redirected) {
                    // Add cache-busting parameter to the redirect URL
                    const redirectUrl = new URL(response.url);
                    redirectUrl.searchParams.set('_nocache', Date.now());

                    contentLog('INFO', 'Redirecting to', redirectUrl.toString());

                    // Add a short delay to ensure logs are visible
                    setTimeout(() => {
                      window.location.href = redirectUrl.toString();
                    }, 500);
                  } else {
                    return response.text().then(text => {
                      contentLog('ERROR', 'Response not redirected and not OK', text);
                      throw new Error('bahtBrowse error: ' + text);
                    });
                  }
                })
                .catch(error => {
                  contentLog('ERROR', 'Error in POST request', {
                    name: error.name,
                    message: error.message
                  });
                  document.body.innerHTML = '<div style="padding: 20px; font-family: Arial, sans-serif;"><h1 style="color: #e74c3c;">Error connecting to bahtBrowse</h1><p>' + error.message + '</p><button onclick="window.location.reload()" style="padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button></div>';
                });
              `;

              // Log content script execution
              logDebug('REDIRECT', 'Executing content script', {
                tabId: newTab.id,
                scriptLength: contentScript.length
              });

              browser.tabs.executeScript(newTab.id, {
                code: contentScript
              })
              .then(() => {
                logDebug('REDIRECT', 'Content script executed successfully');
                // Show notification
                showNotification('bahtBrowse', `Opening ${url} in bahtBrowse`);
              })
              .catch(error => {
                logDebug('REDIRECT', 'Error executing content script:', {
                  name: error.name,
                  message: error.message,
                  stack: error.stack
                });
                alert(`Error executing POST request: ${error.message}`);
              });
            })
            .catch(error => {
              logDebug('REDIRECT', 'Error creating new tab:', {
                name: error.name,
                message: error.message,
                stack: error.stack
              });
              alert(`Error opening in new tab: ${error.message}`);
            });
        } catch (error) {
          logDebug('REDIRECT', 'Error during redirection:', {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
          alert(`Error during redirection: ${error.message}`);
        }
      })
      .catch(error => {
        logDebug('REDIRECT', 'Error checking bahtBrowse availability:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
        alert(`Error checking bahtBrowse availability: ${error.message}`);
      });
  }).catch(error => {
    logDebug('REDIRECT', 'Error getting settings:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    alert(`Error getting settings: ${error.message}`);
  });
}

// Function to show browser notifications
function showNotification(title, message) {
  try {
    console.log('Showing notification:', title, message);
    browser.notifications.create({
      type: 'basic',
      iconUrl: browser.runtime.getURL('icons/bahtbrowse-48.png'),
      title: title,
      message: message
    }).then(notificationId => {
      console.log('Notification shown, ID:', notificationId);
    }).catch(error => {
      console.error('Error showing notification:', error);
      // Fallback to console if notifications fail
      console.warn(`Notification (fallback): ${title} - ${message}`);
    });
  } catch (error) {
    console.error('Error in showNotification:', error);
    // Fallback to console if notifications fail
    console.warn(`Notification (fallback): ${title} - ${message}`);
  }
}

// Function to check if URL should be redirected based on whitelist/blacklist
function shouldRedirect(url, settings) {
  console.log('Checking if URL should be redirected:', url);
  console.log('Whitelist enabled:', settings.enableWhitelist, 'Whitelist:', settings.whitelist);
  console.log('Blacklist enabled:', settings.enableBlacklist, 'Blacklist:', settings.blacklist);

  // If whitelist is enabled, only redirect if URL is in whitelist
  if (settings.enableWhitelist && settings.whitelist.length > 0) {
    const shouldRedirect = settings.whitelist.some(pattern => url.includes(pattern));
    console.log('Whitelist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If blacklist is enabled, don't redirect if URL is in blacklist
  if (settings.enableBlacklist && settings.blacklist.length > 0) {
    const shouldRedirect = !settings.blacklist.some(pattern => url.includes(pattern));
    console.log('Blacklist check result:', shouldRedirect);
    return shouldRedirect;
  }

  // If neither whitelist nor blacklist is enabled, always redirect
  console.log('No whitelist/blacklist enabled, should redirect');
  return true;
}

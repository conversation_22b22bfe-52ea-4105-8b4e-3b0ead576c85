// Popup script for bahtBrowse Redirector

// Default settings
const DEFAULT_SETTINGS = {
  bahtbrowseHost: 'localhost',
  bahtbrowsePort: '8082',
  openInNewTab: true,
  preferredBrowser: 'firefox', // Add browser preference
  enableWhitelist: false,
  whitelist: [],
  enableBlacklist: false,
  blacklist: []
};

// Add error logging
window.addEventListener('error', function(event) {
  console.error('Popup error caught:', event.error);
});

// Add unhandled promise rejection logging
window.addEventListener('unhandledrejection', function(event) {
  console.error('Popup unhandled promise rejection:', event.reason);
});

// Function to initialize the popup
function initPopup() {
  console.log('Popup initialization started');
  try {
    // Get current tab URL and display it
    browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
      console.log('Got current tab:', tabs[0]);
      const currentUrl = tabs[0].url;
      document.getElementById('currentUrl').textContent = currentUrl;
    }).catch(error => {
      console.error('Error getting current tab:', error);
    });

    // Set up browser toggle
    setupBrowserToggle();

    // Set up button event listeners
    console.log('Setting up button event listeners');
    const redirectButton = document.getElementById('redirectButton');
    const optionsButton = document.getElementById('optionsButton');
    const debugLink = document.getElementById('debugLink');
    const menuDebugLink = document.getElementById('menuDebugLink');

    console.log('Redirect button found:', !!redirectButton);
    console.log('Options button found:', !!optionsButton);
    console.log('Debug link found:', !!debugLink);
    console.log('Menu debug link found:', !!menuDebugLink);

    if (redirectButton) {
      redirectButton.addEventListener('click', function() {
        console.log('Redirect button clicked');
        redirectCurrentPage();
      });
    } else {
      console.error('Redirect button not found in the DOM');
    }

    if (optionsButton) {
      optionsButton.addEventListener('click', function() {
        console.log('Options button clicked');
        openOptions();
      });
    } else {
      console.error('Options button not found in the DOM');
    }

    if (debugLink) {
      debugLink.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Debug link clicked');
        openDebugLogs();
      });
    } else {
      console.error('Debug link not found in the DOM');
    }

    if (menuDebugLink) {
      menuDebugLink.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Menu debug link clicked');
        openContextMenuDebugLogs();
      });
    } else {
      console.error('Menu debug link not found in the DOM');
    }
  } catch (error) {
    console.error('Error in popup initialization:', error);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', initPopup);

// Function to check if bahtBrowse service is available
function checkBahtBrowseAvailability(host, port) {
  return new Promise((resolve, reject) => {
    console.log(`Checking if bahtBrowse is available at ${host}:${port}`);

    // Try HTTPS first, then fall back to HTTP if that fails
    tryHttpsConnection(host, port)
      .then(isAvailable => {
        if (isAvailable) {
          console.log('HTTPS connection successful');
          resolve(true);
        } else {
          console.log('HTTPS connection failed, trying HTTP...');
          return tryHttpConnection(host, port);
        }
      })
      .then(isAvailable => {
        if (isAvailable) {
          console.log('HTTP connection successful');
          resolve(true);
        } else {
          console.log('Both HTTPS and HTTP connections failed');
          resolve(false);
        }
      })
      .catch(error => {
        console.error('Error during connection tests:', error);
        resolve(false);
      });
  });
}

// Function to try HTTPS connection
function tryHttpsConnection(host, port) {
  return new Promise((resolve) => {
    // Create a test URL to the bahtBrowse service with HTTPS
    const testUrl = `https://${host}:${port}/browse/test-connection`;
    console.log(`Trying HTTPS URL: ${testUrl}`);

    // Use fetch with a timeout to check if the service is available
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('HTTPS connection timeout')), 5000);
    });

    Promise.race([
      fetch(testUrl, {
        method: 'GET',
        headers: {
          'X-Test-Connection': 'true'
        },
        mode: 'no-cors'
      }),
      timeoutPromise
    ])
      .then(response => {
        console.log('HTTPS response:', response);
        // If we get any response, consider it available
        window.lastSuccessfulProtocol = 'https';
        resolve(true);
      })
      .catch(error => {
        console.error('HTTPS connection failed:', error);
        resolve(false);
      });
  });
}

// Function to try HTTP connection
function tryHttpConnection(host, port) {
  return new Promise((resolve) => {
    // Create a test URL to the bahtBrowse service with HTTP
    const testUrl = `http://${host}:${port}/browse/test-connection`;
    console.log(`Trying HTTP URL: ${testUrl}`);

    // Use fetch with a timeout to check if the service is available
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('HTTP connection timeout')), 5000);
    });

    Promise.race([
      fetch(testUrl, {
        method: 'GET',
        headers: {
          'X-Test-Connection': 'true'
        },
        mode: 'no-cors'
      }),
      timeoutPromise
    ])
      .then(response => {
        console.log('HTTP response:', response);
        // If we get any response, consider it available
        window.lastSuccessfulProtocol = 'http';
        resolve(true);
      })
      .catch(error => {
        console.error('HTTP connection failed:', error);
        resolve(false);
      });
  });
}

// Function to show status message
function showStatus(message, isError = false) {
  const statusElement = document.getElementById('status');
  if (!statusElement) {
    // Create status element if it doesn't exist
    const statusDiv = document.createElement('div');
    statusDiv.id = 'status';
    statusDiv.style.padding = '10px';
    statusDiv.style.marginTop = '10px';
    statusDiv.style.borderRadius = '4px';
    document.body.appendChild(statusDiv);
  }

  const status = document.getElementById('status');
  status.textContent = message;
  status.style.backgroundColor = isError ? '#ffdddd' : '#ddffdd';
  status.style.color = isError ? '#990000' : '#006600';
  status.style.display = 'block';
}

// Function to redirect current page to bahtBrowse
function redirectCurrentPage() {
  browser.tabs.query({ active: true, currentWindow: true }).then((tabs) => {
    const currentTab = tabs[0];
    const currentUrl = currentTab.url;

    browser.storage.sync.get('settings').then((result) => {
      const settings = result.settings || DEFAULT_SETTINGS;

      // Show checking status
      showStatus(`Checking if bahtBrowse is available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}...`);

      // First check if bahtBrowse service is available
      checkBahtBrowseAvailability(settings.bahtbrowseHost, settings.bahtbrowsePort)
        .then(isAvailable => {
          if (!isAvailable) {
            const errorMsg = `bahtBrowse service is not available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}. Please make sure the service is running.`;
            console.error(errorMsg);
            showStatus(errorMsg, true);
            return;
          }

          // Service is available, proceed with redirection
          // Use the protocol that worked during the connection test
          const protocol = window.lastSuccessfulProtocol || 'https'; // Default to HTTPS
          const bahtbrowseUrl = `${protocol}://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(currentUrl)}&browser=${settings.preferredBrowser || 'firefox'}`;
          console.log(`Redirect URL (using ${protocol}):`, bahtbrowseUrl);

          try {
            if (settings.openInNewTab) {
              browser.tabs.create({ url: bahtbrowseUrl })
                .then(() => {
                  console.log('Opened in new tab successfully');
                  window.close();
                })
                .catch(error => {
                  console.error('Error creating new tab:', error);
                  showStatus(`Error opening in new tab: ${error.message}`, true);
                });
            } else {
              browser.tabs.update(currentTab.id, { url: bahtbrowseUrl })
                .then(() => {
                  console.log('Updated current tab successfully');
                  window.close();
                })
                .catch(error => {
                  console.error('Error updating tab:', error);
                  showStatus(`Error updating tab: ${error.message}`, true);
                });
            }
          } catch (error) {
            console.error('Error during redirection:', error);
            showStatus(`Error during redirection: ${error.message}`, true);
          }
        })
        .catch(error => {
          console.error('Error checking bahtBrowse availability:', error);
          showStatus(`Error checking bahtBrowse availability: ${error.message}`, true);
        });
    }).catch(error => {
      console.error('Error getting settings:', error);
      showStatus(`Error getting settings: ${error.message}`, true);
    });
  }).catch(error => {
    console.error('Error getting current tab:', error);
    showStatus(`Error getting current tab: ${error.message}`, true);
  });
}

// Function to open options page
function openOptions() {
  console.log('openOptions function called');
  try {
    browser.runtime.openOptionsPage().then(() => {
      console.log('Options page opened successfully');
      window.close(); // Close the popup after opening settings
    }).catch(error => {
      console.error('Error opening options page:', error);
      showStatus('Error opening settings page: ' + error.message, true);
    });
  } catch (error) {
    console.error('Exception in openOptions function:', error);
  }
}

// Function to check status display on initial load
function checkStatus() {
  // This function can be expanded if needed
  console.log('Checking initial status');
}

// Function to open debug logs
function openDebugLogs() {
  console.log('Opening debug logs...');
  browser.tabs.create({ url: browser.runtime.getURL('popup/debug-logs.html') })
    .then(() => window.close())
    .catch(error => {
      console.error('Error opening debug logs:', error);
      showStatus('Error opening debug logs: ' + error.message, true);
    });
}

// Function to open context menu debug logs
function openContextMenuDebugLogs() {
  console.log('Opening context menu debug logs...');
  browser.tabs.create({ url: browser.runtime.getURL('popup/context-menu-debug.html') })
    .then(() => window.close())
    .catch(error => {
      console.error('Error opening context menu debug logs:', error);
      showStatus('Error opening context menu debug logs: ' + error.message, true);
    });
}

// Function to set up browser toggle
function setupBrowserToggle() {
  console.log('Setting up browser toggle');

  // Get the toggle options
  const firefoxOption = document.getElementById('firefox-option');
  const chromeOption = document.getElementById('chrome-option');

  if (!firefoxOption || !chromeOption) {
    console.error('Browser toggle options not found in the DOM');
    return;
  }

  // Get current preferred browser from settings
  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    const currentBrowser = settings.preferredBrowser || 'firefox';

    console.log('Current preferred browser:', currentBrowser);

    // Set active class based on current setting
    if (currentBrowser === 'firefox') {
      firefoxOption.classList.add('active');
      chromeOption.classList.remove('active');
    } else {
      chromeOption.classList.add('active');
      firefoxOption.classList.remove('active');
    }

    // Add click event listeners
    firefoxOption.addEventListener('click', function() {
      setPreferredBrowser('firefox');
      firefoxOption.classList.add('active');
      chromeOption.classList.remove('active');
    });

    chromeOption.addEventListener('click', function() {
      setPreferredBrowser('chromium');
      chromeOption.classList.add('active');
      firefoxOption.classList.remove('active');
    });
  }).catch(error => {
    console.error('Error getting settings for browser toggle:', error);
  });
}

// Function to set preferred browser
function setPreferredBrowser(browserType) {
  console.log('Setting preferred browser to:', browserType);

  browser.storage.sync.get('settings').then((result) => {
    const settings = result.settings || DEFAULT_SETTINGS;
    settings.preferredBrowser = browserType;

    browser.storage.sync.set({ settings }).then(() => {
      console.log('Preferred browser saved successfully');
    }).catch(error => {
      console.error('Error saving preferred browser:', error);
    });
  }).catch(error => {
    console.error('Error getting settings for saving preferred browser:', error);
  });
}

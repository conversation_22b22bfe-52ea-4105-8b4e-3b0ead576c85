// Debug logs script

// Get DOM elements
const logsContainer = document.getElementById('logs');
const refreshButton = document.getElementById('refresh');
const clearButton = document.getElementById('clear');
const filterInput = document.getElementById('filter');

// Store logs
let allLogs = [];

// Function to request logs from background script
function fetchLogs() {
  browser.runtime.sendMessage({ type: 'getLogs' })
    .then(response => {
      allLogs = response.logs || [];
      renderLogs();
    })
    .catch(error => {
      showError('Error fetching logs: ' + error.message);
    });
}

// Function to clear the logs display
function clearLogs() {
  logsContainer.innerHTML = '<div class="no-logs">No logs available</div>';
}

// Function to show an error
function showError(message) {
  logsContainer.innerHTML = `<div class="log-entry" style="color: red">${message}</div>`;
}

// Function to render logs
function renderLogs() {
  // Clear logs container
  logsContainer.innerHTML = '';

  // Get filter value
  const filterValue = filterInput.value.toLowerCase();

  // Filter logs
  const filteredLogs = filterValue
    ? allLogs.filter(log =>
        log.category.toLowerCase().includes(filterValue) ||
        log.message.toLowerCase().includes(filterValue) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(filterValue)))
    : allLogs;

  // Check if logs are empty
  if (filteredLogs.length === 0) {
    logsContainer.innerHTML = '<div class="no-logs">No logs available</div>';
    return;
  }

  // Render logs
  filteredLogs.forEach(log => {
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry';

    // Format timestamp
    const timestamp = new Date(log.timestamp);
    const formattedTime = timestamp.toLocaleTimeString();

    // Create elements
    logEntry.innerHTML = `
      <span class="timestamp">[${formattedTime}]</span>
      <span class="category ${log.category}">${log.category}</span>
      <span class="message">${log.message}</span>
      ${log.data ? `<div class="data">${JSON.stringify(log.data, null, 2)}</div>` : ''}
    `;

    logsContainer.appendChild(logEntry);
  });
}

// Set up event listeners
refreshButton.addEventListener('click', fetchLogs);
clearButton.addEventListener('click', clearLogs);
filterInput.addEventListener('input', renderLogs);

// Fetch logs on load
fetchLogs();

// Auto-refresh logs every 5 seconds
setInterval(fetchLogs, 5000);

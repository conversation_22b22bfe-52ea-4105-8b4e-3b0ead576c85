<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>10baht Browse Debug Logs</title>
  <style>
    body {
      font-family: monospace;
      background-color: #333;
      color: #eee;
      margin: 0;
      padding: 0;
      font-size: 12px;
    }

    h1 {
      background-color: #a520c6;
      color: white;
      padding: 10px;
      margin: 0;
      font-size: 18px;
    }

    .controls {
      padding: 10px;
      border-bottom: 1px solid #555;
      display: flex;
      align-items: center;
    }

    button {
      background: #a520c6;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
      margin-right: 10px;
    }

    button:hover {
      background: #8a1dad;
    }

    .filter {
      margin-left: auto;
    }

    .filter input {
      background: #555;
      color: white;
      border: 1px solid #777;
      padding: 5px;
      border-radius: 3px;
    }

    #logs {
      padding: 10px;
      overflow-y: auto;
      height: calc(100vh - 100px);
    }

    .log-entry {
      margin-bottom: 5px;
      padding: 5px;
      border-radius: 3px;
    }

    .log-entry:hover {
      background-color: #444;
    }

    .log-entry .timestamp {
      color: #aaa;
    }

    .log-entry .category {
      display: inline-block;
      padding: 2px 5px;
      border-radius: 3px;
      margin: 0 5px;
      font-weight: bold;
    }

    .log-entry .category.REDIRECT {
      background-color: #2980b9;
      color: white;
    }

    .log-entry .category.CONNECTION {
      background-color: #27ae60;
      color: white;
    }

    .log-entry .category.ERROR {
      background-color: #c0392b;
      color: white;
    }

    .log-entry .message {
      color: #fff;
    }

    .log-entry .data {
      color: #aaa;
      padding-left: 20px;
      word-break: break-all;
      white-space: pre-wrap;
    }

    .no-logs {
      text-align: center;
      color: #aaa;
      padding: 20px;
    }
  </style>
</head>
<body>
  <h1>10baht Browse Debug Logs</h1>

  <div class="controls">
    <button id="refresh">Refresh Logs</button>
    <button id="clear">Clear Logs</button>
    <div class="filter">
      <input type="text" id="filter" placeholder="Filter logs...">
    </div>
  </div>

  <div id="logs">
    <div class="no-logs">Loading logs...</div>
  </div>

  <script src="debug-logs.js"></script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>bahtBrowse Context Menu Debug Logs</title>
  <style>
    body {
      background-color: #333;
      color: #eee;
      font-family: monospace;
      padding: 20px;
      margin: 0;
      min-width: 700px;
      min-height: 400px;
    }

    header {
      padding: 15px;
      background-color: #a520c6;
      color: white;
      margin-bottom: 20px;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    h1 {
      margin: 0;
      font-size: 20px;
    }

    .controls {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    button {
      background-color: #444;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
    }

    button:hover {
      background-color: #555;
    }

    .refresh {
      background-color: #2980b9;
      color: white;
    }

    .clear {
      background-color: #c0392b;
      color: white;
    }

    .test {
      background-color: #27ae60;
      color: white;
    }

    .log-container {
      height: 350px;
      overflow-y: auto;
      border: 1px solid #555;
      border-radius: 4px;
      margin-bottom: 15px;
      padding: 10px;
    }

    .log-entry {
      padding: 5px 0;
      border-bottom: 1px solid #444;
      font-size: 12px;
    }

    .timestamp {
      color: #aaa;
      margin-right: 10px;
    }

    .category {
      display: inline-block;
      width: 120px;
      margin-right: 10px;
    }

    .CONTEXT_MENU {
      color: #3498db;
    }

    .CONTEXT_MENU_ERROR {
      color: #e74c3c;
    }

    .message {
      color: #fff;
    }

    .data {
      color: #aaa;
      margin-top: 5px;
      padding-left: 20px;
      white-space: pre-wrap;
      word-break: break-all;
    }

    .status {
      padding: 10px;
      text-align: center;
      border-radius: 4px;
    }

    .status.success {
      background-color: rgba(39, 174, 96, 0.2);
      color: #2ecc71;
    }

    .status.error {
      background-color: rgba(192, 57, 43, 0.2);
      color: #e74c3c;
    }

    footer {
      text-align: center;
      color: #aaa;
      font-size: 11px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <header>
    <h1>bahtBrowse Context Menu Debug Logs</h1>
    <div class="controls">
      <button id="refreshButton" class="refresh">Refresh</button>
      <button id="clearButton" class="clear">Clear Logs</button>
      <button id="testButton" class="test">Test Menu</button>
    </div>
  </header>

  <div id="logsContainer" class="log-container">
    <div class="log-entry">Loading logs...</div>
  </div>

  <div id="status" class="status">Ready</div>

  <footer>
    bahtBrowse Firefox Extension - Context Menu Debugging Tool
  </footer>

  <script>
    // Function to format a timestamp
    function formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    }

    // Function to format log data
    function formatData(data) {
      if (!data) return '';
      try {
        return JSON.stringify(data, null, 2);
      } catch (e) {
        return String(data);
      }
    }

    // Function to display logs
    function displayLogs(logs) {
      const logsContainer = document.getElementById('logsContainer');

      if (!logs || logs.length === 0) {
        logsContainer.innerHTML = '<div class="log-entry">No logs available</div>';
        return;
      }

      logsContainer.innerHTML = '';

      logs.forEach(log => {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        const timestamp = document.createElement('span');
        timestamp.className = 'timestamp';
        timestamp.textContent = formatTime(log.timestamp);

        const category = document.createElement('span');
        category.className = 'category ' + log.category;
        category.textContent = log.category;

        const message = document.createElement('span');
        message.className = 'message';
        message.textContent = log.message;

        logEntry.appendChild(timestamp);
        logEntry.appendChild(category);
        logEntry.appendChild(message);

        if (log.data) {
          const data = document.createElement('div');
          data.className = 'data';
          data.textContent = formatData(log.data);
          logEntry.appendChild(data);
        }

        logsContainer.appendChild(logEntry);
      });

      // Scroll to bottom
      logsContainer.scrollTop = logsContainer.scrollHeight;
    }

    // Function to fetch logs
    function fetchLogs() {
      setStatus('Fetching logs...', 'info');

      browser.runtime.sendMessage({ type: 'getContextMenuLogs' })
        .then(response => {
          if (response && response.logs) {
            displayLogs(response.logs);
            setStatus('Logs updated successfully', 'success');
          } else {
            setStatus('No logs available', 'error');
          }
        })
        .catch(error => {
          console.error('Error fetching logs:', error);
          setStatus('Error fetching logs: ' + error.message, 'error');
        });
    }

    // Function to clear logs
    function clearLogs() {
      browser.runtime.sendMessage({ type: 'clearContextMenuLogs' })
        .then(() => {
          displayLogs([]);
          setStatus('Logs cleared', 'success');
        })
        .catch(error => {
          console.error('Error clearing logs:', error);
          setStatus('Error clearing logs: ' + error.message, 'error');
        });
    }

    // Function to test context menu creation
    function testContextMenu() {
      setStatus('Testing context menu...', 'info');

      browser.runtime.sendMessage({ type: 'testContextMenu' })
        .then(response => {
          if (response && response.success) {
            setStatus('Context menu test successful', 'success');
          } else {
            setStatus('Context menu test failed', 'error');
          }

          // Refresh logs after test
          setTimeout(fetchLogs, 500);
        })
        .catch(error => {
          console.error('Error testing context menu:', error);
          setStatus('Error testing context menu: ' + error.message, 'error');
        });
    }

    // Function to set status message
    function setStatus(message, type) {
      const statusElement = document.getElementById('status');
      statusElement.textContent = message;
      statusElement.className = 'status ' + (type || '');

      if (type === 'success' || type === 'error') {
        setTimeout(() => {
          statusElement.className = 'status';
          statusElement.textContent = 'Ready';
        }, 3000);
      }
    }

    // Listen for log updates from background script
    browser.runtime.onMessage.addListener((message) => {
      if (message.type === 'contextMenuDebugLog') {
        // Instead of handling individual messages, just refresh all logs
        fetchLogs();
      }
    });

    // Set up event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Fetch logs on page load
      fetchLogs();

      // Set up button event listeners
      document.getElementById('refreshButton').addEventListener('click', fetchLogs);
      document.getElementById('clearButton').addEventListener('click', clearLogs);
      document.getElementById('testButton').addEventListener('click', testContextMenu);
    });
  </script>
</body>
</html>

const DEFAULT_SETTINGS = {  bahtbrowseHost: 'localhost',  bahtbrowsePort: '8082',  openInNewTab: true,  enableWhitelist: false,  whitelist: [],  enableBlacklist: false,  blacklist: [] };   window.addEventListener('error',function(event){  console.error('Popup error caught:',event.error); });   window.addEventListener('unhandledrejection',function(event){  console.error('Popup unhandled promise rejection:',event.reason); });   function initPopup(){  console.log('Popup initialization started');  try{    browser.tabs.query({ active: true,currentWindow: true }).then((tabs) =>{  console.log('Got current tab:',tabs[0]);  const currentUrl = tabs[0].url;  document.getElementById('currentUrl').textContent = currentUrl;  }).catch(error =>{  console.error('Error getting current tab:',error);  });     console.log('Setting up button event listeners');  const redirectButton = document.getElementById('redirectButton');  const optionsButton = document.getElementById('optionsButton');   console.log('Redirect button found:',!!redirectButton);  console.log('Options button found:',!!optionsButton);   if (redirectButton){  redirectButton.addEventListener('click',function(){  console.log('Redirect button clicked');  redirectCurrentPage();  });  }else{  console.error('Redirect button not found in the DOM');  }   if (optionsButton){  optionsButton.addEventListener('click',function(){  console.log('Options button clicked');  openOptions();  });  }else{  console.error('Options button not found in the DOM');  }  }catch (error){  console.error('Error in popup initialization:',error);  } }   document.addEventListener('DOMContentLoaded',initPopup);   function checkBahtBrowseAvailability(host,port){  return new Promise((resolve,reject) =>{  console.log(`Checking if 10baht Browse is available at ${host}:${port}`);     const testUrl = `http://${host}:${port}`;     const timeoutPromise = new Promise((_,reject) =>{  setTimeout(() => reject(new Error('Connection timeout')),5000);  });   Promise.race([  fetch(testUrl,{ method: 'HEAD',mode: 'no-cors' }),  timeoutPromise  ])  .then(() =>{  console.log('10baht Browse service is available');  resolve(true);  })  .catch(error =>{  console.error('10baht Browse service is not available:',error);  resolve(false);  });  }); }   function showStatus(message,isError = false){  const statusElement = document.getElementById('status');  if (!statusElement){    const statusDiv = document.createElement('div');  statusDiv.id = 'status';  statusDiv.style.padding = '10px';  statusDiv.style.marginTop = '10px';  statusDiv.style.borderRadius = '4px';  document.body.appendChild(statusDiv);  }   const status = document.getElementById('status');  status.textContent = message;  status.style.backgroundColor = isError ? '#ffdddd' : '#ddffdd';  status.style.color = isError ? '#990000' : '#006600';  status.style.display = 'block'; }   function redirectCurrentPage(){  browser.tabs.query({ active: true,currentWindow: true }).then((tabs) =>{  const currentTab = tabs[0];  const currentUrl = currentTab.url;   browser.storage.sync.get('settings').then((result) =>{  const settings = result.settings || DEFAULT_SETTINGS;     showStatus(`Checking if 10baht Browse is available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}...`);     checkBahtBrowseAvailability(settings.bahtbrowseHost,settings.bahtbrowsePort)  .then(isAvailable =>{  if (!isAvailable){  const errorMsg = `10baht Browse service is not available at ${settings.bahtbrowseHost}:${settings.bahtbrowsePort}. Please make sure the service is running.`;  console.error(errorMsg);  showStatus(errorMsg,true);  return;  }     const bahtbrowseUrl = `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(currentUrl)}`;   try{  if (settings.openInNewTab){  browser.tabs.create({ url: bahtbrowseUrl })  .then(() => window.close())  .catch(error =>{  console.error('Error creating new tab:',error);  showStatus(`Error opening in new tab: ${error.message}`,true);  });  }else{  browser.tabs.update(currentTab.id,{ url: bahtbrowseUrl })  .then(() => window.close())  .catch(error =>{  console.error('Error updating tab:',error);  showStatus(`Error updating tab: ${error.message}`,true);  });  }  }catch (error){  console.error('Error during redirection:',error);  showStatus(`Error during redirection: ${error.message}`,true);  }  })  .catch(error =>{  console.error('Error checking 10baht Browse availability:',error);  showStatus(`Error checking 10baht Browse availability: ${error.message}`,true);  });  }).catch(error =>{  console.error('Error getting settings:',error);  showStatus(`Error getting settings: ${error.message}`,true);  });  }).catch(error =>{  console.error('Error getting current tab:',error);  showStatus(`Error getting current tab: ${error.message}`,true);  }); }   function openOptions(){  console.log('openOptions function called');  try{  browser.runtime.openOptionsPage().then(() =>{  console.log('Options page opened successfully');  window.close();  }).catch(error =>{  console.error('Error opening options page:',error);  showStatus('Error opening settings page: ' + error.message,true);  });  }catch (error){  console.error('Exception in openOptions function:',error);  } }   function checkStatus(){    console.log('Checking initial status'); }

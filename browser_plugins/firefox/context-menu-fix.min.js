   function safeRedirectToBahtBrowse(url,settings){    return browser.tabs.create({ url: 'about:blank' })  .then(newTab =>{    return new Promise(resolve =>{  browser.tabs.onUpdated.addListener(function listener(tabId,changeInfo){  if (tabId === newTab.id && changeInfo.status === 'complete'){  browser.tabs.onUpdated.removeListener(listener);  resolve(newTab);  }  });  });  })  .then(newTab =>{    const redirectScript = `    document.body.innerHTML = '<div style="display: flex;justify-content: center;align-items: center;height: 100vh;font-family: Arial,sans-serif;"><div style="text-align: center;"><h1>Redirecting to bahtBrowse...</h1><p>Please wait while we securely redirect you.</p></div></div>';      setTimeout(() =>{    window.location.href = "${settings.bahtbrowseHost ? `http:  },500);  `;    return browser.tabs.executeScript(newTab.id,{  code: redirectScript  });  }); }   window.safeRedirectToBahtBrowse = safeRedirectToBahtBrowse; 
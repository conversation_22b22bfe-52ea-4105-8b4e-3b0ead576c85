<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="options.css">
  <link href="https://fonts.googleapis.com/css2?family=Vibur&display=swap" rel="stylesheet">
  <title>bahtBrowse - Settings</title>
  <style>
    /* Inline debug styles */
    .debug-info {
      margin-top: 20px;
      padding: 10px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      overflow-wrap: break-word;
      white-space: pre-wrap;
    }
    .debug-title {
      font-weight: bold;
      margin-bottom: 5px;
    }

    /* Status dot inside button */
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 8px;
      display: inline-block;
    }

    .status-dot.connected {
      background-color: #28a745;
      box-shadow: 0 0 5px #28a745;
    }

    .status-dot.disconnected {
      background-color: #dc3545;
      box-shadow: 0 0 5px #dc3545;
    }

    .last-checked {
      font-size: 12px;
      color: #6c757d;
      text-align: center;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header-logo">
      <img src="../icons/bahtbrowse-48.png" alt="bahtBrowse Logo" width="48" height="48">
    </div>
    <h1>bahtBrowse Redirector Settings</h1>

    <!-- Debug information - will be filled by JavaScript -->
    <div class="debug-info" id="debugInfo" style="display: none;">
      <div class="debug-title">Debug Information:</div>
      <div id="debugContent">Loading debug info...</div>
    </div>

    <div class="section" id="serverSettingsSection">
      <h2>bahtBrowse Server</h2>
      <div class="form-group">
        <label for="bahtbrowseHost">Host:</label>
        <input type="text" id="bahtbrowseHost" placeholder="localhost">
      </div>
      <div class="form-group">
        <label for="bahtbrowsePort">Port:</label>
        <input type="text" id="bahtbrowsePort" placeholder="8081">
      </div>
      <div class="form-group test-connection-group">
        <div class="test-connection-container">
          <button id="testConnectionButton" class="secondary-button">
            <span class="status-dot disconnected" id="statusDot"></span> Test Connection
          </button>
          <div id="lastChecked" class="last-checked"></div>
          <div id="connectionStatus" class="connection-status"></div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Behaviour</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="openInNewTab">
        <label for="openInNewTab">Open pages in new tab</label>
      </div>
      <div class="form-group">
        <label for="preferredBrowser">Preferred Browser:</label>
        <select id="preferredBrowser">
          <option value="firefox">Firefox</option>
          <option value="chromium">Ungoogled-Chromium</option>
        </select>
        <p class="option-description">Select which browser to use in BahtBrowse.</p>
      </div>
      <div class="form-group checkbox">
        <input type="checkbox" id="showNotifications">
        <label for="showNotifications">Show notifications</label>
      </div>
      <div class="form-group checkbox">
        <input type="checkbox" id="connectionStatusCheck">
        <label for="connectionStatusCheck">Enable periodic connection status check</label>
      </div>
      <div class="form-group">
        <label for="connectionCheckInterval">Check interval (seconds):</label>
        <input type="number" id="connectionCheckInterval" min="5" max="300" placeholder="30">
      </div>
    </div>

    <div class="section">
      <h2>URL Filtering</h2>
      <div class="form-group checkbox">
        <input type="checkbox" id="enableWhitelist">
        <label for="enableWhitelist">Enable whitelist (only redirect these domains)</label>
      </div>
      <div class="form-group">
        <label for="whitelist">Whitelist (one domain per line):</label>
        <textarea id="whitelist" placeholder="example.com"></textarea>
      </div>

      <div class="form-group checkbox">
        <input type="checkbox" id="enableBlacklist">
        <label for="enableBlacklist">Enable blacklist (never redirect these domains)</label>
      </div>
      <div class="form-group">
        <label for="blacklist">Blacklist (one domain per line):</label>
        <textarea id="blacklist" placeholder="trusted-site.com"></textarea>
      </div>
    </div>

    <div class="buttons">
      <button id="saveButton">Save Settings</button>
      <button id="resetButton">Reset to Defaults</button>
    </div>

    <div id="status" class="status"></div>
  </div>
  <script src="options.js"></script>
</body>
</html>

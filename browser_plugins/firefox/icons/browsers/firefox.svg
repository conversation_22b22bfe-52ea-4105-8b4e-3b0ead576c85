<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 77.42 79.97">
  <defs>
    <radialGradient id="a" cx="41.613" cy="22.886" r="37.93" gradientTransform="translate(-8.92 -2.01)" gradientUnits="userSpaceOnUse">
      <stop offset="0.045" stop-color="#ffea00"/>
      <stop offset="0.12" stop-color="#ffde00"/>
      <stop offset="0.254" stop-color="#ffbf00"/>
      <stop offset="0.429" stop-color="#ff8e00"/>
      <stop offset="0.769" stop-color="#ff272d"/>
      <stop offset="0.872" stop-color="#e0255a"/>
      <stop offset="0.953" stop-color="#cc2477"/>
      <stop offset="1" stop-color="#c42482"/>
    </radialGradient>
    <radialGradient id="b" cx="-7287.81" cy="7881.29" r="80.8" gradientTransform="matrix(0.1, 0, 0, -0.1, 737.5, 798.14)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00ccda"/>
      <stop offset="0.22" stop-color="#0083ff"/>
      <stop offset="0.261" stop-color="#007af9"/>
      <stop offset="0.33" stop-color="#0060e8"/>
      <stop offset="0.333" stop-color="#005fe7"/>
      <stop offset="0.438" stop-color="#2639ad"/>
      <stop offset="0.522" stop-color="#401e84"/>
      <stop offset="0.566" stop-color="#4a1475"/>
    </radialGradient>
    <linearGradient id="c" x1="70.79" y1="10.8" x2="15.14" y2="66.46" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#000f43" stop-opacity="0.4"/>
      <stop offset="0.485" stop-color="#001962" stop-opacity="0.173"/>
      <stop offset="1" stop-color="#002079" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="d" cx="-7316.57" cy="7851.37" r="73.26" gradientTransform="matrix(0.1, 0, 0, -0.1, 737.5, 798.14)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffea00"/>
      <stop offset="0.5" stop-color="#ff272d"/>
      <stop offset="1" stop-color="#c42482"/>
    </radialGradient>
    <radialGradient id="e" cx="-7154.89" cy="7826.63" r="86.5" gradientTransform="matrix(0.1, 0, 0, -0.1, 737.5, 798.14)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#ffe900"/>
      <stop offset="0.157" stop-color="#ffaf0e"/>
      <stop offset="0.316" stop-color="#ff7a1b"/>
      <stop offset="0.472" stop-color="#ff4e26"/>
      <stop offset="0.621" stop-color="#ff2c2e"/>
      <stop offset="0.762" stop-color="#ff1434"/>
      <stop offset="0.892" stop-color="#ff0538"/>
      <stop offset="1" stop-color="#ff0039"/>
    </radialGradient>
    <radialGradient id="f" cx="-7170.43" cy="7791.76" r="123.3" gradientTransform="matrix(0.1, 0, 0, -0.1, 737.5, 798.14)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff44f"/>
      <stop offset="0.203" stop-color="#ffe847"/>
      <stop offset="0.499" stop-color="#ffc830"/>
      <stop offset="0.852" stop-color="#ff980e"/>
      <stop offset="1" stop-color="#ff8b00"/>
    </radialGradient>
    <linearGradient id="g" x1="70.01" y1="12.11" x2="15.27" y2="66.85" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff44f" stop-opacity="0.8"/>
      <stop offset="0.094" stop-color="#fff44f" stop-opacity="0.699"/>
      <stop offset="0.752" stop-color="#fff44f" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="h" x1="32.83" y1="20.43" x2="79.27" y2="66.87" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3a8ee6"/>
      <stop offset="0.237" stop-color="#5c79f0"/>
      <stop offset="0.639" stop-color="#9059ff"/>
      <stop offset="1" stop-color="#c139e6"/>
    </linearGradient>
    <linearGradient id="i" x1="686.37" y1="31.5" x2="709.82" y2="31.5" gradientTransform="matrix(1, 0, 0, -1, -651.43, 79.2)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#6b57ff"/>
      <stop offset="0.184" stop-color="#884bff"/>
      <stop offset="0.361" stop-color="#9e41ff"/>
      <stop offset="0.516" stop-color="#ab3aff"/>
      <stop offset="0.634" stop-color="#b037ff"/>
      <stop offset="0.935" stop-color="#c139e6"/>
    </linearGradient>
  </defs>
  <path d="M74.62,26.83c-1.68-4.12-5.09-8.55-7.87-10-1.62-8.18-5.87-15.47-13.64-19.89A28.94,28.94,0,0,0,39.84,0,29.26,29.26,0,0,0,16.93,9.68c-1.5,1.33-3.09,2.88-4.5,4.2a18.45,18.45,0,0,0-1.47,1.62C5.11,21.24,3.41,29.48,4.3,36c.89,6.52.67,10.31,0,12.77a16.4,16.4,0,0,0-.6,1.9s.21,2.09.62,3.16A32.47,32.47,0,0,0,30,80c1.58,0,3.16-.09,4.73-.26a29.58,29.58,0,0,0,17.71-7.93A31.73,31.73,0,0,0,63.93,48.24c.2-7.92-1.51-14.87.82-19.86C65.36,27.24,74.62,26.83,74.62,26.83Z" fill="url(#a)"/>
  <path d="M74.62,26.83c-1.68-4.12-5.09-8.55-7.87-10-1.62-8.18-5.87-15.47-13.64-19.89A28.94,28.94,0,0,0,39.84,0,29.26,29.26,0,0,0,16.93,9.68c-1.5,1.33-3.09,2.88-4.5,4.2a18.45,18.45,0,0,0-1.47,1.62C5.11,21.24,3.41,29.48,4.3,36c.89,6.52.67,10.31,0,12.77a16.4,16.4,0,0,0-.6,1.9s.21,2.09.62,3.16A32.47,32.47,0,0,0,30,80c1.58,0,3.16-.09,4.73-.26a29.58,29.58,0,0,0,17.71-7.93A31.73,31.73,0,0,0,63.93,48.24c.2-7.92-1.51-14.87.82-19.86C65.36,27.24,74.62,26.83,74.62,26.83Z" fill="url(#b)"/>
  <path d="M74.62,26.83c-1.68-4.12-5.09-8.55-7.87-10-1.62-8.18-5.87-15.47-13.64-19.89A28.94,28.94,0,0,0,39.84,0,29.26,29.26,0,0,0,16.93,9.68c-1.5,1.33-3.09,2.88-4.5,4.2a18.45,18.45,0,0,0-1.47,1.62C5.11,21.24,3.41,29.48,4.3,36c.89,6.52.67,10.31,0,12.77a16.4,16.4,0,0,0-.6,1.9s.21,2.09.62,3.16A32.47,32.47,0,0,0,30,80c1.58,0,3.16-.09,4.73-.26a29.58,29.58,0,0,0,17.71-7.93A31.73,31.73,0,0,0,63.93,48.24c.2-7.92-1.51-14.87.82-19.86C65.36,27.24,74.62,26.83,74.62,26.83Z" fill="url(#c)"/>
  <path d="M39.84,65.33c-.44,0-.87,0-1.3,0A17.11,17.11,0,0,1,21.42,48.24a17.6,17.6,0,0,1,17.12-17.92c.44,0,.87,0,1.3,0A17.11,17.11,0,0,1,57,47.45,17.6,17.6,0,0,1,39.84,65.33Z" fill="#fff"/>
  <path d="M39.84,30.32c.44,0,.87,0,1.3,0A17.11,17.11,0,0,1,57,47.45,17.6,17.6,0,0,1,39.84,65.33c-.44,0-.87,0-1.3,0A17.11,17.11,0,0,1,21.42,48.24,17.6,17.6,0,0,1,38.54,30.32c.44,0,.87,0,1.3,0m0-1.3A18.9,18.9,0,0,0,20.12,48.24,18.38,18.38,0,0,0,38.54,66.63c.44,0,.87,0,1.3,0A18.9,18.9,0,0,0,58.26,47.45,18.38,18.38,0,0,0,41.14,29.06c-.44,0-.87,0-1.3,0Z" fill="#fff" opacity="0.2"/>
  <path d="M50.8,22.94c-5.25-3-11.48-3.14-13.62-3.14-11.48,0-17.58,5.5-21.27,12.66,0,0,4.16-3.07,10.11-5.1a32.94,32.94,0,0,1,11.35-1.8,25.58,25.58,0,0,1,9.2,1.9,22.24,22.24,0,0,1,13.88,14.06c0,.09,0,.18.05.27A27.49,27.49,0,0,0,50.8,22.94Z" fill="url(#d)"/>
  <path d="M39.84,0A28.94,28.94,0,0,0,26.33-3.05c8.92,2.45,16.5,9,20.71,19.2,1.45,3.5,2.81,9,1.93,14.5,0,0,8.33-2.5,8.33-11.11C57.3,10.83,48.26,2.5,39.84,0Z" fill="url(#e)"/>
  <path d="M39.84,0A28.94,28.94,0,0,0,26.33-3.05c8.92,2.45,16.5,9,20.71,19.2,1.45,3.5,2.81,9,1.93,14.5,0,0,8.33-2.5,8.33-11.11C57.3,10.83,48.26,2.5,39.84,0Z" fill="url(#f)"/>
  <path d="M39.84,0A28.94,28.94,0,0,0,26.33-3.05c8.92,2.45,16.5,9,20.71,19.2,1.45,3.5,2.81,9,1.93,14.5,0,0,8.33-2.5,8.33-11.11C57.3,10.83,48.26,2.5,39.84,0Z" fill="url(#g)"/>
  <path d="M4.3,36c.89,6.52.67,10.31,0,12.77a16.4,16.4,0,0,0-.6,1.9s.21,2.09.62,3.16c.57,1.46,1.24,2.85,1.9,4.18,1.43,2.9,2.9,5.07,4.49,7.42a27.09,27.09,0,0,0,3.57,4.39,29.72,29.72,0,0,0,15.76,9.94c1.5.36,3,.63,4.56.82a32.22,32.22,0,0,0,4.73.26,29.72,29.72,0,0,0,10.28-1.76,29.58,29.58,0,0,0,7.43-3.76,31.73,31.73,0,0,0,11.49-23.57c.2-7.92-1.51-14.87.82-19.86.64-1.14,9.9-1.55,9.9-1.55-1.68-4.12-5.09-8.55-7.87-10-1.62-8.18-5.87-15.47-13.64-19.89A28.94,28.94,0,0,0,39.84,0,29.26,29.26,0,0,0,16.93,9.68c-1.5,1.33-3.09,2.88-4.5,4.2a18.45,18.45,0,0,0-1.47,1.62C5.11,21.24,3.41,29.48,4.3,36Z" fill="url(#h)"/>
  <path d="M57.3,19.54c0,8.61-8.33,11.11-8.33,11.11.88-5.5-.48-11-1.93-14.5C42.83,5.95,35.25-.6,26.33-3.05A28.94,28.94,0,0,0,39.84,0C48.26,2.5,57.3,10.83,57.3,19.54Z" fill="url(#i)"/>
</svg>

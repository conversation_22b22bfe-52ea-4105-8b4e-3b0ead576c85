// Improved context menu handler for bahtBrowse

// Function to safely redirect to bahtBrowse
function safeRedirectToBahtBrowse(url, settings) {
  // Create a new tab with about:blank first
  return browser.tabs.create({ url: 'about:blank' })
    .then(newTab => {
      // Wait for the tab to be fully loaded
      return new Promise(resolve => {
        browser.tabs.onUpdated.addListener(function listener(tabId, changeInfo) {
          if (tabId === newTab.id && changeInfo.status === 'complete') {
            browser.tabs.onUpdated.removeListener(listener);
            resolve(newTab);
          }
        });
      });
    })
    .then(newTab => {
      // Now that the tab is fully loaded, execute a script to handle the redirection
      const redirectScript = `
        // Show a loading message
        document.body.innerHTML = '<div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;"><div style="text-align: center;"><h1>Redirecting to bahtBrowse...</h1><p>Please wait while we securely redirect you.</p></div></div>';
        
        // Use a timeout to ensure the page has time to render
        setTimeout(() => {
          // Navigate to the bahtBrowse URL
          window.location.href = "${settings.bahtbrowseHost ? `http://${settings.bahtbrowseHost}:${settings.bahtbrowsePort}/browse/?url=${encodeURIComponent(url)}&browser=${settings.preferredBrowser || 'firefox'}` : ''}";
        }, 500);
      `;
      
      return browser.tabs.executeScript(newTab.id, {
        code: redirectScript
      });
    });
}

// Export the function
window.safeRedirectToBahtBrowse = safeRedirectToBahtBrowse;

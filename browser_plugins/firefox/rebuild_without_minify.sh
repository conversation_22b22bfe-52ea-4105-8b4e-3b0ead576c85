#!/bin/bash

# Rebuild script without minification for 10baht Browse Firefox Plugin
# This avoids minification issues with template literals

# Ensure the script exits on any error
set -e

echo "======================================================"
echo "10baht Browse Firefox Extension - Non-minified Build"
echo "======================================================"

echo "Step 1: Cleaning previous builds..."
rm -rf ./build

# Create a temporary directory for packaging
TEMP_DIR=$(mktemp -d)
echo "Step 2: Created temporary directory: $TEMP_DIR"

# Create directory structure
echo "Step 3: Creating directory structure..."
mkdir -p $TEMP_DIR/icons $TEMP_DIR/options $TEMP_DIR/popup

# Copy all necessary files to the temporary directory
echo "Step 4: Copying files to temporary directory..."
cp manifest.json README.md INSTALLATION.md package.json $TEMP_DIR/
cp -r icons/* $TEMP_DIR/icons/
cp options/options.css $TEMP_DIR/options/
cp options/options.html $TEMP_DIR/options/
cp popup/redirect.css $TEMP_DIR/popup/
cp popup/redirect.html $TEMP_DIR/popup/

# Copy only the regular JavaScript files, not the minified ones
echo "Step 5: Copying JavaScript files..."
cp background.js $TEMP_DIR/
cp options/options.js $TEMP_DIR/options/
cp popup/redirect.js $TEMP_DIR/popup/

# Update HTML files to use regular JavaScript
echo "Step 6: Ensuring HTML files use regular JavaScript..."
sed -i 's/redirect.min.js/redirect.js/g' $TEMP_DIR/popup/redirect.html
sed -i 's/options.min.js/options.js/g' $TEMP_DIR/options/options.html

# Update manifest.json to use regular background.js
echo "Step 7: Ensuring manifest.json uses regular JavaScript..."
sed -i 's/"background.min.js"/"background.js"/g' $TEMP_DIR/manifest.json

# Create build directory if it doesn't exist
mkdir -p build
XPI_FILE="build/bahtbrowse_bouncer.xpi"
echo "Step 8: Creating XPI file: $XPI_FILE"

# Create the XPI file
cd $TEMP_DIR
echo "Step 9: Creating ZIP archive..."
zip -r "$OLDPWD/$XPI_FILE" *
ZIP_RESULT=$?
cd - > /dev/null

# Check if the XPI file was created successfully
if [ $ZIP_RESULT -eq 0 ] && [ -f "$XPI_FILE" ]; then
    echo "Step 10: XPI file created successfully: $XPI_FILE ($(du -h "$XPI_FILE" | cut -f1))"

    echo "You can install it in Firefox by going to:"
    echo "  - Temporary installation: about:debugging > This Firefox > Load Temporary Add-on"
    echo "  - Permanent installation: about:addons > gear icon > Install Add-on From File..."
else
    echo "Step 10: Failed to create XPI file. Error code: $ZIP_RESULT"
    exit 1
fi

# Clean up
rm -rf "$TEMP_DIR"
echo "Step 11: Cleaned up temporary directory"

echo "======================================================"
echo "Non-minified build complete!"
echo "======================================================"

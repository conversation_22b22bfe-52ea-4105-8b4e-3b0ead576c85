# BahtBrowse Browser Plugins

This directory contains browser plugins for BahtBrowse.

## Firefox Plugin

The Firefox plugin allows users to redirect their current browsing session to the BahtBrowse containerized browser environment with a single click. It enhances security by enabling users to open potentially risky websites in an isolated container.

### Features

- Toolbar button for quick redirection
- Context menu integration
- Configurable host and port settings
- Browser preference selection (Firefox or Chromium)
- Connection status checking
- URL filtering with whitelist/blacklist

### Installation

See [INSTALLATION.md](firefox/INSTALLATION.md) for installation instructions.

### Development

The plugin is built using the Firefox WebExtensions API. To build the plugin:

1. Navigate to the `firefox` directory
2. Run `./rebuild_all.sh` to build the plugin with minification
3. The built plugin will be available in the `build` directory

## Chrome Plugin (Future)

A Chrome/Chromium version of the plugin is planned for future development.

# 10Baht bahtBrowse Load Testing Suite

## Overview

The load testing suite is a comprehensive framework for simulating different load scenarios on the 10Baht bahtBrowse system. It provides tools for measuring performance, tracking session metrics, and visualizing results in real-time.

## Features

- **Multiple Load Scenarios**: Low, medium, and high load configurations
- **Browser Type Support**: Test with Firefox, Chromium, or mixed browser types
- **Rich CLI Visualization**: Real-time progress tracking and metrics display
- **Detailed Metrics Collection**: Session timing, browser performance, resource usage
- **Comprehensive Reporting**: HTML reports with interactive charts and data exports

## Architecture

```mermaid
graph TD
    A[Load Test Runner] --> B[Browser Session Tests]
    A --> C[Metrics Collector]
    A --> D[Report Generator]
    
    B --> E[Playwright Browser Control]
    B --> F[User Action Simulation]
    B --> G[Performance Measurement]
    
    C --> H[Session Tracking]
    C --> I[Performance Statistics]
    C --> J[Resource Monitoring]
    
    D --> K[HTML Reports]
    D --> L[Data Exports]
    D --> M[Performance Charts]
    
    subgraph "Load Scenarios"
        N[Low Load: 5 users]
        O[Medium Load: 20 users]
        P[High Load: 50 users]
    end
    
    subgraph "Browser Types"
        Q[Firefox]
        R[Chromium]
    end
    
    A --> N
    A --> O
    A --> P
    B --> Q
    B --> R
```

## Load Scenarios

| Scenario | Concurrent Users | Ramp-up Time | Test Duration | Think Time |
|----------|------------------|--------------|--------------|------------|
| Low      | 5                | 30 seconds   | 5 minutes    | 5-15 seconds |
| Medium   | 20               | 60 seconds   | 10 minutes   | 3-10 seconds |
| High     | 50               | 120 seconds  | 20 minutes   | 2-8 seconds  |

## Metrics Collected

- **Session Metrics**: ID, browser type, start/end time, total duration
- **Container Metrics**: Request time, container ID, IP address
- **Browser Metrics**: Launch time, ready time, navigation time
- **Performance Metrics**: DOM complete time, first input delay
- **Resource Metrics**: Memory usage, CPU usage

## Usage

Run the load testing suite with the following command:

```bash
python -m tests.load_testing.load_test_runner --scenario [low|medium|high] --browser [firefox|chromium]
```

### Examples

```bash
# Run a low load test with Firefox
python -m tests.load_testing.load_test_runner --scenario low --browser firefox

# Run a medium load test with Chromium
python -m tests.load_testing.load_test_runner --scenario medium --browser chromium

# Run a high load test with mixed browser types
python -m tests.load_testing.load_test_runner --scenario high
```

## Reports

The load testing suite generates comprehensive reports in multiple formats:

- **HTML Reports**: Interactive reports with charts and tables
- **CSV Data**: Raw session data for further analysis
- **JSON Data**: Structured data for programmatic access
- **Charts**: Performance visualizations and distributions

Reports are saved in the `tests/load_testing/reports` directory.

## Implementation Details

The load testing suite consists of several key components:

1. **Load Test Runner**: Orchestrates the test execution and visualization
2. **Browser Session Test**: Simulates user interactions with browsers
3. **Metrics Collector**: Gathers and aggregates performance data
4. **Report Generator**: Creates detailed reports and visualizations

### Key Files

- `load_test_runner.py`: Main entry point for the load testing suite
- `scenarios/browser_session.py`: Browser session simulation
- `utils/metrics_collector.py`: Metrics collection and aggregation
- `utils/report_generator.py`: Report generation and visualization
- `config.py`: Configuration for load scenarios and metrics

## Future Enhancements

- Integration with CI/CD pipelines for automated load testing
- Additional load scenarios for specific use cases
- Extended metrics collection for deeper performance analysis
- Real-time dashboard for monitoring load test results
- Comparison reports between test runs

# BahtBrowse Production Deployment Guide

This guide provides instructions for deploying BahtBrowse to a production environment.

## Prerequisites

Before deploying BahtBrowse to production, ensure you have the following:

1. A server with Docker and Docker Compose installed
2. Sufficient disk space for the Docker images and containers
3. Appropriate network access and firewall rules
4. Domain name (optional but recommended)
5. SSL certificates (optional but recommended for production)

## Deployment Steps

### 1. Clone the Repository

```bash
git clone https://github.com/tenbahtsecurity/bahtbrowse.git
cd bahtbrowse
```

### 2. Checkout the Production-Ready Branch

```bash
git checkout refactor/cw17-pre-integration
```

### 3. Prepare for Production Deployment

Run the production preparation script:

```bash
./scripts/utils/prepare_production.sh
```

This script will:
- Run tests to ensure everything is working
- Build the Firefox plugin
- Build the Docker images
- Create a production configuration file
- Create a deployment script

### 4. Deploy to Production

Run the deployment script:

```bash
./scripts/utils/deploy_production.sh
```

This script will:
- Start the services using the production configuration
- Test the connection to ensure everything is working
- Display the running containers

### 5. Verify the Deployment

After deployment, verify that all services are working correctly:

```bash
curl http://localhost/browse/test-connection
```

You should also manually test the following:
- Access the BahtBrowse interface at http://localhost
- Test URL redirection by entering a URL in the interface
- Test the Firefox plugin if installed

## Configuration

### Environment Variables

The following environment variables can be configured in the `docker-compose.production.yml` file:

- `REDIS_HOST`: The hostname of the Redis server (default: redis)
- `REDIS_PORT`: The port of the Redis server (default: 6379)
- `LOG_LEVEL`: The log level (default: INFO)
- `VNC_RESOLUTION`: The resolution of the VNC display (default: 1280x800)
- `VNC_PASSWORD`: The password for the VNC server (default: 123456)

### Nginx Configuration

The nginx configuration is located at `nginx/conf.d/bahtbrowse.conf`. You may need to modify this file to:
- Add SSL configuration
- Configure custom domains
- Adjust proxy settings

### Redis Persistence

Redis data is persisted in a Docker volume named `redis_data`. To backup this data:

```bash
docker run --rm -v bahtbrowse-prod_redis_data:/data -v $(pwd)/backup:/backup alpine tar -czvf /backup/redis_data.tar.gz /data
```

## Monitoring

### Logs

Logs are stored in the `logs` directory. You can view them with:

```bash
ls -la logs
cat logs/api.log
cat logs/worker.log
```

### Container Status

To check the status of the containers:

```bash
docker ps --filter "name=bahtbrowse-prod"
```

### Resource Usage

To monitor resource usage:

```bash
docker stats $(docker ps --filter "name=bahtbrowse-prod" --format "{{.Names}}")
```

## Troubleshooting

### Service Not Starting

If a service fails to start, check the logs:

```bash
docker compose -f docker/compose/docker-compose.production.yml -p bahtbrowse-prod logs <service_name>
```

### Connection Issues

If you can't connect to the BahtBrowse interface:

1. Check if the containers are running:
   ```bash
   docker ps --filter "name=bahtbrowse-prod"
   ```

2. Check the nginx logs:
   ```bash
   docker logs bahtbrowse-prod_nginx_1
   ```

3. Test the API directly:
   ```bash
   curl http://localhost:8082/test-connection
   ```

### VNC Issues

If you have issues with the VNC interface:

1. Check the VNC logs:
   ```bash
   docker logs bahtbrowse-prod_vnc_1
   ```

2. Test the VNC connection directly:
   ```bash
   curl http://localhost:6080
   ```

## Maintenance

### Updating

To update BahtBrowse:

1. Pull the latest changes:
   ```bash
   git pull origin refactor/cw17-pre-integration
   ```

2. Rebuild and redeploy:
   ```bash
   ./scripts/utils/prepare_production.sh
   ./scripts/utils/deploy_production.sh
   ```

### Backup

To backup the BahtBrowse data:

1. Backup the Redis data:
   ```bash
   docker run --rm -v bahtbrowse-prod_redis_data:/data -v $(pwd)/backup:/backup alpine tar -czvf /backup/redis_data.tar.gz /data
   ```

2. Backup the logs:
   ```bash
   tar -czvf backup/logs.tar.gz logs
   ```

### Scaling

To scale the worker service:

```bash
docker compose -f docker/compose/docker-compose.production.yml -p bahtbrowse-prod up -d --scale worker=3
```

## Security Considerations

### VNC Password

Change the default VNC password in the `docker-compose.production.yml` file:

```yaml
vnc:
  environment:
    - VNC_PASSWORD=your_secure_password
```

### Redis Security

By default, Redis is not exposed to the public network. If you need to expose it, add authentication:

1. Create a Redis configuration file:
   ```bash
   echo "requirepass your_secure_password" > redis.conf
   ```

2. Update the Redis service in `docker-compose.production.yml`:
   ```yaml
   redis:
     command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
     volumes:
       - ./redis.conf:/usr/local/etc/redis/redis.conf
   ```

3. Update the API and worker services to use the password:
   ```yaml
   api:
     environment:
       - REDIS_PASSWORD=your_secure_password
   worker:
     environment:
       - REDIS_PASSWORD=your_secure_password
   ```

### SSL Configuration

For production, it's recommended to use SSL. Update the nginx configuration to use SSL:

1. Obtain SSL certificates (e.g., using Let's Encrypt)

2. Update the nginx configuration in `nginx/conf.d/bahtbrowse.conf`:
   ```nginx
   server {
       listen 80;
       server_name your_domain.com;
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name your_domain.com;

       ssl_certificate /etc/nginx/ssl/fullchain.pem;
       ssl_certificate_key /etc/nginx/ssl/privkey.pem;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers HIGH:!aNULL:!MD5;

       # Rest of your configuration...
   }
   ```

3. Update the nginx service in `docker-compose.production.yml` to mount the SSL certificates:
   ```yaml
   nginx:
     volumes:
       - ./ssl:/etc/nginx/ssl
   ```

## Conclusion

Following this guide, you should have a production-ready deployment of BahtBrowse. If you encounter any issues, refer to the troubleshooting section or check the logs for more information.

# Product Requirements Document: Celery and Container Logging Integration with ELK Stack

## 1. Introduction

### 1.1 Purpose

This document outlines the requirements for integrating Celery task logging and Docker container logging with the ELK (Elasticsearch, Logstash, Kibana) stack in the 10Baht bahtBrowse system. This integration will provide comprehensive visibility into task execution, container lifecycle, and application performance.

### 1.2 Scope

This integration will focus on:
1. **Celery Task Logging**: Capturing task execution details, performance metrics, and errors
2. **Container Logging**: Collecting logs from all Docker containers, with emphasis on browser containers
3. **Log Aggregation**: Centralizing logs from all system components in the ELK stack
4. **Visualization**: Creating dashboards for monitoring Celery tasks and container performance

### 1.3 Definitions

- **Celery**: Distributed task queue system used in 10Baht bahtBrowse
- **Flower**: Web-based tool for monitoring Celery tasks
- **Docker Container**: Isolated environment for running applications
- **ELK Stack**: Elasticsearch, Logstash, and Kibana for log management
- **Filebeat**: Lightweight log shipper for forwarding logs to Logstash/Elasticsearch
- **Structured Logging**: Logging in a consistent, machine-readable format (e.g., JSON)

## 2. Product Overview

### 2.1 Product Perspective

The Celery and container logging integration will extend the existing ELK stack implementation to capture detailed information about task execution and container operations. This will provide a unified view of system activity and performance.

```mermaid
graph TD
    subgraph "10Baht bahtBrowse"
        A[Celery Workers] --> |Task Logs| B[Celery Logger]
        C[Browser Containers] --> |Container Logs| D[Docker Logging Driver]
        E[API Server] --> |Application Logs| F[Application Logger]
        
        B --> G[Log Files]
        D --> H[Docker Log Files]
        F --> G
        
        I[Filebeat] --> G
        I --> H
    end
    
    subgraph "ELK Stack"
        J[Logstash] --> K[Elasticsearch]
        I --> J
        K --> L[Kibana]
    end
    
    subgraph "Monitoring Dashboards"
        L --> M[Celery Task Dashboard]
        L --> N[Container Performance Dashboard]
        L --> O[System Health Dashboard]
    end
```

### 2.2 Product Functions

The Celery and container logging integration will:

1. Capture detailed information about Celery task execution
2. Collect logs from all Docker containers in the system
3. Structure and enrich logs with metadata
4. Aggregate logs in the ELK stack
5. Provide real-time dashboards for monitoring tasks and containers
6. Enable alerting for task failures and container issues
7. Support historical analysis of task performance and container health

### 2.3 User Classes and Characteristics

- **System Administrators**: Need comprehensive monitoring of system health
- **DevOps Engineers**: Require detailed metrics for troubleshooting
- **Development Team**: Need visibility into task execution and errors
- **Product Managers**: Interested in system performance and user experience

## 3. Requirements

### 3.1 Functional Requirements

#### 3.1.1 Celery Logging

- **FR1.1**: Capture task execution details (task ID, name, arguments, result)
- **FR1.2**: Log task state transitions (pending, started, success, failure)
- **FR1.3**: Record task execution time and resource usage
- **FR1.4**: Log task retries and failures with error details
- **FR1.5**: Capture worker information (hostname, process ID)
- **FR1.6**: Log periodic task execution (scheduled tasks)
- **FR1.7**: Record task queue metrics (queue length, processing time)
- **FR1.8**: Log task revocation and termination events

#### 3.1.2 Container Logging

- **FR2.1**: Capture container lifecycle events (creation, start, stop, removal)
- **FR2.2**: Log container stdout and stderr output
- **FR2.3**: Record container resource usage (CPU, memory, network)
- **FR2.4**: Log container health check results
- **FR2.5**: Capture container metadata (ID, name, image, labels)
- **FR2.6**: Record container network activity
- **FR2.7**: Log container exit codes and error conditions
- **FR2.8**: Capture container environment variables (non-sensitive)

#### 3.1.3 Log Processing and Enrichment

- **FR3.1**: Structure logs in JSON format
- **FR3.2**: Enrich logs with metadata (timestamp, hostname, service name)
- **FR3.3**: Correlate logs with request IDs and session IDs
- **FR3.4**: Parse log levels and categorize messages
- **FR3.5**: Extract error details and stack traces
- **FR3.6**: Normalize timestamps to UTC
- **FR3.7**: Filter sensitive information from logs
- **FR3.8**: Add context information to logs (environment, version)

#### 3.1.4 Log Aggregation and Storage

- **FR4.1**: Collect logs from all system components
- **FR4.2**: Forward logs to Elasticsearch via Logstash
- **FR4.3**: Index logs with appropriate mappings
- **FR4.4**: Implement log rotation and retention policies
- **FR4.5**: Support log archiving for long-term storage
- **FR4.6**: Enable log compression for efficient storage
- **FR4.7**: Implement index lifecycle management
- **FR4.8**: Support log backup and recovery

#### 3.1.5 Visualization and Monitoring

- **FR5.1**: Create Celery task monitoring dashboard
- **FR5.2**: Develop container performance dashboard
- **FR5.3**: Implement system health overview dashboard
- **FR5.4**: Create error tracking and analysis dashboard
- **FR5.5**: Develop task queue monitoring visualizations
- **FR5.6**: Implement container resource usage visualizations
- **FR5.7**: Create log search and analysis interface
- **FR5.8**: Develop custom visualizations for specific metrics

#### 3.1.6 Alerting

- **FR6.1**: Configure alerts for task failures
- **FR6.2**: Set up notifications for container crashes
- **FR6.3**: Implement alerts for resource usage thresholds
- **FR6.4**: Create alerts for error rate spikes
- **FR6.5**: Set up notifications for queue backlog
- **FR6.6**: Implement alerts for system health issues
- **FR6.7**: Create alerts for security-related events
- **FR6.8**: Support multiple notification channels (email, Slack)

### 3.2 Non-Functional Requirements

#### 3.2.1 Performance

- **NFR1.1**: Log collection should have minimal impact on task execution
- **NFR1.2**: Container logging should not affect container performance
- **NFR1.3**: Log processing should handle peak load during high activity
- **NFR1.4**: Elasticsearch queries should respond within 2 seconds
- **NFR1.5**: Dashboards should load within 3 seconds

#### 3.2.2 Scalability

- **NFR2.1**: Support logging from at least 100 concurrent containers
- **NFR2.2**: Handle logs from at least 50 Celery workers
- **NFR2.3**: Process at least 1000 log messages per second
- **NFR2.4**: Scale horizontally to accommodate increased load

#### 3.2.3 Reliability

- **NFR3.1**: No log loss during normal operation
- **NFR3.2**: Buffering of logs during temporary ELK stack unavailability
- **NFR3.3**: Automatic recovery from logging component failures
- **NFR3.4**: Monitoring of the logging infrastructure itself

#### 3.2.4 Security

- **NFR4.1**: Secure transmission of logs (TLS)
- **NFR4.2**: Filtering of sensitive information from logs
- **NFR4.3**: Authentication for accessing Kibana dashboards
- **NFR4.4**: Authorization controls for log access
- **NFR4.5**: Audit logging for ELK stack operations

#### 3.2.5 Maintainability

- **NFR5.1**: Consistent logging format across all components
- **NFR5.2**: Documentation of logging configuration
- **NFR5.3**: Versioning of dashboards and visualizations
- **NFR5.4**: Automated deployment of logging configuration

## 4. Implementation Details

### 4.1 Celery Logging Implementation

#### 4.1.1 Celery Configuration

```python
# celery_config.py
import logging
import json
from celery.signals import task_prerun, task_postrun, task_failure, task_retry, worker_ready
from celery.utils.log import get_task_logger
from logging.handlers import RotatingFileHandler
import socket
import time
import os

# Configure Celery logger
logger = get_task_logger(__name__)

# JSON Formatter for structured logging
class JsonFormatter(logging.Formatter):
    def format(self, record):
        log_record = {
            "timestamp": time.time(),
            "level": record.levelname,
            "message": record.getMessage(),
            "logger": record.name,
            "path": record.pathname,
            "line": record.lineno,
            "process": record.process,
            "thread": record.thread,
            "host": socket.gethostname(),
            "service": "celery",
        }
        
        # Add exception info if available
        if record.exc_info:
            log_record["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
        
        # Add extra fields from record
        if hasattr(record, "task_id"):
            log_record["task_id"] = record.task_id
        if hasattr(record, "task_name"):
            log_record["task_name"] = record.task_name
        if hasattr(record, "task_args"):
            log_record["task_args"] = str(record.task_args)
        if hasattr(record, "task_kwargs"):
            log_record["task_kwargs"] = str(record.task_kwargs)
        if hasattr(record, "task_runtime"):
            log_record["task_runtime"] = record.task_runtime
        if hasattr(record, "task_state"):
            log_record["task_state"] = record.task_state
        if hasattr(record, "task_result"):
            log_record["task_result"] = str(record.task_result)
        if hasattr(record, "worker_id"):
            log_record["worker_id"] = record.worker_id
        
        return json.dumps(log_record)

# Configure file handler
log_file = os.path.join('/var/log/bahtbrowse', 'celery.log')
handler = RotatingFileHandler(log_file, maxBytes=10485760, backupCount=10)
handler.setFormatter(JsonFormatter())

# Add handler to logger
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Celery signal handlers
@task_prerun.connect
def task_prerun_handler(task_id, task, args, kwargs, **kw):
    logger.info(
        "Task started",
        extra={
            "task_id": task_id,
            "task_name": task.name,
            "task_args": args,
            "task_kwargs": kwargs,
            "task_state": "STARTED"
        }
    )

@task_postrun.connect
def task_postrun_handler(task_id, task, args, kwargs, retval, state, **kw):
    runtime = kw.get('runtime', 0)
    logger.info(
        f"Task completed with state: {state}",
        extra={
            "task_id": task_id,
            "task_name": task.name,
            "task_args": args,
            "task_kwargs": kwargs,
            "task_state": state,
            "task_result": retval,
            "task_runtime": runtime
        }
    )

@task_failure.connect
def task_failure_handler(task_id, exception, args, kwargs, traceback, einfo, **kw):
    logger.error(
        f"Task failed: {str(exception)}",
        extra={
            "task_id": task_id,
            "task_name": kwargs.get('sender', {}).name if kwargs.get('sender') else 'unknown',
            "task_args": args,
            "task_kwargs": kwargs,
            "task_state": "FAILURE",
            "exception": str(exception),
            "traceback": str(einfo)
        }
    )

@task_retry.connect
def task_retry_handler(request, reason, einfo, **kwargs):
    logger.warning(
        f"Task retry: {reason}",
        extra={
            "task_id": request.id,
            "task_name": request.task,
            "task_args": request.args,
            "task_kwargs": request.kwargs,
            "task_state": "RETRY",
            "retry_reason": str(reason),
            "exception": str(einfo) if einfo else None
        }
    )

@worker_ready.connect
def worker_ready_handler(**kwargs):
    logger.info(
        "Worker ready",
        extra={
            "worker_id": socket.gethostname(),
            "service": "celery_worker"
        }
    )
```

#### 4.1.2 Task Execution Metrics

```python
# task_metrics.py
from celery.signals import task_prerun, task_postrun
import time
import psutil
import json
import os

# Store task start times
task_start_times = {}

# Directory for metrics
METRICS_DIR = '/var/log/bahtbrowse/metrics'
os.makedirs(METRICS_DIR, exist_ok=True)

@task_prerun.connect
def task_prerun_metrics(task_id, task, args, kwargs, **kw):
    # Record start time and initial resource usage
    task_start_times[task_id] = {
        'start_time': time.time(),
        'initial_cpu': psutil.cpu_percent(interval=None),
        'initial_memory': psutil.virtual_memory().percent
    }

@task_postrun.connect
def task_postrun_metrics(task_id, task, args, kwargs, retval, state, **kw):
    if task_id in task_start_times:
        # Calculate metrics
        end_time = time.time()
        start_data = task_start_times.pop(task_id)
        
        duration = end_time - start_data['start_time']
        cpu_usage = psutil.cpu_percent(interval=None) - start_data['initial_cpu']
        memory_usage = psutil.virtual_memory().percent - start_data['initial_memory']
        
        # Create metrics record
        metrics = {
            'timestamp': end_time,
            'task_id': task_id,
            'task_name': task.name,
            'duration': duration,
            'state': state,
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'args_length': len(args) if args else 0,
            'kwargs_length': len(kwargs) if kwargs else 0
        }
        
        # Write metrics to file
        metrics_file = os.path.join(METRICS_DIR, f'task_metrics_{int(end_time)}.json')
        with open(metrics_file, 'w') as f:
            json.dump(metrics, f)
```

### 4.2 Container Logging Implementation

#### 4.2.1 Docker Compose Configuration

```yaml
# docker-compose.yml (excerpt)
services:
  browser-container:
    image: bahtbrowse/browser:latest
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "browser-container"
        tag: "{{.Name}}/{{.ID}}"
        env: "BROWSER_TYPE,CONTAINER_ID,SESSION_ID"
    labels:
      app: "bahtbrowse"
      component: "browser"
      browser_type: "${BROWSER_TYPE:-firefox}"
      container_id: "${CONTAINER_ID}"
```

#### 4.2.2 Container Logging Configuration

```yaml
# filebeat-container.yml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: log
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"
    - add_labels:
        labels: ["app", "component", "browser_type", "container_id"]
    - add_fields:
        target: ''
        fields:
          service: "container"
          log_source: "docker"
```

#### 4.2.3 Container Metrics Collection

```yaml
# metricbeat-container.yml
metricbeat.modules:
- module: docker
  metricsets:
    - "container"
    - "cpu"
    - "memory"
    - "network"
    - "diskio"
  hosts: ["unix:///var/run/docker.sock"]
  period: 10s
  labels.dedot: true
  labels.include: ["app", "component", "browser_type", "container_id"]
```

### 4.3 Log Aggregation Implementation

#### 4.3.1 Filebeat Configuration

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/bahtbrowse/*.log
  json.keys_under_root: true
  json.add_error_key: true
  json.message_key: message
  fields:
    service: bahtbrowse
    log_source: application
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/bahtbrowse/metrics/*.json
  json.keys_under_root: true
  fields:
    service: bahtbrowse
    log_source: metrics
  fields_under_root: true

filebeat.modules:
- module: system
  syslog:
    enabled: true
  auth:
    enabled: true

output.logstash:
  hosts: ["logstash:5044"]
  ssl.enabled: false
```

#### 4.3.2 Logstash Pipeline

```
# celery-container.conf
input {
  beats {
    port => 5044
  }
}

filter {
  # Process Celery logs
  if [service] == "celery" {
    mutate {
      add_field => { "[@metadata][type]" => "celery" }
    }
    
    # Extract task information
    if [task_id] {
      mutate {
        add_field => { "task_id" => "%{[task_id]}" }
      }
    }
    
    if [task_name] {
      mutate {
        add_field => { "task_name" => "%{[task_name]}" }
      }
    }
    
    if [task_state] {
      mutate {
        add_field => { "task_state" => "%{[task_state]}" }
      }
    }
    
    # Parse task runtime
    if [task_runtime] {
      mutate {
        convert => { "task_runtime" => "float" }
      }
    }
  }
  
  # Process container logs
  if [service] == "container" {
    mutate {
      add_field => { "[@metadata][type]" => "container" }
    }
    
    # Extract container information
    if [container][labels][browser_type] {
      mutate {
        add_field => { "browser_type" => "%{[container][labels][browser_type]}" }
      }
    }
    
    if [container][labels][container_id] {
      mutate {
        add_field => { "container_id" => "%{[container][labels][container_id]}" }
      }
    }
    
    if [container][labels][component] {
      mutate {
        add_field => { "component" => "%{[container][labels][component]}" }
      }
    }
  }
  
  # Process metrics
  if [log_source] == "metrics" {
    mutate {
      add_field => { "[@metadata][type]" => "metrics" }
    }
    
    # Convert numeric fields
    if [duration] {
      mutate {
        convert => { "duration" => "float" }
      }
    }
    
    if [cpu_usage] {
      mutate {
        convert => { "cpu_usage" => "float" }
      }
    }
    
    if [memory_usage] {
      mutate {
        convert => { "memory_usage" => "float" }
      }
    }
  }
  
  # Add timestamp
  date {
    match => [ "timestamp", "UNIX", "UNIX_MS", "ISO8601" ]
    target => "@timestamp"
    remove_field => [ "timestamp" ]
  }
}

output {
  # Output to Elasticsearch with different indices based on data type
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "bahtbrowse-%{[@metadata][type]}-%{+YYYY.MM.dd}"
    
    # Set default type if not specified
    if [@metadata][type] == "" {
      index => "bahtbrowse-logs-%{+YYYY.MM.dd}"
    }
  }
}
```

### 4.4 Visualization Implementation

#### 4.4.1 Celery Task Dashboard

```mermaid
graph TD
    A[Celery Task Dashboard]
    A --> B[Task Execution Count]
    A --> C[Task Duration]
    A --> D[Task Success/Failure]
    A --> E[Task Queue Length]
    A --> F[Worker Status]
    A --> G[Task Resource Usage]
    A --> H[Task Error Rate]
```

#### 4.4.2 Container Performance Dashboard

```mermaid
graph TD
    A[Container Performance Dashboard]
    A --> B[Container Count by Type]
    A --> C[Container CPU Usage]
    A --> D[Container Memory Usage]
    A --> E[Container Network I/O]
    A --> F[Container Disk I/O]
    A --> G[Container Lifecycle Events]
    A --> H[Container Error Rate]
```

#### 4.4.3 System Health Dashboard

```mermaid
graph TD
    A[System Health Dashboard]
    A --> B[Overall Error Rate]
    A --> C[API Response Time]
    A --> D[Task Processing Time]
    A --> E[Container Startup Time]
    A --> F[Resource Utilization]
    A --> G[Queue Backlog]
    A --> H[System Alerts]
```

## 5. Implementation Plan

### 5.1 Phase 1: Celery Logging Setup

1. Implement Celery logging configuration
2. Configure signal handlers for task events
3. Set up task metrics collection
4. Configure log rotation and storage
5. Test logging with sample tasks

### 5.2 Phase 2: Container Logging Setup

1. Configure Docker logging drivers
2. Set up container labels for metadata
3. Configure Filebeat for container logs
4. Set up Metricbeat for container metrics
5. Test logging with sample containers

### 5.3 Phase 3: Log Aggregation

1. Configure Filebeat for all log sources
2. Set up Logstash pipeline for log processing
3. Configure Elasticsearch indices and mappings
4. Implement log retention policies
5. Test end-to-end log flow

### 5.4 Phase 4: Visualization and Alerting

1. Create Kibana dashboards for Celery tasks
2. Set up container performance dashboards
3. Implement system health dashboards
4. Configure alerts for critical events
5. Test dashboards and alerts

### 5.5 Phase 5: Integration and Optimization

1. Integrate logging with existing monitoring
2. Optimize log processing for performance
3. Fine-tune log retention and archiving
4. Document logging architecture
5. Train team on using dashboards

## 6. Technical Requirements

### 6.1 Software Requirements

- Celery 5.x
- Docker 20.10+
- Filebeat 8.x
- Metricbeat 8.x
- Logstash 8.x
- Elasticsearch 8.x
- Kibana 8.x
- Python 3.8+

### 6.2 Configuration Requirements

- JSON logging format for all components
- Consistent field naming across log sources
- Proper log rotation and retention
- Secure log transmission
- Appropriate index lifecycle management

### 6.3 Performance Requirements

- Minimal impact on task execution
- Efficient log processing pipeline
- Optimized Elasticsearch queries
- Responsive Kibana dashboards

## 7. Appendices

### 7.1 Sample Celery Task Log

```json
{
  "timestamp": 1619712345.678,
  "level": "INFO",
  "message": "Task completed with state: SUCCESS",
  "logger": "celery.tasks",
  "path": "/app/tasks.py",
  "line": 42,
  "process": 1234,
  "thread": 5678,
  "host": "celery-worker-1",
  "service": "celery",
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "task_name": "app.tasks.process_browser_request",
  "task_args": "(1, 2, 3)",
  "task_kwargs": "{'browser_type': 'firefox', 'user_id': '12345'}",
  "task_state": "SUCCESS",
  "task_result": "Container ID: cont-789",
  "task_runtime": 1.234
}
```

### 7.2 Sample Container Log

```json
{
  "log": "Browser started successfully",
  "stream": "stdout",
  "time": "2023-04-28T12:34:56.789012345Z",
  "container_id": "abcdef123456",
  "container_name": "/browser-firefox-1",
  "source": "stdout",
  "docker": {
    "container_id": "abcdef123456"
  },
  "container": {
    "id": "abcdef123456",
    "name": "/browser-firefox-1",
    "image": {
      "name": "bahtbrowse/browser:latest"
    },
    "labels": {
      "app": "bahtbrowse",
      "component": "browser",
      "browser_type": "firefox",
      "container_id": "cont-789"
    }
  },
  "service": "container",
  "log_source": "docker",
  "browser_type": "firefox",
  "component": "browser"
}
```

### 7.3 Sample Task Metrics

```json
{
  "timestamp": 1619712346.789,
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "task_name": "app.tasks.process_browser_request",
  "duration": 1.234,
  "state": "SUCCESS",
  "cpu_usage": 15.6,
  "memory_usage": 8.7,
  "args_length": 3,
  "kwargs_length": 2,
  "service": "bahtbrowse",
  "log_source": "metrics"
}
```

## 8. Conclusion

The integration of Celery task logging and Docker container logging with the ELK stack will provide comprehensive visibility into the 10Baht bahtBrowse system. This will enable better monitoring, troubleshooting, and optimization of the system, leading to improved performance and reliability.

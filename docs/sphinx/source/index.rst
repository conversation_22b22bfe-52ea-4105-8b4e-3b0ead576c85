.. bahtBrowse documentation master file

Welcome to bahtBrowse Documentation
===================================

**bahtBrowse** is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.

.. image:: _static/bahtbrowse_logo.png
   :alt: bahtBrowse Logo
   :align: center
   :width: 400px

Key Features
-----------

* **Secure Isolation**: Browse the web in isolated Docker containers
* **Multiple Browser Support**: Firefox and Chromium support
* **Seamless Integration**: Firefox plugin for easy access
* **VNC Interface**: Remote access to containerized browsers
* **API Server**: Manage browser sessions programmatically
* **Health Dashboard**: Monitor system health and service status
* **Scalable Architecture**: Deploy for individual users or enterprise environments

User Guides
----------

* :doc:`getting_started/index`: Get up and running quickly
* :doc:`user_guide/index`: Learn how to use bahtBrowse effectively
* :doc:`troubleshooting/index`: Solve common issues

Administrator Guides
------------------

* :doc:`admin_guide/index`: Deploy and manage bahtBrowse
* :doc:`admin_guide/configuration`: Configure bahtBrowse for your environment

Developer Guides
--------------

* :doc:`developer_guide/index`: Understand the architecture and contribute
* :doc:`developer_guide/health_dashboard`: Learn about the Health Dashboard
* :doc:`api_reference/index`: API documentation for developers

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: User Documentation

   getting_started/index
   user_guide/index
   troubleshooting/index

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: Administrator Documentation

   admin_guide/index
   admin_guide/deployment
   admin_guide/configuration
   admin_guide/maintenance

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: Developer Documentation

   developer_guide/index
   developer_guide/architecture
   developer_guide/health_dashboard
   developer_guide/contributing
   developer_guide/testing
   api_reference/index

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

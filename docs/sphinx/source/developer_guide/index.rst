.. _developer_guide:

Developer Guide
=============

This developer guide provides detailed information for developers who want to understand, modify, or contribute to bahtBrowse.

.. toctree::
   :maxdepth: 2

   architecture
   container_management
   health_dashboard
   contributing
   testing

Introduction
-----------

bahtBrowse is an open-source Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. This developer guide will help you understand the architecture, codebase, and development workflow of bahtBrowse.

Whether you want to contribute to the project, extend its functionality, or integrate it into your own systems, this guide will provide the information you need.

System Architecture
-----------------

bahtBrowse consists of several components that work together to provide a secure browsing experience:

1. **Docker Containers**: Isolated environments running Firefox or Chromium browsers
2. **API Server**: Manages browser sessions and handles requests
3. **VNC Interface**: Provides remote access to containerized browsers
4. **Firefox Plugin**: Enables seamless integration with Firefox
5. **Nginx Configuration**: Handles proxying and serving static files
6. **Health Dashboard**: Monitors system health and service status

For a detailed explanation of the architecture, see :doc:`architecture`.

Health Dashboard
--------------

The Health Dashboard provides real-time monitoring of the bahtBrowse system components, including service status, response times, and system resource usage.

Key features include:

1. **Service Status Monitoring**: Real-time monitoring of all bahtBrowse services
2. **Response Time Tracking**: Measurement and display of service response times
3. **Average Response Time Calculation**: Calculation and display of average response times over time
4. **System Resource Monitoring**: Real-time monitoring of CPU, memory, and disk usage

For a detailed explanation of the Health Dashboard, see :doc:`health_dashboard`.

Codebase Structure
----------------

The bahtBrowse codebase is organized as follows:

.. code-block:: text

    bahtbrowse/
    ├── docker/                 # Docker-related files
    │   ├── Dockerfile          # Main Dockerfile for Firefox container
    │   ├── Dockerfile.api      # Dockerfile for API server
    │   └── ...
    ├── files/                  # Files to be copied into containers
    │   ├── app.py              # API server application
    │   ├── session_manager.py  # Session management module
    │   └── ...
    ├── firefox_plugin/         # Firefox plugin source code
    │   ├── background.js       # Background script
    │   ├── manifest.json       # Plugin manifest
    │   └── ...
    ├── docs/                   # Documentation
    │   ├── sphinx/             # Sphinx documentation
    │   └── ...
    ├── tests/                  # Test suite
    │   ├── test_docker.py      # Docker-related tests
    │   ├── test_redirect.py    # Redirection tests
    │   └── ...
    ├── docker-compose.yml      # Docker Compose configuration
    ├── health_api.py           # Health API server
    ├── static/                 # Static files for Health Dashboard
    │   ├── css/                # CSS files
    │   ├── js/                 # JavaScript files
    │   └── icons/              # Icon files
    └── ...

Development Environment
---------------------

To set up a development environment for bahtBrowse, follow these steps:

1. Clone the repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Create a Python virtual environment:

   .. code-block:: bash

      python3 -m venv venv
      source venv/bin/activate

3. Install the development dependencies:

   .. code-block:: bash

      pip install -e ".[dev]"

4. Set up pre-commit hooks:

   .. code-block:: bash

      pre-commit install

5. Build and run the containers:

   .. code-block:: bash

      docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

For more detailed information on setting up a development environment, see :doc:`contributing`.

Development Workflow
------------------

The typical development workflow for bahtBrowse is as follows:

1. **Create a Feature Branch**: Create a new branch for your feature or bug fix.
2. **Make Changes**: Make your changes to the codebase.
3. **Run Tests**: Run the test suite to ensure your changes don't break existing functionality.
4. **Submit a Pull Request**: Submit a pull request to the main repository.

For more detailed information on the development workflow, see :doc:`contributing`.

Testing
------

bahtBrowse has a comprehensive test suite that covers various aspects of the system. To run the tests, use the following command:

.. code-block:: bash

   pytest

For more detailed information on testing, see :doc:`testing`.

API Reference
-----------

For detailed information on the bahtBrowse API, see the :doc:`../api_reference/index` section.

Next Steps
---------

* :doc:`architecture`: Learn about the architecture of bahtBrowse
* :doc:`container_management`: Learn about container management and inactivity timeout
* :doc:`health_dashboard`: Learn about the Health Dashboard
* :doc:`contributing`: Learn how to contribute to bahtBrowse
* :doc:`testing`: Learn how to test bahtBrowse
* :doc:`../api_reference/index`: Explore the API reference

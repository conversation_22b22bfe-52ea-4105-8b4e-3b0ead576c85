.. _health_dashboard:

Health Dashboard
===============

The Health Dashboard provides real-time monitoring of the bahtBrowse system components, including service status, response times, and system resource usage. This document describes the architecture, features, and implementation details of the Health Dashboard.

Overview
-------

The Health Dashboard is a web-based interface that displays the health status of all bahtBrowse services. It provides administrators and developers with a centralized view of the system's operational status, making it easier to identify and troubleshoot issues.

.. figure:: ../_static/health_dashboard.png
   :alt: Health Dashboard
   :align: center

   bahtBrowse Health Dashboard

Key Features
----------

* **Service Status Monitoring**: Real-time monitoring of all bahtBrowse services
* **Response Time Tracking**: Measurement and display of service response times
* **Average Response Time Calculation**: Calculation and display of average response times over time
* **System Resource Monitoring**: Real-time monitoring of CPU, memory, and disk usage
* **Dark Mode Support**: Toggle between light and dark modes for different viewing preferences
* **Auto-Refresh**: Automatic refresh of dashboard data at configurable intervals
* **API Documentation**: Integrated Swagger UI for API documentation

Component Architecture
-------------------

The Health Dashboard consists of several components:

1. **Health API**: A FastAPI-based API that collects and serves health data
2. **Frontend Interface**: A web-based interface that displays health data
3. **Data Collection Services**: Services that collect health data from various components
4. **Storage**: In-memory storage for health data

.. code-block:: text

   Health API ---> bahtBrowse Services (Collects Data)
   Health API ---> Frontend Interface (Serves Data)
   Frontend Interface ---> Service Status (Displays)
   Frontend Interface ---> Response Times (Displays)
   Frontend Interface ---> System Resources (Displays)
   User ---> Frontend Interface (Views)

Health API
--------

The Health API is responsible for collecting and serving health data. It is implemented using FastAPI and provides endpoints for retrieving health data.

**Key Features**:

- **Health Data Collection**: Collects health data from various components
- **Health Data Serving**: Serves health data to the frontend interface
- **API Documentation**: Provides Swagger UI for API documentation

**API Endpoints**:

- ``/health``: Returns the health status of all services
- ``/api-docs``: Returns API documentation in JSON format
- ``/docs``: Serves the Health Dashboard and API documentation

**Implementation**:

The Health API is implemented in Python using the FastAPI framework. It collects health data from various components and stores it in memory.

Frontend Interface
--------------

The Frontend Interface is a web-based interface that displays health data. It is implemented using HTML, CSS, and JavaScript and is served by the Health API.

**Key Features**:

- **Service Status Display**: Displays the status of all services
- **Response Time Display**: Displays response times for all services
- **Average Response Time Display**: Displays average response times over time
- **System Resource Display**: Displays CPU, memory, and disk usage
- **Dark Mode Support**: Supports toggling between light and dark modes
- **Auto-Refresh**: Automatically refreshes data at configurable intervals

**Implementation**:

The Frontend Interface is implemented using HTML, CSS, and JavaScript. It uses fetch API to retrieve data from the Health API and updates the display accordingly.

Response Time Tracking
------------------

The Health Dashboard tracks response times for all services and calculates average response times over time. This provides valuable insights into service performance and helps identify performance issues.

**Key Features**:

- **Response Time Measurement**: Measures response times for all services
- **Average Response Time Calculation**: Calculates average response times over time
- **Per-Service Response Time Tracking**: Tracks response times for each service separately
- **Visual Indicators**: Provides visual indicators for response time trends

**Implementation**:

Response times are measured by the Health API when it collects health data from services. The response time is the time it takes for a service to respond to a health check request.

Average response times are calculated using a running average algorithm:

.. code-block:: javascript

   // Calculate average response time
   let totalResponseTime = 0;
   let responseTimeCount = 0;

   // Sum up all response times from this update
   Object.entries(services).forEach(([serviceName, service]) => {
       if (service.response_time) {
           totalResponseTime += service.response_time;
           responseTimeCount++;

           // Track per-service response times
           if (!responseTimes.services[serviceName]) {
               responseTimes.services[serviceName] = {
                   count: 0,
                   total: 0,
                   average: 0
               };
           }

           responseTimes.services[serviceName].total += service.response_time;
           responseTimes.services[serviceName].count++;
           responseTimes.services[serviceName].average =
               responseTimes.services[serviceName].total / responseTimes.services[serviceName].count;
       }
   });

   // Update the running average
   if (responseTimeCount > 0) {
       const currentAvg = totalResponseTime / responseTimeCount;
       responseTimes.total += currentAvg;
       responseTimes.count++;
       responseTimes.average = responseTimes.total / responseTimes.count;
   }

The average response time is displayed in the Health Dashboard for each service and for the overall system.

System Resource Monitoring
----------------------

The Health Dashboard monitors system resources such as CPU, memory, and disk usage. This provides insights into system performance and helps identify resource constraints.

**Key Features**:

- **CPU Usage Monitoring**: Monitors CPU usage
- **Memory Usage Monitoring**: Monitors memory usage
- **Disk Usage Monitoring**: Monitors disk usage
- **Visual Indicators**: Provides visual indicators for resource usage

**Implementation**:

System resources are monitored by the Health API using the psutil library. The data is collected and served to the frontend interface, which displays it using progress bars and other visual indicators.

Dark Mode Support
-------------

The Health Dashboard supports toggling between light and dark modes for different viewing preferences.

**Key Features**:

- **Light Mode**: Default mode with light background and dark text
- **Dark Mode**: Alternative mode with dark background and light text
- **System Preference Detection**: Detects system preference for light or dark mode
- **User Preference Storage**: Stores user preference for light or dark mode

**Implementation**:

Dark mode is implemented using CSS variables and a toggle button. The user's preference is stored in localStorage and applied when the dashboard loads.

Auto-Refresh
---------

The Health Dashboard automatically refreshes data at configurable intervals to provide real-time monitoring.

**Key Features**:

- **Configurable Refresh Interval**: Allows configuring the refresh interval
- **Manual Refresh**: Allows manually refreshing the data
- **Visual Indicators**: Provides visual indicators for refresh status

**Implementation**:

Auto-refresh is implemented using JavaScript's setInterval function. The refresh interval is configurable and defaults to 15 seconds.

API Documentation
-------------

The Health Dashboard includes integrated Swagger UI for API documentation.

**Key Features**:

- **API Endpoint Documentation**: Documents all API endpoints
- **Request and Response Examples**: Provides examples of requests and responses
- **Interactive Testing**: Allows testing API endpoints directly from the documentation

**Implementation**:

API documentation is implemented using Swagger UI and is served by the Health API. The documentation is generated from the API endpoints and their descriptions.

Usage
----

To access the Health Dashboard, navigate to the `/docs` endpoint of the Health API:

.. code-block:: bash

   http://localhost:8000/docs

The dashboard will display the health status of all services, response times, and system resource usage.

To toggle between light and dark modes, click the moon/sun icon in the top right corner of the dashboard.

To manually refresh the data, click the refresh button in the dashboard.

Configuration
----------

The Health Dashboard can be configured through environment variables:

- ``HEALTH_API_PORT``: Port for the Health API (default: 8000)

Future Enhancements
----------------

The following enhancements are planned for future versions of the Health Dashboard:

**Historical Data**:

Implement historical data storage and visualization to track performance over time.

**Alerting**:

Add alerting capabilities to notify administrators of service issues.

**Customizable Dashboard**:

Allow customizing the dashboard layout and displayed metrics.

**Service Dependencies**:

Visualize service dependencies and their impact on system health.

**Performance Optimization**:

Optimize data collection and display for improved performance.

For more information on contributing to these enhancements, see the :doc:`contributing` section.

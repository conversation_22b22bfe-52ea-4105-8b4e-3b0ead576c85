.. _container_management:

Container Management
===================

This document provides detailed information about the container management system in bahtBrowse, including container creation, recycling, and inactivity timeout.

Overview
--------

The container management system is responsible for creating, managing, and recycling Docker containers for browser sessions. Each container provides an isolated environment for a browser session, ensuring security and privacy.

The container management system ensures that:

- Containers are created on demand
- Containers are isolated from each other
- Containers are recycled after use
- Containers are terminated after a period of inactivity
- Resources are efficiently managed

Container Lifecycle
------------------

Containers in bahtBrowse go through the following lifecycle:

1. **Creation**: A new container is created when a user requests a browser session
2. **Initialization**: The container is initialized with the necessary software and configuration
3. **Assignment**: The container is assigned to a user
4. **Usage**: The user interacts with the browser in the container
5. **Inactivity**: If the user stops interacting with the container, it becomes idle
6. **Timeout**: After a period of inactivity, the container is terminated
7. **Recycling**: The container is cleaned and returned to the pool or terminated

Inactivity Timeout
-----------------

One of the key features of the container management system is the inactivity timeout. This feature automatically terminates containers that have been idle for a specified period, helping to conserve resources and enhance security.

.. code-block:: python

   # Configuration for inactivity timeout
   CONTAINER_IDLE_TIMEOUT = 180  # 3 minutes in seconds

The inactivity timeout works as follows:

1. The system tracks the last activity time for each container
2. A periodic task checks for containers that have been idle for longer than the timeout period
3. If a container has been idle for too long, it is terminated and recycled
4. A new container is queued to replace the terminated one

This ensures that resources are not wasted on idle containers and that containers are promptly recycled when they are no longer needed.

Activity Tracking
----------------

To implement the inactivity timeout, the system needs to track user activity in each container. This is done through the following mechanisms:

1. **API Endpoint**: The frontend periodically calls an API endpoint to update the last activity timestamp
2. **Event Listeners**: The frontend listens for user events (mouse movements, keyboard input, etc.) to detect activity
3. **Redis Storage**: The last activity timestamp is stored in Redis for each container

.. code-block:: python

   # Update container activity
   def update_container_activity(container_id):
       """Update the last activity timestamp for a container."""
       redis_client.hset(
           f"container:{container_id}",
           "last_activity",
           time.time()
       )

The frontend includes a JavaScript activity tracker that monitors user activity and periodically sends updates to the server:

.. code-block:: javascript

   class ActivityTracker {
       constructor(userId, pingInterval = 60000) {
           this.userId = userId;
           this.pingInterval = pingInterval;
           this.lastActivityTime = Date.now();
           this.activityEvents = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];
       }

       start() {
           // Add event listeners for user activity
           this.activityEvents.forEach(eventType => {
               window.addEventListener(eventType, this.handleUserActivity.bind(this), true);
           });

           // Start the ping timer
           this.timerId = setInterval(() => this.pingServer(), this.pingInterval);
       }

       handleUserActivity() {
           this.lastActivityTime = Date.now();
       }

       async pingServer() {
           // Only ping if there has been activity since the last ping
           const now = Date.now();
           const idleTime = now - this.lastActivityTime;
           
           // If user has been idle for more than 2 minutes, don't ping
           if (idleTime > 120000) {
               return;
           }
           
           // Send activity update to server
           await axios.post('/api/containers/activity', {
               user_id: this.userId
           });
       }
   }

Container Cleanup
----------------

When a container is terminated due to inactivity, the system performs the following cleanup tasks:

1. **Logging**: The termination event is logged for auditing purposes
2. **Container Recycling**: The container is recycled to clean up any user data
3. **Resource Release**: Resources allocated to the container are released
4. **Pool Management**: The container pool is updated to reflect the change

.. code-block:: python

   # Cleanup idle containers
   def cleanup_idle_containers(idle_timeout=CONTAINER_IDLE_TIMEOUT):
       """Clean up idle containers."""
       # Get all assigned containers
       assigned_containers = redis_client.smembers('assigned_containers')
       
       current_time = time.time()
       
       for container_id in assigned_containers:
           container_details = get_container_details(container_id)
           
           if not container_details:
               continue
           
           # Check if container is idle
           last_activity = float(container_details.get('last_activity', 0))
           idle_time = current_time - last_activity
           
           if idle_time > idle_timeout:
               logger.info(f"INACTIVITY TIMEOUT: Container {container_id} idle for {idle_time:.2f} seconds")
               recycle_container(container_id)

Configuration
------------

The inactivity timeout can be configured through environment variables or configuration files:

.. code-block:: yaml

   # In docker-compose.yml
   services:
     worker:
       environment:
         - CONTAINER_IDLE_TIMEOUT=180  # 3 minutes in seconds

.. code-block:: python

   # In config.py
   CONTAINER_IDLE_TIMEOUT = int(os.environ.get('CONTAINER_IDLE_TIMEOUT', 180))

The default timeout is 3 minutes (180 seconds), but this can be adjusted based on your specific requirements. A shorter timeout conserves resources but may interrupt users who take short breaks, while a longer timeout provides a better user experience but consumes more resources.

Benefits
-------

The inactivity timeout feature provides several benefits:

1. **Resource Conservation**: Automatically terminates idle containers to free up resources
2. **Enhanced Security**: Reduces the window of exposure for containers
3. **Improved Scalability**: Allows the system to handle more users with the same resources
4. **Automatic Cleanup**: Ensures that containers are properly cleaned up after use
5. **Efficient Resource Allocation**: Allocates resources to active users rather than idle sessions

For more information on the bahtBrowse architecture, see :doc:`architecture`.

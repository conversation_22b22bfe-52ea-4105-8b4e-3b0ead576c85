.. _troubleshooting:

Troubleshooting
==============

This section provides solutions to common issues you might encounter when using bahtBrowse.

.. toctree::
   :maxdepth: 2

   common_issues
   faq

Introduction
-----------

Even with careful setup and configuration, you might encounter issues when using bahtBrowse. This troubleshooting guide aims to help you identify and resolve common problems.

If you can't find a solution to your issue in this guide, please check the :doc:`faq` or consider :doc:`../developer_guide/contributing` to report the issue.

Common Issues
-----------

Here are some common issues you might encounter when using bahtBrowse:

Connection Issues
~~~~~~~~~~~~~~~

**Issue**: Unable to connect to the bahtBrowse service.

**Solution**: 

1. Check if the bahtBrowse service is running:

   .. code-block:: bash

      docker-compose ps

2. Verify that the ports are correctly mapped:

   .. code-block:: bash

      docker-compose ps

3. Check if the ports are in use by other services:

   .. code-block:: bash

      netstat -tuln | grep 8082

4. Restart the service:

   .. code-block:: bash

      docker-compose down
      docker-compose up -d

For more details, see :doc:`common_issues`.

404 Errors
~~~~~~~~~

**Issue**: Receiving 404 errors when trying to access a URL through bahtBrowse.

**Solution**:

1. Check if the URL is valid and accessible directly from your browser.
2. Verify that the API server is running and accessible.
3. Check the logs for any error messages:

   .. code-block:: bash

      docker logs bahtbrowse-firefox

4. Ensure that the nginx configuration is correct and that the novnc_proxy.html file is being served correctly.

For more details, see :doc:`common_issues`.

Firefox Plugin Issues
~~~~~~~~~~~~~~~~~~

**Issue**: The Firefox plugin is not working correctly.

**Solution**:

1. Check if the plugin is installed correctly:
   - Open Firefox
   - Navigate to ``about:addons``
   - Verify that the bahtBrowse plugin is listed and enabled

2. Check if the plugin is configured correctly:
   - Click on the plugin icon in the toolbar
   - Click on "Options"
   - Verify that the host and port settings are correct

3. Check the browser console for any error messages:
   - Press F12 to open the developer tools
   - Go to the Console tab
   - Look for any error messages related to the plugin

For more details, see :doc:`common_issues`.

Performance Issues
~~~~~~~~~~~~~~~

**Issue**: The bahtBrowse service is slow or unresponsive.

**Solution**:

1. Check the system resources:
   - CPU usage: ``top``
   - Memory usage: ``free -h``
   - Disk usage: ``df -h``

2. Check the Docker container resources:
   - ``docker stats``

3. Consider increasing the resources allocated to Docker.

4. Optimize the browser settings for performance.

For more details, see :doc:`common_issues`.

Logging and Debugging
------------------

bahtBrowse provides several logging mechanisms to help you diagnose issues:

1. **API Server Logs**:
   
   .. code-block:: bash

      docker logs bahtbrowse-api

2. **Browser Container Logs**:
   
   .. code-block:: bash

      docker logs bahtbrowse-firefox

3. **Browser Console Logs**:
   
   These are logged to ``/tmp/browser_console.log`` inside the container.

4. **VNC Server Logs**:
   
   These are logged to ``/tmp/x11vnc.log`` inside the container.

For more advanced debugging techniques, see :doc:`common_issues`.

Frequently Asked Questions
------------------------

For answers to frequently asked questions, see :doc:`faq`.

Getting Help
----------

If you can't find a solution to your issue in this guide, you can:

1. Check the :doc:`faq` for answers to common questions.
2. Search for similar issues in the GitHub repository.
3. Report the issue on GitHub following the guidelines in :doc:`../developer_guide/contributing`.

.. _common_issues:

Common Issues
===========

This document provides solutions to common issues you might encounter when using bahtBrowse.

Connection Issues
--------------

Unable to Connect to bahtBrowse Service
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: You cannot connect to the bahtBrowse service.

**Symptoms**:
- Firefox plugin shows a disconnected icon
- Error message: "Unable to connect to bahtBrowse service"
- Browser redirects fail with connection errors

**Solutions**:

1. **Check if the service is running**:

   .. code-block:: bash

      docker-compose ps

   Ensure that the bahtbrowse-firefox and bahtbrowse-api containers are running.

2. **Check container logs**:

   .. code-block:: bash

      docker logs bahtbrowse-firefox
      docker logs bahtbrowse-api

   Look for any error messages that might indicate the cause of the issue.

3. **Check port mappings**:

   .. code-block:: bash

      docker-compose ps

   Ensure that the ports are correctly mapped:
   - 6081:6080 for VNC
   - 8080:80 for HTTP
   - 8082:8082 for API

4. **Check if ports are in use**:

   .. code-block:: bash

      netstat -tuln | grep 8082
      netstat -tuln | grep 6081
      netstat -tuln | grep 8080

   If the ports are in use by other services, you'll need to stop those services or configure bahtBrowse to use different ports.

5. **Restart the service**:

   .. code-block:: bash

      docker-compose down
      docker-compose up -d

   This will stop and restart the containers.

6. **Check firewall settings**:

   Ensure that your firewall allows connections to the required ports.

7. **Check network configuration**:

   If you're running bahtBrowse on a remote server, ensure that the server is accessible from your network.

VNC Connection Fails
~~~~~~~~~~~~~~~~

**Issue**: You can connect to the bahtBrowse service, but the VNC connection fails.

**Symptoms**:
- Error message: "Unable to connect to VNC server"
- Black screen in the VNC interface
- VNC interface loads but shows an error

**Solutions**:

1. **Check if the VNC server is running**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox ps aux | grep x11vnc

   You should see an x11vnc process running.

2. **Check VNC server logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /tmp/x11vnc.log

   Look for any error messages that might indicate the cause of the issue.

3. **Check if the VNC port is accessible**:

   .. code-block:: bash

      telnet localhost 6081

   If you can connect, the port is accessible.

4. **Check nginx configuration**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /etc/nginx/sites-enabled/proxy-server.conf

   Ensure that the nginx configuration includes a location block for the VNC interface:

   .. code-block:: nginx

      location /vnc1/ {
          proxy_pass http://localhost:6080/;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
          
          # WebSocket support
          proxy_read_timeout 61s;
          proxy_buffering off;
      }

5. **Restart the VNC server**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox pkill x11vnc
      docker exec -it bahtbrowse-firefox x11vnc -display :1 -forever -shared -rfbport 6080 -nopw -listen localhost -xkb -bg -logfile /tmp/x11vnc.log

   This will stop and restart the VNC server.

Browser Issues
-----------

Browser Fails to Launch
~~~~~~~~~~~~~~~~~~~

**Issue**: The browser fails to launch in the container.

**Symptoms**:
- Error message: "Error launching browser"
- VNC interface shows a blank screen
- Browser process is not running in the container

**Solutions**:

1. **Check if the browser is installed**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox ls -la /tmp/firefox

   For Firefox, you should see the Firefox executable.

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox ls -la /usr/bin/chromium-browser

   For Chromium, you should see the Chromium executable.

2. **Check browser logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /tmp/browser.log

   Look for any error messages that might indicate the cause of the issue.

3. **Check if the browser process is running**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox ps aux | grep firefox
      docker exec -it bahtbrowse-firefox ps aux | grep chromium

   You should see a browser process running.

4. **Check if the X server is running**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox ps aux | grep Xvfb

   You should see an Xvfb process running.

5. **Restart the browser**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox pkill firefox
      docker exec -it bahtbrowse-firefox DISPLAY=:1 /tmp/firefox/firefox --new-instance --url "https://example.com" --kiosk &

   This will stop and restart the browser.

Browser Crashes
~~~~~~~~~~~~

**Issue**: The browser crashes during use.

**Symptoms**:
- Browser window disappears
- Error message: "Browser has crashed"
- VNC interface shows a blank screen

**Solutions**:

1. **Check browser logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /tmp/browser.log

   Look for any error messages that might indicate the cause of the crash.

2. **Check system resources**:

   .. code-block:: bash

      docker stats bahtbrowse-firefox

   Ensure that the container has enough CPU and memory resources.

3. **Try a different browser**:

   If Firefox is crashing, try Chromium:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=chromium

   If Chromium is crashing, try Firefox:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=firefox

4. **Try compatibility mode**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&mode=compatibility

   Compatibility mode enables additional features that may help with complex websites.

5. **Disable JavaScript execution**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&disable_js_execution=true

   This can help if the crash is caused by JavaScript.

6. **Increase JavaScript timeout**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&js_timeout=30000

   This increases the JavaScript execution timeout to 30 seconds.

7. **Restart the container**:

   .. code-block:: bash

      docker-compose restart bahtbrowse-firefox

   This will restart the container.

Website Compatibility Issues
~~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: Some websites don't work correctly in bahtBrowse.

**Symptoms**:
- Website layout is broken
- Interactive elements don't work
- Website shows an error message

**Solutions**:

1. **Try compatibility mode**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&mode=compatibility

   Compatibility mode enables additional features that may help with complex websites.

2. **Try a different browser**:

   If Firefox is having issues, try Chromium:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=chromium

   If Chromium is having issues, try Firefox:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=firefox

3. **Check if JavaScript is enabled**:

   Some websites require JavaScript to function correctly. Ensure that JavaScript is enabled:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&disable_js_execution=false

4. **Check if the website requires cookies**:

   Some websites require cookies to function correctly. Ensure that cookies are enabled in the browser.

5. **Check if the website requires authentication**:

   Some websites require authentication to access. Ensure that you're logged in to the website.

6. **Check if the website requires a specific locale**:

   Some websites require a specific locale to function correctly. Try setting a different locale:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&locale=en-US
      http://localhost:8082/browse/?url=https://example.com&locale=de-DE

Firefox Plugin Issues
-----------------

Plugin Not Connecting
~~~~~~~~~~~~~~~~~

**Issue**: The Firefox plugin cannot connect to the bahtBrowse service.

**Symptoms**:
- Plugin icon shows a disconnected state
- Error message: "Unable to connect to bahtBrowse service"
- Clicking the plugin button does nothing

**Solutions**:

1. **Check plugin settings**:

   - Click on the plugin icon in the Firefox toolbar
   - Click on "Options" (gear icon)
   - Verify that the host and port settings are correct:
     - Host: ``localhost`` (or the IP address of the server)
     - Port: ``8082``

2. **Check if the service is running**:

   .. code-block:: bash

      docker-compose ps

   Ensure that the bahtbrowse-firefox and bahtbrowse-api containers are running.

3. **Check if the API server is accessible**:

   .. code-block:: bash

      curl http://localhost:8082/test-connection

   You should receive a JSON response indicating that the service is available.

4. **Check browser console for errors**:

   - Press F12 to open the developer tools
   - Go to the Console tab
   - Look for any error messages related to the plugin

5. **Reinstall the plugin**:

   - Open Firefox
   - Navigate to ``about:addons``
   - Find the bahtBrowse plugin and click "Remove"
   - Install the plugin again

Plugin Not Working Correctly
~~~~~~~~~~~~~~~~~~~~~~~~

**Issue**: The Firefox plugin is not working correctly.

**Symptoms**:
- Plugin icon is visible but clicking it does nothing
- Right-click context menu items are missing
- Plugin settings cannot be saved

**Solutions**:

1. **Check browser console for errors**:

   - Press F12 to open the developer tools
   - Go to the Console tab
   - Look for any error messages related to the plugin

2. **Check plugin permissions**:

   - Open Firefox
   - Navigate to ``about:addons``
   - Find the bahtBrowse plugin and click "Permissions"
   - Ensure that the plugin has the necessary permissions

3. **Check if the plugin is enabled**:

   - Open Firefox
   - Navigate to ``about:addons``
   - Find the bahtBrowse plugin and ensure it's enabled

4. **Restart Firefox**:

   - Close Firefox
   - Open Firefox again
   - Check if the plugin works correctly

5. **Reinstall the plugin**:

   - Open Firefox
   - Navigate to ``about:addons``
   - Find the bahtBrowse plugin and click "Remove"
   - Install the plugin again

Performance Issues
--------------

Slow Browser Performance
~~~~~~~~~~~~~~~~~~~

**Issue**: The browser in bahtBrowse is slow or unresponsive.

**Symptoms**:
- Browser takes a long time to load pages
- Browser is unresponsive to user input
- Browser animations are choppy

**Solutions**:

1. **Check system resources**:

   .. code-block:: bash

      docker stats bahtbrowse-firefox

   Ensure that the container has enough CPU and memory resources.

2. **Try a different browser**:

   If Firefox is slow, try Chromium:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=chromium

   If Chromium is slow, try Firefox:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&browser=firefox

3. **Try compatibility mode**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&mode=compatibility

   Compatibility mode enables additional features that may help with complex websites.

4. **Disable JavaScript execution**:

   .. code-block:: bash

      http://localhost:8082/browse/?url=https://example.com&disable_js_execution=true

   This can help if the slowness is caused by JavaScript.

5. **Increase container resources**:

   Edit the ``docker-compose.yml`` file to allocate more CPU and memory to the container:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          deploy:
            resources:
              limits:
                cpus: '2'
                memory: 2G

   This allocates 2 CPU cores and 2GB of memory to the container.

6. **Optimize browser settings**:

   - Disable browser extensions
   - Disable browser animations
   - Disable browser hardware acceleration

7. **Restart the container**:

   .. code-block:: bash

      docker-compose restart bahtbrowse-firefox

   This will restart the container.

Slow VNC Performance
~~~~~~~~~~~~~~~~

**Issue**: The VNC interface is slow or unresponsive.

**Symptoms**:
- VNC interface takes a long time to load
- VNC interface is unresponsive to user input
- VNC interface animations are choppy

**Solutions**:

1. **Check network bandwidth**:

   Ensure that you have sufficient network bandwidth for VNC.

2. **Reduce VNC quality**:

   Edit the ``docker-compose.yml`` file to reduce the VNC quality:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          environment:
            - VNC_QUALITY=50

   This reduces the VNC quality to 50%.

3. **Reduce VNC resolution**:

   Edit the ``docker-compose.yml`` file to reduce the VNC resolution:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          environment:
            - VNC_RESOLUTION=1024x768

   This reduces the VNC resolution to 1024x768.

4. **Use a different VNC client**:

   Instead of using the web-based VNC client, you can use a native VNC client:

   .. code-block:: bash

      vncviewer localhost:6081

   This may provide better performance.

5. **Restart the VNC server**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox pkill x11vnc
      docker exec -it bahtbrowse-firefox x11vnc -display :1 -forever -shared -rfbport 6080 -nopw -listen localhost -xkb -bg -logfile /tmp/x11vnc.log

   This will stop and restart the VNC server.

Security Issues
-----------

Session Hijacking
~~~~~~~~~~~~~

**Issue**: Someone else can access your browser session.

**Symptoms**:
- You see unexpected activity in your browser session
- Your browser session is being used by someone else
- You are redirected to unexpected websites

**Solutions**:

1. **Use HTTPS**:

   Configure bahtBrowse to use HTTPS to encrypt traffic between the client and server.

2. **Enable session validation**:

   Ensure that session validation is enabled in the API server.

3. **Use a firewall**:

   Configure a firewall to restrict access to the bahtBrowse service.

4. **Use a VPN**:

   Use a VPN to encrypt traffic between the client and server.

5. **Implement authentication**:

   Implement authentication to restrict access to the bahtBrowse service.

Container Escape
~~~~~~~~~~~~

**Issue**: A malicious website can escape the container and affect the host system.

**Symptoms**:
- Unexpected files or processes on the host system
- Host system resources are being used by unknown processes
- Host system is compromised

**Solutions**:

1. **Keep Docker up to date**:

   Ensure that Docker is up to date to benefit from security fixes.

2. **Use security options**:

   Edit the ``docker-compose.yml`` file to add security options:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          security_opt:
            - no-new-privileges:true
            - seccomp=unconfined

   This prevents the container from gaining new privileges and uses the default seccomp profile.

3. **Use read-only file systems**:

   Edit the ``docker-compose.yml`` file to make the file system read-only:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          read_only: true
          tmpfs:
            - /tmp
            - /var/run
            - /var/log

   This makes the file system read-only, with temporary file systems for directories that need to be writable.

4. **Use user namespaces**:

   Configure Docker to use user namespaces to map container users to host users.

5. **Use AppArmor or SELinux**:

   Configure AppArmor or SELinux to restrict container capabilities.

Logging and Debugging
-----------------

Viewing Logs
~~~~~~~~~

To view logs for the bahtBrowse service:

1. **API Server Logs**:

   .. code-block:: bash

      docker logs bahtbrowse-api

2. **Browser Container Logs**:

   .. code-block:: bash

      docker logs bahtbrowse-firefox

3. **Browser Console Logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /tmp/browser_console.log

4. **VNC Server Logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /tmp/x11vnc.log

5. **Nginx Logs**:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox cat /var/log/nginx/access.log
      docker exec -it bahtbrowse-firefox cat /var/log/nginx/error.log

Enabling Debug Mode
~~~~~~~~~~~~~~~

To enable debug mode for the bahtBrowse service:

1. **API Server Debug Mode**:

   Edit the ``docker-compose.yml`` file to enable debug mode for the API server:

   .. code-block:: yaml

      services:
        api:
          image: bahtbrowse-api
          environment:
            - DEBUG=true

2. **Browser Debug Mode**:

   Edit the ``docker-compose.yml`` file to enable debug mode for the browser:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          environment:
            - BROWSER_DEBUG=true

3. **VNC Debug Mode**:

   Edit the ``docker-compose.yml`` file to enable debug mode for the VNC server:

   .. code-block:: yaml

      services:
        firefox:
          image: bahtbrowse-firefox
          environment:
            - VNC_DEBUG=true

4. **Nginx Debug Mode**:

   Edit the nginx configuration to enable debug mode:

   .. code-block:: nginx

      error_log /var/log/nginx/error.log debug;

   Then restart nginx:

   .. code-block:: bash

      docker exec -it bahtbrowse-firefox service nginx restart

Debugging Firefox Plugin
~~~~~~~~~~~~~~~~~~~

To debug the Firefox plugin:

1. **Enable Browser Console**:

   - Open Firefox
   - Press Ctrl+Shift+J to open the Browser Console
   - Look for messages related to the plugin

2. **Enable Plugin Debugging**:

   - Open Firefox
   - Navigate to ``about:debugging``
   - Click on "This Firefox"
   - Find the bahtBrowse plugin and click "Inspect"
   - Use the browser developer tools to debug the plugin

3. **Enable Verbose Logging**:

   - Click on the plugin icon in the Firefox toolbar
   - Click on "Options" (gear icon)
   - Enable "Verbose Logging"
   - Check the Browser Console for detailed logs

Getting Help
---------

If you're still experiencing issues after trying the solutions in this guide, you can:

1. **Check the FAQ**:

   See the :doc:`faq` for answers to frequently asked questions.

2. **Search for Similar Issues**:

   Search for similar issues in the GitHub repository.

3. **Report the Issue**:

   Report the issue on GitHub following the guidelines in :doc:`../developer_guide/contributing`.

4. **Contact the Maintainers**:

   Contact the maintainers for help with complex issues.

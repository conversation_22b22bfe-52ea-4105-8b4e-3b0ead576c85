/* Custom CSS for bahtBrowse documentation */

/* General styling */
body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    color: #333;
}

/* Header styling */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
}

/* Link styling */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Code block styling */
pre {
    background-color: #f8f8f8;
    border: 1px solid #e1e4e5;
    border-radius: 4px;
    padding: 12px;
    font-size: 90%;
    overflow-x: auto;
}

code {
    background-color: #f8f8f8;
    border: 1px solid #e1e4e5;
    border-radius: 3px;
    padding: 0 5px;
    font-size: 90%;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    border: 1px solid #e1e4e5;
    margin-bottom: 24px;
    width: 100%;
}

table.docutils td, table.docutils th {
    padding: 8px 16px;
    border: 1px solid #e1e4e5;
}

table.docutils th {
    background-color: #f3f6f6;
    font-weight: 600;
}

/* Note and warning styling */
.admonition {
    padding: 12px;
    margin-bottom: 24px;
    border-radius: 4px;
}

.admonition.note {
    background-color: #e7f2fa;
    border-left: 6px solid #3498db;
}

.admonition.warning {
    background-color: #fcf3e7;
    border-left: 6px solid #e67e22;
}

.admonition.danger {
    background-color: #fae7e7;
    border-left: 6px solid #e74c3c;
}

.admonition-title {
    font-weight: 600;
    margin-top: 0;
}

/* Sidebar styling */
.wy-nav-side {
    background-color: #2c3e50;
}

.wy-side-nav-search {
    background-color: #3498db;
}

.wy-menu-vertical a {
    color: #fff;
}

.wy-menu-vertical a:hover {
    background-color: #3498db;
}

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
    .wy-nav-content-wrap {
        margin-left: 0;
    }
}

/* Copy button styling */
button.copybtn {
    opacity: 0.5;
    transition: opacity 0.3s ease-in-out;
}

div.highlight:hover button.copybtn, button.copybtn:hover {
    opacity: 1;
}

/* API documentation styling */
dl.class, dl.function, dl.method, dl.attribute {
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #e1e4e5;
    border-radius: 4px;
}

dl.class > dt, dl.function > dt, dl.method > dt, dl.attribute > dt {
    background-color: #f3f6f6;
    padding: 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 100%;
}

/* Search results styling */
ul.search li {
    padding: 10px 0;
    border-bottom: 1px solid #e1e4e5;
}

ul.search li:last-child {
    border-bottom: none;
}

/* Footer styling */
footer {
    color: #999;
    font-size: 85%;
}

/* Print styling */
@media print {
    .wy-nav-side {
        display: none;
    }
    
    .wy-nav-content-wrap {
        margin-left: 0;
    }
    
    .rst-footer-buttons {
        display: none;
    }
}

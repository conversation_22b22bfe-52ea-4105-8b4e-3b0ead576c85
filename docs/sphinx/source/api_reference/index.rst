.. _api_reference:

API Reference
============

This document describes the bahtBrowse API.

.. toctree::
   :maxdepth: 2

   session_management

Overview
-------

The bahtBrowse API provides programmatic access to bahtBrowse functionality. It allows you to create and manage browser sessions, access browser containers, and monitor system health.

API Endpoints
-----------

The bahtBrowse API provides the following endpoints:

Health API
---------

The Health API provides endpoints for monitoring system health.

**Endpoints**:

- ``/health``: Returns the health status of all services
- ``/api-docs``: Returns API documentation in JSON format

**Example**:

.. code-block:: bash

   curl http://localhost:8000/health

**Response**:

.. code-block:: json

   {
     "services": {
       "Central API": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 12.34
       },
       "Docker Queue API": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 23.45
       },
       "Redis": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 34.56
       },
       "Celery Flower": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 45.67
       },
       "Firefox Alpine": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 56.78
       },
       "Chromium Alpine": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 67.89
       },
       "VNC WebSocket": {
         "status": "healthy",
         "message": "Service is running",
         "response_time": 78.90
       }
     },
     "system_info": {
       "cpu_percent": 12.3,
       "memory_used": **********,
       "memory_total": **********1,
       "memory_percent": 10.0,
       "disk_used": **********1,
       "disk_total": **********12,
       "disk_percent": 10.0,
       "python_version": "3.9.0"
     },
     "timestamp": "2023-01-01T00:00:00Z"
   }

Browser API
---------

The Browser API provides endpoints for creating and managing browser sessions.

**Endpoints**:

- ``/browse``: Creates a new browser session
- ``/browse/{session_id}``: Accesses an existing browser session
- ``/browse/{session_id}/status``: Returns the status of a browser session
- ``/browse/{session_id}/screenshot``: Takes a screenshot of a browser session
- ``/browse/{session_id}/close``: Closes a browser session

**Example**:

.. code-block:: bash

   curl http://localhost:8001/browse?url=https://example.com

**Response**:

.. code-block:: json

   {
     "session_id": "**********",
     "url": "https://example.com",
     "browser": "firefox",
     "status": "running",
     "created_at": "2023-01-01T00:00:00Z",
     "vnc_url": "http://localhost:8001/vnc/?path=websockify/?token=**********"
   }

Authentication
------------

The bahtBrowse API supports authentication using API keys. To authenticate, include the API key in the `Authorization` header:

.. code-block:: bash

   curl -H "Authorization: Bearer YOUR_API_KEY" http://localhost:8001/browse

Rate Limiting
-----------

The bahtBrowse API implements rate limiting to prevent abuse. Rate limits are applied per API key and are reset hourly.

Error Handling
-----------

The bahtBrowse API returns standard HTTP status codes to indicate success or failure. In case of an error, the response body will contain an error message:

.. code-block:: json

   {
     "error": "Invalid URL",
     "status_code": 400
   }

Pagination
--------

API endpoints that return multiple items support pagination. Use the `page` and `per_page` query parameters to control pagination:

.. code-block:: bash

   curl http://localhost:8001/browse?page=1&per_page=10

The response will include pagination metadata:

.. code-block:: json

   {
     "items": [...],
     "page": 1,
     "per_page": 10,
     "total": 100
   }

Versioning
--------

The bahtBrowse API is versioned. The current version is v1. To specify a version, include it in the URL:

.. code-block:: bash

   curl http://localhost:8001/v1/browse

CORS
----

The bahtBrowse API supports Cross-Origin Resource Sharing (CORS). CORS is enabled for all origins by default.

Webhooks
-------

The bahtBrowse API supports webhooks for event notifications. To register a webhook, use the `/webhooks` endpoint:

.. code-block:: bash

   curl -X POST -H "Content-Type: application/json" -d '{"url": "https://example.com/webhook", "events": ["session.created", "session.closed"]}' http://localhost:8001/webhooks

When an event occurs, the API will send a POST request to the registered webhook URL with the event data:

.. code-block:: json

   {
     "event": "session.created",
     "data": {
       "session_id": "**********",
       "url": "https://example.com",
       "browser": "firefox",
       "status": "running",
       "created_at": "2023-01-01T00:00:00Z"
     }
   }

API Client Libraries
-----------------

The bahtBrowse API provides client libraries for various programming languages:

- Python: `bahtbrowse-python`
- JavaScript: `bahtbrowse-js`
- Ruby: `bahtbrowse-ruby`
- Go: `bahtbrowse-go`
- PHP: `bahtbrowse-php`

For more information on using the client libraries, see the client library documentation.

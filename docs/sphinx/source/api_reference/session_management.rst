.. _session_management:

Session Management
===============

This document provides detailed information about the session management system in bahtBrowse, including session creation, validation, and cleanup.

Overview
-------

The session management system is responsible for creating and managing browser sessions. Each session is identified by a unique session ID (UUID) and is associated with a specific client IP address.

The session manager ensures that:

- Each session has a unique ID
- Sessions are isolated from each other
- Sessions are cleaned up after a period of inactivity
- Session IDs are validated before use

Session Manager
-----------

The session manager is implemented in the ``session_manager.py`` file. It provides methods for creating, validating, and cleaning up sessions.

.. code-block:: python

   class SessionManager:
       """Manage browser sessions."""

       def __init__(self):
           """Initialize the session manager."""
           self.sessions = {}
           self.session_timeout = 1800  # 30 minutes

       def create_session(self, client_ip):
           """Create a new session for the given client IP."""
           session_id = str(uuid.uuid4())
           self.sessions[session_id] = {
               'client_ip': client_ip,
               'created_at': time.time(),
               'last_accessed': time.time()
           }
           return session_id

       def validate_session(self, session_id, client_ip):
           """Validate the session ID for the given client IP."""
           if session_id not in self.sessions:
               return False
           
           session = self.sessions[session_id]
           if session['client_ip'] != client_ip:
               return False
           
           # Update last accessed time
           session['last_accessed'] = time.time()
           return True

       def cleanup_sessions(self):
           """Clean up expired sessions."""
           now = time.time()
           expired_sessions = []
           
           for session_id, session in self.sessions.items():
               if now - session['last_accessed'] > self.session_timeout:
                   expired_sessions.append(session_id)
           
           for session_id in expired_sessions:
               del self.sessions[session_id]

Session Creation
------------

Sessions are created when a user requests a new browser session through the API server. The session manager creates a new session with a unique ID and associates it with the client IP address.

.. code-block:: python

   # Create a new session
   session_id = session_manager.create_session(client_ip)

The session ID is a UUID (Universally Unique Identifier) that is generated using the ``uuid.uuid4()`` function. This ensures that each session has a unique ID.

The session is stored in the session manager's ``sessions`` dictionary, with the session ID as the key and a dictionary containing the client IP address, creation time, and last accessed time as the value.

Session Validation
--------------

Before a session is used, it is validated to ensure that it exists and is associated with the correct client IP address. This prevents unauthorized access to sessions.

.. code-block:: python

   # Validate a session
   is_valid = session_manager.validate_session(session_id, client_ip)

The validation process checks that:

- The session ID exists in the session manager's ``sessions`` dictionary
- The session is associated with the correct client IP address

If the session is valid, the last accessed time is updated to the current time. This ensures that active sessions are not cleaned up.

Session Cleanup
-----------

Sessions are automatically cleaned up after a period of inactivity to prevent resource exhaustion. The default timeout is 30 minutes (1800 seconds).

.. code-block:: python

   # Clean up expired sessions
   session_manager.cleanup_sessions()

The cleanup process checks each session's last accessed time. If the session has not been accessed for longer than the timeout period, it is removed from the session manager's ``sessions`` dictionary.

The cleanup process is typically run periodically, such as once per minute, to ensure that expired sessions are removed in a timely manner.

Inactivity Timeout
--------------

In addition to session cleanup, bahtBrowse also implements an inactivity timeout for containers. This feature automatically terminates containers that have been idle for a specified period, helping to conserve resources and enhance security.

The default container inactivity timeout is 3 minutes (180 seconds), which is separate from the session timeout:

.. code-block:: python

   # Configuration for inactivity timeout
   CONTAINER_IDLE_TIMEOUT = 180  # 3 minutes in seconds

When a container is terminated due to inactivity, the associated session remains valid until it reaches its own timeout. This allows users to resume their session with a new container if they return before the session timeout expires.

For more details on container inactivity timeout, see :doc:`../developer_guide/container_management`.

Session Security
------------

The session management system includes several security features to prevent unauthorized access to sessions:

- **Session IDs**: Session IDs are UUIDs, which are cryptographically secure and difficult to guess.
- **Client IP Binding**: Sessions are bound to the client IP address, preventing session hijacking.
- **Session Timeout**: Sessions expire after a period of inactivity, limiting the window of opportunity for attacks.
- **Session Validation**: Session IDs are validated before use, preventing unauthorized access.
- **Container Inactivity Timeout**: Containers are terminated after a short period of inactivity, reducing the exposure window.

For additional security, consider implementing:

- **HTTPS**: Use HTTPS to encrypt traffic between the client and server, preventing eavesdropping.
- **CSRF Protection**: Implement Cross-Site Request Forgery (CSRF) protection to prevent unauthorized requests.
- **Rate Limiting**: Implement rate limiting to prevent brute force attacks on session IDs.

Session Management API
------------------

The session manager provides the following API for managing sessions:

+------------------------+------------------------------------------+---------------------------+
| Method                 | Description                              | Parameters                |
+========================+==========================================+===========================+
| ``create_session``     | Create a new session                     | ``client_ip``            |
+------------------------+------------------------------------------+---------------------------+
| ``validate_session``   | Validate a session                       | ``session_id``, ``client_ip`` |
+------------------------+------------------------------------------+---------------------------+
| ``cleanup_sessions``   | Clean up expired sessions                | None                      |
+------------------------+------------------------------------------+---------------------------+

Conclusion
--------

The session management system is a critical component of the bahtBrowse system. It ensures that browser sessions are isolated, secure, and properly managed.

For more information on the bahtBrowse architecture, see the :doc:`../developer_guide/architecture` section.

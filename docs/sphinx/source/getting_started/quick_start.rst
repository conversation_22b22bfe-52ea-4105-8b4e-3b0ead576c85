.. _quick_start:

Quick Start Guide
================

This guide will help you quickly get started with bahtBrowse after installation.

Overview
-------

bahtBrowse provides a secure way to browse the web by isolating browsers in Docker containers. This quick start guide will show you how to:

1. Start the bahtBrowse service
2. Access websites through bahtBrowse
3. Use the Firefox plugin
4. Understand the basic workflow

Starting bahtBrowse
-----------------

If you've followed the :doc:`installation` guide, you should have bahtBrowse installed on your system.

1. Start the bahtBrowse service:

   .. code-block:: bash

      docker-compose up -d

2. Verify that the service is running:

   .. code-block:: bash

      docker-compose ps

   You should see the containers running with status "Up".

3. Test the API server:

   .. code-block:: bash

      curl http://localhost:8082/test-connection

   You should receive a JSON response indicating that the service is available.

Accessing Websites
----------------

There are several ways to access websites through bahtBrowse:

Using the API Directly
~~~~~~~~~~~~~~~~~~~~~

1. Open a web browser and navigate to:

   .. code-block:: text

      http://localhost:8082/browse/?url=https://example.com

   Replace ``https://example.com`` with the URL you want to visit.

2. You will be redirected to the VNC interface where you can interact with the isolated browser.

Using the Firefox Plugin
~~~~~~~~~~~~~~~~~~~~~~

If you've installed the Firefox plugin as described in the :doc:`installation` guide, you can use it to access websites through bahtBrowse.

1. Open Firefox
2. Click on the bahtBrowse icon in the toolbar
3. Enter the URL you want to visit or click "Browse Current Page Securely"
4. The page will open in a new tab, running in the isolated browser container

.. note::
   The Firefox plugin provides the most seamless experience for using bahtBrowse.

Understanding the Workflow
------------------------

Here's what happens when you access a website through bahtBrowse:

1. Your request is sent to the bahtBrowse API server
2. The API server creates a new browser session in a Docker container
3. The URL is loaded in the containerized browser
4. You are redirected to a VNC interface where you can interact with the browser
5. All browsing activity is isolated in the container, protecting your main system

.. figure:: ../_static/workflow_diagram.png
   :alt: bahtBrowse Workflow
   :align: center

   bahtBrowse Workflow Diagram

Browser Selection
---------------

bahtBrowse supports multiple browsers. You can specify which browser to use by adding the ``browser`` parameter to the URL:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&browser=firefox
   http://localhost:8082/browse/?url=https://example.com&browser=chromium

If no browser is specified, Firefox will be used by default.

Session Management
---------------

Each browsing session is isolated in its own container. Sessions are automatically created when you access a URL through bahtBrowse.

Sessions are identified by a unique session ID, which is included in the URL when you are redirected to the VNC interface.

.. code-block:: text

   http://localhost:6081/vnc1/run?session=12345678-1234-1234-1234-123456789abc

Sessions are automatically cleaned up after a period of inactivity (default: 30 minutes).

Next Steps
---------

Now that you've got the basics of bahtBrowse, you can:

* :doc:`../user_guide/index`: Learn more about using bahtBrowse
* :doc:`../admin_guide/configuration`: Configure bahtBrowse for your environment
* :doc:`../developer_guide/index`: Understand the architecture and contribute to bahtBrowse

.. _installation:

Installation Guide
=================

This guide will walk you through the process of installing bahtBrowse on your system.

Prerequisites
------------

Before installing bahtBrowse, ensure your system meets the :ref:`system requirements <getting_started>`.

Installation Methods
------------------

There are several ways to install bahtBrowse:

1. **Docker Compose** (Recommended)
2. **Manual Installation**
3. **Development Setup**

Docker Compose Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~

The recommended way to install bahtBrowse is using Docker Compose, which simplifies the process of managing multiple containers.

1. Clone the bahtBrowse repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Build and start the containers:

   .. code-block:: bash

      docker-compose up -d

   This will build the necessary Docker images and start the containers in detached mode.

3. Verify the installation:

   .. code-block:: bash

      docker-compose ps

   You should see the following containers running:
   
   - bahtbrowse-firefox
   - bahtbrowse-api

4. Test the API server:

   .. code-block:: bash

      curl http://localhost:8082/test-connection

   You should receive a JSON response indicating that the service is available.

Manual Installation
~~~~~~~~~~~~~~~~~

If you prefer to have more control over the installation process, you can install bahtBrowse manually.

1. Clone the bahtBrowse repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Build the Firefox container:

   .. code-block:: bash

      docker build -t bahtbrowse-firefox -f docker/Dockerfile .

3. Build the API server container:

   .. code-block:: bash

      docker build -t bahtbrowse-api -f docker/Dockerfile.api .

4. Run the Firefox container:

   .. code-block:: bash

      docker run -d --name=bahtbrowse-firefox -p 6081:6080 -p 8080:80 bahtbrowse-firefox

5. Run the API server container:

   .. code-block:: bash

      docker run -d --name=bahtbrowse-api -p 8082:8082 --link bahtbrowse-firefox bahtbrowse-api

6. Verify the installation:

   .. code-block:: bash

      docker ps

   You should see both containers running.

Development Setup
~~~~~~~~~~~~~~~

For development purposes, you may want to set up bahtBrowse with additional tools and configurations.

1. Clone the bahtBrowse repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Create a Python virtual environment:

   .. code-block:: bash

      python3 -m venv venv
      source venv/bin/activate

3. Install the development dependencies:

   .. code-block:: bash

      pip install -e ".[dev]"

4. Set up pre-commit hooks:

   .. code-block:: bash

      pre-commit install

5. Build and run the containers:

   .. code-block:: bash

      docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

6. Run the tests to verify the setup:

   .. code-block:: bash

      pytest

Firefox Plugin Installation
-------------------------

To use bahtBrowse with Firefox, you need to install the Firefox plugin.

1. Build the Firefox plugin:

   .. code-block:: bash

      cd firefox_plugin
      ./build.sh

2. Install the plugin in Firefox:

   a. Open Firefox
   b. Navigate to ``about:debugging``
   c. Click on "This Firefox"
   d. Click on "Load Temporary Add-on..."
   e. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

   Alternatively, you can install the plugin permanently:

   a. Open Firefox
   b. Navigate to ``about:addons``
   c. Click the gear icon and select "Install Add-on From File..."
   d. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

Configuration
------------

After installation, you may want to configure bahtBrowse to suit your needs.

Basic Configuration
~~~~~~~~~~~~~~~~~

The basic configuration can be done through environment variables or a configuration file.

1. Create a ``.env`` file in the project root directory:

   .. code-block:: bash

      # API Server Configuration
      API_PORT=8082
      API_HOST=0.0.0.0
      
      # VNC Configuration
      VNC_PORT=6080
      
      # Browser Configuration
      DEFAULT_BROWSER=firefox
      
      # Logging Configuration
      LOG_LEVEL=INFO

2. Restart the containers:

   .. code-block:: bash

      docker-compose down
      docker-compose up -d

Advanced Configuration
~~~~~~~~~~~~~~~~~~~~

For advanced configuration, refer to the :doc:`../admin_guide/configuration` section.

Troubleshooting
--------------

If you encounter issues during installation, check the following:

1. **Docker Issues**:
   
   - Ensure Docker is running: ``systemctl status docker``
   - Check Docker logs: ``docker logs bahtbrowse-firefox``

2. **Network Issues**:
   
   - Verify port mappings: ``docker-compose ps``
   - Check if ports are in use: ``netstat -tuln | grep 8082``

3. **Permission Issues**:
   
   - Ensure you have permission to run Docker: ``groups | grep docker``
   - Check file permissions: ``ls -la``

For more troubleshooting tips, see the :doc:`../troubleshooting/index` section.

Next Steps
---------

Now that you have installed bahtBrowse, you can:

* :doc:`quick_start`: Learn how to use bahtBrowse
* :doc:`../user_guide/index`: Explore the user guide
* :doc:`../admin_guide/configuration`: Configure bahtBrowse for your environment

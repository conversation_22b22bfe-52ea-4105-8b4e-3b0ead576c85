

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Common Issues &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=051c0d2c" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=f281be69"></script>
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      <script src="../_static/mermaid-init.js?v=fae81138"></script>
      <script src="../_static/dark-mode.js?v=41c052f7"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Frequently Asked Questions" href="faq.html" />
    <link rel="prev" title="Troubleshooting" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html">
            
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting_started/index.html">Getting Started with bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/installation.html">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#docker-compose-installation">Docker Compose Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#development-setup">Development Setup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#firefox-plugin-installation">Firefox Plugin Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#configuration">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/quick_start.html">Quick Start Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#starting-bahtbrowse">Starting bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#accessing-websites">Accessing Websites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-api-directly">Using the API Directly</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-firefox-plugin">Using the Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#understanding-the-workflow">Understanding the Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#session-management">Session Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#system-requirements">System Requirements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#quick-overview">Quick Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../user_guide/index.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/firefox_plugin.html">Firefox Plugin</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#installation">Installation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#temporary-installation">Temporary Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#permanent-installation">Permanent Installation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#using-the-plugin">Using the Plugin</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#toolbar-button">Toolbar Button</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#context-menu">Context Menu</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#keyboard-shortcuts">Keyboard Shortcuts</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#connection-status">Connection Status</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#advanced-features">Advanced Features</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#whitelist-and-blacklist">Whitelist and Blacklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#development">Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/browsing.html">Secure Browsing with bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#introduction">Introduction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#direct-api-access">Direct API Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#advanced-parameters">Advanced Parameters</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#post-requests">POST Requests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#command-line-usage">Command Line Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#accessing-websites">Accessing Websites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#testing-connection">Testing Connection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#logging-console-messages">Logging Console Messages</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#managing-downloads">Managing Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#vnc-interface">VNC Interface</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#navigation">Navigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#keyboard-and-mouse">Keyboard and Mouse</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#copy-and-paste">Copy and Paste</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#file-downloads">File Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browser-selection">Browser Selection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#firefox">Firefox</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#chromium">Chromium</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browsing-modes">Browsing Modes</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#normal-mode">Normal Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#compatibility-mode">Compatibility Mode</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#basic-concepts">Basic Concepts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#remote-browser-isolation-rbi">Remote Browser Isolation (RBI)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#containerization">Containerization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#session-management">Session Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#using-bahtbrowse">Using bahtBrowse</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Troubleshooting</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#connection-issues">Connection Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#unable-to-connect-to-bahtbrowse-service">Unable to Connect to bahtBrowse Service</a></li>
<li class="toctree-l4"><a class="reference internal" href="#vnc-connection-fails">VNC Connection Fails</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#browser-issues">Browser Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#browser-fails-to-launch">Browser Fails to Launch</a></li>
<li class="toctree-l4"><a class="reference internal" href="#browser-crashes">Browser Crashes</a></li>
<li class="toctree-l4"><a class="reference internal" href="#website-compatibility-issues">Website Compatibility Issues</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#firefox-plugin-issues">Firefox Plugin Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#plugin-not-connecting">Plugin Not Connecting</a></li>
<li class="toctree-l4"><a class="reference internal" href="#plugin-not-working-correctly">Plugin Not Working Correctly</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#slow-browser-performance">Slow Browser Performance</a></li>
<li class="toctree-l4"><a class="reference internal" href="#slow-vnc-performance">Slow VNC Performance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#security-issues">Security Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#session-hijacking">Session Hijacking</a></li>
<li class="toctree-l4"><a class="reference internal" href="#container-escape">Container Escape</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#logging-and-debugging">Logging and Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#viewing-logs">Viewing Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="#enabling-debug-mode">Enabling Debug Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="#debugging-firefox-plugin">Debugging Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="faq.html">Frequently Asked Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="faq.html#general-questions">General Questions</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#what-is-bahtbrowse">What is bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-does-bahtbrowse-work">How does bahtBrowse work?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#what-are-the-system-requirements-for-bahtbrowse">What are the system requirements for bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#is-bahtbrowse-free-to-use">Is bahtBrowse free to use?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-install-bahtbrowse">How do I install bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-install-the-firefox-plugin">How do I install the Firefox plugin?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-configure-bahtbrowse">How do I configure bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-run-bahtbrowse-on-windows-or-macos">Can I run bahtBrowse on Windows or macOS?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#usage">Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-access-websites-through-bahtbrowse">How do I access websites through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-use-bahtbrowse-with-browsers-other-than-firefox">Can I use bahtBrowse with browsers other than Firefox?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-download-files-through-bahtbrowse">How do I download files through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-use-bahtbrowse-for-multiple-users">Can I use bahtBrowse for multiple users?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-use-bahtbrowse-with-a-proxy-server">How do I use bahtBrowse with a proxy server?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#security">Security</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#is-bahtbrowse-secure">Is bahtBrowse secure?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-bahtbrowse-protect-me-from-all-web-threats">Can bahtBrowse protect me from all web threats?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-use-bahtbrowse-for-sensitive-browsing">Can I use bahtBrowse for sensitive browsing?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#performance">Performance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#why-is-bahtbrowse-slow">Why is bahtBrowse slow?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-can-i-improve-bahtbrowse-performance">How can I improve bahtBrowse performance?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-run-bahtbrowse-on-low-end-hardware">Can I run bahtBrowse on low-end hardware?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#why-can-t-i-connect-to-bahtbrowse">Why can’t I connect to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#why-does-the-vnc-connection-fail">Why does the VNC connection fail?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#why-does-the-browser-crash">Why does the browser crash?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#development">Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-contribute-to-bahtbrowse">How do I contribute to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-report-a-bug">How do I report a bug?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-request-a-feature">How do I request a feature?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-set-up-a-development-environment">How do I set up a development environment?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-use-bahtbrowse-with-other-browsers">Can I use bahtBrowse with other browsers?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#is-bahtbrowse-suitable-for-enterprise-use">Is bahtBrowse suitable for enterprise use?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-does-bahtbrowse-compare-to-commercial-rbi-solutions">How does bahtBrowse compare to commercial RBI solutions?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#can-i-use-bahtbrowse-on-mobile-devices">Can I use bahtBrowse on mobile devices?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="faq.html#getting-help">Getting Help</a><ul>
<li class="toctree-l4"><a class="reference internal" href="faq.html#where-can-i-get-help-with-bahtbrowse">Where can I get help with bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="faq.html#how-do-i-stay-updated-on-bahtbrowse-developments">How do I stay updated on bahtBrowse developments?</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="index.html#connection-issues">Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#errors">404 Errors</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#firefox-plugin-issues">Firefox Plugin Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#logging-and-debugging">Logging and Debugging</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#frequently-asked-questions">Frequently Asked Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Administrator Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/index.html">Administrator Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#deployment">Deployment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#maintenance">Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#clone-the-repository">Clone the Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#build-and-start-the-containers">Build and Start the Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#verify-the-deployment">Verify the Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#architecture">Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-deployment">Example Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#aws-deployment">AWS Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#google-cloud-deployment">Google Cloud Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#azure-deployment">Azure Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#id2">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#deployment-steps">Deployment Steps</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-manifests">Example Manifests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-images">Docker Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-mode">Network Mode</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#default-ports">Default Ports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-conflict-detection-and-resolution">Port Conflict Detection and Resolution</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-ranges">Port Ranges</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#using-the-port-conflict-resolution-system">Using the Port Conflict Resolution System</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-types">Browser Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-pool-configuration">Browser Pool Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#container-isolation">Container Isolation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-security">Network Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#resource-limits">Resource Limits</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#scaling">Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-bahtbrowse">Updating bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-dependencies">Updating Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#elk-stack">ELK Stack</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#redis-data">Redis Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#docker-volumes">Docker Volumes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#configuration-files">Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-issues">Container Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflicts">Port Conflicts</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#network-issues">Network Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-build-tests">Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-detection-tests">Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#enhanced-tests">Enhanced Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/api_server.html">API Server</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#launch-browser-session">Launch Browser Session</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#request">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#response">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#test-connection">Test Connection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id4">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id5">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#log-console-messages">Log Console Messages</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id6">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id7">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#downloads-manager">Downloads Manager</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id8">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id9">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#list-downloaded-files">List Downloaded Files</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id10">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id11">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#delete-downloaded-file">Delete Downloaded File</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id12">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id13">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#session-management">Session Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#browser-launching">Browser Launching</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-client-examples">API Client Examples</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#python">Python</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#javascript">JavaScript</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#curl">cURL</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/session_management.html">Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-manager">Session Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-creation">Session Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validation">Session Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-cleanup">Session Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validator">Session Validator</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-storage">Session Storage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-security">Session Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-api">Session Management API</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#example-usage">Example Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-in-docker-containers">Session Management in Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#launch-a-browser-session">Launch a Browser Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#test-the-connection">Test the Connection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#log-console-messages">Log Console Messages</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-client-libraries">API Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Troubleshooting</a></li>
      <li class="breadcrumb-item active">Common Issues</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/troubleshooting/common_issues.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="common-issues">
<span id="id1"></span><h1>Common Issues<a class="headerlink" href="#common-issues" title="Link to this heading"></a></h1>
<p>This document provides solutions to common issues you might encounter when using bahtBrowse.</p>
<section id="connection-issues">
<h2>Connection Issues<a class="headerlink" href="#connection-issues" title="Link to this heading"></a></h2>
<section id="unable-to-connect-to-bahtbrowse-service">
<h3>Unable to Connect to bahtBrowse Service<a class="headerlink" href="#unable-to-connect-to-bahtbrowse-service" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: You cannot connect to the bahtBrowse service.</p>
<p><strong>Symptoms</strong>:
- Firefox plugin shows a disconnected icon
- Error message: “Unable to connect to bahtBrowse service”
- Browser redirects fail with connection errors</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check if the service is running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
<p>Ensure that the bahtbrowse-firefox and bahtbrowse-api containers are running.</p>
</li>
<li><p><strong>Check container logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>logs<span class="w"> </span>bahtbrowse-firefox
docker<span class="w"> </span>logs<span class="w"> </span>bahtbrowse-api
</pre></div>
</div>
<p>Look for any error messages that might indicate the cause of the issue.</p>
</li>
<li><p><strong>Check port mappings</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
<p>Ensure that the ports are correctly mapped:
- 6081:6080 for VNC
- 8080:80 for HTTP
- 8082:8082 for API</p>
</li>
<li><p><strong>Check if ports are in use</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>netstat<span class="w"> </span>-tuln<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span><span class="m">8082</span>
netstat<span class="w"> </span>-tuln<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span><span class="m">6081</span>
netstat<span class="w"> </span>-tuln<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span><span class="m">8080</span>
</pre></div>
</div>
<p>If the ports are in use by other services, you’ll need to stop those services or configure bahtBrowse to use different ports.</p>
</li>
<li><p><strong>Restart the service</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>down
docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
<p>This will stop and restart the containers.</p>
</li>
<li><p><strong>Check firewall settings</strong>:</p>
<p>Ensure that your firewall allows connections to the required ports.</p>
</li>
<li><p><strong>Check network configuration</strong>:</p>
<p>If you’re running bahtBrowse on a remote server, ensure that the server is accessible from your network.</p>
</li>
</ol>
</section>
<section id="vnc-connection-fails">
<h3>VNC Connection Fails<a class="headerlink" href="#vnc-connection-fails" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: You can connect to the bahtBrowse service, but the VNC connection fails.</p>
<p><strong>Symptoms</strong>:
- Error message: “Unable to connect to VNC server”
- Black screen in the VNC interface
- VNC interface loads but shows an error</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check if the VNC server is running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ps<span class="w"> </span>aux<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>x11vnc
</pre></div>
</div>
<p>You should see an x11vnc process running.</p>
</li>
<li><p><strong>Check VNC server logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/tmp/x11vnc.log
</pre></div>
</div>
<p>Look for any error messages that might indicate the cause of the issue.</p>
</li>
<li><p><strong>Check if the VNC port is accessible</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>telnet<span class="w"> </span>localhost<span class="w"> </span><span class="m">6081</span>
</pre></div>
</div>
<p>If you can connect, the port is accessible.</p>
</li>
<li><p><strong>Check nginx configuration</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/etc/nginx/sites-enabled/proxy-server.conf
</pre></div>
</div>
<p>Ensure that the nginx configuration includes a location block for the VNC interface:</p>
<div class="highlight-nginx notranslate"><div class="highlight"><pre><span></span><span class="k">location</span><span class="w"> </span><span class="s">/vnc1/</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">proxy_pass</span><span class="w"> </span><span class="s">http://localhost:6080/</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_http_version</span><span class="w"> </span><span class="mi">1</span><span class="s">.1</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">Upgrade</span><span class="w"> </span><span class="nv">$http_upgrade</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">Connection</span><span class="w"> </span><span class="s">&quot;upgrade&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">Host</span><span class="w"> </span><span class="nv">$host</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Real-IP</span><span class="w"> </span><span class="nv">$remote_addr</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Forwarded-For</span><span class="w"> </span><span class="nv">$proxy_add_x_forwarded_for</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_set_header</span><span class="w"> </span><span class="s">X-Forwarded-Proto</span><span class="w"> </span><span class="nv">$scheme</span><span class="p">;</span>

<span class="w">    </span><span class="c1"># WebSocket support</span>
<span class="w">    </span><span class="kn">proxy_read_timeout</span><span class="w"> </span><span class="s">61s</span><span class="p">;</span>
<span class="w">    </span><span class="kn">proxy_buffering</span><span class="w"> </span><span class="no">off</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
</li>
<li><p><strong>Restart the VNC server</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>pkill<span class="w"> </span>x11vnc
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>x11vnc<span class="w"> </span>-display<span class="w"> </span>:1<span class="w"> </span>-forever<span class="w"> </span>-shared<span class="w"> </span>-rfbport<span class="w"> </span><span class="m">6080</span><span class="w"> </span>-nopw<span class="w"> </span>-listen<span class="w"> </span>localhost<span class="w"> </span>-xkb<span class="w"> </span>-bg<span class="w"> </span>-logfile<span class="w"> </span>/tmp/x11vnc.log
</pre></div>
</div>
<p>This will stop and restart the VNC server.</p>
</li>
</ol>
</section>
</section>
<section id="browser-issues">
<h2>Browser Issues<a class="headerlink" href="#browser-issues" title="Link to this heading"></a></h2>
<section id="browser-fails-to-launch">
<h3>Browser Fails to Launch<a class="headerlink" href="#browser-fails-to-launch" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The browser fails to launch in the container.</p>
<p><strong>Symptoms</strong>:
- Error message: “Error launching browser”
- VNC interface shows a blank screen
- Browser process is not running in the container</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check if the browser is installed</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ls<span class="w"> </span>-la<span class="w"> </span>/tmp/firefox
</pre></div>
</div>
<p>For Firefox, you should see the Firefox executable.</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ls<span class="w"> </span>-la<span class="w"> </span>/usr/bin/chromium-browser
</pre></div>
</div>
<p>For Chromium, you should see the Chromium executable.</p>
</li>
<li><p><strong>Check browser logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/tmp/browser.log
</pre></div>
</div>
<p>Look for any error messages that might indicate the cause of the issue.</p>
</li>
<li><p><strong>Check if the browser process is running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ps<span class="w"> </span>aux<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>firefox
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ps<span class="w"> </span>aux<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>chromium
</pre></div>
</div>
<p>You should see a browser process running.</p>
</li>
<li><p><strong>Check if the X server is running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>ps<span class="w"> </span>aux<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>Xvfb
</pre></div>
</div>
<p>You should see an Xvfb process running.</p>
</li>
<li><p><strong>Restart the browser</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>pkill<span class="w"> </span>firefox
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span><span class="nv">DISPLAY</span><span class="o">=</span>:1<span class="w"> </span>/tmp/firefox/firefox<span class="w"> </span>--new-instance<span class="w"> </span>--url<span class="w"> </span><span class="s2">&quot;https://example.com&quot;</span><span class="w"> </span>--kiosk<span class="w"> </span><span class="p">&amp;</span>
</pre></div>
</div>
<p>This will stop and restart the browser.</p>
</li>
</ol>
</section>
<section id="browser-crashes">
<h3>Browser Crashes<a class="headerlink" href="#browser-crashes" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The browser crashes during use.</p>
<p><strong>Symptoms</strong>:
- Browser window disappears
- Error message: “Browser has crashed”
- VNC interface shows a blank screen</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check browser logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/tmp/browser.log
</pre></div>
</div>
<p>Look for any error messages that might indicate the cause of the crash.</p>
</li>
<li><p><strong>Check system resources</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>stats<span class="w"> </span>bahtbrowse-firefox
</pre></div>
</div>
<p>Ensure that the container has enough CPU and memory resources.</p>
</li>
<li><p><strong>Try a different browser</strong>:</p>
<p>If Firefox is crashing, try Chromium:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>chromium
</pre></div>
</div>
<p>If Chromium is crashing, try Firefox:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>firefox
</pre></div>
</div>
</li>
<li><p><strong>Try compatibility mode</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">mode</span><span class="o">=</span>compatibility
</pre></div>
</div>
<p>Compatibility mode enables additional features that may help with complex websites.</p>
</li>
<li><p><strong>Disable JavaScript execution</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">disable_js_execution</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
<p>This can help if the crash is caused by JavaScript.</p>
</li>
<li><p><strong>Increase JavaScript timeout</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">js_timeout</span><span class="o">=</span><span class="m">30000</span>
</pre></div>
</div>
<p>This increases the JavaScript execution timeout to 30 seconds.</p>
</li>
<li><p><strong>Restart the container</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>restart<span class="w"> </span>bahtbrowse-firefox
</pre></div>
</div>
<p>This will restart the container.</p>
</li>
</ol>
</section>
<section id="website-compatibility-issues">
<h3>Website Compatibility Issues<a class="headerlink" href="#website-compatibility-issues" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: Some websites don’t work correctly in bahtBrowse.</p>
<p><strong>Symptoms</strong>:
- Website layout is broken
- Interactive elements don’t work
- Website shows an error message</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Try compatibility mode</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">mode</span><span class="o">=</span>compatibility
</pre></div>
</div>
<p>Compatibility mode enables additional features that may help with complex websites.</p>
</li>
<li><p><strong>Try a different browser</strong>:</p>
<p>If Firefox is having issues, try Chromium:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>chromium
</pre></div>
</div>
<p>If Chromium is having issues, try Firefox:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>firefox
</pre></div>
</div>
</li>
<li><p><strong>Check if JavaScript is enabled</strong>:</p>
<p>Some websites require JavaScript to function correctly. Ensure that JavaScript is enabled:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">disable_js_execution</span><span class="o">=</span><span class="nb">false</span>
</pre></div>
</div>
</li>
<li><p><strong>Check if the website requires cookies</strong>:</p>
<p>Some websites require cookies to function correctly. Ensure that cookies are enabled in the browser.</p>
</li>
<li><p><strong>Check if the website requires authentication</strong>:</p>
<p>Some websites require authentication to access. Ensure that you’re logged in to the website.</p>
</li>
<li><p><strong>Check if the website requires a specific locale</strong>:</p>
<p>Some websites require a specific locale to function correctly. Try setting a different locale:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">locale</span><span class="o">=</span>en-US
http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">locale</span><span class="o">=</span>de-DE
</pre></div>
</div>
</li>
</ol>
</section>
</section>
<section id="firefox-plugin-issues">
<h2>Firefox Plugin Issues<a class="headerlink" href="#firefox-plugin-issues" title="Link to this heading"></a></h2>
<section id="plugin-not-connecting">
<h3>Plugin Not Connecting<a class="headerlink" href="#plugin-not-connecting" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The Firefox plugin cannot connect to the bahtBrowse service.</p>
<p><strong>Symptoms</strong>:
- Plugin icon shows a disconnected state
- Error message: “Unable to connect to bahtBrowse service”
- Clicking the plugin button does nothing</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check plugin settings</strong>:</p>
<ul class="simple">
<li><p>Click on the plugin icon in the Firefox toolbar</p></li>
<li><p>Click on “Options” (gear icon)</p></li>
<li><p>Verify that the host and port settings are correct:
- Host: <code class="docutils literal notranslate"><span class="pre">localhost</span></code> (or the IP address of the server)
- Port: <code class="docutils literal notranslate"><span class="pre">8082</span></code></p></li>
</ul>
</li>
<li><p><strong>Check if the service is running</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
<p>Ensure that the bahtbrowse-firefox and bahtbrowse-api containers are running.</p>
</li>
<li><p><strong>Check if the API server is accessible</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8082/test-connection
</pre></div>
</div>
<p>You should receive a JSON response indicating that the service is available.</p>
</li>
<li><p><strong>Check browser console for errors</strong>:</p>
<ul class="simple">
<li><p>Press F12 to open the developer tools</p></li>
<li><p>Go to the Console tab</p></li>
<li><p>Look for any error messages related to the plugin</p></li>
</ul>
</li>
<li><p><strong>Reinstall the plugin</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Find the bahtBrowse plugin and click “Remove”</p></li>
<li><p>Install the plugin again</p></li>
</ul>
</li>
</ol>
</section>
<section id="plugin-not-working-correctly">
<h3>Plugin Not Working Correctly<a class="headerlink" href="#plugin-not-working-correctly" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The Firefox plugin is not working correctly.</p>
<p><strong>Symptoms</strong>:
- Plugin icon is visible but clicking it does nothing
- Right-click context menu items are missing
- Plugin settings cannot be saved</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic simple">
<li><p><strong>Check browser console for errors</strong>:</p>
<ul class="simple">
<li><p>Press F12 to open the developer tools</p></li>
<li><p>Go to the Console tab</p></li>
<li><p>Look for any error messages related to the plugin</p></li>
</ul>
</li>
<li><p><strong>Check plugin permissions</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Find the bahtBrowse plugin and click “Permissions”</p></li>
<li><p>Ensure that the plugin has the necessary permissions</p></li>
</ul>
</li>
<li><p><strong>Check if the plugin is enabled</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Find the bahtBrowse plugin and ensure it’s enabled</p></li>
</ul>
</li>
<li><p><strong>Restart Firefox</strong>:</p>
<ul class="simple">
<li><p>Close Firefox</p></li>
<li><p>Open Firefox again</p></li>
<li><p>Check if the plugin works correctly</p></li>
</ul>
</li>
<li><p><strong>Reinstall the plugin</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Find the bahtBrowse plugin and click “Remove”</p></li>
<li><p>Install the plugin again</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="performance-issues">
<h2>Performance Issues<a class="headerlink" href="#performance-issues" title="Link to this heading"></a></h2>
<section id="slow-browser-performance">
<h3>Slow Browser Performance<a class="headerlink" href="#slow-browser-performance" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The browser in bahtBrowse is slow or unresponsive.</p>
<p><strong>Symptoms</strong>:
- Browser takes a long time to load pages
- Browser is unresponsive to user input
- Browser animations are choppy</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check system resources</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>stats<span class="w"> </span>bahtbrowse-firefox
</pre></div>
</div>
<p>Ensure that the container has enough CPU and memory resources.</p>
</li>
<li><p><strong>Try a different browser</strong>:</p>
<p>If Firefox is slow, try Chromium:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>chromium
</pre></div>
</div>
<p>If Chromium is slow, try Firefox:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">browser</span><span class="o">=</span>firefox
</pre></div>
</div>
</li>
<li><p><strong>Try compatibility mode</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">mode</span><span class="o">=</span>compatibility
</pre></div>
</div>
<p>Compatibility mode enables additional features that may help with complex websites.</p>
</li>
<li><p><strong>Disable JavaScript execution</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url<span class="o">=</span>https://example.com<span class="p">&amp;</span><span class="nv">disable_js_execution</span><span class="o">=</span><span class="nb">true</span>
</pre></div>
</div>
<p>This can help if the slowness is caused by JavaScript.</p>
</li>
<li><p><strong>Increase container resources</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to allocate more CPU and memory to the container:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">deploy</span><span class="p">:</span>
<span class="w">      </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">        </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">          </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;2&#39;</span>
<span class="w">          </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2G</span>
</pre></div>
</div>
<p>This allocates 2 CPU cores and 2GB of memory to the container.</p>
</li>
<li><p><strong>Optimize browser settings</strong>:</p>
<ul class="simple">
<li><p>Disable browser extensions</p></li>
<li><p>Disable browser animations</p></li>
<li><p>Disable browser hardware acceleration</p></li>
</ul>
</li>
<li><p><strong>Restart the container</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>restart<span class="w"> </span>bahtbrowse-firefox
</pre></div>
</div>
<p>This will restart the container.</p>
</li>
</ol>
</section>
<section id="slow-vnc-performance">
<h3>Slow VNC Performance<a class="headerlink" href="#slow-vnc-performance" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: The VNC interface is slow or unresponsive.</p>
<p><strong>Symptoms</strong>:
- VNC interface takes a long time to load
- VNC interface is unresponsive to user input
- VNC interface animations are choppy</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Check network bandwidth</strong>:</p>
<p>Ensure that you have sufficient network bandwidth for VNC.</p>
</li>
<li><p><strong>Reduce VNC quality</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to reduce the VNC quality:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">VNC_QUALITY=50</span>
</pre></div>
</div>
<p>This reduces the VNC quality to 50%.</p>
</li>
<li><p><strong>Reduce VNC resolution</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to reduce the VNC resolution:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">VNC_RESOLUTION=1024x768</span>
</pre></div>
</div>
<p>This reduces the VNC resolution to 1024x768.</p>
</li>
<li><p><strong>Use a different VNC client</strong>:</p>
<p>Instead of using the web-based VNC client, you can use a native VNC client:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>vncviewer<span class="w"> </span>localhost:6081
</pre></div>
</div>
<p>This may provide better performance.</p>
</li>
<li><p><strong>Restart the VNC server</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>pkill<span class="w"> </span>x11vnc
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>x11vnc<span class="w"> </span>-display<span class="w"> </span>:1<span class="w"> </span>-forever<span class="w"> </span>-shared<span class="w"> </span>-rfbport<span class="w"> </span><span class="m">6080</span><span class="w"> </span>-nopw<span class="w"> </span>-listen<span class="w"> </span>localhost<span class="w"> </span>-xkb<span class="w"> </span>-bg<span class="w"> </span>-logfile<span class="w"> </span>/tmp/x11vnc.log
</pre></div>
</div>
<p>This will stop and restart the VNC server.</p>
</li>
</ol>
</section>
</section>
<section id="security-issues">
<h2>Security Issues<a class="headerlink" href="#security-issues" title="Link to this heading"></a></h2>
<section id="session-hijacking">
<h3>Session Hijacking<a class="headerlink" href="#session-hijacking" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: Someone else can access your browser session.</p>
<p><strong>Symptoms</strong>:
- You see unexpected activity in your browser session
- Your browser session is being used by someone else
- You are redirected to unexpected websites</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Use HTTPS</strong>:</p>
<p>Configure bahtBrowse to use HTTPS to encrypt traffic between the client and server.</p>
</li>
<li><p><strong>Enable session validation</strong>:</p>
<p>Ensure that session validation is enabled in the API server.</p>
</li>
<li><p><strong>Use a firewall</strong>:</p>
<p>Configure a firewall to restrict access to the bahtBrowse service.</p>
</li>
<li><p><strong>Use a VPN</strong>:</p>
<p>Use a VPN to encrypt traffic between the client and server.</p>
</li>
<li><p><strong>Implement authentication</strong>:</p>
<p>Implement authentication to restrict access to the bahtBrowse service.</p>
</li>
</ol>
</section>
<section id="container-escape">
<h3>Container Escape<a class="headerlink" href="#container-escape" title="Link to this heading"></a></h3>
<p><strong>Issue</strong>: A malicious website can escape the container and affect the host system.</p>
<p><strong>Symptoms</strong>:
- Unexpected files or processes on the host system
- Host system resources are being used by unknown processes
- Host system is compromised</p>
<p><strong>Solutions</strong>:</p>
<ol class="arabic">
<li><p><strong>Keep Docker up to date</strong>:</p>
<p>Ensure that Docker is up to date to benefit from security fixes.</p>
</li>
<li><p><strong>Use security options</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to add security options:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">security_opt</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">no-new-privileges:true</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">seccomp=unconfined</span>
</pre></div>
</div>
<p>This prevents the container from gaining new privileges and uses the default seccomp profile.</p>
</li>
<li><p><strong>Use read-only file systems</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to make the file system read-only:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">read_only</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">tmpfs</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/tmp</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/var/run</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/var/log</span>
</pre></div>
</div>
<p>This makes the file system read-only, with temporary file systems for directories that need to be writable.</p>
</li>
<li><p><strong>Use user namespaces</strong>:</p>
<p>Configure Docker to use user namespaces to map container users to host users.</p>
</li>
<li><p><strong>Use AppArmor or SELinux</strong>:</p>
<p>Configure AppArmor or SELinux to restrict container capabilities.</p>
</li>
</ol>
</section>
</section>
<section id="logging-and-debugging">
<h2>Logging and Debugging<a class="headerlink" href="#logging-and-debugging" title="Link to this heading"></a></h2>
<section id="viewing-logs">
<h3>Viewing Logs<a class="headerlink" href="#viewing-logs" title="Link to this heading"></a></h3>
<p>To view logs for the bahtBrowse service:</p>
<ol class="arabic">
<li><p><strong>API Server Logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>logs<span class="w"> </span>bahtbrowse-api
</pre></div>
</div>
</li>
<li><p><strong>Browser Container Logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span>logs<span class="w"> </span>bahtbrowse-firefox
</pre></div>
</div>
</li>
<li><p><strong>Browser Console Logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/tmp/browser_console.log
</pre></div>
</div>
</li>
<li><p><strong>VNC Server Logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/tmp/x11vnc.log
</pre></div>
</div>
</li>
<li><p><strong>Nginx Logs</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/var/log/nginx/access.log
docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>cat<span class="w"> </span>/var/log/nginx/error.log
</pre></div>
</div>
</li>
</ol>
</section>
<section id="enabling-debug-mode">
<h3>Enabling Debug Mode<a class="headerlink" href="#enabling-debug-mode" title="Link to this heading"></a></h3>
<p>To enable debug mode for the bahtBrowse service:</p>
<ol class="arabic">
<li><p><strong>API Server Debug Mode</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to enable debug mode for the API server:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-api</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG=true</span>
</pre></div>
</div>
</li>
<li><p><strong>Browser Debug Mode</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to enable debug mode for the browser:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">BROWSER_DEBUG=true</span>
</pre></div>
</div>
</li>
<li><p><strong>VNC Debug Mode</strong>:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file to enable debug mode for the VNC server:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-firefox</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">VNC_DEBUG=true</span>
</pre></div>
</div>
</li>
<li><p><strong>Nginx Debug Mode</strong>:</p>
<p>Edit the nginx configuration to enable debug mode:</p>
<div class="highlight-nginx notranslate"><div class="highlight"><pre><span></span><span class="k">error_log</span><span class="w"> </span><span class="s">/var/log/nginx/error.log</span><span class="w"> </span><span class="s">debug</span><span class="p">;</span>
</pre></div>
</div>
<p>Then restart nginx:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>bahtbrowse-firefox<span class="w"> </span>service<span class="w"> </span>nginx<span class="w"> </span>restart
</pre></div>
</div>
</li>
</ol>
</section>
<section id="debugging-firefox-plugin">
<h3>Debugging Firefox Plugin<a class="headerlink" href="#debugging-firefox-plugin" title="Link to this heading"></a></h3>
<p>To debug the Firefox plugin:</p>
<ol class="arabic simple">
<li><p><strong>Enable Browser Console</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Press Ctrl+Shift+J to open the Browser Console</p></li>
<li><p>Look for messages related to the plugin</p></li>
</ul>
</li>
<li><p><strong>Enable Plugin Debugging</strong>:</p>
<ul class="simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:debugging</span></code></p></li>
<li><p>Click on “This Firefox”</p></li>
<li><p>Find the bahtBrowse plugin and click “Inspect”</p></li>
<li><p>Use the browser developer tools to debug the plugin</p></li>
</ul>
</li>
<li><p><strong>Enable Verbose Logging</strong>:</p>
<ul class="simple">
<li><p>Click on the plugin icon in the Firefox toolbar</p></li>
<li><p>Click on “Options” (gear icon)</p></li>
<li><p>Enable “Verbose Logging”</p></li>
<li><p>Check the Browser Console for detailed logs</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<p>If you’re still experiencing issues after trying the solutions in this guide, you can:</p>
<ol class="arabic">
<li><p><strong>Check the FAQ</strong>:</p>
<p>See the <a class="reference internal" href="faq.html"><span class="doc">Frequently Asked Questions</span></a> for answers to frequently asked questions.</p>
</li>
<li><p><strong>Search for Similar Issues</strong>:</p>
<p>Search for similar issues in the GitHub repository.</p>
</li>
<li><p><strong>Report the Issue</strong>:</p>
<p>Report the issue on GitHub following the guidelines in <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a>.</p>
</li>
<li><p><strong>Contact the Maintainers</strong>:</p>
<p>Contact the maintainers for help with complex issues.</p>
</li>
</ol>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Troubleshooting" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="faq.html" class="btn btn-neutral float-right" title="Frequently Asked Questions" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, bahtBrowse Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
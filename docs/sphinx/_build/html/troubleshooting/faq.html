

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Frequently Asked Questions &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=051c0d2c" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=f281be69"></script>
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      <script src="../_static/mermaid-init.js?v=fae81138"></script>
      <script src="../_static/dark-mode.js?v=41c052f7"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Administrator Guide" href="../admin_guide/index.html" />
    <link rel="prev" title="Common Issues" href="common_issues.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html">
            
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting_started/index.html">Getting Started with bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/installation.html">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#docker-compose-installation">Docker Compose Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#development-setup">Development Setup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#firefox-plugin-installation">Firefox Plugin Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#configuration">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/quick_start.html">Quick Start Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#starting-bahtbrowse">Starting bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#accessing-websites">Accessing Websites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-api-directly">Using the API Directly</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-firefox-plugin">Using the Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#understanding-the-workflow">Understanding the Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#session-management">Session Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#system-requirements">System Requirements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#quick-overview">Quick Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../user_guide/index.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/firefox_plugin.html">Firefox Plugin</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#installation">Installation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#temporary-installation">Temporary Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#permanent-installation">Permanent Installation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#using-the-plugin">Using the Plugin</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#toolbar-button">Toolbar Button</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#context-menu">Context Menu</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#keyboard-shortcuts">Keyboard Shortcuts</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#connection-status">Connection Status</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#advanced-features">Advanced Features</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#whitelist-and-blacklist">Whitelist and Blacklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#development">Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/browsing.html">Secure Browsing with bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#introduction">Introduction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#direct-api-access">Direct API Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#advanced-parameters">Advanced Parameters</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#post-requests">POST Requests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#command-line-usage">Command Line Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#accessing-websites">Accessing Websites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#testing-connection">Testing Connection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#logging-console-messages">Logging Console Messages</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#managing-downloads">Managing Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#vnc-interface">VNC Interface</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#navigation">Navigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#keyboard-and-mouse">Keyboard and Mouse</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#copy-and-paste">Copy and Paste</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#file-downloads">File Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browser-selection">Browser Selection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#firefox">Firefox</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#chromium">Chromium</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browsing-modes">Browsing Modes</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#normal-mode">Normal Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#compatibility-mode">Compatibility Mode</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#basic-concepts">Basic Concepts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#remote-browser-isolation-rbi">Remote Browser Isolation (RBI)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#containerization">Containerization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#session-management">Session Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#using-bahtbrowse">Using bahtBrowse</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Troubleshooting</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="common_issues.html">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#connection-issues">Connection Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#unable-to-connect-to-bahtbrowse-service">Unable to Connect to bahtBrowse Service</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#vnc-connection-fails">VNC Connection Fails</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#browser-issues">Browser Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#browser-fails-to-launch">Browser Fails to Launch</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#browser-crashes">Browser Crashes</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#website-compatibility-issues">Website Compatibility Issues</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#firefox-plugin-issues">Firefox Plugin Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#plugin-not-connecting">Plugin Not Connecting</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#plugin-not-working-correctly">Plugin Not Working Correctly</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#slow-browser-performance">Slow Browser Performance</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#slow-vnc-performance">Slow VNC Performance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#security-issues">Security Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#session-hijacking">Session Hijacking</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#container-escape">Container Escape</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#logging-and-debugging">Logging and Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#viewing-logs">Viewing Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#enabling-debug-mode">Enabling Debug Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="common_issues.html#debugging-firefox-plugin">Debugging Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="common_issues.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Frequently Asked Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#general-questions">General Questions</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#what-is-bahtbrowse">What is bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-does-bahtbrowse-work">How does bahtBrowse work?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#what-are-the-system-requirements-for-bahtbrowse">What are the system requirements for bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#is-bahtbrowse-free-to-use">Is bahtBrowse free to use?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-install-bahtbrowse">How do I install bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-install-the-firefox-plugin">How do I install the Firefox plugin?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-configure-bahtbrowse">How do I configure bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-run-bahtbrowse-on-windows-or-macos">Can I run bahtBrowse on Windows or macOS?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#usage">Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-access-websites-through-bahtbrowse">How do I access websites through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-use-bahtbrowse-with-browsers-other-than-firefox">Can I use bahtBrowse with browsers other than Firefox?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-download-files-through-bahtbrowse">How do I download files through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-use-bahtbrowse-for-multiple-users">Can I use bahtBrowse for multiple users?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-use-bahtbrowse-with-a-proxy-server">How do I use bahtBrowse with a proxy server?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#security">Security</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#is-bahtbrowse-secure">Is bahtBrowse secure?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-bahtbrowse-protect-me-from-all-web-threats">Can bahtBrowse protect me from all web threats?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-use-bahtbrowse-for-sensitive-browsing">Can I use bahtBrowse for sensitive browsing?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#performance">Performance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#why-is-bahtbrowse-slow">Why is bahtBrowse slow?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-can-i-improve-bahtbrowse-performance">How can I improve bahtBrowse performance?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-run-bahtbrowse-on-low-end-hardware">Can I run bahtBrowse on low-end hardware?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#why-can-t-i-connect-to-bahtbrowse">Why can’t I connect to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#why-does-the-vnc-connection-fail">Why does the VNC connection fail?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#why-does-the-browser-crash">Why does the browser crash?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#development">Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-contribute-to-bahtbrowse">How do I contribute to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-report-a-bug">How do I report a bug?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-request-a-feature">How do I request a feature?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-set-up-a-development-environment">How do I set up a development environment?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#can-i-use-bahtbrowse-with-other-browsers">Can I use bahtBrowse with other browsers?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#is-bahtbrowse-suitable-for-enterprise-use">Is bahtBrowse suitable for enterprise use?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-does-bahtbrowse-compare-to-commercial-rbi-solutions">How does bahtBrowse compare to commercial RBI solutions?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#can-i-use-bahtbrowse-on-mobile-devices">Can I use bahtBrowse on mobile devices?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#getting-help">Getting Help</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#where-can-i-get-help-with-bahtbrowse">Where can I get help with bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="#how-do-i-stay-updated-on-bahtbrowse-developments">How do I stay updated on bahtBrowse developments?</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="index.html#connection-issues">Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#errors">404 Errors</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#firefox-plugin-issues">Firefox Plugin Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#logging-and-debugging">Logging and Debugging</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#frequently-asked-questions">Frequently Asked Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Administrator Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/index.html">Administrator Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#deployment">Deployment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#maintenance">Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#clone-the-repository">Clone the Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#build-and-start-the-containers">Build and Start the Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#verify-the-deployment">Verify the Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#architecture">Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-deployment">Example Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#aws-deployment">AWS Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#google-cloud-deployment">Google Cloud Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#azure-deployment">Azure Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#id2">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#deployment-steps">Deployment Steps</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-manifests">Example Manifests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-images">Docker Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-mode">Network Mode</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#default-ports">Default Ports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-conflict-detection-and-resolution">Port Conflict Detection and Resolution</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-ranges">Port Ranges</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#using-the-port-conflict-resolution-system">Using the Port Conflict Resolution System</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-types">Browser Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-pool-configuration">Browser Pool Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#container-isolation">Container Isolation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-security">Network Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#resource-limits">Resource Limits</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#scaling">Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-bahtbrowse">Updating bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-dependencies">Updating Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#elk-stack">ELK Stack</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#redis-data">Redis Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#docker-volumes">Docker Volumes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#configuration-files">Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-issues">Container Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflicts">Port Conflicts</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#network-issues">Network Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-build-tests">Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-detection-tests">Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#enhanced-tests">Enhanced Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/api_server.html">API Server</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#launch-browser-session">Launch Browser Session</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#request">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#response">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#test-connection">Test Connection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id4">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id5">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#log-console-messages">Log Console Messages</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id6">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id7">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#downloads-manager">Downloads Manager</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id8">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id9">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#list-downloaded-files">List Downloaded Files</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id10">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id11">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#delete-downloaded-file">Delete Downloaded File</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id12">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id13">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#session-management">Session Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#browser-launching">Browser Launching</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-client-examples">API Client Examples</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#python">Python</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#javascript">JavaScript</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#curl">cURL</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/session_management.html">Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-manager">Session Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-creation">Session Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validation">Session Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-cleanup">Session Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validator">Session Validator</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-storage">Session Storage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-security">Session Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-api">Session Management API</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#example-usage">Example Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-in-docker-containers">Session Management in Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#launch-a-browser-session">Launch a Browser Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#test-the-connection">Test the Connection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#log-console-messages">Log Console Messages</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-client-libraries">API Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Troubleshooting</a></li>
      <li class="breadcrumb-item active">Frequently Asked Questions</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/troubleshooting/faq.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="frequently-asked-questions">
<span id="faq"></span><h1>Frequently Asked Questions<a class="headerlink" href="#frequently-asked-questions" title="Link to this heading"></a></h1>
<p>This document provides answers to frequently asked questions about bahtBrowse.</p>
<section id="general-questions">
<h2>General Questions<a class="headerlink" href="#general-questions" title="Link to this heading"></a></h2>
<section id="what-is-bahtbrowse">
<h3>What is bahtBrowse?<a class="headerlink" href="#what-is-bahtbrowse" title="Link to this heading"></a></h3>
<p>bahtBrowse is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.</p>
</section>
<section id="how-does-bahtbrowse-work">
<h3>How does bahtBrowse work?<a class="headerlink" href="#how-does-bahtbrowse-work" title="Link to this heading"></a></h3>
<p>bahtBrowse works by running browsers (Firefox or Chromium) in isolated Docker containers. When you access a website through bahtBrowse, the system:</p>
<ol class="arabic simple">
<li><p>Creates a new browser session in a Docker container</p></li>
<li><p>Loads the specified URL in the containerized browser</p></li>
<li><p>Provides access to the browser through a VNC interface</p></li>
<li><p>Isolates the browsing activity from your local system</p></li>
</ol>
<p>This approach protects your local system from web-based threats, as any malicious code is contained within the isolated container.</p>
</section>
<section id="what-are-the-system-requirements-for-bahtbrowse">
<h3>What are the system requirements for bahtBrowse?<a class="headerlink" href="#what-are-the-system-requirements-for-bahtbrowse" title="Link to this heading"></a></h3>
<p>To run bahtBrowse, you need:</p>
<ul class="simple">
<li><p><strong>Operating System</strong>: Linux (Ubuntu 20.04 or later recommended)</p></li>
<li><p><strong>Docker</strong>: Docker 20.10 or later</p></li>
<li><p><strong>Memory</strong>: At least 4GB RAM</p></li>
<li><p><strong>Storage</strong>: At least 10GB free disk space</p></li>
<li><p><strong>Network</strong>: Stable internet connection</p></li>
</ul>
<p>For Firefox plugin users:
- <strong>Firefox</strong>: Version 78 or later</p>
<p>For development:
- <strong>Python</strong>: 3.8 or later
- <strong>Git</strong>: 2.25 or later</p>
</section>
<section id="is-bahtbrowse-free-to-use">
<h3>Is bahtBrowse free to use?<a class="headerlink" href="#is-bahtbrowse-free-to-use" title="Link to this heading"></a></h3>
<p>Yes, bahtBrowse is open-source software and free to use. It is licensed under the MIT License, which allows you to use, modify, and distribute the software freely.</p>
</section>
</section>
<section id="installation-and-setup">
<h2>Installation and Setup<a class="headerlink" href="#installation-and-setup" title="Link to this heading"></a></h2>
<section id="how-do-i-install-bahtbrowse">
<h3>How do I install bahtBrowse?<a class="headerlink" href="#how-do-i-install-bahtbrowse" title="Link to this heading"></a></h3>
<p>To install bahtBrowse, follow these steps:</p>
<ol class="arabic">
<li><p>Clone the repository:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/10Baht/bahtbrowse.git
<span class="nb">cd</span><span class="w"> </span>bahtbrowse
</pre></div>
</div>
</li>
<li><p>Build and start the containers:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</li>
</ol>
<p>For detailed installation instructions, see the <a class="reference internal" href="../getting_started/installation.html"><span class="doc">Installation Guide</span></a> guide.</p>
</section>
<section id="how-do-i-install-the-firefox-plugin">
<h3>How do I install the Firefox plugin?<a class="headerlink" href="#how-do-i-install-the-firefox-plugin" title="Link to this heading"></a></h3>
<p>To install the Firefox plugin:</p>
<ol class="arabic">
<li><p>Build the Firefox plugin:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span><span class="w"> </span>firefox_plugin
./build.sh
</pre></div>
</div>
</li>
<li><p>Install the plugin in Firefox:</p>
<ol class="loweralpha simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:debugging</span></code></p></li>
<li><p>Click on “This Firefox”</p></li>
<li><p>Click on “Load Temporary Add-on…”</p></li>
<li><p>Select the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_bouncer.xpi</span></code> file from the <code class="docutils literal notranslate"><span class="pre">firefox_plugin/build</span></code> directory</p></li>
</ol>
<p>Alternatively, you can install the plugin permanently:</p>
<ol class="loweralpha simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Click the gear icon and select “Install Add-on From File…”</p></li>
<li><p>Select the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_bouncer.xpi</span></code> file from the <code class="docutils literal notranslate"><span class="pre">firefox_plugin/build</span></code> directory</p></li>
</ol>
</li>
</ol>
<p>For more information on the Firefox plugin, see the <a class="reference internal" href="../user_guide/firefox_plugin.html"><span class="doc">Firefox Plugin</span></a> guide.</p>
</section>
<section id="how-do-i-configure-bahtbrowse">
<h3>How do I configure bahtBrowse?<a class="headerlink" href="#how-do-i-configure-bahtbrowse" title="Link to this heading"></a></h3>
<p>You can configure bahtBrowse through environment variables or a configuration file. To create a basic configuration:</p>
<ol class="arabic">
<li><p>Create a <code class="docutils literal notranslate"><span class="pre">.env</span></code> file in the project root directory:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># API Server Configuration</span>
<span class="nv">API_PORT</span><span class="o">=</span><span class="m">8082</span>
<span class="nv">API_HOST</span><span class="o">=</span><span class="m">0</span>.0.0.0

<span class="c1"># VNC Configuration</span>
<span class="nv">VNC_PORT</span><span class="o">=</span><span class="m">6080</span>

<span class="c1"># Browser Configuration</span>
<span class="nv">DEFAULT_BROWSER</span><span class="o">=</span>firefox

<span class="c1"># Logging Configuration</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span>INFO
</pre></div>
</div>
</li>
<li><p>Restart the containers:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>down
docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</li>
</ol>
<p>For more advanced configuration options, see the <a class="reference internal" href="../admin_guide/configuration.html"><span class="doc">Configuration</span></a> section.</p>
</section>
<section id="can-i-run-bahtbrowse-on-windows-or-macos">
<h3>Can I run bahtBrowse on Windows or macOS?<a class="headerlink" href="#can-i-run-bahtbrowse-on-windows-or-macos" title="Link to this heading"></a></h3>
<p>While bahtBrowse is primarily designed for Linux, you can run it on Windows or macOS using Docker Desktop. However, performance may be lower compared to running on Linux.</p>
<p>To run bahtBrowse on Windows or macOS:</p>
<ol class="arabic simple">
<li><p>Install Docker Desktop for your operating system</p></li>
<li><p>Follow the standard installation instructions</p></li>
<li><p>Note that some features may not work as expected on non-Linux systems</p></li>
</ol>
</section>
</section>
<section id="usage">
<h2>Usage<a class="headerlink" href="#usage" title="Link to this heading"></a></h2>
<section id="how-do-i-access-websites-through-bahtbrowse">
<h3>How do I access websites through bahtBrowse?<a class="headerlink" href="#how-do-i-access-websites-through-bahtbrowse" title="Link to this heading"></a></h3>
<p>There are several ways to access websites through bahtBrowse:</p>
<ol class="arabic">
<li><p><strong>Firefox Plugin</strong>: The recommended way to use bahtBrowse is through the Firefox plugin. Simply click on the plugin icon in the Firefox toolbar and enter the URL you want to visit.</p></li>
<li><p><strong>Direct API Access</strong>: You can access websites through bahtBrowse by opening the following URL in your browser:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url=https://example.com
</pre></div>
</div>
<p>Replace <code class="docutils literal notranslate"><span class="pre">https://example.com</span></code> with the URL you want to visit.</p>
</li>
<li><p><strong>Command Line</strong>: You can use the command line to access websites through bahtBrowse:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-L<span class="w"> </span><span class="s2">&quot;http://localhost:8082/browse/?url=https://example.com&quot;</span>
</pre></div>
</div>
</li>
</ol>
<p>For more information on using bahtBrowse, see the <a class="reference internal" href="../user_guide/browsing.html"><span class="doc">Secure Browsing with bahtBrowse</span></a> guide.</p>
</section>
<section id="can-i-use-bahtbrowse-with-browsers-other-than-firefox">
<h3>Can I use bahtBrowse with browsers other than Firefox?<a class="headerlink" href="#can-i-use-bahtbrowse-with-browsers-other-than-firefox" title="Link to this heading"></a></h3>
<p>Yes, bahtBrowse supports both Firefox and Chromium browsers. You can specify which browser to use by adding the <code class="docutils literal notranslate"><span class="pre">browser</span></code> parameter to the URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url=https://example.com&amp;browser=firefox
http://localhost:8082/browse/?url=https://example.com&amp;browser=chromium
</pre></div>
</div>
<p>If no browser is specified, Firefox will be used by default.</p>
</section>
<section id="how-do-i-download-files-through-bahtbrowse">
<h3>How do I download files through bahtBrowse?<a class="headerlink" href="#how-do-i-download-files-through-bahtbrowse" title="Link to this heading"></a></h3>
<p>When you download a file in the containerized browser, it is saved in the container. You can access downloaded files through the downloads manager:</p>
<ol class="arabic simple">
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">http://localhost:8082/downloads</span></code></p></li>
<li><p>You will see a list of downloaded files</p></li>
<li><p>Click on a file to download it to your local system</p></li>
</ol>
<p>Note that downloaded files are stored in the container and will be lost when the container is restarted. To persist downloads, you can mount a volume to the container.</p>
</section>
<section id="can-i-use-bahtbrowse-for-multiple-users">
<h3>Can I use bahtBrowse for multiple users?<a class="headerlink" href="#can-i-use-bahtbrowse-for-multiple-users" title="Link to this heading"></a></h3>
<p>Yes, bahtBrowse can be used by multiple users simultaneously. Each user will have their own isolated browser session.</p>
<p>However, the default configuration does not include user authentication or session management. For multi-user environments, you should implement:</p>
<ol class="arabic simple">
<li><p><strong>Authentication</strong>: Add user authentication to restrict access to the bahtBrowse service</p></li>
<li><p><strong>Session Management</strong>: Implement session management to associate sessions with users</p></li>
<li><p><strong>Resource Limits</strong>: Set resource limits to prevent resource exhaustion</p></li>
</ol>
<p>For more information on multi-user setups, see the <a class="reference internal" href="../admin_guide/configuration.html"><span class="doc">Configuration</span></a> section.</p>
</section>
<section id="how-do-i-use-bahtbrowse-with-a-proxy-server">
<h3>How do I use bahtBrowse with a proxy server?<a class="headerlink" href="#how-do-i-use-bahtbrowse-with-a-proxy-server" title="Link to this heading"></a></h3>
<p>To use bahtBrowse with a proxy server, you can configure the proxy settings in the browser:</p>
<ol class="arabic">
<li><p>For Firefox:</p>
<p>Edit the <code class="docutils literal notranslate"><span class="pre">user.js</span></code> file in the Firefox profile directory to include proxy settings:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.type&quot;</span><span class="p">,</span><span class="w"> </span><span class="mf">1</span><span class="p">);</span>
<span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.http&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;proxy.example.com&quot;</span><span class="p">);</span>
<span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.http_port&quot;</span><span class="p">,</span><span class="w"> </span><span class="mf">8080</span><span class="p">);</span>
<span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.ssl&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;proxy.example.com&quot;</span><span class="p">);</span>
<span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.ssl_port&quot;</span><span class="p">,</span><span class="w"> </span><span class="mf">8080</span><span class="p">);</span>
<span class="nx">user_pref</span><span class="p">(</span><span class="s2">&quot;network.proxy.no_proxies_on&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;localhost, 127.0.0.1&quot;</span><span class="p">);</span>
</pre></div>
</div>
</li>
<li><p>For Chromium:</p>
<p>Launch Chromium with proxy settings:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>chromium-browser<span class="w"> </span>--proxy-server<span class="o">=</span><span class="s2">&quot;http://proxy.example.com:8080&quot;</span>
</pre></div>
</div>
</li>
</ol>
<p>Alternatively, you can configure the proxy at the Docker level by setting the <code class="docutils literal notranslate"><span class="pre">HTTP_PROXY</span></code> and <code class="docutils literal notranslate"><span class="pre">HTTPS_PROXY</span></code> environment variables in the <code class="docutils literal notranslate"><span class="pre">docker-compose.yml</span></code> file.</p>
</section>
</section>
<section id="security">
<h2>Security<a class="headerlink" href="#security" title="Link to this heading"></a></h2>
<section id="is-bahtbrowse-secure">
<h3>Is bahtBrowse secure?<a class="headerlink" href="#is-bahtbrowse-secure" title="Link to this heading"></a></h3>
<p>bahtBrowse is designed with security in mind. It isolates web browsing activities in Docker containers, protecting your local system from web-based threats.</p>
<p>However, no security solution is perfect, and there are some security considerations to keep in mind:</p>
<ul class="simple">
<li><p><strong>Container Isolation</strong>: While Docker provides good isolation, it is not as strong as virtual machines. Container escape vulnerabilities could potentially expose your system.</p></li>
<li><p><strong>Network Traffic</strong>: Traffic between your browser and the bahtBrowse service is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.</p></li>
<li><p><strong>Session Management</strong>: Sessions are identified by unique IDs, but there is no authentication by default. Consider implementing authentication for multi-user environments.</p></li>
</ul>
<p>For more information on security considerations, see the <a class="reference internal" href="../developer_guide/architecture.html"><span class="doc">System Architecture</span></a> section.</p>
</section>
<section id="can-bahtbrowse-protect-me-from-all-web-threats">
<h3>Can bahtBrowse protect me from all web threats?<a class="headerlink" href="#can-bahtbrowse-protect-me-from-all-web-threats" title="Link to this heading"></a></h3>
<p>No security solution can protect you from all threats. While bahtBrowse provides good protection against many web-based threats, it is not a substitute for good security practices.</p>
<p>To maximize your security:</p>
<ol class="arabic simple">
<li><p><strong>Keep Software Updated</strong>: Keep bahtBrowse, Docker, and your operating system up to date</p></li>
<li><p><strong>Use Strong Passwords</strong>: Use strong, unique passwords for all your accounts</p></li>
<li><p><strong>Enable Two-Factor Authentication</strong>: Enable two-factor authentication where available</p></li>
<li><p><strong>Be Cautious</strong>: Be cautious about the websites you visit and the files you download</p></li>
<li><p><strong>Use Multiple Security Layers</strong>: Use bahtBrowse as part of a comprehensive security strategy</p></li>
</ol>
</section>
<section id="can-i-use-bahtbrowse-for-sensitive-browsing">
<h3>Can I use bahtBrowse for sensitive browsing?<a class="headerlink" href="#can-i-use-bahtbrowse-for-sensitive-browsing" title="Link to this heading"></a></h3>
<p>bahtBrowse can be used for sensitive browsing, but you should take additional precautions:</p>
<ol class="arabic simple">
<li><p><strong>Use HTTPS</strong>: Configure bahtBrowse to use HTTPS to encrypt traffic between your browser and the bahtBrowse service</p></li>
<li><p><strong>Implement Authentication</strong>: Add user authentication to restrict access to the bahtBrowse service</p></li>
<li><p><strong>Use a VPN</strong>: Use a VPN to encrypt traffic between your device and the bahtBrowse service</p></li>
<li><p><strong>Secure the Host</strong>: Ensure that the host running bahtBrowse is secure</p></li>
<li><p><strong>Regular Security Audits</strong>: Conduct regular security audits of your bahtBrowse installation</p></li>
</ol>
<p>For more information on securing bahtBrowse, see the <span class="xref std std-doc">../admin_guide/security</span> section.</p>
</section>
</section>
<section id="performance">
<h2>Performance<a class="headerlink" href="#performance" title="Link to this heading"></a></h2>
<section id="why-is-bahtbrowse-slow">
<h3>Why is bahtBrowse slow?<a class="headerlink" href="#why-is-bahtbrowse-slow" title="Link to this heading"></a></h3>
<p>There are several reasons why bahtBrowse might be slow:</p>
<ol class="arabic simple">
<li><p><strong>Resource Constraints</strong>: The container might not have enough CPU or memory resources</p></li>
<li><p><strong>Network Latency</strong>: High network latency can affect the VNC connection</p></li>
<li><p><strong>Browser Performance</strong>: The browser in the container might be slow due to the website or browser configuration</p></li>
<li><p><strong>VNC Performance</strong>: The VNC connection might be slow due to network conditions or VNC settings</p></li>
<li><p><strong>Container Overhead</strong>: Running browsers in containers adds some overhead</p></li>
</ol>
<p>To improve performance, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="how-can-i-improve-bahtbrowse-performance">
<h3>How can I improve bahtBrowse performance?<a class="headerlink" href="#how-can-i-improve-bahtbrowse-performance" title="Link to this heading"></a></h3>
<p>To improve bahtBrowse performance:</p>
<ol class="arabic simple">
<li><p><strong>Increase Container Resources</strong>: Allocate more CPU and memory to the container</p></li>
<li><p><strong>Optimize VNC Settings</strong>: Reduce VNC quality and resolution</p></li>
<li><p><strong>Use a Different Browser</strong>: Try using Chromium instead of Firefox, or vice versa</p></li>
<li><p><strong>Use Compatibility Mode</strong>: Try using compatibility mode for complex websites</p></li>
<li><p><strong>Optimize Browser Settings</strong>: Disable browser extensions and animations</p></li>
<li><p><strong>Use a Faster Network</strong>: Use a faster network connection</p></li>
<li><p><strong>Use a Native VNC Client</strong>: Use a native VNC client instead of the web-based client</p></li>
</ol>
<p>For more detailed performance optimization tips, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="can-i-run-bahtbrowse-on-low-end-hardware">
<h3>Can I run bahtBrowse on low-end hardware?<a class="headerlink" href="#can-i-run-bahtbrowse-on-low-end-hardware" title="Link to this heading"></a></h3>
<p>Yes, bahtBrowse can run on low-end hardware, but performance may be limited. To run bahtBrowse on low-end hardware:</p>
<ol class="arabic simple">
<li><p><strong>Reduce Container Resources</strong>: Allocate fewer resources to the container</p></li>
<li><p><strong>Use Chromium</strong>: Chromium generally uses fewer resources than Firefox</p></li>
<li><p><strong>Disable JavaScript</strong>: Disable JavaScript execution for better performance</p></li>
<li><p><strong>Reduce VNC Quality</strong>: Reduce VNC quality and resolution</p></li>
<li><p><strong>Limit Concurrent Sessions</strong>: Limit the number of concurrent browser sessions</p></li>
</ol>
<p>For more information on running bahtBrowse on low-end hardware, see the <a class="reference internal" href="../admin_guide/configuration.html"><span class="doc">Configuration</span></a> section.</p>
</section>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<section id="why-can-t-i-connect-to-bahtbrowse">
<h3>Why can’t I connect to bahtBrowse?<a class="headerlink" href="#why-can-t-i-connect-to-bahtbrowse" title="Link to this heading"></a></h3>
<p>If you can’t connect to bahtBrowse, check the following:</p>
<ol class="arabic simple">
<li><p><strong>Service Running</strong>: Ensure that the bahtBrowse service is running</p></li>
<li><p><strong>Port Mapping</strong>: Ensure that the ports are correctly mapped</p></li>
<li><p><strong>Firewall</strong>: Ensure that your firewall allows connections to the required ports</p></li>
<li><p><strong>Network Configuration</strong>: Ensure that the server is accessible from your network</p></li>
<li><p><strong>Plugin Configuration</strong>: Ensure that the Firefox plugin is configured correctly</p></li>
</ol>
<p>For more detailed troubleshooting steps, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="why-does-the-vnc-connection-fail">
<h3>Why does the VNC connection fail?<a class="headerlink" href="#why-does-the-vnc-connection-fail" title="Link to this heading"></a></h3>
<p>If the VNC connection fails, check the following:</p>
<ol class="arabic simple">
<li><p><strong>VNC Server Running</strong>: Ensure that the VNC server is running in the container</p></li>
<li><p><strong>Port Accessibility</strong>: Ensure that the VNC port is accessible</p></li>
<li><p><strong>Nginx Configuration</strong>: Ensure that the nginx configuration includes a location block for the VNC interface</p></li>
<li><p><strong>WebSocket Support</strong>: Ensure that WebSocket support is enabled in nginx</p></li>
<li><p><strong>Browser Support</strong>: Ensure that your browser supports WebSockets</p></li>
</ol>
<p>For more detailed troubleshooting steps, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="why-does-the-browser-crash">
<h3>Why does the browser crash?<a class="headerlink" href="#why-does-the-browser-crash" title="Link to this heading"></a></h3>
<p>If the browser crashes, check the following:</p>
<ol class="arabic simple">
<li><p><strong>Browser Logs</strong>: Check the browser logs for error messages</p></li>
<li><p><strong>System Resources</strong>: Ensure that the container has enough CPU and memory resources</p></li>
<li><p><strong>Browser Compatibility</strong>: Try a different browser (Firefox or Chromium)</p></li>
<li><p><strong>Website Compatibility</strong>: Try compatibility mode for complex websites</p></li>
<li><p><strong>JavaScript Issues</strong>: Try disabling JavaScript execution</p></li>
</ol>
<p>For more detailed troubleshooting steps, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
</section>
<section id="development">
<h2>Development<a class="headerlink" href="#development" title="Link to this heading"></a></h2>
<section id="how-do-i-contribute-to-bahtbrowse">
<h3>How do I contribute to bahtBrowse?<a class="headerlink" href="#how-do-i-contribute-to-bahtbrowse" title="Link to this heading"></a></h3>
<p>To contribute to bahtBrowse:</p>
<ol class="arabic simple">
<li><p><strong>Fork the Repository</strong>: Fork the bahtBrowse repository on GitHub</p></li>
<li><p><strong>Create a Feature Branch</strong>: Create a branch for your feature or bug fix</p></li>
<li><p><strong>Make Changes</strong>: Make your changes to the codebase</p></li>
<li><p><strong>Run Tests</strong>: Run the test suite to ensure your changes don’t break existing functionality</p></li>
<li><p><strong>Submit a Pull Request</strong>: Submit a pull request to the main repository</p></li>
</ol>
<p>For more information on contributing to bahtBrowse, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
<section id="how-do-i-report-a-bug">
<h3>How do I report a bug?<a class="headerlink" href="#how-do-i-report-a-bug" title="Link to this heading"></a></h3>
<p>To report a bug:</p>
<ol class="arabic simple">
<li><p><strong>Check Existing Issues</strong>: Check if the bug has already been reported</p></li>
<li><p><strong>Create a New Issue</strong>: If the bug hasn’t been reported, create a new issue on GitHub</p></li>
<li><p><strong>Provide Details</strong>: Provide as much detail as possible about the bug, including:
- Steps to reproduce
- Expected behavior
- Actual behavior
- Error messages
- System information</p></li>
<li><p><strong>Include Logs</strong>: Include relevant logs from the container</p></li>
<li><p><strong>Attach Screenshots</strong>: If applicable, attach screenshots illustrating the issue</p></li>
</ol>
<p>For more information on reporting bugs, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
<section id="how-do-i-request-a-feature">
<h3>How do I request a feature?<a class="headerlink" href="#how-do-i-request-a-feature" title="Link to this heading"></a></h3>
<p>To request a feature:</p>
<ol class="arabic simple">
<li><p><strong>Check Existing Requests</strong>: Check if the feature has already been requested</p></li>
<li><p><strong>Create a New Issue</strong>: If the feature hasn’t been requested, create a new issue on GitHub</p></li>
<li><p><strong>Provide Details</strong>: Provide as much detail as possible about the feature, including:
- Description of the feature
- Use cases
- Benefits
- Potential implementation approaches</p></li>
<li><p><strong>Label as Enhancement</strong>: Label the issue as an enhancement</p></li>
<li><p><strong>Discuss with the Community</strong>: Discuss the feature with the community to gather feedback</p></li>
</ol>
<p>For more information on requesting features, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
<section id="how-do-i-set-up-a-development-environment">
<h3>How do I set up a development environment?<a class="headerlink" href="#how-do-i-set-up-a-development-environment" title="Link to this heading"></a></h3>
<p>To set up a development environment for bahtBrowse:</p>
<ol class="arabic simple">
<li><p><strong>Clone the Repository</strong>: Clone the bahtBrowse repository</p></li>
<li><p><strong>Create a Virtual Environment</strong>: Create a Python virtual environment</p></li>
<li><p><strong>Install Dependencies</strong>: Install the development dependencies</p></li>
<li><p><strong>Set Up Pre-commit Hooks</strong>: Set up pre-commit hooks for code quality</p></li>
<li><p><strong>Build and Run Containers</strong>: Build and run the containers in development mode</p></li>
</ol>
<p>For detailed instructions on setting up a development environment, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
</section>
<section id="miscellaneous">
<h2>Miscellaneous<a class="headerlink" href="#miscellaneous" title="Link to this heading"></a></h2>
<section id="can-i-use-bahtbrowse-with-other-browsers">
<h3>Can I use bahtBrowse with other browsers?<a class="headerlink" href="#can-i-use-bahtbrowse-with-other-browsers" title="Link to this heading"></a></h3>
<p>Currently, bahtBrowse supports Firefox and Chromium browsers. Support for other browsers may be added in the future.</p>
<p>If you need to use a different browser, you can:</p>
<ol class="arabic simple">
<li><p><strong>Modify the Docker Image</strong>: Create a custom Docker image with the browser of your choice</p></li>
<li><p><strong>Update the API Server</strong>: Modify the API server to support the new browser</p></li>
<li><p><strong>Contribute the Changes</strong>: Contribute your changes back to the project</p></li>
</ol>
<p>For more information on extending bahtBrowse, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
<section id="is-bahtbrowse-suitable-for-enterprise-use">
<h3>Is bahtBrowse suitable for enterprise use?<a class="headerlink" href="#is-bahtbrowse-suitable-for-enterprise-use" title="Link to this heading"></a></h3>
<p>Yes, bahtBrowse can be used in enterprise environments. However, for enterprise use, you should consider:</p>
<ol class="arabic simple">
<li><p><strong>Authentication</strong>: Implement user authentication to restrict access to the bahtBrowse service</p></li>
<li><p><strong>Authorization</strong>: Implement authorization to control what users can do</p></li>
<li><p><strong>Logging and Monitoring</strong>: Set up logging and monitoring to track usage and detect issues</p></li>
<li><p><strong>High Availability</strong>: Set up a high-availability configuration to ensure service continuity</p></li>
<li><p><strong>Scalability</strong>: Implement load balancing to distribute browser sessions across multiple servers</p></li>
<li><p><strong>Security Hardening</strong>: Apply additional security measures to protect the service</p></li>
</ol>
<p>For more information on enterprise deployments, see the <a class="reference internal" href="../admin_guide/deployment.html"><span class="doc">Deployment</span></a> section.</p>
</section>
<section id="how-does-bahtbrowse-compare-to-commercial-rbi-solutions">
<h3>How does bahtBrowse compare to commercial RBI solutions?<a class="headerlink" href="#how-does-bahtbrowse-compare-to-commercial-rbi-solutions" title="Link to this heading"></a></h3>
<p>bahtBrowse is an open-source alternative to commercial Remote Browser Isolation (RBI) solutions. Compared to commercial solutions, bahtBrowse:</p>
<p><strong>Advantages</strong>:
- <strong>Cost</strong>: Free and open-source
- <strong>Customizability</strong>: Can be customized to meet specific requirements
- <strong>Transparency</strong>: Source code is available for review
- <strong>Community Support</strong>: Supported by the open-source community</p>
<p><strong>Disadvantages</strong>:
- <strong>Features</strong>: May have fewer features than commercial solutions
- <strong>Support</strong>: No commercial support (though community support is available)
- <strong>Integration</strong>: May require more effort to integrate with existing systems
- <strong>Documentation</strong>: Documentation may be less comprehensive</p>
<p>For a detailed comparison with specific commercial solutions, see the <span class="xref std std-doc">../admin_guide/comparison</span> section.</p>
</section>
<section id="can-i-use-bahtbrowse-on-mobile-devices">
<h3>Can I use bahtBrowse on mobile devices?<a class="headerlink" href="#can-i-use-bahtbrowse-on-mobile-devices" title="Link to this heading"></a></h3>
<p>bahtBrowse is primarily designed for desktop use, but you can access it from mobile devices through a web browser. However, the user experience may not be optimized for mobile devices.</p>
<p>To use bahtBrowse on mobile devices:</p>
<ol class="arabic simple">
<li><p><strong>Access via Browser</strong>: Open your mobile browser and navigate to the bahtBrowse URL</p></li>
<li><p><strong>VNC Interface</strong>: Use the VNC interface to interact with the containerized browser</p></li>
<li><p><strong>Mobile Limitations</strong>: Be aware of mobile-specific limitations, such as screen size and touch input</p></li>
</ol>
<p>For a better mobile experience, consider using a mobile-optimized VNC client.</p>
</section>
</section>
<section id="getting-help">
<h2>Getting Help<a class="headerlink" href="#getting-help" title="Link to this heading"></a></h2>
<section id="where-can-i-get-help-with-bahtbrowse">
<h3>Where can I get help with bahtBrowse?<a class="headerlink" href="#where-can-i-get-help-with-bahtbrowse" title="Link to this heading"></a></h3>
<p>If you need help with bahtBrowse, you can:</p>
<ol class="arabic simple">
<li><p><strong>Check the Documentation</strong>: Read the documentation for answers to common questions</p></li>
<li><p><strong>Search for Similar Issues</strong>: Search for similar issues on GitHub</p></li>
<li><p><strong>Ask the Community</strong>: Ask the community for help on GitHub Discussions or other forums</p></li>
<li><p><strong>Report an Issue</strong>: If you’ve found a bug, report it on GitHub</p></li>
<li><p><strong>Contact the Maintainers</strong>: Contact the maintainers for help with complex issues</p></li>
</ol>
<p>For more information on getting help, see the <a class="reference internal" href="common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="how-do-i-stay-updated-on-bahtbrowse-developments">
<h3>How do I stay updated on bahtBrowse developments?<a class="headerlink" href="#how-do-i-stay-updated-on-bahtbrowse-developments" title="Link to this heading"></a></h3>
<p>To stay updated on bahtBrowse developments:</p>
<ol class="arabic simple">
<li><p><strong>Watch the Repository</strong>: Watch the bahtBrowse repository on GitHub</p></li>
<li><p><strong>Star the Repository</strong>: Star the repository to show your interest</p></li>
<li><p><strong>Follow the Maintainers</strong>: Follow the maintainers on GitHub</p></li>
<li><p><strong>Join the Community</strong>: Join the bahtBrowse community on GitHub Discussions or other forums</p></li>
<li><p><strong>Subscribe to Releases</strong>: Subscribe to release notifications on GitHub</p></li>
</ol>
<p>For more information on staying updated, see the project’s GitHub repository.</p>
</section>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="common_issues.html" class="btn btn-neutral float-left" title="Common Issues" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../admin_guide/index.html" class="btn btn-neutral float-right" title="Administrator Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, bahtBrowse Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
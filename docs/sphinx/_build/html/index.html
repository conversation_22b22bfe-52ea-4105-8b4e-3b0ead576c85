<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Welcome to bahtBrowse Documentation &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="_static/jquery.js?v=5d32c60e"></script>
        <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="_static/documentation_options.js?v=8d563738"></script>
        <script src="_static/doctools.js?v=9a2dae69"></script>
        <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Developer Guide" href="developer_guide/index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="#" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="developer_guide/index.html">Developer Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer_guide/architecture.html">Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer_guide/health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer_guide/contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="developer_guide/testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="#" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Welcome to bahtBrowse Documentation</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="welcome-to-bahtbrowse-documentation">
<h1>Welcome to bahtBrowse Documentation<a class="headerlink" href="#welcome-to-bahtbrowse-documentation" title="Link to this heading"></a></h1>
<p><strong>bahtBrowse</strong> is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.</p>
<a class="reference internal image-reference" href="_static/bahtbrowse_logo.png"><img alt="bahtBrowse Logo" class="align-center" src="_static/bahtbrowse_logo.png" style="width: 400px;" />
</a>
<section id="key-features">
<h2>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>Secure Isolation</strong>: Browse the web in isolated Docker containers</p></li>
<li><p><strong>Multiple Browser Support</strong>: Firefox and Chromium support</p></li>
<li><p><strong>Seamless Integration</strong>: Firefox plugin for easy access</p></li>
<li><p><strong>VNC Interface</strong>: Remote access to containerized browsers</p></li>
<li><p><strong>API Server</strong>: Manage browser sessions programmatically</p></li>
<li><p><strong>Health Dashboard</strong>: Monitor system health and service status</p></li>
<li><p><strong>Scalable Architecture</strong>: Deploy for individual users or enterprise environments</p></li>
</ul>
</section>
<section id="user-guides">
<h2>User Guides<a class="headerlink" href="#user-guides" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><span class="xref std std-doc">getting_started/index</span>: Get up and running quickly</p></li>
<li><p><span class="xref std std-doc">user_guide/index</span>: Learn how to use bahtBrowse effectively</p></li>
<li><p><span class="xref std std-doc">troubleshooting/index</span>: Solve common issues</p></li>
</ul>
</section>
<section id="administrator-guides">
<h2>Administrator Guides<a class="headerlink" href="#administrator-guides" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><span class="xref std std-doc">admin_guide/index</span>: Deploy and manage bahtBrowse</p></li>
<li><p><span class="xref std std-doc">admin_guide/configuration</span>: Configure bahtBrowse for your environment</p></li>
</ul>
</section>
<section id="developer-guides">
<h2>Developer Guides<a class="headerlink" href="#developer-guides" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="developer_guide/index.html"><span class="doc">Developer Guide</span></a>: Understand the architecture and contribute</p></li>
<li><p><a class="reference internal" href="developer_guide/health_dashboard.html"><span class="doc">Health Dashboard</span></a>: Learn about the Health Dashboard</p></li>
<li><p><a class="reference internal" href="api_reference/index.html"><span class="doc">API Reference</span></a>: API documentation for developers</p></li>
</ul>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
<div class="toctree-wrapper compound">
</div>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading"></a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="developer_guide/index.html" class="btn btn-neutral float-right" title="Developer Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>


<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Configuration &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=051c0d2c" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=f281be69"></script>
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      <script src="../_static/mermaid-init.js?v=fae81138"></script>
      <script src="../_static/dark-mode.js?v=41c052f7"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Maintenance" href="maintenance.html" />
    <link rel="prev" title="Deployment" href="deployment.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html">
            
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../getting_started/index.html">Getting Started with bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/installation.html">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#docker-compose-installation">Docker Compose Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#development-setup">Development Setup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#firefox-plugin-installation">Firefox Plugin Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#configuration">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/quick_start.html">Quick Start Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#starting-bahtbrowse">Starting bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#accessing-websites">Accessing Websites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-api-directly">Using the API Directly</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-firefox-plugin">Using the Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#understanding-the-workflow">Understanding the Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#session-management">Session Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#system-requirements">System Requirements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#quick-overview">Quick Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../user_guide/index.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/firefox_plugin.html">Firefox Plugin</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#installation">Installation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#temporary-installation">Temporary Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#permanent-installation">Permanent Installation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#using-the-plugin">Using the Plugin</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#toolbar-button">Toolbar Button</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#context-menu">Context Menu</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#keyboard-shortcuts">Keyboard Shortcuts</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#connection-status">Connection Status</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#advanced-features">Advanced Features</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#whitelist-and-blacklist">Whitelist and Blacklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#development">Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/browsing.html">Secure Browsing with bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#introduction">Introduction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#direct-api-access">Direct API Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#advanced-parameters">Advanced Parameters</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#post-requests">POST Requests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#command-line-usage">Command Line Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#accessing-websites">Accessing Websites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#testing-connection">Testing Connection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#logging-console-messages">Logging Console Messages</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#managing-downloads">Managing Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#vnc-interface">VNC Interface</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#navigation">Navigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#keyboard-and-mouse">Keyboard and Mouse</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#copy-and-paste">Copy and Paste</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#file-downloads">File Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browser-selection">Browser Selection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#firefox">Firefox</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#chromium">Chromium</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browsing-modes">Browsing Modes</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#normal-mode">Normal Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#compatibility-mode">Compatibility Mode</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#basic-concepts">Basic Concepts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#remote-browser-isolation-rbi">Remote Browser Isolation (RBI)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#containerization">Containerization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#session-management">Session Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#using-bahtbrowse">Using bahtBrowse</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/index.html">Troubleshooting</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/common_issues.html">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#connection-issues">Connection Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#unable-to-connect-to-bahtbrowse-service">Unable to Connect to bahtBrowse Service</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#vnc-connection-fails">VNC Connection Fails</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-issues">Browser Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-fails-to-launch">Browser Fails to Launch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-crashes">Browser Crashes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#website-compatibility-issues">Website Compatibility Issues</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#firefox-plugin-issues">Firefox Plugin Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-connecting">Plugin Not Connecting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-working-correctly">Plugin Not Working Correctly</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-browser-performance">Slow Browser Performance</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-vnc-performance">Slow VNC Performance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#security-issues">Security Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#session-hijacking">Session Hijacking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#container-escape">Container Escape</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#logging-and-debugging">Logging and Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#viewing-logs">Viewing Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#enabling-debug-mode">Enabling Debug Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#debugging-firefox-plugin">Debugging Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#general-questions">General Questions</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-is-bahtbrowse">What is bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-work">How does bahtBrowse work?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-are-the-system-requirements-for-bahtbrowse">What are the system requirements for bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-free-to-use">Is bahtBrowse free to use?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-bahtbrowse">How do I install bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-the-firefox-plugin">How do I install the Firefox plugin?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-configure-bahtbrowse">How do I configure bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-windows-or-macos">Can I run bahtBrowse on Windows or macOS?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#usage">Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-access-websites-through-bahtbrowse">How do I access websites through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-browsers-other-than-firefox">Can I use bahtBrowse with browsers other than Firefox?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-download-files-through-bahtbrowse">How do I download files through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-multiple-users">Can I use bahtBrowse for multiple users?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-use-bahtbrowse-with-a-proxy-server">How do I use bahtBrowse with a proxy server?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#security">Security</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-secure">Is bahtBrowse secure?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-bahtbrowse-protect-me-from-all-web-threats">Can bahtBrowse protect me from all web threats?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-sensitive-browsing">Can I use bahtBrowse for sensitive browsing?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#performance">Performance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-is-bahtbrowse-slow">Why is bahtBrowse slow?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-can-i-improve-bahtbrowse-performance">How can I improve bahtBrowse performance?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-low-end-hardware">Can I run bahtBrowse on low-end hardware?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-can-t-i-connect-to-bahtbrowse">Why can’t I connect to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-vnc-connection-fail">Why does the VNC connection fail?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-browser-crash">Why does the browser crash?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#development">Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-contribute-to-bahtbrowse">How do I contribute to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-report-a-bug">How do I report a bug?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-request-a-feature">How do I request a feature?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-set-up-a-development-environment">How do I set up a development environment?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-other-browsers">Can I use bahtBrowse with other browsers?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-suitable-for-enterprise-use">Is bahtBrowse suitable for enterprise use?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-compare-to-commercial-rbi-solutions">How does bahtBrowse compare to commercial RBI solutions?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-on-mobile-devices">Can I use bahtBrowse on mobile devices?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#getting-help">Getting Help</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#where-can-i-get-help-with-bahtbrowse">Where can I get help with bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-stay-updated-on-bahtbrowse-developments">How do I stay updated on bahtBrowse developments?</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#connection-issues">Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#errors">404 Errors</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#firefox-plugin-issues">Firefox Plugin Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#logging-and-debugging">Logging and Debugging</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#frequently-asked-questions">Frequently Asked Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Administrator Documentation</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Administrator Guide</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#deployment">Deployment</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#configuration">Configuration</a></li>
<li class="toctree-l2 current"><a class="reference internal" href="index.html#maintenance">Maintenance</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="deployment.html">Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#distributed-deployment">Distributed Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#cloud-deployment">Cloud Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#kubernetes-deployment">Kubernetes Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="#docker-configuration">Docker Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#network-configuration">Network Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#port-configuration">Port Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#browser-configuration">Browser Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#security-configuration">Security Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#performance-configuration">Performance Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html">Maintenance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#updating">Updating</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#monitoring">Monitoring</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#backup">Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#testing">Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="deployment.html">Deployment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#single-server-deployment">Single Server Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#clone-the-repository">Clone the Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#build-and-start-the-containers">Build and Start the Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#verify-the-deployment">Verify the Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#distributed-deployment">Distributed Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#architecture">Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#example-deployment">Example Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#cloud-deployment">Cloud Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#aws-deployment">AWS Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#google-cloud-deployment">Google Cloud Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#azure-deployment">Azure Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#kubernetes-deployment">Kubernetes Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#id2">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#deployment-steps">Deployment Steps</a></li>
<li class="toctree-l3"><a class="reference internal" href="deployment.html#example-manifests">Example Manifests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#docker-configuration">Docker Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="#docker-images">Docker Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#network-configuration">Network Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#network-mode">Network Mode</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#port-configuration">Port Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#default-ports">Default Ports</a></li>
<li class="toctree-l3"><a class="reference internal" href="#port-conflict-detection-and-resolution">Port Conflict Detection and Resolution</a></li>
<li class="toctree-l3"><a class="reference internal" href="#port-ranges">Port Ranges</a></li>
<li class="toctree-l3"><a class="reference internal" href="#using-the-port-conflict-resolution-system">Using the Port Conflict Resolution System</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#browser-configuration">Browser Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#browser-types">Browser Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="#browser-pool-configuration">Browser Pool Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#container-isolation">Container Isolation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#network-security">Network Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#performance-configuration">Performance Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#resource-limits">Resource Limits</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scaling">Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="maintenance.html">Maintenance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#updating">Updating</a><ul>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#updating-bahtbrowse">Updating bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#updating-dependencies">Updating Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#monitoring">Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#elk-stack">ELK Stack</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#backup">Backup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#redis-data">Redis Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#docker-volumes">Docker Volumes</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#configuration-files">Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#container-issues">Container Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#port-conflicts">Port Conflicts</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#network-issues">Network Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#container-build-tests">Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#container-detection-tests">Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="maintenance.html#enhanced-tests">Enhanced Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/api_server.html">API Server</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#launch-browser-session">Launch Browser Session</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#request">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#response">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#test-connection">Test Connection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id4">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id5">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#log-console-messages">Log Console Messages</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id6">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id7">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#downloads-manager">Downloads Manager</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id8">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id9">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#list-downloaded-files">List Downloaded Files</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id10">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id11">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#delete-downloaded-file">Delete Downloaded File</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id12">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id13">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#session-management">Session Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#browser-launching">Browser Launching</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-client-examples">API Client Examples</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#python">Python</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#javascript">JavaScript</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#curl">cURL</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/session_management.html">Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-manager">Session Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-creation">Session Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validation">Session Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-cleanup">Session Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validator">Session Validator</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-storage">Session Storage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-security">Session Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-api">Session Management API</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#example-usage">Example Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-in-docker-containers">Session Management in Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#launch-a-browser-session">Launch a Browser Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#test-the-connection">Test the Connection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#log-console-messages">Log Console Messages</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-client-libraries">API Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Administrator Guide</a></li>
      <li class="breadcrumb-item active">Configuration</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/admin_guide/configuration.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="configuration">
<span id="id1"></span><h1>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h1>
<p>This guide provides information on how to configure bahtBrowse for your specific needs.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>bahtBrowse can be configured in various ways to suit your specific requirements. This guide covers the following configuration topics:</p>
<ul class="simple">
<li><p><strong>Docker Configuration</strong>: How to configure Docker containers</p></li>
<li><p><strong>Network Configuration</strong>: How to configure network settings</p></li>
<li><p><strong>Port Configuration</strong>: How to configure port settings and handle port conflicts</p></li>
<li><p><strong>Browser Configuration</strong>: How to configure browser settings</p></li>
<li><p><strong>Security Configuration</strong>: How to configure security settings</p></li>
<li><p><strong>Performance Configuration</strong>: How to configure performance settings</p></li>
</ul>
</section>
<section id="docker-configuration">
<h2>Docker Configuration<a class="headerlink" href="#docker-configuration" title="Link to this heading"></a></h2>
<p>Docker containers are the core of bahtBrowse. Each browser instance runs in its own Docker container, providing isolation and security.</p>
<section id="docker-compose">
<h3>Docker Compose<a class="headerlink" href="#docker-compose" title="Link to this heading"></a></h3>
<p>bahtBrowse uses Docker Compose to manage its containers. The Docker Compose configuration is defined in the <code class="docutils literal notranslate"><span class="pre">docker-compose.fixed.yml</span></code> file.</p>
<p>Example:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3&#39;</span>
<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">build</span><span class="p">:</span>
<span class="w">      </span><span class="nt">context</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">.</span>
<span class="w">      </span><span class="nt">dockerfile</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker/Dockerfile.api</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8082:8082&quot;</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./files:/app/files</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_HOST=redis</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:alpine</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;6379:6379&quot;</span>
<span class="w">  </span><span class="nt">worker</span><span class="p">:</span>
<span class="w">    </span><span class="nt">build</span><span class="p">:</span>
<span class="w">      </span><span class="nt">context</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">.</span>
<span class="w">      </span><span class="nt">dockerfile</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker/Dockerfile.worker</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/var/run/docker.sock:/var/run/docker.sock</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">REDIS_HOST=redis</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
</pre></div>
</div>
</section>
<section id="docker-images">
<h3>Docker Images<a class="headerlink" href="#docker-images" title="Link to this heading"></a></h3>
<p>bahtBrowse uses several Docker images:</p>
<ul class="simple">
<li><p><strong>API Server</strong>: Handles API requests and manages browser sessions</p></li>
<li><p><strong>Redis</strong>: Stores session data and coordinates between components</p></li>
<li><p><strong>Worker</strong>: Manages browser containers and handles background tasks</p></li>
<li><p><strong>Browser Containers</strong>: Run the actual browsers (Firefox, Chromium, etc.)</p></li>
</ul>
</section>
</section>
<section id="network-configuration">
<h2>Network Configuration<a class="headerlink" href="#network-configuration" title="Link to this heading"></a></h2>
<p>bahtBrowse uses Docker’s networking capabilities to connect its containers.</p>
<section id="network-mode">
<h3>Network Mode<a class="headerlink" href="#network-mode" title="Link to this heading"></a></h3>
<p>By default, bahtBrowse uses the <code class="docutils literal notranslate"><span class="pre">bridge</span></code> network mode, which creates a virtual network for the containers.</p>
<p>Example:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">networks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-network</span>
<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">networks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bahtbrowse-network</span>

<span class="nt">networks</span><span class="p">:</span>
<span class="w">  </span><span class="nt">bahtbrowse-network</span><span class="p">:</span>
<span class="w">    </span><span class="nt">driver</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bridge</span>
</pre></div>
</div>
</section>
</section>
<section id="port-configuration">
<h2>Port Configuration<a class="headerlink" href="#port-configuration" title="Link to this heading"></a></h2>
<p>bahtBrowse uses several ports for its services. These ports can be configured to avoid conflicts with other services.</p>
<section id="default-ports">
<h3>Default Ports<a class="headerlink" href="#default-ports" title="Link to this heading"></a></h3>
<p>The default ports used by bahtBrowse are:</p>
<ul class="simple">
<li><p><strong>API Server</strong>: 8082</p></li>
<li><p><strong>Redis</strong>: 6379</p></li>
<li><p><strong>Flower</strong>: 5555</p></li>
<li><p><strong>Nginx</strong>: 80</p></li>
<li><p><strong>Admin Dashboard</strong>: 9001</p></li>
<li><p><strong>Elasticsearch</strong>: 9200</p></li>
<li><p><strong>Kibana</strong>: 5601</p></li>
<li><p><strong>Logstash</strong>: 9600</p></li>
</ul>
</section>
<section id="port-conflict-detection-and-resolution">
<h3>Port Conflict Detection and Resolution<a class="headerlink" href="#port-conflict-detection-and-resolution" title="Link to this heading"></a></h3>
<p>bahtBrowse includes a port conflict detection and resolution system that automatically detects when default ports are already in use and finds alternative ports. This makes the system more resilient when running in environments where the default ports might already be in use by other applications.</p>
<p>The port conflict detection and resolution system is implemented in the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_cli.py</span></code> file and provides the following features:</p>
<ul class="simple">
<li><p><strong>Automatic detection of port conflicts</strong>: The system checks if the default ports are already in use.</p></li>
<li><p><strong>Dynamic port allocation</strong>: If a default port is in use, the system finds an available port in the appropriate range.</p></li>
<li><p><strong>Visual indicators</strong>: Services using non-default ports are highlighted in the status table.</p></li>
<li><p><strong>Configurable port ranges</strong>: Port ranges can be configured for different service categories.</p></li>
<li><p><strong>Seamless communication</strong>: Services can still communicate with each other when using alternative ports.</p></li>
</ul>
</section>
<section id="port-ranges">
<h3>Port Ranges<a class="headerlink" href="#port-ranges" title="Link to this heading"></a></h3>
<p>The port conflict resolution system defines the following port ranges for different service categories:</p>
<ul class="simple">
<li><p><strong>Web Services</strong>: 8000-8100</p></li>
<li><p><strong>Monitoring Services</strong>: 5500-5700</p></li>
<li><p><strong>Infrastructure Services</strong>: 6300-6400</p></li>
<li><p><strong>Core Services</strong>: 8000-8100</p></li>
</ul>
<p>These ranges can be configured in the <code class="docutils literal notranslate"><span class="pre">PORT_RANGES</span></code> dictionary in the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_cli.py</span></code> file.</p>
<p>Example:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">PORT_RANGES</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;web&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">8000</span><span class="p">,</span> <span class="mi">8100</span><span class="p">),</span>           <span class="c1"># Web services</span>
    <span class="s2">&quot;monitoring&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">5500</span><span class="p">,</span> <span class="mi">5700</span><span class="p">),</span>    <span class="c1"># Monitoring services</span>
    <span class="s2">&quot;infrastructure&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">6300</span><span class="p">,</span> <span class="mi">6400</span><span class="p">),</span> <span class="c1"># Infrastructure services</span>
    <span class="s2">&quot;core&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">8000</span><span class="p">,</span> <span class="mi">8100</span><span class="p">)</span>           <span class="c1"># Core services</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="using-the-port-conflict-resolution-system">
<h3>Using the Port Conflict Resolution System<a class="headerlink" href="#using-the-port-conflict-resolution-system" title="Link to this heading"></a></h3>
<p>The port conflict resolution system is integrated into the bahtBrowse CLI tool. When you start services using the CLI, it automatically checks if the default ports are available and finds alternative ports if needed.</p>
<p>To start all services with automatic port conflict resolution:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./bahtbrowse_cli.py<span class="w"> </span>--start-all
</pre></div>
</div>
<p>To start a specific service:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./bahtbrowse_cli.py<span class="w"> </span>--start<span class="w"> </span>&lt;service_name&gt;
</pre></div>
</div>
<p>To view the status of all services, including their port assignments:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>./bahtbrowse_cli.py<span class="w"> </span>--status
</pre></div>
</div>
<p>In the status table, services using non-default ports will have their port numbers highlighted in yellow.</p>
</section>
</section>
<section id="browser-configuration">
<h2>Browser Configuration<a class="headerlink" href="#browser-configuration" title="Link to this heading"></a></h2>
<p>bahtBrowse supports multiple browsers, each with its own configuration options.</p>
<section id="browser-types">
<h3>Browser Types<a class="headerlink" href="#browser-types" title="Link to this heading"></a></h3>
<p>bahtBrowse supports the following browser types:</p>
<ul class="simple">
<li><p><strong>Firefox</strong>: Mozilla Firefox browser</p></li>
<li><p><strong>Chromium</strong>: Chromium browser</p></li>
<li><p><strong>Tor Browser</strong>: Tor Browser for anonymous browsing</p></li>
</ul>
</section>
<section id="browser-pool-configuration">
<h3>Browser Pool Configuration<a class="headerlink" href="#browser-pool-configuration" title="Link to this heading"></a></h3>
<p>bahtBrowse includes a browser pool configuration feature that allows you to configure how many browsers of each type should be kept ready. This feature is implemented in the <code class="docutils literal notranslate"><span class="pre">docker_queue/api/queue_config_routes.py</span></code> file.</p>
<p>The browser pool configuration includes the following settings:</p>
<ul class="simple">
<li><p><strong>Min Pool Size</strong>: The minimum number of browser containers to keep ready</p></li>
<li><p><strong>Max Pool Size</strong>: The maximum number of browser containers to allow</p></li>
<li><p><strong>Idle Timeout</strong>: How long to keep idle containers before removing them</p></li>
<li><p><strong>Per-Browser Type Settings</strong>: Configure settings for each browser type</p></li>
</ul>
<p>Example:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;global&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;min_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;idle_timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;firefox&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;min_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;idle_timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;chromium&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;min_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;idle_timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">300</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;tor-browser&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;min_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_pool_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;idle_timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">600</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="security-configuration">
<h2>Security Configuration<a class="headerlink" href="#security-configuration" title="Link to this heading"></a></h2>
<p>bahtBrowse includes several security features that can be configured.</p>
<section id="container-isolation">
<h3>Container Isolation<a class="headerlink" href="#container-isolation" title="Link to this heading"></a></h3>
<p>Browser containers are isolated from each other and from the host system. This isolation can be configured in the Docker Compose file.</p>
<p>Example:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">security_opt</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">seccomp=seccomp-firefox.json</span>
<span class="w">    </span><span class="nt">cap_drop</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ALL</span>
<span class="w">    </span><span class="nt">cap_add</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NET_ADMIN</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SYS_ADMIN</span>
</pre></div>
</div>
</section>
<section id="network-security">
<h3>Network Security<a class="headerlink" href="#network-security" title="Link to this heading"></a></h3>
<p>bahtBrowse includes network security features that can be configured.</p>
<p>Example:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">nginx</span><span class="p">:</span>
<span class="w">    </span><span class="nt">networks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend</span>
<span class="w">  </span><span class="nt">api</span><span class="p">:</span>
<span class="w">    </span><span class="nt">networks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend</span>
<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">networks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend</span>

<span class="nt">networks</span><span class="p">:</span>
<span class="w">  </span><span class="nt">frontend</span><span class="p">:</span>
<span class="w">    </span><span class="nt">driver</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bridge</span>
<span class="w">  </span><span class="nt">backend</span><span class="p">:</span>
<span class="w">    </span><span class="nt">driver</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bridge</span>
<span class="w">    </span><span class="nt">internal</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</pre></div>
</div>
</section>
</section>
<section id="performance-configuration">
<h2>Performance Configuration<a class="headerlink" href="#performance-configuration" title="Link to this heading"></a></h2>
<p>bahtBrowse includes several performance features that can be configured.</p>
<section id="resource-limits">
<h3>Resource Limits<a class="headerlink" href="#resource-limits" title="Link to this heading"></a></h3>
<p>Docker containers can have resource limits configured in the Docker Compose file.</p>
<p>Example:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">firefox</span><span class="p">:</span>
<span class="w">    </span><span class="nt">mem_limit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2g</span>
<span class="w">    </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">chromium</span><span class="p">:</span>
<span class="w">    </span><span class="nt">mem_limit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2g</span>
<span class="w">    </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">tor-browser</span><span class="p">:</span>
<span class="w">    </span><span class="nt">mem_limit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1g</span>
<span class="w">    </span><span class="nt">cpus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
</pre></div>
</div>
</section>
<section id="scaling">
<h3>Scaling<a class="headerlink" href="#scaling" title="Link to this heading"></a></h3>
<p>bahtBrowse can be scaled to handle more users by increasing the number of worker containers.</p>
<p>Example:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.fixed.yml<span class="w"> </span>up<span class="w"> </span>-d<span class="w"> </span>--scale<span class="w"> </span><span class="nv">worker</span><span class="o">=</span><span class="m">3</span>
</pre></div>
</div>
</section>
</section>
<section id="conclusion">
<h2>Conclusion<a class="headerlink" href="#conclusion" title="Link to this heading"></a></h2>
<p>This guide covered the various configuration options available in bahtBrowse. By configuring these options, you can customize bahtBrowse to suit your specific needs.</p>
<p>For more information on deploying bahtBrowse, see the <a class="reference internal" href="deployment.html"><span class="doc">Deployment</span></a> section.</p>
<p>For more information on maintaining bahtBrowse, see the <a class="reference internal" href="maintenance.html"><span class="doc">Maintenance</span></a> section.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="deployment.html" class="btn btn-neutral float-left" title="Deployment" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="maintenance.html" class="btn btn-neutral float-right" title="Maintenance" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, bahtBrowse Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
.. _api_server:

API Server
=========

The bahtBrowse API server provides a RESTful API for launching and managing browser sessions. This document provides detailed information about the API endpoints, request parameters, and response formats.

Overview
-------

The API server is implemented in Python using the aiohttp framework. It provides endpoints for launching browser sessions, testing the connection, logging browser console messages, and managing downloads.

The API server is designed to be simple and easy to use, with a focus on providing a seamless browsing experience.

API Endpoints
-----------

The API server provides the following endpoints:

+------------------------+------------------------------------------+---------------------------+
| Endpoint               | Description                              | Methods                   |
+========================+==========================================+===========================+
| ``/browse/``           | Launch a browser session                 | GET, POST                 |
+------------------------+------------------------------------------+---------------------------+
| ``/test-connection``   | Test the connection to the service       | GET, POST                 |
+------------------------+------------------------------------------+---------------------------+
| ``/log-console``       | Log browser console messages             | POST                      |
+------------------------+------------------------------------------+---------------------------+
| ``/downloads``         | Access the downloads manager             | GET                       |
+------------------------+------------------------------------------+---------------------------+
| ``/downloads-api/list``| List downloaded files                    | GET                       |
+------------------------+------------------------------------------+---------------------------+
| ``/downloads-api/delete``| Delete a downloaded file               | POST                      |
+------------------------+------------------------------------------+---------------------------+

Launch Browser Session
-------------------

The ``/browse/`` endpoint is used to launch a browser session with the specified URL.

Request
~~~~~~

**GET /browse/**

Parameters:

+------------------------+------------------------------------------+---------------------------+------------------+
| Parameter              | Description                              | Example                   | Required         |
+========================+==========================================+===========================+==================+
| ``url``                | The URL to visit                         | ``url=https://example.com``| Yes             |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``browser``            | The browser to use (firefox or chromium) | ``browser=firefox``      | No (default: firefox) |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``mode``               | Browsing mode (normal or compatibility)  | ``mode=normal``          | No (default: normal) |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``disable_js_execution``| Disable JavaScript execution (true/false)| ``disable_js_execution=false``| No (default: false) |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``js_timeout``         | JavaScript execution timeout in ms       | ``js_timeout=10000``     | No (default: 10000) |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``locale``             | Browser locale                           | ``locale=en-US``         | No (default: en-US) |
+------------------------+------------------------------------------+---------------------------+------------------+

Example:

.. code-block:: text

   GET /browse/?url=https://example.com&browser=firefox&mode=normal HTTP/1.1
   Host: localhost:8082

**POST /browse/**

Parameters:

The same parameters as the GET request, but sent in the request body as form data.

Example:

.. code-block:: text

   POST /browse/ HTTP/1.1
   Host: localhost:8082
   Content-Type: application/x-www-form-urlencoded

   url=https://example.com&browser=firefox&mode=normal

Response
~~~~~~~

The response is a redirect to the VNC interface with the session ID.

Status Code: ``302 Found``

Headers:

.. code-block:: text

   Location: http://localhost:6081/vnc1/run?session=12345678-1234-1234-1234-123456789abc

Error Responses:

+------------------------+------------------------------------------+---------------------------+
| Status Code            | Description                              | Example                   |
+========================+==========================================+===========================+
| ``400 Bad Request``    | Invalid request parameters               | ``URL is required``      |
+------------------------+------------------------------------------+---------------------------+
| ``500 Internal Server Error`` | Server error                      | ``Error launching browser`` |
+------------------------+------------------------------------------+---------------------------+

Test Connection
------------

The ``/test-connection`` endpoint is used to test the connection to the service.

Request
~~~~~~

**GET /test-connection**

No parameters required.

Example:

.. code-block:: text

   GET /test-connection HTTP/1.1
   Host: localhost:8082

**POST /test-connection**

No parameters required.

Example:

.. code-block:: text

   POST /test-connection HTTP/1.1
   Host: localhost:8082

Response
~~~~~~~

The response is a JSON object with information about the service.

Status Code: ``200 OK``

Body:

.. code-block:: json

   {
     "status": "success",
     "message": "BahtBrowse service is available",
     "timestamp": "2025-04-27T20:00:00",
     "client_ip": "127.0.0.1",
     "client_info": "Firefox Plugin",
     "request_source": "background-script",
     "plugin_version": "1.0.0",
     "host": "localhost",
     "port": "8082",
     "server_version": "1.1.0",
     "compatibility_mode": true,
     "supported_browsers": ["firefox", "chromium"],
     "supported_sites": ["amazon.com", "amazon.*", "amzn.*"],
     "server_time": "2025-04-27T20:00:00.000Z"
   }

Log Console Messages
----------------

The ``/log-console`` endpoint is used to log browser console messages.

Request
~~~~~~

**POST /log-console**

Parameters (JSON):

+------------------------+------------------------------------------+---------------------------+------------------+
| Parameter              | Description                              | Example                   | Required         |
+========================+==========================================+===========================+==================+
| ``level``              | Log level (info, warn, error)            | ``level=info``           | Yes              |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``message``            | Log message                              | ``message=Test message`` | Yes              |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``url``                | URL where the message originated         | ``url=https://example.com``| No              |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``session_id``         | Session ID                               | ``session_id=12345678-1234-1234-1234-123456789abc``| No |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``line``               | Line number                              | ``line=42``              | No               |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``column``             | Column number                            | ``column=10``            | No               |
+------------------------+------------------------------------------+---------------------------+------------------+
| ``stack``              | Stack trace                              | ``stack=Error: Test error\n    at test.js:42:10``| No |
+------------------------+------------------------------------------+---------------------------+------------------+

Example:

.. code-block:: text

   POST /log-console HTTP/1.1
   Host: localhost:8082
   Content-Type: application/json

   {
     "level": "info",
     "message": "Test message",
     "url": "https://example.com",
     "session_id": "12345678-1234-1234-1234-123456789abc",
     "line": 42,
     "column": 10,
     "stack": "Error: Test error\n    at test.js:42:10"
   }

Response
~~~~~~~

The response is a JSON object indicating success or failure.

Status Code: ``200 OK``

Body:

.. code-block:: json

   {
     "status": "success",
     "message": "Console log received"
   }

Error Responses:

+------------------------+------------------------------------------+---------------------------+
| Status Code            | Description                              | Example                   |
+========================+==========================================+===========================+
| ``400 Bad Request``    | Invalid request parameters               | ``level is required``    |
+------------------------+------------------------------------------+---------------------------+
| ``500 Internal Server Error`` | Server error                      | ``Error processing console log`` |
+------------------------+------------------------------------------+---------------------------+

Downloads Manager
-------------

The ``/downloads`` endpoint is used to access the downloads manager.

Request
~~~~~~

**GET /downloads**

No parameters required.

Example:

.. code-block:: text

   GET /downloads HTTP/1.1
   Host: localhost:8082

Response
~~~~~~~

The response is an HTML page with the downloads manager.

Status Code: ``200 OK``

Content-Type: ``text/html``

List Downloaded Files
-----------------

The ``/downloads-api/list`` endpoint is used to list downloaded files.

Request
~~~~~~

**GET /downloads-api/list**

No parameters required.

Example:

.. code-block:: text

   GET /downloads-api/list HTTP/1.1
   Host: localhost:8082

Response
~~~~~~~

The response is a JSON object with the list of downloaded files.

Status Code: ``200 OK``

Body:

.. code-block:: json

   {
     "success": true,
     "files": [
       {
         "name": "example.pdf",
         "size": 12345,
         "mtime": 1619539200000
       },
       {
         "name": "image.jpg",
         "size": 67890,
         "mtime": 1619539100000
       }
     ]
   }

Error Responses:

+------------------------+------------------------------------------+---------------------------+
| Status Code            | Description                              | Example                   |
+========================+==========================================+===========================+
| ``500 Internal Server Error`` | Server error                      | ``Error listing downloads`` |
+------------------------+------------------------------------------+---------------------------+

Delete Downloaded File
------------------

The ``/downloads-api/delete`` endpoint is used to delete a downloaded file.

Request
~~~~~~

**POST /downloads-api/delete**

Parameters (JSON):

+------------------------+------------------------------------------+---------------------------+------------------+
| Parameter              | Description                              | Example                   | Required         |
+========================+==========================================+===========================+==================+
| ``filename``           | Name of the file to delete               | ``filename=example.pdf`` | Yes              |
+------------------------+------------------------------------------+---------------------------+------------------+

Example:

.. code-block:: text

   POST /downloads-api/delete HTTP/1.1
   Host: localhost:8082
   Content-Type: application/json

   {
     "filename": "example.pdf"
   }

Response
~~~~~~~

The response is a JSON object indicating success or failure.

Status Code: ``200 OK``

Body:

.. code-block:: json

   {
     "success": true
   }

Error Responses:

+------------------------+------------------------------------------+---------------------------+
| Status Code            | Description                              | Example                   |
+========================+==========================================+===========================+
| ``400 Bad Request``    | Invalid request parameters               | ``Filename is required`` |
+------------------------+------------------------------------------+---------------------------+
| ``404 Not Found``      | File not found                           | ``File not found``       |
+------------------------+------------------------------------------+---------------------------+
| ``500 Internal Server Error`` | Server error                      | ``Error deleting file``  |
+------------------------+------------------------------------------+---------------------------+

Implementation Details
------------------

The API server is implemented in Python using the aiohttp framework. It communicates with the Docker Engine to create and manage containers.

Session Management
~~~~~~~~~~~~~~

The API server uses a session manager to create and manage browser sessions. Each session is identified by a unique session ID (UUID).

The session manager is responsible for:

- Creating new sessions
- Validating session IDs
- Cleaning up expired sessions

Browser Launching
~~~~~~~~~~~~~

The API server launches browsers in Docker containers. It supports both Firefox and Chromium browsers.

The browser launching process involves:

1. Creating a new Docker container
2. Starting the VNC server in the container
3. Launching the browser with the specified URL
4. Redirecting the user to the VNC interface

Error Handling
~~~~~~~~~~

The API server includes comprehensive error handling to ensure a smooth user experience. It handles errors such as:

- Invalid request parameters
- Browser launch failures
- Container creation failures
- VNC server failures

Logging
~~~~~

The API server logs all requests and errors to help with debugging and monitoring. It logs:

- Request parameters
- Response status codes
- Error messages
- Browser console messages

Security Considerations
------------------

The API server is designed with security in mind. However, there are some security considerations to keep in mind:

- **Authentication**: The API server does not include authentication by default. Consider implementing authentication for multi-user environments.
- **Input Validation**: The API server validates all input parameters to prevent injection attacks.
- **Container Isolation**: Browsers run in isolated Docker containers to prevent malicious websites from affecting the host system.
- **Network Security**: Traffic between components is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.

For more information on security considerations, see the :doc:`../developer_guide/architecture` section.

API Client Examples
---------------

Here are some examples of how to use the API from different programming languages:

Python
~~~~~

.. code-block:: python

   import requests

   # Launch a browser session
   response = requests.get("http://localhost:8082/browse/", params={"url": "https://example.com"})
   
   # Follow the redirect
   if response.status_code == 302:
       redirect_url = response.headers["Location"]
       print(f"Redirected to: {redirect_url}")

JavaScript
~~~~~~~~

.. code-block:: javascript

   // Launch a browser session
   fetch("http://localhost:8082/browse/?url=https://example.com")
     .then(response => {
       if (response.redirected) {
         window.location.href = response.url;
       } else {
         return response.text().then(text => {
           throw new Error(`Error: ${text}`);
         });
       }
     })
     .catch(error => {
       console.error("Error:", error);
     });

cURL
~~~~

.. code-block:: bash

   # Launch a browser session
   curl -L "http://localhost:8082/browse/?url=https://example.com"

   # Test the connection
   curl "http://localhost:8082/test-connection"

   # Log a console message
   curl -X POST -H "Content-Type: application/json" -d '{"level": "info", "message": "Test message", "url": "https://example.com", "session_id": "12345678-1234-1234-1234-123456789abc"}' "http://localhost:8082/log-console"

   # List downloaded files
   curl "http://localhost:8082/downloads-api/list"

   # Delete a downloaded file
   curl -X POST -H "Content-Type: application/json" -d '{"filename": "example.pdf"}' "http://localhost:8082/downloads-api/delete"

Conclusion
--------

The bahtBrowse API server provides a simple and easy-to-use API for launching and managing browser sessions. It is designed to be flexible and extensible, allowing for a wide range of use cases.

For more information on the bahtBrowse architecture, see the :doc:`../developer_guide/architecture` section.

.. _session_management:

Session Management
===============

This document provides detailed information about the session management system in bahtBrowse, including session creation, validation, and cleanup.

Overview
-------

The session management system is responsible for creating and managing browser sessions. Each session is identified by a unique session ID (UUID) and is associated with a specific client IP address.

The session manager ensures that:

- Each session has a unique ID
- Sessions are isolated from each other
- Sessions are cleaned up after a period of inactivity
- Session IDs are validated before use

Session Manager
-----------

The session manager is implemented in the ``session_manager.py`` file. It provides methods for creating, validating, and cleaning up sessions.

.. code-block:: python

   class SessionManager:
       """Manage browser sessions."""

       def __init__(self):
           """Initialize the session manager."""
           self.sessions = {}
           self.session_timeout = 1800  # 30 minutes

       def create_session(self, client_ip):
           """Create a new session for the given client IP."""
           session_id = str(uuid.uuid4())
           self.sessions[session_id] = {
               'client_ip': client_ip,
               'created_at': time.time(),
               'last_accessed': time.time()
           }
           return session_id

       def validate_session(self, session_id, client_ip):
           """Validate the session ID for the given client IP."""
           if session_id not in self.sessions:
               return False
           
           session = self.sessions[session_id]
           if session['client_ip'] != client_ip:
               return False
           
           # Update last accessed time
           session['last_accessed'] = time.time()
           return True

       def cleanup_sessions(self):
           """Clean up expired sessions."""
           now = time.time()
           expired_sessions = []
           
           for session_id, session in self.sessions.items():
               if now - session['last_accessed'] > self.session_timeout:
                   expired_sessions.append(session_id)
           
           for session_id in expired_sessions:
               del self.sessions[session_id]

Session Creation
------------

Sessions are created when a user requests a new browser session through the API server. The session manager creates a new session with a unique ID and associates it with the client IP address.

.. code-block:: python

   # Create a new session
   session_id = session_manager.create_session(client_ip)

The session ID is a UUID (Universally Unique Identifier) that is generated using the ``uuid.uuid4()`` function. This ensures that each session has a unique ID.

The session is stored in the session manager's ``sessions`` dictionary, with the session ID as the key and a dictionary containing the client IP address, creation time, and last accessed time as the value.

Session Validation
--------------

Before a session is used, it is validated to ensure that it exists and is associated with the correct client IP address. This prevents unauthorized access to sessions.

.. code-block:: python

   # Validate a session
   is_valid = session_manager.validate_session(session_id, client_ip)

The validation process checks that:

- The session ID exists in the session manager's ``sessions`` dictionary
- The session is associated with the correct client IP address

If the session is valid, the last accessed time is updated to the current time. This ensures that active sessions are not cleaned up.

Session Cleanup
-----------

Sessions are automatically cleaned up after a period of inactivity to prevent resource exhaustion. The default timeout is 30 minutes (1800 seconds).

.. code-block:: python

   # Clean up expired sessions
   session_manager.cleanup_sessions()

The cleanup process checks each session's last accessed time. If the session has not been accessed for longer than the timeout period, it is removed from the session manager's ``sessions`` dictionary.

The cleanup process is typically run periodically, such as once per minute, to ensure that expired sessions are removed in a timely manner.

Session Validator
-------------

The session validator is a middleware component that validates session IDs in incoming requests. It is implemented in the ``session_validator.py`` file.

.. code-block:: python

   class SessionValidator:
       """Validate session IDs in incoming requests."""

       def __init__(self, session_manager):
           """Initialize the session validator."""
           self.session_manager = session_manager

       async def validate(self, request):
           """Validate the session ID in the request."""
           session_id = request.query.get('session')
           if not session_id:
               return False
           
           client_ip = request.remote
           return self.session_manager.validate_session(session_id, client_ip)

The session validator is used by the API server to validate session IDs in incoming requests. It extracts the session ID from the request query parameters and validates it using the session manager.

Session Storage
-----------

Sessions are stored in memory in the session manager's ``sessions`` dictionary. This provides fast access to session data but means that sessions are lost if the server is restarted.

For production environments, it is recommended to use a persistent storage backend, such as Redis or a database, to store session data. This ensures that sessions are not lost if the server is restarted.

Here's an example of how to implement a Redis-based session manager:

.. code-block:: python

   import redis
   import uuid
   import time
   import json

   class RedisSessionManager:
       """Manage browser sessions using Redis."""

       def __init__(self, redis_url='redis://localhost:6379/0'):
           """Initialize the session manager."""
           self.redis = redis.from_url(redis_url)
           self.session_timeout = 1800  # 30 minutes

       def create_session(self, client_ip):
           """Create a new session for the given client IP."""
           session_id = str(uuid.uuid4())
           session_data = {
               'client_ip': client_ip,
               'created_at': time.time(),
               'last_accessed': time.time()
           }
           self.redis.setex(
               f'session:{session_id}',
               self.session_timeout,
               json.dumps(session_data)
           )
           return session_id

       def validate_session(self, session_id, client_ip):
           """Validate the session ID for the given client IP."""
           session_key = f'session:{session_id}'
           session_data_str = self.redis.get(session_key)
           
           if not session_data_str:
               return False
           
           session_data = json.loads(session_data_str)
           if session_data['client_ip'] != client_ip:
               return False
           
           # Update last accessed time
           session_data['last_accessed'] = time.time()
           self.redis.setex(
               session_key,
               self.session_timeout,
               json.dumps(session_data)
           )
           return True

       def cleanup_sessions(self):
           """Clean up expired sessions."""
           # Redis automatically expires keys based on the timeout
           pass

Session Security
------------

The session management system includes several security features to prevent unauthorized access to sessions:

- **Session IDs**: Session IDs are UUIDs, which are cryptographically secure and difficult to guess.
- **Client IP Binding**: Sessions are bound to the client IP address, preventing session hijacking.
- **Session Timeout**: Sessions expire after a period of inactivity, limiting the window of opportunity for attacks.
- **Session Validation**: Session IDs are validated before use, preventing unauthorized access.

For additional security, consider implementing:

- **HTTPS**: Use HTTPS to encrypt traffic between the client and server, preventing eavesdropping.
- **CSRF Protection**: Implement Cross-Site Request Forgery (CSRF) protection to prevent unauthorized requests.
- **Rate Limiting**: Implement rate limiting to prevent brute force attacks on session IDs.

Session Management API
------------------

The session manager provides the following API for managing sessions:

+------------------------+------------------------------------------+---------------------------+
| Method                 | Description                              | Parameters                |
+========================+==========================================+===========================+
| ``create_session``     | Create a new session                     | ``client_ip``            |
+------------------------+------------------------------------------+---------------------------+
| ``validate_session``   | Validate a session                       | ``session_id``, ``client_ip`` |
+------------------------+------------------------------------------+---------------------------+
| ``cleanup_sessions``   | Clean up expired sessions                | None                      |
+------------------------+------------------------------------------+---------------------------+

Example Usage
----------

Here's an example of how to use the session manager in the API server:

.. code-block:: python

   from session_manager import SessionManager

   # Initialize the session manager
   session_manager = SessionManager()

   async def handle_request(request):
       """Handle a request to launch a browser session."""
       # Get the client IP address
       client_ip = request.remote
       
       # Create a new session
       session_id = session_manager.create_session(client_ip)
       
       # Redirect to the VNC interface with the session ID
       return web.HTTPFound(f'/vnc?session={session_id}')

   async def handle_vnc_request(request):
       """Handle a request to access the VNC interface."""
       # Get the session ID from the request
       session_id = request.query.get('session')
       if not session_id:
           return web.Response(status=400, text='Session ID is required')
       
       # Get the client IP address
       client_ip = request.remote
       
       # Validate the session
       if not session_manager.validate_session(session_id, client_ip):
           return web.Response(status=403, text='Invalid session ID')
       
       # Serve the VNC interface
       return web.FileResponse('/path/to/vnc.html')

   # Set up a periodic task to clean up expired sessions
   async def cleanup_task():
       """Periodically clean up expired sessions."""
       while True:
           session_manager.cleanup_sessions()
           await asyncio.sleep(60)  # Clean up every minute

   # Start the cleanup task
   asyncio.create_task(cleanup_task())

Session Management in Docker Containers
----------------------------------

When using Docker containers, session management becomes more complex because each container has its own session manager. To ensure that sessions are consistent across containers, you can use a shared storage backend, such as Redis.

Here's an example of how to configure the Redis session manager in a Docker environment:

.. code-block:: python

   # In docker-compose.yml
   services:
     api:
       image: bahtbrowse-api
       environment:
         - REDIS_URL=redis://redis:6379/0
       depends_on:
         - redis
     redis:
       image: redis:6.0
       volumes:
         - redis-data:/data

   # In session_manager.py
   import os
   import redis
   import uuid
   import time
   import json

   class RedisSessionManager:
       """Manage browser sessions using Redis."""

       def __init__(self, redis_url=None):
           """Initialize the session manager."""
           redis_url = redis_url or os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
           self.redis = redis.from_url(redis_url)
           self.session_timeout = 1800  # 30 minutes

       # ... rest of the implementation ...

Conclusion
--------

The session management system is a critical component of the bahtBrowse system. It ensures that browser sessions are isolated, secure, and properly managed.

For more information on the bahtBrowse architecture, see the :doc:`../developer_guide/architecture` section.

.. _faq:

Frequently Asked Questions
=======================

This document provides answers to frequently asked questions about bahtBrowse.

General Questions
--------------

What is bahtBrowse?
~~~~~~~~~~~~~~~

bahtBrowse is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.

How does bahtBrowse work?
~~~~~~~~~~~~~~~~~~~~

bahtBrowse works by running browsers (Firefox or Chromium) in isolated Docker containers. When you access a website through bahtBrowse, the system:

1. Creates a new browser session in a Docker container
2. Loads the specified URL in the containerized browser
3. Provides access to the browser through a VNC interface
4. Isolates the browsing activity from your local system

This approach protects your local system from web-based threats, as any malicious code is contained within the isolated container.

What are the system requirements for bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To run bahtBrowse, you need:

- **Operating System**: Linux (Ubuntu 20.04 or later recommended)
- **Docker**: Docker 20.10 or later
- **Memory**: At least 4GB RAM
- **Storage**: At least 10GB free disk space
- **Network**: Stable internet connection

For Firefox plugin users:
- **Firefox**: Version 78 or later

For development:
- **Python**: 3.8 or later
- **Git**: 2.25 or later

Is bahtBrowse free to use?
~~~~~~~~~~~~~~~~~~~~~

Yes, bahtBrowse is open-source software and free to use. It is licensed under the MIT License, which allows you to use, modify, and distribute the software freely.

Installation and Setup
------------------

How do I install bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~

To install bahtBrowse, follow these steps:

1. Clone the repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Build and start the containers:

   .. code-block:: bash

      docker-compose up -d

For detailed installation instructions, see the :doc:`../getting_started/installation` guide.

How do I install the Firefox plugin?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To install the Firefox plugin:

1. Build the Firefox plugin:

   .. code-block:: bash

      cd firefox_plugin
      ./build.sh

2. Install the plugin in Firefox:

   a. Open Firefox
   b. Navigate to ``about:debugging``
   c. Click on "This Firefox"
   d. Click on "Load Temporary Add-on..."
   e. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

   Alternatively, you can install the plugin permanently:

   a. Open Firefox
   b. Navigate to ``about:addons``
   c. Click the gear icon and select "Install Add-on From File..."
   d. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

For more information on the Firefox plugin, see the :doc:`../user_guide/firefox_plugin` guide.

How do I configure bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~

You can configure bahtBrowse through environment variables or a configuration file. To create a basic configuration:

1. Create a ``.env`` file in the project root directory:

   .. code-block:: bash

      # API Server Configuration
      API_PORT=8082
      API_HOST=0.0.0.0
      
      # VNC Configuration
      VNC_PORT=6080
      
      # Browser Configuration
      DEFAULT_BROWSER=firefox
      
      # Logging Configuration
      LOG_LEVEL=INFO

2. Restart the containers:

   .. code-block:: bash

      docker-compose down
      docker-compose up -d

For more advanced configuration options, see the :doc:`../admin_guide/configuration` section.

Can I run bahtBrowse on Windows or macOS?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

While bahtBrowse is primarily designed for Linux, you can run it on Windows or macOS using Docker Desktop. However, performance may be lower compared to running on Linux.

To run bahtBrowse on Windows or macOS:

1. Install Docker Desktop for your operating system
2. Follow the standard installation instructions
3. Note that some features may not work as expected on non-Linux systems

Usage
----

How do I access websites through bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

There are several ways to access websites through bahtBrowse:

1. **Firefox Plugin**: The recommended way to use bahtBrowse is through the Firefox plugin. Simply click on the plugin icon in the Firefox toolbar and enter the URL you want to visit.

2. **Direct API Access**: You can access websites through bahtBrowse by opening the following URL in your browser:

   .. code-block:: text

      http://localhost:8082/browse/?url=https://example.com

   Replace ``https://example.com`` with the URL you want to visit.

3. **Command Line**: You can use the command line to access websites through bahtBrowse:

   .. code-block:: bash

      curl -L "http://localhost:8082/browse/?url=https://example.com"

For more information on using bahtBrowse, see the :doc:`../user_guide/browsing` guide.

Can I use bahtBrowse with browsers other than Firefox?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, bahtBrowse supports both Firefox and Chromium browsers. You can specify which browser to use by adding the ``browser`` parameter to the URL:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&browser=firefox
   http://localhost:8082/browse/?url=https://example.com&browser=chromium

If no browser is specified, Firefox will be used by default.

How do I download files through bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

When you download a file in the containerized browser, it is saved in the container. You can access downloaded files through the downloads manager:

1. Navigate to ``http://localhost:8082/downloads``
2. You will see a list of downloaded files
3. Click on a file to download it to your local system

Note that downloaded files are stored in the container and will be lost when the container is restarted. To persist downloads, you can mount a volume to the container.

Can I use bahtBrowse for multiple users?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, bahtBrowse can be used by multiple users simultaneously. Each user will have their own isolated browser session.

However, the default configuration does not include user authentication or session management. For multi-user environments, you should implement:

1. **Authentication**: Add user authentication to restrict access to the bahtBrowse service
2. **Session Management**: Implement session management to associate sessions with users
3. **Resource Limits**: Set resource limits to prevent resource exhaustion

For more information on multi-user setups, see the :doc:`../admin_guide/configuration` section.

How do I use bahtBrowse with a proxy server?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To use bahtBrowse with a proxy server, you can configure the proxy settings in the browser:

1. For Firefox:

   Edit the ``user.js`` file in the Firefox profile directory to include proxy settings:

   .. code-block:: javascript

      user_pref("network.proxy.type", 1);
      user_pref("network.proxy.http", "proxy.example.com");
      user_pref("network.proxy.http_port", 8080);
      user_pref("network.proxy.ssl", "proxy.example.com");
      user_pref("network.proxy.ssl_port", 8080);
      user_pref("network.proxy.no_proxies_on", "localhost, 127.0.0.1");

2. For Chromium:

   Launch Chromium with proxy settings:

   .. code-block:: bash

      chromium-browser --proxy-server="http://proxy.example.com:8080"

Alternatively, you can configure the proxy at the Docker level by setting the ``HTTP_PROXY`` and ``HTTPS_PROXY`` environment variables in the ``docker-compose.yml`` file.

Security
------

Is bahtBrowse secure?
~~~~~~~~~~~~~~~~~

bahtBrowse is designed with security in mind. It isolates web browsing activities in Docker containers, protecting your local system from web-based threats.

However, no security solution is perfect, and there are some security considerations to keep in mind:

- **Container Isolation**: While Docker provides good isolation, it is not as strong as virtual machines. Container escape vulnerabilities could potentially expose your system.
- **Network Traffic**: Traffic between your browser and the bahtBrowse service is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.
- **Session Management**: Sessions are identified by unique IDs, but there is no authentication by default. Consider implementing authentication for multi-user environments.

For more information on security considerations, see the :doc:`../developer_guide/architecture` section.

Can bahtBrowse protect me from all web threats?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

No security solution can protect you from all threats. While bahtBrowse provides good protection against many web-based threats, it is not a substitute for good security practices.

To maximize your security:

1. **Keep Software Updated**: Keep bahtBrowse, Docker, and your operating system up to date
2. **Use Strong Passwords**: Use strong, unique passwords for all your accounts
3. **Enable Two-Factor Authentication**: Enable two-factor authentication where available
4. **Be Cautious**: Be cautious about the websites you visit and the files you download
5. **Use Multiple Security Layers**: Use bahtBrowse as part of a comprehensive security strategy

Can I use bahtBrowse for sensitive browsing?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

bahtBrowse can be used for sensitive browsing, but you should take additional precautions:

1. **Use HTTPS**: Configure bahtBrowse to use HTTPS to encrypt traffic between your browser and the bahtBrowse service
2. **Implement Authentication**: Add user authentication to restrict access to the bahtBrowse service
3. **Use a VPN**: Use a VPN to encrypt traffic between your device and the bahtBrowse service
4. **Secure the Host**: Ensure that the host running bahtBrowse is secure
5. **Regular Security Audits**: Conduct regular security audits of your bahtBrowse installation

For more information on securing bahtBrowse, see the :doc:`../admin_guide/security` section.

Performance
---------

Why is bahtBrowse slow?
~~~~~~~~~~~~~~~~~~~

There are several reasons why bahtBrowse might be slow:

1. **Resource Constraints**: The container might not have enough CPU or memory resources
2. **Network Latency**: High network latency can affect the VNC connection
3. **Browser Performance**: The browser in the container might be slow due to the website or browser configuration
4. **VNC Performance**: The VNC connection might be slow due to network conditions or VNC settings
5. **Container Overhead**: Running browsers in containers adds some overhead

To improve performance, see the :doc:`common_issues` section.

How can I improve bahtBrowse performance?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To improve bahtBrowse performance:

1. **Increase Container Resources**: Allocate more CPU and memory to the container
2. **Optimize VNC Settings**: Reduce VNC quality and resolution
3. **Use a Different Browser**: Try using Chromium instead of Firefox, or vice versa
4. **Use Compatibility Mode**: Try using compatibility mode for complex websites
5. **Optimize Browser Settings**: Disable browser extensions and animations
6. **Use a Faster Network**: Use a faster network connection
7. **Use a Native VNC Client**: Use a native VNC client instead of the web-based client

For more detailed performance optimization tips, see the :doc:`common_issues` section.

Can I run bahtBrowse on low-end hardware?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, bahtBrowse can run on low-end hardware, but performance may be limited. To run bahtBrowse on low-end hardware:

1. **Reduce Container Resources**: Allocate fewer resources to the container
2. **Use Chromium**: Chromium generally uses fewer resources than Firefox
3. **Disable JavaScript**: Disable JavaScript execution for better performance
4. **Reduce VNC Quality**: Reduce VNC quality and resolution
5. **Limit Concurrent Sessions**: Limit the number of concurrent browser sessions

For more information on running bahtBrowse on low-end hardware, see the :doc:`../admin_guide/configuration` section.

Troubleshooting
------------

Why can't I connect to bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If you can't connect to bahtBrowse, check the following:

1. **Service Running**: Ensure that the bahtBrowse service is running
2. **Port Mapping**: Ensure that the ports are correctly mapped
3. **Firewall**: Ensure that your firewall allows connections to the required ports
4. **Network Configuration**: Ensure that the server is accessible from your network
5. **Plugin Configuration**: Ensure that the Firefox plugin is configured correctly

For more detailed troubleshooting steps, see the :doc:`common_issues` section.

Why does the VNC connection fail?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If the VNC connection fails, check the following:

1. **VNC Server Running**: Ensure that the VNC server is running in the container
2. **Port Accessibility**: Ensure that the VNC port is accessible
3. **Nginx Configuration**: Ensure that the nginx configuration includes a location block for the VNC interface
4. **WebSocket Support**: Ensure that WebSocket support is enabled in nginx
5. **Browser Support**: Ensure that your browser supports WebSockets

For more detailed troubleshooting steps, see the :doc:`common_issues` section.

Why does the browser crash?
~~~~~~~~~~~~~~~~~~~~~~~

If the browser crashes, check the following:

1. **Browser Logs**: Check the browser logs for error messages
2. **System Resources**: Ensure that the container has enough CPU and memory resources
3. **Browser Compatibility**: Try a different browser (Firefox or Chromium)
4. **Website Compatibility**: Try compatibility mode for complex websites
5. **JavaScript Issues**: Try disabling JavaScript execution

For more detailed troubleshooting steps, see the :doc:`common_issues` section.

Development
---------

How do I contribute to bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To contribute to bahtBrowse:

1. **Fork the Repository**: Fork the bahtBrowse repository on GitHub
2. **Create a Feature Branch**: Create a branch for your feature or bug fix
3. **Make Changes**: Make your changes to the codebase
4. **Run Tests**: Run the test suite to ensure your changes don't break existing functionality
5. **Submit a Pull Request**: Submit a pull request to the main repository

For more information on contributing to bahtBrowse, see the :doc:`../developer_guide/contributing` section.

How do I report a bug?
~~~~~~~~~~~~~~~~~

To report a bug:

1. **Check Existing Issues**: Check if the bug has already been reported
2. **Create a New Issue**: If the bug hasn't been reported, create a new issue on GitHub
3. **Provide Details**: Provide as much detail as possible about the bug, including:
   - Steps to reproduce
   - Expected behavior
   - Actual behavior
   - Error messages
   - System information
4. **Include Logs**: Include relevant logs from the container
5. **Attach Screenshots**: If applicable, attach screenshots illustrating the issue

For more information on reporting bugs, see the :doc:`../developer_guide/contributing` section.

How do I request a feature?
~~~~~~~~~~~~~~~~~~~~~~

To request a feature:

1. **Check Existing Requests**: Check if the feature has already been requested
2. **Create a New Issue**: If the feature hasn't been requested, create a new issue on GitHub
3. **Provide Details**: Provide as much detail as possible about the feature, including:
   - Description of the feature
   - Use cases
   - Benefits
   - Potential implementation approaches
4. **Label as Enhancement**: Label the issue as an enhancement
5. **Discuss with the Community**: Discuss the feature with the community to gather feedback

For more information on requesting features, see the :doc:`../developer_guide/contributing` section.

How do I set up a development environment?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To set up a development environment for bahtBrowse:

1. **Clone the Repository**: Clone the bahtBrowse repository
2. **Create a Virtual Environment**: Create a Python virtual environment
3. **Install Dependencies**: Install the development dependencies
4. **Set Up Pre-commit Hooks**: Set up pre-commit hooks for code quality
5. **Build and Run Containers**: Build and run the containers in development mode

For detailed instructions on setting up a development environment, see the :doc:`../developer_guide/contributing` section.

Miscellaneous
----------

Can I use bahtBrowse with other browsers?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Currently, bahtBrowse supports Firefox and Chromium browsers. Support for other browsers may be added in the future.

If you need to use a different browser, you can:

1. **Modify the Docker Image**: Create a custom Docker image with the browser of your choice
2. **Update the API Server**: Modify the API server to support the new browser
3. **Contribute the Changes**: Contribute your changes back to the project

For more information on extending bahtBrowse, see the :doc:`../developer_guide/contributing` section.

Is bahtBrowse suitable for enterprise use?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Yes, bahtBrowse can be used in enterprise environments. However, for enterprise use, you should consider:

1. **Authentication**: Implement user authentication to restrict access to the bahtBrowse service
2. **Authorization**: Implement authorization to control what users can do
3. **Logging and Monitoring**: Set up logging and monitoring to track usage and detect issues
4. **High Availability**: Set up a high-availability configuration to ensure service continuity
5. **Scalability**: Implement load balancing to distribute browser sessions across multiple servers
6. **Security Hardening**: Apply additional security measures to protect the service

For more information on enterprise deployments, see the :doc:`../admin_guide/deployment` section.

How does bahtBrowse compare to commercial RBI solutions?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

bahtBrowse is an open-source alternative to commercial Remote Browser Isolation (RBI) solutions. Compared to commercial solutions, bahtBrowse:

**Advantages**:
- **Cost**: Free and open-source
- **Customizability**: Can be customized to meet specific requirements
- **Transparency**: Source code is available for review
- **Community Support**: Supported by the open-source community

**Disadvantages**:
- **Features**: May have fewer features than commercial solutions
- **Support**: No commercial support (though community support is available)
- **Integration**: May require more effort to integrate with existing systems
- **Documentation**: Documentation may be less comprehensive

For a detailed comparison with specific commercial solutions, see the :doc:`../admin_guide/comparison` section.

Can I use bahtBrowse on mobile devices?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

bahtBrowse is primarily designed for desktop use, but you can access it from mobile devices through a web browser. However, the user experience may not be optimized for mobile devices.

To use bahtBrowse on mobile devices:

1. **Access via Browser**: Open your mobile browser and navigate to the bahtBrowse URL
2. **VNC Interface**: Use the VNC interface to interact with the containerized browser
3. **Mobile Limitations**: Be aware of mobile-specific limitations, such as screen size and touch input

For a better mobile experience, consider using a mobile-optimized VNC client.

Getting Help
---------

Where can I get help with bahtBrowse?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

If you need help with bahtBrowse, you can:

1. **Check the Documentation**: Read the documentation for answers to common questions
2. **Search for Similar Issues**: Search for similar issues on GitHub
3. **Ask the Community**: Ask the community for help on GitHub Discussions or other forums
4. **Report an Issue**: If you've found a bug, report it on GitHub
5. **Contact the Maintainers**: Contact the maintainers for help with complex issues

For more information on getting help, see the :doc:`common_issues` section.

How do I stay updated on bahtBrowse developments?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

To stay updated on bahtBrowse developments:

1. **Watch the Repository**: Watch the bahtBrowse repository on GitHub
2. **Star the Repository**: Star the repository to show your interest
3. **Follow the Maintainers**: Follow the maintainers on GitHub
4. **Join the Community**: Join the bahtBrowse community on GitHub Discussions or other forums
5. **Subscribe to Releases**: Subscribe to release notifications on GitHub

For more information on staying updated, see the project's GitHub repository.

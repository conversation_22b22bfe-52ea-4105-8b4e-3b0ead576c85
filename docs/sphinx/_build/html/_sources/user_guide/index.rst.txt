.. _user_guide:

User Guide
=========

This user guide provides detailed information on how to use bahtBrowse effectively.

.. toctree::
   :maxdepth: 2

   firefox_plugin
   browsing

Introduction
-----------

bahtBrowse is designed to provide a secure browsing experience by isolating web browsing activities in Docker containers. This approach protects your main system from web-based threats while providing a seamless browsing experience.

This user guide will help you understand how to use bahtBrowse effectively, including:

- Using the Firefox plugin
- Browsing websites securely
- Managing browser sessions
- Troubleshooting common issues

Basic Concepts
-------------

Before diving into the details, it's important to understand some basic concepts:

Remote Browser Isolation (RBI)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Remote Browser Isolation (RBI) is a security approach that separates the browsing process from the user's device. In bahtBrowse, this is achieved by running browsers in Docker containers and providing access through a VNC interface.

Containerization
~~~~~~~~~~~~~~

bahtBrowse uses Docker containers to isolate browsers from the host system. Each browser session runs in its own container, providing a clean and isolated environment for browsing.

VNC Interface
~~~~~~~~~~~

The VNC (Virtual Network Computing) interface allows you to interact with the containerized browser. It provides a graphical interface to the browser running in the container.

Session Management
~~~~~~~~~~~~~~~

bahtBrowse manages browser sessions automatically. Each session is identified by a unique session ID and is isolated from other sessions.

Getting Started
-------------

If you haven't installed bahtBrowse yet, please refer to the :doc:`../getting_started/installation` guide.

For a quick introduction to using bahtBrowse, see the :doc:`../getting_started/quick_start` guide.

Using bahtBrowse
--------------

There are several ways to use bahtBrowse:

1. **Firefox Plugin**: The recommended way to use bahtBrowse is through the Firefox plugin, which provides a seamless integration with Firefox. See :doc:`firefox_plugin` for details.

2. **Direct API Access**: You can access websites through bahtBrowse by using the API directly. See :doc:`browsing` for details.

3. **Command Line**: Advanced users can use the command line to interact with bahtBrowse. See :doc:`browsing` for details.

Next Steps
---------

* :doc:`firefox_plugin`: Learn how to use the Firefox plugin
* :doc:`browsing`: Learn how to browse websites securely
* :doc:`../troubleshooting/index`: Troubleshoot common issues

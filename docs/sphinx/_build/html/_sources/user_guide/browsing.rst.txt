.. _browsing:

Secure Browsing with bahtBrowse
=============================

This guide explains how to use bahtBrowse for secure web browsing, including direct API access and command-line usage.

Introduction
-----------

bahtBrowse provides a secure browsing experience by isolating web browsing activities in Docker containers. This approach protects your main system from web-based threats while providing a seamless browsing experience.

While the :doc:`firefox_plugin` provides the most user-friendly way to access bahtBrowse, you can also use the API directly or through the command line for more advanced use cases.

Direct API Access
--------------

You can access websites through bahtBrowse by using the API directly.

Basic Usage
~~~~~~~~~

To access a website through bahtBrowse, simply open the following URL in your browser:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com

Replace ``https://example.com`` with the URL you want to visit.

This will:

1. Create a new browser session in a Docker container
2. Load the specified URL in the containerized browser
3. Redirect you to the VNC interface where you can interact with the browser

Advanced Parameters
~~~~~~~~~~~~~~~~

The API supports several parameters to customize your browsing experience:

+------------------------+------------------------------------------+---------------------------+
| Parameter              | Description                              | Example                   |
+========================+==========================================+===========================+
| ``url``                | The URL to visit (required)              | ``url=https://example.com``|
+------------------------+------------------------------------------+---------------------------+
| ``browser``            | The browser to use (firefox or chromium) | ``browser=firefox``      |
+------------------------+------------------------------------------+---------------------------+
| ``mode``               | Browsing mode (normal or compatibility)  | ``mode=normal``          |
+------------------------+------------------------------------------+---------------------------+
| ``disable_js_execution``| Disable JavaScript execution (true/false)| ``disable_js_execution=false``|
+------------------------+------------------------------------------+---------------------------+
| ``js_timeout``         | JavaScript execution timeout in ms       | ``js_timeout=10000``     |
+------------------------+------------------------------------------+---------------------------+
| ``locale``             | Browser locale                           | ``locale=en-US``         |
+------------------------+------------------------------------------+---------------------------+

Example with multiple parameters:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&browser=chromium&mode=compatibility

POST Requests
~~~~~~~~~~~

You can also use POST requests to access websites through bahtBrowse:

.. code-block:: bash

   curl -X POST -d "url=https://example.com&browser=firefox" http://localhost:8082/browse/

This is useful for scripts or applications that need to programmatically access websites through bahtBrowse.

Command Line Usage
---------------

You can use the command line to interact with bahtBrowse for more advanced use cases.

Accessing Websites
~~~~~~~~~~~~~~~

To access a website through bahtBrowse from the command line:

.. code-block:: bash

   curl -L "http://localhost:8082/browse/?url=https://example.com"

The ``-L`` flag tells curl to follow redirects, which is necessary because the API will redirect to the VNC interface.

Testing Connection
~~~~~~~~~~~~~~

To test the connection to the bahtBrowse service:

.. code-block:: bash

   curl http://localhost:8082/test-connection

This will return a JSON response indicating whether the service is available.

Logging Console Messages
~~~~~~~~~~~~~~~~~~~~

To log browser console messages:

.. code-block:: bash

   curl -X POST -H "Content-Type: application/json" -d '{"level": "info", "message": "Test message", "url": "https://example.com", "session_id": "12345678-1234-1234-1234-123456789abc"}' http://localhost:8082/log-console

This is useful for debugging or monitoring browser activity.

Managing Downloads
~~~~~~~~~~~~~~

To list downloaded files:

.. code-block:: bash

   curl http://localhost:8082/downloads-api/list

To delete a downloaded file:

.. code-block:: bash

   curl -X POST -H "Content-Type: application/json" -d '{"filename": "example.pdf"}' http://localhost:8082/downloads-api/delete

VNC Interface
-----------

When you access a website through bahtBrowse, you are redirected to the VNC interface where you can interact with the containerized browser.

.. figure:: ../_static/vnc_interface.png
   :alt: VNC Interface
   :align: center

   VNC Interface

The VNC interface provides a graphical interface to the browser running in the container. You can interact with it just like you would with a regular browser.

Navigation
~~~~~~~~

The VNC interface provides a toolbar at the top with navigation controls:

- **Back**: Go back to the previous page
- **Forward**: Go forward to the next page
- **Refresh**: Refresh the current page
- **Home**: Go to the home page
- **Address Bar**: Enter a URL to navigate to

Keyboard and Mouse
~~~~~~~~~~~~~~~

You can use your keyboard and mouse to interact with the browser in the VNC interface:

- **Mouse**: Click, double-click, right-click, scroll
- **Keyboard**: Type, use keyboard shortcuts

.. note::
   Some keyboard shortcuts may be intercepted by your local browser or operating system. In such cases, you can use the on-screen keyboard provided by the VNC interface.

Copy and Paste
~~~~~~~~~~~

To copy text from the containerized browser:

1. Select the text in the containerized browser
2. Right-click and select "Copy"

To paste text into the containerized browser:

1. Copy the text in your local environment
2. Right-click in the containerized browser and select "Paste"

.. note::
   Copy and paste functionality may be limited for security reasons. If you encounter issues, try using the clipboard provided by the VNC interface.

File Downloads
~~~~~~~~~~~

When you download a file in the containerized browser, it is saved in the container. You can access downloaded files through the downloads manager:

1. Navigate to ``http://localhost:8082/downloads``
2. You will see a list of downloaded files
3. Click on a file to download it to your local system

Browser Selection
--------------

bahtBrowse supports multiple browsers. You can specify which browser to use by adding the ``browser`` parameter to the URL:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&browser=firefox
   http://localhost:8082/browse/?url=https://example.com&browser=chromium

If no browser is specified, Firefox will be used by default.

Each browser has its own advantages and limitations:

Firefox
~~~~~~

- **Advantages**: Better compatibility with most websites, more features
- **Limitations**: Slightly higher resource usage

Chromium
~~~~~~~

- **Advantages**: Faster startup, lower resource usage
- **Limitations**: May have compatibility issues with some websites

Browsing Modes
-----------

bahtBrowse supports different browsing modes to optimize for different use cases:

Normal Mode
~~~~~~~~~

The default mode, suitable for most websites:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&mode=normal

Compatibility Mode
~~~~~~~~~~~~~~

Optimized for complex websites that may have compatibility issues:

.. code-block:: text

   http://localhost:8082/browse/?url=https://example.com&mode=compatibility

This mode enables additional compatibility features and may improve performance on complex websites.

Security Considerations
-------------------

While bahtBrowse provides a secure browsing experience by isolating browsers in containers, there are still some security considerations to keep in mind:

- **Network Traffic**: Traffic between your browser and the bahtBrowse service is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.
- **Session Isolation**: Each session is isolated, but sessions from the same user are not isolated from each other. Be cautious when browsing sensitive websites in multiple sessions.
- **Container Escape**: While unlikely, container escape vulnerabilities could potentially expose your system. Keep your Docker installation up to date.

For more information on security considerations, see the :doc:`../admin_guide/security` section.

Troubleshooting
-------------

If you encounter issues while browsing with bahtBrowse, see the :doc:`../troubleshooting/common_issues` section for solutions to common problems.

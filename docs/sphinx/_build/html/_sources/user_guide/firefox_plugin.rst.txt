.. _firefox_plugin:

Firefox Plugin
============

The bahtBrowse Firefox plugin provides a seamless way to access the bahtBrowse Remote Browser Isolation (RBI) service directly from your Firefox browser.

Installation
----------

There are two ways to install the bahtBrowse Firefox plugin:

Temporary Installation
~~~~~~~~~~~~~~~~~~~~

For testing or development purposes, you can install the plugin temporarily:

1. Open Firefox
2. Navigate to ``about:debugging``
3. Click on "This Firefox"
4. Click on "Load Temporary Add-on..."
5. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

.. note::
   Temporary installations will be removed when Firefox is closed.

Permanent Installation
~~~~~~~~~~~~~~~~~~~

For regular use, you can install the plugin permanently:

1. Open Firefox
2. Navigate to ``about:addons``
3. Click the gear icon and select "Install Add-on From File..."
4. Select the ``bahtbrowse_bouncer.xpi`` file from the ``firefox_plugin/build`` directory

Configuration
-----------

After installation, you need to configure the plugin to connect to your bahtBrowse service:

1. Click on the bahtBrowse icon in the Firefox toolbar
2. Click on "Options" (gear icon)
3. Enter the following settings:
   - **Host**: The hostname or IP address of your bahtBrowse service (default: ``localhost``)
   - **Port**: The port number of your bahtBrowse API server (default: ``8082``)
   - **Open in New Tab**: Whether to open bahtBrowse sessions in a new tab (default: ``true``)
   - **Whitelist/Blacklist**: Configure which sites should be opened in bahtBrowse

.. figure:: ../_static/firefox_plugin_options.png
   :alt: Firefox Plugin Options
   :align: center

   Firefox Plugin Options

Using the Plugin
-------------

The bahtBrowse Firefox plugin provides several ways to access websites through the bahtBrowse service:

Toolbar Button
~~~~~~~~~~~~

The simplest way to use the plugin is through the toolbar button:

1. Click on the bahtBrowse icon in the Firefox toolbar
2. Enter the URL you want to visit or click "Browse Current Page Securely"
3. The page will open in a new tab, running in the isolated browser container

.. figure:: ../_static/firefox_plugin_popup.png
   :alt: Firefox Plugin Popup
   :align: center

   Firefox Plugin Popup

Context Menu
~~~~~~~~~~

You can also access bahtBrowse through the context menu:

1. Right-click on a link or anywhere on a page
2. Select "Open in 10baht Browse" from the context menu
3. The link or page will open in a new tab, running in the isolated browser container

.. figure:: ../_static/firefox_plugin_context_menu.png
   :alt: Firefox Plugin Context Menu
   :align: center

   Firefox Plugin Context Menu

Keyboard Shortcuts
~~~~~~~~~~~~~~~

The plugin also provides keyboard shortcuts for quick access:

- **Ctrl+Shift+B**: Open the current page in bahtBrowse
- **Ctrl+Shift+L**: Open the bahtBrowse popup

You can customize these shortcuts in Firefox's keyboard shortcuts settings.

Connection Status
--------------

The plugin icon in the toolbar indicates the connection status to the bahtBrowse service:

- **Blue Icon**: The service is available
- **Gray Icon**: The service is not available

You can click on the icon to check the connection status and troubleshoot any issues.

Troubleshooting
-------------

If you encounter issues with the Firefox plugin, try the following:

1. **Check Connection**: Ensure that the bahtBrowse service is running and accessible
2. **Check Configuration**: Verify that the host and port settings are correct
3. **Check Browser Console**: Open the browser console (F12) and look for any error messages
4. **Reinstall Plugin**: Try reinstalling the plugin

For more troubleshooting tips, see the :doc:`../troubleshooting/common_issues` section.

Advanced Features
--------------

The bahtBrowse Firefox plugin provides several advanced features:

Browser Selection
~~~~~~~~~~~~~~

You can specify which browser to use for each session:

1. Click on the bahtBrowse icon in the Firefox toolbar
2. Click on "Options" (gear icon)
3. Select the default browser (Firefox or Chromium)
4. You can also override this setting for individual sessions by using the dropdown in the popup

Whitelist and Blacklist
~~~~~~~~~~~~~~~~~~~~

You can configure which sites should be opened in bahtBrowse:

1. Click on the bahtBrowse icon in the Firefox toolbar
2. Click on "Options" (gear icon)
3. Enable whitelist or blacklist
4. Add sites to the list (one per line)

With whitelist enabled, only sites in the list will be opened in bahtBrowse. With blacklist enabled, sites in the list will not be opened in bahtBrowse.

Logging
~~~~~~

The plugin logs various events to help with debugging:

1. Open the browser console (F12)
2. Filter for "bahtBrowse" to see plugin-related logs

You can also enable verbose logging in the plugin options for more detailed logs.

Development
---------

If you want to modify or contribute to the Firefox plugin, see the :doc:`../developer_guide/contributing` section.

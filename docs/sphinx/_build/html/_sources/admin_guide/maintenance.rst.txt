.. _maintenance:

Maintenance
=========

This guide provides information on how to maintain and update bahtBrowse.

Overview
-------

Maintaining bahtBrowse involves several tasks, including:

- **Updating**: Keeping bahtBrowse and its dependencies up to date
- **Monitoring**: Monitoring the health and performance of bahtBrowse
- **Backup**: Backing up bahtBrowse data
- **Troubleshooting**: Troubleshooting common issues
- **Testing**: Testing bahtBrowse to ensure it's working correctly

Updating
------

Keeping bahtBrowse up to date is important for security and performance.

Updating bahtBrowse
~~~~~~~~~~~~~~

To update bahtBrowse:

1. Pull the latest changes from the repository:

   .. code-block:: bash

      cd bahtbrowse
      git pull

2. Rebuild the Docker images:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml build

3. Restart the containers:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml up -d

Updating Dependencies
~~~~~~~~~~~~~~~~

bahtBrowse depends on several external components, including Docker, Docker Compose, and Python packages.

To update Docker:

.. code-block:: bash

   sudo apt-get update
   sudo apt-get upgrade docker-ce

To update Docker Compose:

.. code-block:: bash

   sudo curl -L "https://github.com/docker/compose/releases/download/latest/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose

To update Python packages:

.. code-block:: bash

   pip install -U -r requirements.txt

Monitoring
--------

Monitoring bahtBrowse is important for ensuring it's running correctly and efficiently.

Health Checks
~~~~~~~~~~

bahtBrowse includes health checks for its services. You can use the CLI tool to check the health of all services:

.. code-block:: bash

   ./bahtbrowse_cli.py --status

This will show the status and health of all services.

Logs
~~~

You can view the logs of bahtBrowse services using Docker Compose:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml logs

To view the logs of a specific service:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml logs <service_name>

For example, to view the logs of the API server:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml logs api

ELK Stack
~~~~~~~

bahtBrowse includes an ELK (Elasticsearch, Logstash, Kibana) stack for advanced monitoring and logging.

To access Kibana:

1. Open a web browser
2. Navigate to http://localhost:5601
3. Log in with the default credentials (username: elastic, password: changeme)

Kibana provides a web interface for searching, analyzing, and visualizing logs.

Backup
-----

Backing up bahtBrowse data is important for disaster recovery.

Redis Data
~~~~~~~

Redis data can be backed up using the Redis CLI:

.. code-block:: bash

   docker exec bahtbrowse_redis_1 redis-cli SAVE

This will create a snapshot of the Redis data.

To back up the Redis data to a file:

.. code-block:: bash

   docker exec bahtbrowse_redis_1 redis-cli SAVE
   docker cp bahtbrowse_redis_1:/data/dump.rdb /backup/redis-dump.rdb

Docker Volumes
~~~~~~~~~~~

Docker volumes can be backed up using the docker cp command:

.. code-block:: bash

   docker cp bahtbrowse_redis_1:/data /backup/redis-data

Configuration Files
~~~~~~~~~~~~~~~

Configuration files can be backed up using standard file system tools:

.. code-block:: bash

   cp docker-compose.fixed.yml /backup/docker-compose.fixed.yml

Troubleshooting
------------

This section covers common issues and their solutions.

Container Issues
~~~~~~~~~~~~

If a container is not starting:

1. Check the container logs:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml logs <service_name>

2. Check the container status:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml ps <service_name>

3. Try restarting the container:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml restart <service_name>

Port Conflicts
~~~~~~~~~~

If a port conflict occurs, bahtBrowse will automatically detect it and find an alternative port. You can view the port assignments using the CLI tool:

.. code-block:: bash

   ./bahtbrowse_cli.py --status

In the status table, services using non-default ports will have their port numbers highlighted in yellow.

If you need to manually configure ports, you can edit the ``docker-compose.fixed.yml`` file and change the port mappings.

Network Issues
~~~~~~~~~~

If services cannot communicate with each other:

1. Check that all services are running:

   .. code-block:: bash

      docker-compose -f docker-compose.fixed.yml ps

2. Check the network configuration:

   .. code-block:: bash

      docker network ls
      docker network inspect bahtbrowse_default

3. Try restarting the Docker daemon:

   .. code-block:: bash

      sudo systemctl restart docker

Testing
-----

Testing bahtBrowse is important for ensuring it's working correctly.

Container Build Tests
~~~~~~~~~~~~~~~~

bahtBrowse includes a container build test suite that verifies all Docker containers can be built successfully. To run the container build tests:

.. code-block:: bash

   ./tests/run_container_build_tests.sh

This will attempt to build each Dockerfile in the project and report any failures.

Port Conflict Resolution Tests
~~~~~~~~~~~~~~~~~~~~~~~~~

bahtBrowse includes a port conflict resolution test suite that verifies the port conflict detection and resolution system works correctly. To run the port conflict resolution tests:

.. code-block:: bash

   python3 tests/test_port_conflict_resolution.py

This will test the port conflict detection and resolution system.

Container Detection Tests
~~~~~~~~~~~~~~~~~~~

bahtBrowse includes a container detection test suite that verifies the container detection functionality in the CLI tool works correctly. To run the container detection tests:

.. code-block:: bash

   python3 tests/test_container_detection.py

This will test the container detection functionality.

Enhanced Tests
~~~~~~~~~~

bahtBrowse includes an enhanced test suite that runs all the above tests. To run the enhanced tests:

.. code-block:: bash

   ./tests/run_enhanced_tests.sh

This will run the container build tests, port conflict resolution tests, and container detection tests, and save the results to a file.

Conclusion
--------

This guide covered the various maintenance tasks for bahtBrowse. By following these guidelines, you can keep bahtBrowse running smoothly and efficiently.

For more information on deploying bahtBrowse, see the :doc:`deployment` section.

For more information on configuring bahtBrowse, see the :doc:`configuration` section.

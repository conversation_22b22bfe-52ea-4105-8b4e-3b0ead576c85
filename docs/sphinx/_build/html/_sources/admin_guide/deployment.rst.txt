.. _deployment:

Deployment
========

This guide provides information on how to deploy bahtBrowse in various environments.

Overview
-------

bahtBrowse can be deployed in various environments, from a single server to a distributed cluster. This guide covers the following deployment topics:

- **Prerequisites**: What you need before deploying bahtBrowse
- **Single Server Deployment**: How to deploy bahtBrowse on a single server
- **Distributed Deployment**: How to deploy bahtBrowse across multiple servers
- **Cloud Deployment**: How to deploy bahtBrowse in the cloud
- **Kubernetes Deployment**: How to deploy bahtBrowse on Kubernetes

Prerequisites
-----------

Before deploying bahtBrowse, you need to have the following prerequisites:

- **Docker**: bahtBrowse uses Docker to run its containers
- **Docker Compose**: bahtBrowse uses Docker Compose to manage its containers
- **Python 3.9+**: bahtBrowse requires Python 3.9 or later
- **Git**: To clone the bahtBrowse repository

Single Server Deployment
--------------------

The simplest way to deploy bahtBrowse is on a single server.

Clone the Repository
~~~~~~~~~~~~~~~

First, clone the bahtBrowse repository:

.. code-block:: bash

   git clone https://github.com/10Baht/bahtbrowse.git
   cd bahtbrowse

Build and Start the Containers
~~~~~~~~~~~~~~~~~~~~~~~~~

Use Docker Compose to build and start the containers:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml up -d

This will build the Docker images and start the containers in the background.

Verify the Deployment
~~~~~~~~~~~~~~~~

Verify that the containers are running:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml ps

You should see all the containers running.

Distributed Deployment
------------------

For larger deployments, you can distribute bahtBrowse across multiple servers.

Architecture
~~~~~~~~~

A distributed deployment of bahtBrowse typically consists of the following components:

- **API Servers**: Handle API requests and manage browser sessions
- **Redis Servers**: Store session data and coordinate between components
- **Worker Servers**: Manage browser containers and handle background tasks
- **Browser Servers**: Run the actual browsers (Firefox, Chromium, etc.)
- **Monitoring Servers**: Run monitoring tools like Elasticsearch, Kibana, and Logstash

Each component can be deployed on a separate server or group of servers.

Example Deployment
~~~~~~~~~~~~~~

Here's an example of a distributed deployment:

- **Server 1**: API Servers and Redis
- **Server 2**: Worker Servers
- **Server 3**: Browser Servers
- **Server 4**: Monitoring Servers

Cloud Deployment
-------------

bahtBrowse can be deployed in the cloud using various cloud providers.

AWS Deployment
~~~~~~~~~~

To deploy bahtBrowse on AWS:

1. Create EC2 instances for each component
2. Install Docker and Docker Compose on each instance
3. Clone the bahtBrowse repository on each instance
4. Configure the Docker Compose file for the distributed deployment
5. Start the containers on each instance

Google Cloud Deployment
~~~~~~~~~~~~~~~~~~

To deploy bahtBrowse on Google Cloud:

1. Create Compute Engine instances for each component
2. Install Docker and Docker Compose on each instance
3. Clone the bahtBrowse repository on each instance
4. Configure the Docker Compose file for the distributed deployment
5. Start the containers on each instance

Azure Deployment
~~~~~~~~~~~~

To deploy bahtBrowse on Azure:

1. Create Virtual Machines for each component
2. Install Docker and Docker Compose on each VM
3. Clone the bahtBrowse repository on each VM
4. Configure the Docker Compose file for the distributed deployment
5. Start the containers on each VM

Kubernetes Deployment
-----------------

bahtBrowse can be deployed on Kubernetes for better scalability and management.

Prerequisites
~~~~~~~~~~

Before deploying bahtBrowse on Kubernetes, you need to have the following:

- **Kubernetes Cluster**: A running Kubernetes cluster
- **kubectl**: The Kubernetes command-line tool
- **Helm**: The Kubernetes package manager (optional)

Deployment Steps
~~~~~~~~~~~~

To deploy bahtBrowse on Kubernetes:

1. Create Kubernetes manifests for each component
2. Apply the manifests to the Kubernetes cluster
3. Verify that the pods are running
4. Configure ingress for external access

Example Manifests
~~~~~~~~~~~~~

Here's an example of a Kubernetes manifest for the API server:

.. code-block:: yaml

   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: api
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: api
     template:
       metadata:
         labels:
           app: api
       spec:
         containers:
         - name: api
           image: bahtbrowse/api:latest
           ports:
           - containerPort: 8082
           env:
           - name: REDIS_HOST
             value: redis
   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: api
   spec:
     selector:
       app: api
     ports:
     - port: 8082
       targetPort: 8082
     type: ClusterIP

Conclusion
--------

This guide covered the various deployment options for bahtBrowse. By following these guidelines, you can deploy bahtBrowse in the environment that best suits your needs.

For more information on configuring bahtBrowse, see the :doc:`configuration` section.

For more information on maintaining bahtBrowse, see the :doc:`maintenance` section.

.. _configuration:

Configuration
===========

This guide provides information on how to configure bahtBrowse for your specific needs.

Overview
-------

bahtBrowse can be configured in various ways to suit your specific requirements. This guide covers the following configuration topics:

- **Docker Configuration**: How to configure Docker containers
- **Network Configuration**: How to configure network settings
- **Port Configuration**: How to configure port settings and handle port conflicts
- **Browser Configuration**: How to configure browser settings
- **Security Configuration**: How to configure security settings
- **Performance Configuration**: How to configure performance settings

Docker Configuration
-----------------

Docker containers are the core of bahtBrowse. Each browser instance runs in its own Docker container, providing isolation and security.

Docker Compose
~~~~~~~~~~~

bahtBrowse uses Docker Compose to manage its containers. The Docker Compose configuration is defined in the ``docker-compose.fixed.yml`` file.

Example:

.. code-block:: yaml

   version: '3'
   services:
     api:
       build:
         context: .
         dockerfile: docker/Dockerfile.api
       ports:
         - "8082:8082"
       volumes:
         - ./files:/app/files
       environment:
         - REDIS_HOST=redis
       depends_on:
         - redis
     redis:
       image: redis:alpine
       ports:
         - "6379:6379"
     worker:
       build:
         context: .
         dockerfile: docker/Dockerfile.worker
       volumes:
         - /var/run/docker.sock:/var/run/docker.sock
       environment:
         - REDIS_HOST=redis
       depends_on:
         - redis

Docker Images
~~~~~~~~~~

bahtBrowse uses several Docker images:

- **API Server**: Handles API requests and manages browser sessions
- **Redis**: Stores session data and coordinates between components
- **Worker**: Manages browser containers and handles background tasks
- **Browser Containers**: Run the actual browsers (Firefox, Chromium, etc.)

Network Configuration
-----------------

bahtBrowse uses Docker's networking capabilities to connect its containers.

Network Mode
~~~~~~~~~

By default, bahtBrowse uses the ``bridge`` network mode, which creates a virtual network for the containers.

Example:

.. code-block:: yaml

   services:
     api:
       networks:
         - bahtbrowse-network
     redis:
       networks:
         - bahtbrowse-network

   networks:
     bahtbrowse-network:
       driver: bridge

Port Configuration
--------------

bahtBrowse uses several ports for its services. These ports can be configured to avoid conflicts with other services.

Default Ports
~~~~~~~~~~

The default ports used by bahtBrowse are:

- **API Server**: 8082
- **Redis**: 6379
- **Flower**: 5555
- **Nginx**: 80
- **Admin Dashboard**: 9001
- **Elasticsearch**: 9200
- **Kibana**: 5601
- **Logstash**: 9600

Port Conflict Detection and Resolution
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

bahtBrowse includes a port conflict detection and resolution system that automatically detects when default ports are already in use and finds alternative ports. This makes the system more resilient when running in environments where the default ports might already be in use by other applications.

The port conflict detection and resolution system is implemented in the ``bahtbrowse_cli.py`` file and provides the following features:

- **Automatic detection of port conflicts**: The system checks if the default ports are already in use.
- **Dynamic port allocation**: If a default port is in use, the system finds an available port in the appropriate range.
- **Visual indicators**: Services using non-default ports are highlighted in the status table.
- **Configurable port ranges**: Port ranges can be configured for different service categories.
- **Seamless communication**: Services can still communicate with each other when using alternative ports.

Port Ranges
~~~~~~~~

The port conflict resolution system defines the following port ranges for different service categories:

- **Web Services**: 8000-8100
- **Monitoring Services**: 5500-5700
- **Infrastructure Services**: 6300-6400
- **Core Services**: 8000-8100

These ranges can be configured in the ``PORT_RANGES`` dictionary in the ``bahtbrowse_cli.py`` file.

Example:

.. code-block:: python

   PORT_RANGES = {
       "web": (8000, 8100),           # Web services
       "monitoring": (5500, 5700),    # Monitoring services
       "infrastructure": (6300, 6400), # Infrastructure services
       "core": (8000, 8100)           # Core services
   }

Using the Port Conflict Resolution System
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The port conflict resolution system is integrated into the bahtBrowse CLI tool. When you start services using the CLI, it automatically checks if the default ports are available and finds alternative ports if needed.

To start all services with automatic port conflict resolution:

.. code-block:: bash

   ./bahtbrowse_cli.py --start-all

To start a specific service:

.. code-block:: bash

   ./bahtbrowse_cli.py --start <service_name>

To view the status of all services, including their port assignments:

.. code-block:: bash

   ./bahtbrowse_cli.py --status

In the status table, services using non-default ports will have their port numbers highlighted in yellow.

Browser Configuration
-----------------

bahtBrowse supports multiple browsers, each with its own configuration options.

Browser Types
~~~~~~~~~~

bahtBrowse supports the following browser types:

- **Firefox**: Mozilla Firefox browser
- **Chromium**: Chromium browser
- **Tor Browser**: Tor Browser for anonymous browsing

Browser Pool Configuration
~~~~~~~~~~~~~~~~~~~~~

bahtBrowse includes a browser pool configuration feature that allows you to configure how many browsers of each type should be kept ready. This feature is implemented in the ``docker_queue/api/queue_config_routes.py`` file.

The browser pool configuration includes the following settings:

- **Min Pool Size**: The minimum number of browser containers to keep ready
- **Max Pool Size**: The maximum number of browser containers to allow
- **Idle Timeout**: How long to keep idle containers before removing them
- **Per-Browser Type Settings**: Configure settings for each browser type

Example:

.. code-block:: json

   {
     "global": {
       "min_pool_size": 2,
       "max_pool_size": 10,
       "idle_timeout": 300
     },
     "firefox": {
       "min_pool_size": 1,
       "max_pool_size": 5,
       "idle_timeout": 300
     },
     "chromium": {
       "min_pool_size": 1,
       "max_pool_size": 5,
       "idle_timeout": 300
     },
     "tor-browser": {
       "min_pool_size": 0,
       "max_pool_size": 2,
       "idle_timeout": 600
     }
   }

Security Configuration
------------------

bahtBrowse includes several security features that can be configured.

Container Isolation
~~~~~~~~~~~~~~~

Browser containers are isolated from each other and from the host system. This isolation can be configured in the Docker Compose file.

Example:

.. code-block:: yaml

   services:
     firefox:
       security_opt:
         - seccomp=seccomp-firefox.json
       cap_drop:
         - ALL
       cap_add:
         - NET_ADMIN
         - SYS_ADMIN

Network Security
~~~~~~~~~~~~

bahtBrowse includes network security features that can be configured.

Example:

.. code-block:: yaml

   services:
     nginx:
       networks:
         - frontend
     api:
       networks:
         - frontend
         - backend
     redis:
       networks:
         - backend

   networks:
     frontend:
       driver: bridge
     backend:
       driver: bridge
       internal: true

Performance Configuration
--------------------

bahtBrowse includes several performance features that can be configured.

Resource Limits
~~~~~~~~~~~

Docker containers can have resource limits configured in the Docker Compose file.

Example:

.. code-block:: yaml

   services:
     firefox:
       mem_limit: 2g
       cpus: 2
     chromium:
       mem_limit: 2g
       cpus: 2
     tor-browser:
       mem_limit: 1g
       cpus: 1

Scaling
~~~~~

bahtBrowse can be scaled to handle more users by increasing the number of worker containers.

Example:

.. code-block:: bash

   docker-compose -f docker-compose.fixed.yml up -d --scale worker=3

Conclusion
--------

This guide covered the various configuration options available in bahtBrowse. By configuring these options, you can customize bahtBrowse to suit your specific needs.

For more information on deploying bahtBrowse, see the :doc:`deployment` section.

For more information on maintaining bahtBrowse, see the :doc:`maintenance` section.

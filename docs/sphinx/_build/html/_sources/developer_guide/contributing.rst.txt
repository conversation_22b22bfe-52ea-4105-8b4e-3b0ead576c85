.. _contributing:

Contributing
===========

This document describes how to contribute to bahtBrowse.

Getting Started
-------------

To contribute to bahtBrowse, follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

Development Environment
---------------------

To set up a development environment for bahtBrowse, follow these steps:

1. Clone the repository:

   .. code-block:: bash

      git clone https://github.com/10Baht/bahtbrowse.git
      cd bahtbrowse

2. Create a Python virtual environment:

   .. code-block:: bash

      python3 -m venv venv
      source venv/bin/activate

3. Install the development dependencies:

   .. code-block:: bash

      pip install -e ".[dev]"

4. Set up pre-commit hooks:

   .. code-block:: bash

      pre-commit install

5. Build and run the containers:

   .. code-block:: bash

      docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

Coding Standards
--------------

baht<PERSON>rowse follows the PEP 8 style guide for Python code. All code should be formatted according to PEP 8 and should pass the pre-commit hooks.

Testing
------

All code should be tested. For more information on testing, see :doc:`testing`.

Documentation
-----------

All code should be documented. Documentation should be written in reStructuredText format and should be compatible with Sphinx.

Pull Requests
-----------

Pull requests should be submitted to the main repository. All pull requests should include:

1. A clear description of the changes
2. Tests for the changes
3. Documentation for the changes
4. A reference to any related issues

Code Review
---------

All code will be reviewed before it is merged. Code review will focus on:

1. Code quality
2. Test coverage
3. Documentation
4. Adherence to coding standards

Releases
-------

Releases are managed by the core team. To request a release, open an issue in the main repository.

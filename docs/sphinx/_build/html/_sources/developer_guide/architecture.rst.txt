.. _architecture:

Architecture
===========

This document describes the architecture of bahtBrowse.

Overview
-------

bahtBrowse consists of several components that work together to provide a secure browsing experience:

1. **Docker Containers**: Isolated environments running Firefox or Chromium browsers
2. **API Server**: Manages browser sessions and handles requests
3. **VNC Interface**: Provides remote access to containerized browsers
4. **Firefox Plugin**: Enables seamless integration with Firefox
5. **Nginx Configuration**: Handles proxying and serving static files
6. **Health Dashboard**: Monitors system health and service status

Component Architecture
-------------------

.. code-block:: text

   User Browser ---> Firefox Plugin (Requests Page)
   Firefox Plugin ---> bahtBrowse API (Redirects to)
   bahtBrowse API ---> Browser Container (Creates)
   bahtBrowse API ---> VNC Interface (Serves)
   VNC Interface ---> Browser Container (Connects to)
   Health Dashboard ---> bahtBrowse API (Monitors)
   Health Dashboard ---> Browser Container (Monitors)

Docker Containers
--------------

The Docker containers provide isolated environments for running Firefox or Chromium browsers. Each container is isolated from the host system and from other containers, providing a secure browsing environment.

API Server
--------

The API server manages browser sessions and handles requests from users. It creates and manages Docker containers, provides access to the VNC interface, and handles user authentication.

VNC Interface
----------

The VNC interface provides remote access to containerized browsers. It allows users to interact with browsers running in Docker containers as if they were running locally.

Firefox Plugin
-----------

The Firefox plugin enables seamless integration with Firefox. It allows users to redirect URLs to bahtBrowse with a single click, providing a seamless browsing experience.

Nginx Configuration
---------------

The Nginx configuration handles proxying and serving static files. It routes requests to the appropriate components and handles SSL termination.

Health Dashboard
-------------

The Health Dashboard monitors system health and service status. It provides real-time monitoring of all bahtBrowse components, including service status, response times, and system resource usage.

For more information on the Health Dashboard, see :doc:`health_dashboard`.

.. _getting_started:

Getting Started with baht<PERSON>rowse
===============================

This section will help you get started with bahtBrowse, a secure Remote Browser Isolation (RBI) system.

.. toctree::
   :maxdepth: 2

   installation
   quick_start

Introduction
-----------

bahtBrowse is a containerized browser isolation solution that provides secure web browsing by running browsers in isolated Docker containers. This approach protects your main system from web-based threats while providing a seamless browsing experience.

System Requirements
------------------

Before installing bahtBrowse, ensure your system meets the following requirements:

* **Operating System**: Linux (Ubuntu 20.04 or later recommended)
* **Docker**: Docker 20.10 or later
* **Memory**: At least 4GB RAM
* **Storage**: At least 10GB free disk space
* **Network**: Stable internet connection

For Firefox plugin users:
* **Firefox**: Version 78 or later

For development:
* **Python**: 3.8 or later
* **Git**: 2.25 or later

Quick Overview
-------------

bahtBrowse consists of several components:

1. **Docker Containers**: Isolated environments running Firefox or Chromium browsers
2. **API Server**: Manages browser sessions and handles requests
3. **VNC Interface**: Provides remote access to containerized browsers
4. **Firefox Plugin**: Enables seamless integration with Firefox
5. **Nginx Configuration**: Handles proxying and serving static files

Next Steps
----------

* :doc:`installation`: Detailed installation instructions
* :doc:`quick_start`: Quick start guide for first-time users

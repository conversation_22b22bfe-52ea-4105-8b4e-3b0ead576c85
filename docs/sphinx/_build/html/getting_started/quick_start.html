

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quick Start Guide &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f70ab148" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=f281be69"></script>
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      <script src="../_static/mermaid-init.js?v=fae81138"></script>
      <script src="../_static/dark-mode.js?v=a1be6e65"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="User Guide" href="../user_guide/index.html" />
    <link rel="prev" title="Installation Guide" href="installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html">
            
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Documentation</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Getting Started with bahtBrowse</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="installation.html">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="installation.html#docker-compose-installation">Docker Compose Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="installation.html#development-setup">Development Setup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="installation.html#firefox-plugin-installation">Firefox Plugin Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="installation.html#configuration">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="installation.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="installation.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Quick Start Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="#starting-bahtbrowse">Starting bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="#accessing-websites">Accessing Websites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#using-the-api-directly">Using the API Directly</a></li>
<li class="toctree-l4"><a class="reference internal" href="#using-the-firefox-plugin">Using the Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#understanding-the-workflow">Understanding the Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="#browser-selection">Browser Selection</a></li>
<li class="toctree-l3"><a class="reference internal" href="#session-management">Session Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#system-requirements">System Requirements</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#quick-overview">Quick Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../user_guide/index.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/firefox_plugin.html">Firefox Plugin</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#installation">Installation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#temporary-installation">Temporary Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#permanent-installation">Permanent Installation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#using-the-plugin">Using the Plugin</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#toolbar-button">Toolbar Button</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#context-menu">Context Menu</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#keyboard-shortcuts">Keyboard Shortcuts</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#connection-status">Connection Status</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#advanced-features">Advanced Features</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#whitelist-and-blacklist">Whitelist and Blacklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/firefox_plugin.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/firefox_plugin.html#development">Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/browsing.html">Secure Browsing with bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#introduction">Introduction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#direct-api-access">Direct API Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#advanced-parameters">Advanced Parameters</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#post-requests">POST Requests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#command-line-usage">Command Line Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#accessing-websites">Accessing Websites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#testing-connection">Testing Connection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#logging-console-messages">Logging Console Messages</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#managing-downloads">Managing Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#vnc-interface">VNC Interface</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#navigation">Navigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#keyboard-and-mouse">Keyboard and Mouse</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#copy-and-paste">Copy and Paste</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#file-downloads">File Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browser-selection">Browser Selection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#firefox">Firefox</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#chromium">Chromium</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#browsing-modes">Browsing Modes</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#normal-mode">Normal Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../user_guide/browsing.html#compatibility-mode">Compatibility Mode</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/browsing.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#basic-concepts">Basic Concepts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#remote-browser-isolation-rbi">Remote Browser Isolation (RBI)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#containerization">Containerization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user_guide/index.html#session-management">Session Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#using-bahtbrowse">Using bahtBrowse</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/index.html">Troubleshooting</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/common_issues.html">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#connection-issues">Connection Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#unable-to-connect-to-bahtbrowse-service">Unable to Connect to bahtBrowse Service</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#vnc-connection-fails">VNC Connection Fails</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-issues">Browser Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-fails-to-launch">Browser Fails to Launch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-crashes">Browser Crashes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#website-compatibility-issues">Website Compatibility Issues</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#firefox-plugin-issues">Firefox Plugin Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-connecting">Plugin Not Connecting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-working-correctly">Plugin Not Working Correctly</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-browser-performance">Slow Browser Performance</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-vnc-performance">Slow VNC Performance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#security-issues">Security Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#session-hijacking">Session Hijacking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#container-escape">Container Escape</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#logging-and-debugging">Logging and Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#viewing-logs">Viewing Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#enabling-debug-mode">Enabling Debug Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#debugging-firefox-plugin">Debugging Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#general-questions">General Questions</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-is-bahtbrowse">What is bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-work">How does bahtBrowse work?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-are-the-system-requirements-for-bahtbrowse">What are the system requirements for bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-free-to-use">Is bahtBrowse free to use?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-bahtbrowse">How do I install bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-the-firefox-plugin">How do I install the Firefox plugin?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-configure-bahtbrowse">How do I configure bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-windows-or-macos">Can I run bahtBrowse on Windows or macOS?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#usage">Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-access-websites-through-bahtbrowse">How do I access websites through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-browsers-other-than-firefox">Can I use bahtBrowse with browsers other than Firefox?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-download-files-through-bahtbrowse">How do I download files through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-multiple-users">Can I use bahtBrowse for multiple users?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-use-bahtbrowse-with-a-proxy-server">How do I use bahtBrowse with a proxy server?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#security">Security</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-secure">Is bahtBrowse secure?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-bahtbrowse-protect-me-from-all-web-threats">Can bahtBrowse protect me from all web threats?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-sensitive-browsing">Can I use bahtBrowse for sensitive browsing?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#performance">Performance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-is-bahtbrowse-slow">Why is bahtBrowse slow?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-can-i-improve-bahtbrowse-performance">How can I improve bahtBrowse performance?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-low-end-hardware">Can I run bahtBrowse on low-end hardware?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-can-t-i-connect-to-bahtbrowse">Why can’t I connect to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-vnc-connection-fail">Why does the VNC connection fail?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-browser-crash">Why does the browser crash?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#development">Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-contribute-to-bahtbrowse">How do I contribute to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-report-a-bug">How do I report a bug?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-request-a-feature">How do I request a feature?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-set-up-a-development-environment">How do I set up a development environment?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-other-browsers">Can I use bahtBrowse with other browsers?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-suitable-for-enterprise-use">Is bahtBrowse suitable for enterprise use?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-compare-to-commercial-rbi-solutions">How does bahtBrowse compare to commercial RBI solutions?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-on-mobile-devices">Can I use bahtBrowse on mobile devices?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#getting-help">Getting Help</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#where-can-i-get-help-with-bahtbrowse">Where can I get help with bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-stay-updated-on-bahtbrowse-developments">How do I stay updated on bahtBrowse developments?</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#connection-issues">Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#errors">404 Errors</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#firefox-plugin-issues">Firefox Plugin Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#logging-and-debugging">Logging and Debugging</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#frequently-asked-questions">Frequently Asked Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Administrator Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/index.html">Administrator Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#deployment">Deployment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#maintenance">Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#clone-the-repository">Clone the Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#build-and-start-the-containers">Build and Start the Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#verify-the-deployment">Verify the Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#architecture">Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-deployment">Example Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#aws-deployment">AWS Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#google-cloud-deployment">Google Cloud Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#azure-deployment">Azure Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#id2">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#deployment-steps">Deployment Steps</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-manifests">Example Manifests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-images">Docker Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-mode">Network Mode</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#default-ports">Default Ports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-conflict-detection-and-resolution">Port Conflict Detection and Resolution</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-ranges">Port Ranges</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#using-the-port-conflict-resolution-system">Using the Port Conflict Resolution System</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-types">Browser Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-pool-configuration">Browser Pool Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#container-isolation">Container Isolation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-security">Network Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#resource-limits">Resource Limits</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#scaling">Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-bahtbrowse">Updating bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-dependencies">Updating Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#elk-stack">ELK Stack</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#redis-data">Redis Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#docker-volumes">Docker Volumes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#configuration-files">Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-issues">Container Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflicts">Port Conflicts</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#network-issues">Network Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-build-tests">Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-detection-tests">Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#enhanced-tests">Enhanced Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/api_server.html">API Server</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#launch-browser-session">Launch Browser Session</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#request">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#response">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#test-connection">Test Connection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id4">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id5">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#log-console-messages">Log Console Messages</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id6">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id7">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#downloads-manager">Downloads Manager</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id8">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id9">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#list-downloaded-files">List Downloaded Files</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id10">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id11">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#delete-downloaded-file">Delete Downloaded File</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id12">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id13">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#session-management">Session Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#browser-launching">Browser Launching</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-client-examples">API Client Examples</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#python">Python</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#javascript">JavaScript</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#curl">cURL</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/session_management.html">Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-manager">Session Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-creation">Session Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validation">Session Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-cleanup">Session Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validator">Session Validator</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-storage">Session Storage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-security">Session Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-api">Session Management API</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#example-usage">Example Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-in-docker-containers">Session Management in Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#launch-a-browser-session">Launch a Browser Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#test-the-connection">Test the Connection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#log-console-messages">Log Console Messages</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-client-libraries">API Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Getting Started with bahtBrowse</a></li>
      <li class="breadcrumb-item active">Quick Start Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/getting_started/quick_start.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="quick-start-guide">
<span id="quick-start"></span><h1>Quick Start Guide<a class="headerlink" href="#quick-start-guide" title="Link to this heading"></a></h1>
<p>This guide will help you quickly get started with bahtBrowse after installation.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>bahtBrowse provides a secure way to browse the web by isolating browsers in Docker containers. This quick start guide will show you how to:</p>
<ol class="arabic simple">
<li><p>Start the bahtBrowse service</p></li>
<li><p>Access websites through bahtBrowse</p></li>
<li><p>Use the Firefox plugin</p></li>
<li><p>Understand the basic workflow</p></li>
</ol>
</section>
<section id="starting-bahtbrowse">
<h2>Starting bahtBrowse<a class="headerlink" href="#starting-bahtbrowse" title="Link to this heading"></a></h2>
<p>If you’ve followed the <a class="reference internal" href="installation.html"><span class="doc">Installation Guide</span></a> guide, you should have bahtBrowse installed on your system.</p>
<ol class="arabic">
<li><p>Start the bahtBrowse service:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</li>
<li><p>Verify that the service is running:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>ps
</pre></div>
</div>
<p>You should see the containers running with status “Up”.</p>
</li>
<li><p>Test the API server:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8082/test-connection
</pre></div>
</div>
<p>You should receive a JSON response indicating that the service is available.</p>
</li>
</ol>
</section>
<section id="accessing-websites">
<h2>Accessing Websites<a class="headerlink" href="#accessing-websites" title="Link to this heading"></a></h2>
<p>There are several ways to access websites through bahtBrowse:</p>
<section id="using-the-api-directly">
<h3>Using the API Directly<a class="headerlink" href="#using-the-api-directly" title="Link to this heading"></a></h3>
<ol class="arabic">
<li><p>Open a web browser and navigate to:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url=https://example.com
</pre></div>
</div>
<p>Replace <code class="docutils literal notranslate"><span class="pre">https://example.com</span></code> with the URL you want to visit.</p>
</li>
<li><p>You will be redirected to the VNC interface where you can interact with the isolated browser.</p></li>
</ol>
</section>
<section id="using-the-firefox-plugin">
<h3>Using the Firefox Plugin<a class="headerlink" href="#using-the-firefox-plugin" title="Link to this heading"></a></h3>
<p>If you’ve installed the Firefox plugin as described in the <a class="reference internal" href="installation.html"><span class="doc">Installation Guide</span></a> guide, you can use it to access websites through bahtBrowse.</p>
<ol class="arabic simple">
<li><p>Open Firefox</p></li>
<li><p>Click on the bahtBrowse icon in the toolbar</p></li>
<li><p>Enter the URL you want to visit or click “Browse Current Page Securely”</p></li>
<li><p>The page will open in a new tab, running in the isolated browser container</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The Firefox plugin provides the most seamless experience for using bahtBrowse.</p>
</div>
</section>
</section>
<section id="understanding-the-workflow">
<h2>Understanding the Workflow<a class="headerlink" href="#understanding-the-workflow" title="Link to this heading"></a></h2>
<p>Here’s what happens when you access a website through bahtBrowse:</p>
<ol class="arabic simple">
<li><p>Your request is sent to the bahtBrowse API server</p></li>
<li><p>The API server creates a new browser session in a Docker container</p></li>
<li><p>The URL is loaded in the containerized browser</p></li>
<li><p>You are redirected to a VNC interface where you can interact with the browser</p></li>
<li><p>All browsing activity is isolated in the container, protecting your main system</p></li>
</ol>
<figure class="align-center" id="id1">
<img alt="bahtBrowse Workflow" src="_static/workflow_diagram.png" />
<figcaption>
<p><span class="caption-text">bahtBrowse Workflow Diagram</span><a class="headerlink" href="#id1" title="Link to this image"></a></p>
</figcaption>
</figure>
</section>
<section id="browser-selection">
<h2>Browser Selection<a class="headerlink" href="#browser-selection" title="Link to this heading"></a></h2>
<p>bahtBrowse supports multiple browsers. You can specify which browser to use by adding the <code class="docutils literal notranslate"><span class="pre">browser</span></code> parameter to the URL:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:8082/browse/?url=https://example.com&amp;browser=firefox
http://localhost:8082/browse/?url=https://example.com&amp;browser=chromium
</pre></div>
</div>
<p>If no browser is specified, Firefox will be used by default.</p>
</section>
<section id="session-management">
<h2>Session Management<a class="headerlink" href="#session-management" title="Link to this heading"></a></h2>
<p>Each browsing session is isolated in its own container. Sessions are automatically created when you access a URL through bahtBrowse.</p>
<p>Sessions are identified by a unique session ID, which is included in the URL when you are redirected to the VNC interface.</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>http://localhost:6081/vnc1/run?session=12345678-1234-1234-1234-123456789abc
</pre></div>
</div>
<p>Sessions are automatically cleaned up after a period of inactivity (default: 30 minutes).</p>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>Now that you’ve got the basics of bahtBrowse, you can:</p>
<ul class="simple">
<li><p><a class="reference internal" href="../user_guide/index.html"><span class="doc">User Guide</span></a>: Learn more about using bahtBrowse</p></li>
<li><p><a class="reference internal" href="../admin_guide/configuration.html"><span class="doc">Configuration</span></a>: Configure bahtBrowse for your environment</p></li>
<li><p><a class="reference internal" href="../developer_guide/index.html"><span class="doc">Developer Guide</span></a>: Understand the architecture and contribute to bahtBrowse</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="installation.html" class="btn btn-neutral float-left" title="Installation Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../user_guide/index.html" class="btn btn-neutral float-right" title="User Guide" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, bahtBrowse Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
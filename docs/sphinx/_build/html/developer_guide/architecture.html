<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Architecture &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Health Dashboard" href="health_dashboard.html" />
    <link rel="prev" title="Developer Guide" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">Developer Guide</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#component-architecture">Component Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="#docker-containers">Docker Containers</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-server">API Server</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vnc-interface">VNC Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l2"><a class="reference internal" href="#nginx-configuration">Nginx Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#health-dashboard">Health Dashboard</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Developer Guide</a></li>
      <li class="breadcrumb-item active">Architecture</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/developer_guide/architecture.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="architecture">
<span id="id1"></span><h1>Architecture<a class="headerlink" href="#architecture" title="Link to this heading"></a></h1>
<p>This document describes the architecture of bahtBrowse.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>bahtBrowse consists of several components that work together to provide a secure browsing experience:</p>
<ol class="arabic simple">
<li><p><strong>Docker Containers</strong>: Isolated environments running Firefox or Chromium browsers</p></li>
<li><p><strong>API Server</strong>: Manages browser sessions and handles requests</p></li>
<li><p><strong>VNC Interface</strong>: Provides remote access to containerized browsers</p></li>
<li><p><strong>Firefox Plugin</strong>: Enables seamless integration with Firefox</p></li>
<li><p><strong>Nginx Configuration</strong>: Handles proxying and serving static files</p></li>
<li><p><strong>Health Dashboard</strong>: Monitors system health and service status</p></li>
</ol>
</section>
<section id="component-architecture">
<h2>Component Architecture<a class="headerlink" href="#component-architecture" title="Link to this heading"></a></h2>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>User Browser ---&gt; Firefox Plugin (Requests Page)
Firefox Plugin ---&gt; bahtBrowse API (Redirects to)
bahtBrowse API ---&gt; Browser Container (Creates)
bahtBrowse API ---&gt; VNC Interface (Serves)
VNC Interface ---&gt; Browser Container (Connects to)
Health Dashboard ---&gt; bahtBrowse API (Monitors)
Health Dashboard ---&gt; Browser Container (Monitors)
</pre></div>
</div>
</section>
<section id="docker-containers">
<h2>Docker Containers<a class="headerlink" href="#docker-containers" title="Link to this heading"></a></h2>
<p>The Docker containers provide isolated environments for running Firefox or Chromium browsers. Each container is isolated from the host system and from other containers, providing a secure browsing environment.</p>
</section>
<section id="api-server">
<h2>API Server<a class="headerlink" href="#api-server" title="Link to this heading"></a></h2>
<p>The API server manages browser sessions and handles requests from users. It creates and manages Docker containers, provides access to the VNC interface, and handles user authentication.</p>
</section>
<section id="vnc-interface">
<h2>VNC Interface<a class="headerlink" href="#vnc-interface" title="Link to this heading"></a></h2>
<p>The VNC interface provides remote access to containerized browsers. It allows users to interact with browsers running in Docker containers as if they were running locally.</p>
</section>
<section id="firefox-plugin">
<h2>Firefox Plugin<a class="headerlink" href="#firefox-plugin" title="Link to this heading"></a></h2>
<p>The Firefox plugin enables seamless integration with Firefox. It allows users to redirect URLs to bahtBrowse with a single click, providing a seamless browsing experience.</p>
</section>
<section id="nginx-configuration">
<h2>Nginx Configuration<a class="headerlink" href="#nginx-configuration" title="Link to this heading"></a></h2>
<p>The Nginx configuration handles proxying and serving static files. It routes requests to the appropriate components and handles SSL termination.</p>
</section>
<section id="health-dashboard">
<h2>Health Dashboard<a class="headerlink" href="#health-dashboard" title="Link to this heading"></a></h2>
<p>The Health Dashboard monitors system health and service status. It provides real-time monitoring of all bahtBrowse components, including service status, response times, and system resource usage.</p>
<p>For more information on the Health Dashboard, see <a class="reference internal" href="health_dashboard.html"><span class="doc">Health Dashboard</span></a>.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Developer Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="health_dashboard.html" class="btn btn-neutral float-right" title="Health Dashboard" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
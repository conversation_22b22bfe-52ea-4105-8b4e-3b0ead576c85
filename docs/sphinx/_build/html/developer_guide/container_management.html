<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Container Management &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Health Dashboard" href="health_dashboard.html" />
    <link rel="prev" title="Architecture" href="architecture.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Developer Guide</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Container Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="#container-lifecycle">Container Lifecycle</a></li>
<li class="toctree-l3"><a class="reference internal" href="#inactivity-timeout">Inactivity Timeout</a></li>
<li class="toctree-l3"><a class="reference internal" href="#activity-tracking">Activity Tracking</a></li>
<li class="toctree-l3"><a class="reference internal" href="#container-cleanup">Container Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#benefits">Benefits</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#health-dashboard">Health Dashboard</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Developer Guide</a></li>
      <li class="breadcrumb-item active">Container Management</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/developer_guide/container_management.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="container-management">
<span id="id1"></span><h1>Container Management<a class="headerlink" href="#container-management" title="Link to this heading"></a></h1>
<p>This document provides detailed information about the container management system in bahtBrowse, including container creation, recycling, and inactivity timeout.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The container management system is responsible for creating, managing, and recycling Docker containers for browser sessions. Each container provides an isolated environment for a browser session, ensuring security and privacy.</p>
<p>The container management system ensures that:</p>
<ul class="simple">
<li><p>Containers are created on demand</p></li>
<li><p>Containers are isolated from each other</p></li>
<li><p>Containers are recycled after use</p></li>
<li><p>Containers are terminated after a period of inactivity</p></li>
<li><p>Resources are efficiently managed</p></li>
</ul>
</section>
<section id="container-lifecycle">
<h2>Container Lifecycle<a class="headerlink" href="#container-lifecycle" title="Link to this heading"></a></h2>
<p>Containers in bahtBrowse go through the following lifecycle:</p>
<ol class="arabic simple">
<li><p><strong>Creation</strong>: A new container is created when a user requests a browser session</p></li>
<li><p><strong>Initialization</strong>: The container is initialized with the necessary software and configuration</p></li>
<li><p><strong>Assignment</strong>: The container is assigned to a user</p></li>
<li><p><strong>Usage</strong>: The user interacts with the browser in the container</p></li>
<li><p><strong>Inactivity</strong>: If the user stops interacting with the container, it becomes idle</p></li>
<li><p><strong>Timeout</strong>: After a period of inactivity, the container is terminated</p></li>
<li><p><strong>Recycling</strong>: The container is cleaned and returned to the pool or terminated</p></li>
</ol>
</section>
<section id="inactivity-timeout">
<h2>Inactivity Timeout<a class="headerlink" href="#inactivity-timeout" title="Link to this heading"></a></h2>
<p>One of the key features of the container management system is the inactivity timeout. This feature automatically terminates containers that have been idle for a specified period, helping to conserve resources and enhance security.</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Configuration for inactivity timeout</span>
<span class="n">CONTAINER_IDLE_TIMEOUT</span> <span class="o">=</span> <span class="mi">180</span>  <span class="c1"># 3 minutes in seconds</span>
</pre></div>
</div>
<p>The inactivity timeout works as follows:</p>
<ol class="arabic simple">
<li><p>The system tracks the last activity time for each container</p></li>
<li><p>A periodic task checks for containers that have been idle for longer than the timeout period</p></li>
<li><p>If a container has been idle for too long, it is terminated and recycled</p></li>
<li><p>A new container is queued to replace the terminated one</p></li>
</ol>
<p>This ensures that resources are not wasted on idle containers and that containers are promptly recycled when they are no longer needed.</p>
</section>
<section id="activity-tracking">
<h2>Activity Tracking<a class="headerlink" href="#activity-tracking" title="Link to this heading"></a></h2>
<p>To implement the inactivity timeout, the system needs to track user activity in each container. This is done through the following mechanisms:</p>
<ol class="arabic simple">
<li><p><strong>API Endpoint</strong>: The frontend periodically calls an API endpoint to update the last activity timestamp</p></li>
<li><p><strong>Event Listeners</strong>: The frontend listens for user events (mouse movements, keyboard input, etc.) to detect activity</p></li>
<li><p><strong>Redis Storage</strong>: The last activity timestamp is stored in Redis for each container</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update container activity</span>
<span class="k">def</span> <span class="nf">update_container_activity</span><span class="p">(</span><span class="n">container_id</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Update the last activity timestamp for a container.&quot;&quot;&quot;</span>
    <span class="n">redis_client</span><span class="o">.</span><span class="n">hset</span><span class="p">(</span>
        <span class="sa">f</span><span class="s2">&quot;container:</span><span class="si">{</span><span class="n">container_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="s2">&quot;last_activity&quot;</span><span class="p">,</span>
        <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
    <span class="p">)</span>
</pre></div>
</div>
<p>The frontend includes a JavaScript activity tracker that monitors user activity and periodically sends updates to the server:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">class</span><span class="w"> </span><span class="nx">ActivityTracker</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">userId</span><span class="p">,</span><span class="w"> </span><span class="nx">pingInterval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">60000</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">userId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">userId</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">pingInterval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">pingInterval</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">lastActivityTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">activityEvents</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;mousemove&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;mousedown&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;keypress&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;scroll&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;touchstart&#39;</span><span class="p">];</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nx">start</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Add event listeners for user activity</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">activityEvents</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="nx">eventType</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nb">window</span><span class="p">.</span><span class="nx">addEventListener</span><span class="p">(</span><span class="nx">eventType</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleUserActivity</span><span class="p">.</span><span class="nx">bind</span><span class="p">(</span><span class="k">this</span><span class="p">),</span><span class="w"> </span><span class="kc">true</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>

<span class="w">        </span><span class="c1">// Start the ping timer</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">timerId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">setInterval</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">pingServer</span><span class="p">(),</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">pingInterval</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nx">handleUserActivity</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">lastActivityTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">async</span><span class="w"> </span><span class="nx">pingServer</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Only ping if there has been activity since the last ping</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">now</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">idleTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">now</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">lastActivityTime</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// If user has been idle for more than 2 minutes, don&#39;t ping</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">idleTime</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">120000</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Send activity update to server</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="nx">axios</span><span class="p">.</span><span class="nx">post</span><span class="p">(</span><span class="s1">&#39;/api/containers/activity&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">user_id</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">userId</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="container-cleanup">
<h2>Container Cleanup<a class="headerlink" href="#container-cleanup" title="Link to this heading"></a></h2>
<p>When a container is terminated due to inactivity, the system performs the following cleanup tasks:</p>
<ol class="arabic simple">
<li><p><strong>Logging</strong>: The termination event is logged for auditing purposes</p></li>
<li><p><strong>Container Recycling</strong>: The container is recycled to clean up any user data</p></li>
<li><p><strong>Resource Release</strong>: Resources allocated to the container are released</p></li>
<li><p><strong>Pool Management</strong>: The container pool is updated to reflect the change</p></li>
</ol>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Cleanup idle containers</span>
<span class="k">def</span> <span class="nf">cleanup_idle_containers</span><span class="p">(</span><span class="n">idle_timeout</span><span class="o">=</span><span class="n">CONTAINER_IDLE_TIMEOUT</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Clean up idle containers.&quot;&quot;&quot;</span>
    <span class="c1"># Get all assigned containers</span>
    <span class="n">assigned_containers</span> <span class="o">=</span> <span class="n">redis_client</span><span class="o">.</span><span class="n">smembers</span><span class="p">(</span><span class="s1">&#39;assigned_containers&#39;</span><span class="p">)</span>

    <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

    <span class="k">for</span> <span class="n">container_id</span> <span class="ow">in</span> <span class="n">assigned_containers</span><span class="p">:</span>
        <span class="n">container_details</span> <span class="o">=</span> <span class="n">get_container_details</span><span class="p">(</span><span class="n">container_id</span><span class="p">)</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="n">container_details</span><span class="p">:</span>
            <span class="k">continue</span>

        <span class="c1"># Check if container is idle</span>
        <span class="n">last_activity</span> <span class="o">=</span> <span class="nb">float</span><span class="p">(</span><span class="n">container_details</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;last_activity&#39;</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
        <span class="n">idle_time</span> <span class="o">=</span> <span class="n">current_time</span> <span class="o">-</span> <span class="n">last_activity</span>

        <span class="k">if</span> <span class="n">idle_time</span> <span class="o">&gt;</span> <span class="n">idle_timeout</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;INACTIVITY TIMEOUT: Container </span><span class="si">{</span><span class="n">container_id</span><span class="si">}</span><span class="s2"> idle for </span><span class="si">{</span><span class="n">idle_time</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2"> seconds&quot;</span><span class="p">)</span>
            <span class="n">recycle_container</span><span class="p">(</span><span class="n">container_id</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>The inactivity timeout can be configured through environment variables or configuration files:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># In docker-compose.yml</span>
<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">worker</span><span class="p">:</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CONTAINER_IDLE_TIMEOUT=180</span><span class="w">  </span><span class="c1"># 3 minutes in seconds</span>
</pre></div>
</div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># In config.py</span>
<span class="n">CONTAINER_IDLE_TIMEOUT</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">environ</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;CONTAINER_IDLE_TIMEOUT&#39;</span><span class="p">,</span> <span class="mi">180</span><span class="p">))</span>
</pre></div>
</div>
<p>The default timeout is 3 minutes (180 seconds), but this can be adjusted based on your specific requirements. A shorter timeout conserves resources but may interrupt users who take short breaks, while a longer timeout provides a better user experience but consumes more resources.</p>
</section>
<section id="benefits">
<h2>Benefits<a class="headerlink" href="#benefits" title="Link to this heading"></a></h2>
<p>The inactivity timeout feature provides several benefits:</p>
<ol class="arabic simple">
<li><p><strong>Resource Conservation</strong>: Automatically terminates idle containers to free up resources</p></li>
<li><p><strong>Enhanced Security</strong>: Reduces the window of exposure for containers</p></li>
<li><p><strong>Improved Scalability</strong>: Allows the system to handle more users with the same resources</p></li>
<li><p><strong>Automatic Cleanup</strong>: Ensures that containers are properly cleaned up after use</p></li>
<li><p><strong>Efficient Resource Allocation</strong>: Allocates resources to active users rather than idle sessions</p></li>
</ol>
<p>For more information on the bahtBrowse architecture, see <a class="reference internal" href="architecture.html"><span class="doc">Architecture</span></a>.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="architecture.html" class="btn btn-neutral float-left" title="Architecture" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="health_dashboard.html" class="btn btn-neutral float-right" title="Health Dashboard" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Contributing &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Testing" href="testing.html" />
    <link rel="prev" title="Health Dashboard" href="health_dashboard.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">Developer Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Contributing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="#coding-standards">Coding Standards</a></li>
<li class="toctree-l2"><a class="reference internal" href="#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="#documentation">Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#pull-requests">Pull Requests</a></li>
<li class="toctree-l2"><a class="reference internal" href="#code-review">Code Review</a></li>
<li class="toctree-l2"><a class="reference internal" href="#releases">Releases</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Developer Guide</a></li>
      <li class="breadcrumb-item active">Contributing</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/developer_guide/contributing.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="contributing">
<span id="id1"></span><h1>Contributing<a class="headerlink" href="#contributing" title="Link to this heading"></a></h1>
<p>This document describes how to contribute to bahtBrowse.</p>
<section id="getting-started">
<h2>Getting Started<a class="headerlink" href="#getting-started" title="Link to this heading"></a></h2>
<p>To contribute to bahtBrowse, follow these steps:</p>
<ol class="arabic simple">
<li><p>Fork the repository</p></li>
<li><p>Create a feature branch</p></li>
<li><p>Make your changes</p></li>
<li><p>Submit a pull request</p></li>
</ol>
</section>
<section id="development-environment">
<h2>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading"></a></h2>
<p>To set up a development environment for bahtBrowse, follow these steps:</p>
<ol class="arabic">
<li><p>Clone the repository:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/10Baht/bahtbrowse.git
<span class="nb">cd</span><span class="w"> </span>bahtbrowse
</pre></div>
</div>
</li>
<li><p>Create a Python virtual environment:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python3<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate
</pre></div>
</div>
</li>
<li><p>Install the development dependencies:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span>
</pre></div>
</div>
</li>
<li><p>Set up pre-commit hooks:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pre-commit<span class="w"> </span>install
</pre></div>
</div>
</li>
<li><p>Build and run the containers:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.yml<span class="w"> </span>-f<span class="w"> </span>docker-compose.dev.yml<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</li>
</ol>
</section>
<section id="coding-standards">
<h2>Coding Standards<a class="headerlink" href="#coding-standards" title="Link to this heading"></a></h2>
<p>bahtBrowse follows the PEP 8 style guide for Python code. All code should be formatted according to PEP 8 and should pass the pre-commit hooks.</p>
</section>
<section id="testing">
<h2>Testing<a class="headerlink" href="#testing" title="Link to this heading"></a></h2>
<p>All code should be tested. For more information on testing, see <a class="reference internal" href="testing.html"><span class="doc">Testing</span></a>.</p>
</section>
<section id="documentation">
<h2>Documentation<a class="headerlink" href="#documentation" title="Link to this heading"></a></h2>
<p>All code should be documented. Documentation should be written in reStructuredText format and should be compatible with Sphinx.</p>
</section>
<section id="pull-requests">
<h2>Pull Requests<a class="headerlink" href="#pull-requests" title="Link to this heading"></a></h2>
<p>Pull requests should be submitted to the main repository. All pull requests should include:</p>
<ol class="arabic simple">
<li><p>A clear description of the changes</p></li>
<li><p>Tests for the changes</p></li>
<li><p>Documentation for the changes</p></li>
<li><p>A reference to any related issues</p></li>
</ol>
</section>
<section id="code-review">
<h2>Code Review<a class="headerlink" href="#code-review" title="Link to this heading"></a></h2>
<p>All code will be reviewed before it is merged. Code review will focus on:</p>
<ol class="arabic simple">
<li><p>Code quality</p></li>
<li><p>Test coverage</p></li>
<li><p>Documentation</p></li>
<li><p>Adherence to coding standards</p></li>
</ol>
</section>
<section id="releases">
<h2>Releases<a class="headerlink" href="#releases" title="Link to this heading"></a></h2>
<p>Releases are managed by the core team. To request a release, open an issue in the main repository.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="health_dashboard.html" class="btn btn-neutral float-left" title="Health Dashboard" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="testing.html" class="btn btn-neutral float-right" title="Testing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
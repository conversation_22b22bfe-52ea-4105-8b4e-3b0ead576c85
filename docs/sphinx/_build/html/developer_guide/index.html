<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Developer Guide &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Architecture" href="architecture.html" />
    <link rel="prev" title="Welcome to bahtBrowse Documentation" href="../index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="#health-dashboard">Health Dashboard</a></li>
<li class="toctree-l2"><a class="reference internal" href="#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Developer Guide</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/developer_guide/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="developer-guide">
<span id="id1"></span><h1>Developer Guide<a class="headerlink" href="#developer-guide" title="Link to this heading"></a></h1>
<p>This developer guide provides detailed information for developers who want to understand, modify, or contribute to bahtBrowse.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#component-architecture">Component Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#api-server">API Server</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#nginx-configuration">Nginx Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="architecture.html#health-dashboard">Health Dashboard</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="health_dashboard.html">Health Dashboard</a><ul>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#key-features">Key Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#component-architecture">Component Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#health-api">Health API</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#frontend-interface">Frontend Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#response-time-tracking">Response Time Tracking</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#system-resource-monitoring">System Resource Monitoring</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#dark-mode-support">Dark Mode Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#auto-refresh">Auto-Refresh</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#api-documentation">API Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#usage">Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="health_dashboard.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#coding-standards">Coding Standards</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#documentation">Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#pull-requests">Pull Requests</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#code-review">Code Review</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#releases">Releases</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#unit-tests">Unit Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#integration-tests">Integration Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#performance-tests">Performance Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#test-coverage">Test Coverage</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#continuous-integration">Continuous Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#test-data">Test Data</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#mocking">Mocking</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#test-environment">Test Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#test-documentation">Test Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="testing.html#test-maintenance">Test Maintenance</a></li>
</ul>
</li>
</ul>
</div>
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Link to this heading"></a></h2>
<p>bahtBrowse is an open-source Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. This developer guide will help you understand the architecture, codebase, and development workflow of bahtBrowse.</p>
<p>Whether you want to contribute to the project, extend its functionality, or integrate it into your own systems, this guide will provide the information you need.</p>
</section>
<section id="system-architecture">
<h2>System Architecture<a class="headerlink" href="#system-architecture" title="Link to this heading"></a></h2>
<p>bahtBrowse consists of several components that work together to provide a secure browsing experience:</p>
<ol class="arabic simple">
<li><p><strong>Docker Containers</strong>: Isolated environments running Firefox or Chromium browsers</p></li>
<li><p><strong>API Server</strong>: Manages browser sessions and handles requests</p></li>
<li><p><strong>VNC Interface</strong>: Provides remote access to containerized browsers</p></li>
<li><p><strong>Firefox Plugin</strong>: Enables seamless integration with Firefox</p></li>
<li><p><strong>Nginx Configuration</strong>: Handles proxying and serving static files</p></li>
<li><p><strong>Health Dashboard</strong>: Monitors system health and service status</p></li>
</ol>
<p>For a detailed explanation of the architecture, see <a class="reference internal" href="architecture.html"><span class="doc">Architecture</span></a>.</p>
</section>
<section id="health-dashboard">
<h2>Health Dashboard<a class="headerlink" href="#health-dashboard" title="Link to this heading"></a></h2>
<p>The Health Dashboard provides real-time monitoring of the bahtBrowse system components, including service status, response times, and system resource usage.</p>
<p>Key features include:</p>
<ol class="arabic simple">
<li><p><strong>Service Status Monitoring</strong>: Real-time monitoring of all bahtBrowse services</p></li>
<li><p><strong>Response Time Tracking</strong>: Measurement and display of service response times</p></li>
<li><p><strong>Average Response Time Calculation</strong>: Calculation and display of average response times over time</p></li>
<li><p><strong>System Resource Monitoring</strong>: Real-time monitoring of CPU, memory, and disk usage</p></li>
</ol>
<p>For a detailed explanation of the Health Dashboard, see <a class="reference internal" href="health_dashboard.html"><span class="doc">Health Dashboard</span></a>.</p>
</section>
<section id="codebase-structure">
<h2>Codebase Structure<a class="headerlink" href="#codebase-structure" title="Link to this heading"></a></h2>
<p>The bahtBrowse codebase is organized as follows:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>bahtbrowse/
├── docker/                 # Docker-related files
│   ├── Dockerfile          # Main Dockerfile for Firefox container
│   ├── Dockerfile.api      # Dockerfile for API server
│   └── ...
├── files/                  # Files to be copied into containers
│   ├── app.py              # API server application
│   ├── session_manager.py  # Session management module
│   └── ...
├── firefox_plugin/         # Firefox plugin source code
│   ├── background.js       # Background script
│   ├── manifest.json       # Plugin manifest
│   └── ...
├── docs/                   # Documentation
│   ├── sphinx/             # Sphinx documentation
│   └── ...
├── tests/                  # Test suite
│   ├── test_docker.py      # Docker-related tests
│   ├── test_redirect.py    # Redirection tests
│   └── ...
├── docker-compose.yml      # Docker Compose configuration
├── health_api.py           # Health API server
├── static/                 # Static files for Health Dashboard
│   ├── css/                # CSS files
│   ├── js/                 # JavaScript files
│   └── icons/              # Icon files
└── ...
</pre></div>
</div>
</section>
<section id="development-environment">
<h2>Development Environment<a class="headerlink" href="#development-environment" title="Link to this heading"></a></h2>
<p>To set up a development environment for bahtBrowse, follow these steps:</p>
<ol class="arabic">
<li><p>Clone the repository:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>git<span class="w"> </span>clone<span class="w"> </span>https://github.com/10Baht/bahtbrowse.git
<span class="nb">cd</span><span class="w"> </span>bahtbrowse
</pre></div>
</div>
</li>
<li><p>Create a Python virtual environment:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>python3<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv
<span class="nb">source</span><span class="w"> </span>venv/bin/activate
</pre></div>
</div>
</li>
<li><p>Install the development dependencies:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pip<span class="w"> </span>install<span class="w"> </span>-e<span class="w"> </span><span class="s2">&quot;.[dev]&quot;</span>
</pre></div>
</div>
</li>
<li><p>Set up pre-commit hooks:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pre-commit<span class="w"> </span>install
</pre></div>
</div>
</li>
<li><p>Build and run the containers:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>docker-compose<span class="w"> </span>-f<span class="w"> </span>docker-compose.yml<span class="w"> </span>-f<span class="w"> </span>docker-compose.dev.yml<span class="w"> </span>up<span class="w"> </span>-d
</pre></div>
</div>
</li>
</ol>
<p>For more detailed information on setting up a development environment, see <a class="reference internal" href="contributing.html"><span class="doc">Contributing</span></a>.</p>
</section>
<section id="development-workflow">
<h2>Development Workflow<a class="headerlink" href="#development-workflow" title="Link to this heading"></a></h2>
<p>The typical development workflow for bahtBrowse is as follows:</p>
<ol class="arabic simple">
<li><p><strong>Create a Feature Branch</strong>: Create a new branch for your feature or bug fix.</p></li>
<li><p><strong>Make Changes</strong>: Make your changes to the codebase.</p></li>
<li><p><strong>Run Tests</strong>: Run the test suite to ensure your changes don’t break existing functionality.</p></li>
<li><p><strong>Submit a Pull Request</strong>: Submit a pull request to the main repository.</p></li>
</ol>
<p>For more detailed information on the development workflow, see <a class="reference internal" href="contributing.html"><span class="doc">Contributing</span></a>.</p>
</section>
<section id="testing">
<h2>Testing<a class="headerlink" href="#testing" title="Link to this heading"></a></h2>
<p>bahtBrowse has a comprehensive test suite that covers various aspects of the system. To run the tests, use the following command:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>pytest
</pre></div>
</div>
<p>For more detailed information on testing, see <a class="reference internal" href="testing.html"><span class="doc">Testing</span></a>.</p>
</section>
<section id="api-reference">
<h2>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h2>
<p>For detailed information on the bahtBrowse API, see the <a class="reference internal" href="../api_reference/index.html"><span class="doc">API Reference</span></a> section.</p>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="architecture.html"><span class="doc">Architecture</span></a>: Learn about the architecture of bahtBrowse</p></li>
<li><p><a class="reference internal" href="health_dashboard.html"><span class="doc">Health Dashboard</span></a>: Learn about the Health Dashboard</p></li>
<li><p><a class="reference internal" href="contributing.html"><span class="doc">Contributing</span></a>: Learn how to contribute to bahtBrowse</p></li>
<li><p><a class="reference internal" href="testing.html"><span class="doc">Testing</span></a>: Learn how to test bahtBrowse</p></li>
<li><p><a class="reference internal" href="../api_reference/index.html"><span class="doc">API Reference</span></a>: Explore the API reference</p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../index.html" class="btn btn-neutral float-left" title="Welcome to bahtBrowse Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="architecture.html" class="btn btn-neutral float-right" title="Architecture" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Health Dashboard &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Contributing" href="contributing.html" />
    <link rel="prev" title="Architecture" href="architecture.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="index.html">Developer Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="architecture.html">Architecture</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Health Dashboard</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#key-features">Key Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="#component-architecture">Component Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="#health-api">Health API</a></li>
<li class="toctree-l2"><a class="reference internal" href="#frontend-interface">Frontend Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="#response-time-tracking">Response Time Tracking</a></li>
<li class="toctree-l2"><a class="reference internal" href="#system-resource-monitoring">System Resource Monitoring</a></li>
<li class="toctree-l2"><a class="reference internal" href="#dark-mode-support">Dark Mode Support</a></li>
<li class="toctree-l2"><a class="reference internal" href="#auto-refresh">Auto-Refresh</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-documentation">API Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="#usage">Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="testing.html">Testing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">Developer Guide</a></li>
      <li class="breadcrumb-item active">Health Dashboard</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/developer_guide/health_dashboard.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="health-dashboard">
<span id="id1"></span><h1>Health Dashboard<a class="headerlink" href="#health-dashboard" title="Link to this heading"></a></h1>
<p>The Health Dashboard provides real-time monitoring of the bahtBrowse system components, including service status, response times, and system resource usage. This document describes the architecture, features, and implementation details of the Health Dashboard.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Health Dashboard is a web-based interface that displays the health status of all bahtBrowse services. It provides administrators and developers with a centralized view of the system’s operational status, making it easier to identify and troubleshoot issues.</p>
<figure class="align-center" id="id2">
<img alt="Health Dashboard" src="../_images/health_dashboard.png" />
<figcaption>
<p><span class="caption-text">bahtBrowse Health Dashboard</span><a class="headerlink" href="#id2" title="Link to this image"></a></p>
</figcaption>
</figure>
</section>
<section id="key-features">
<h2>Key Features<a class="headerlink" href="#key-features" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><strong>Service Status Monitoring</strong>: Real-time monitoring of all bahtBrowse services</p></li>
<li><p><strong>Response Time Tracking</strong>: Measurement and display of service response times</p></li>
<li><p><strong>Average Response Time Calculation</strong>: Calculation and display of average response times over time</p></li>
<li><p><strong>System Resource Monitoring</strong>: Real-time monitoring of CPU, memory, and disk usage</p></li>
<li><p><strong>Dark Mode Support</strong>: Toggle between light and dark modes for different viewing preferences</p></li>
<li><p><strong>Auto-Refresh</strong>: Automatic refresh of dashboard data at configurable intervals</p></li>
<li><p><strong>API Documentation</strong>: Integrated Swagger UI for API documentation</p></li>
</ul>
</section>
<section id="component-architecture">
<h2>Component Architecture<a class="headerlink" href="#component-architecture" title="Link to this heading"></a></h2>
<p>The Health Dashboard consists of several components:</p>
<ol class="arabic simple">
<li><p><strong>Health API</strong>: A FastAPI-based API that collects and serves health data</p></li>
<li><p><strong>Frontend Interface</strong>: A web-based interface that displays health data</p></li>
<li><p><strong>Data Collection Services</strong>: Services that collect health data from various components</p></li>
<li><p><strong>Storage</strong>: In-memory storage for health data</p></li>
</ol>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Health API ---&gt; bahtBrowse Services (Collects Data)
Health API ---&gt; Frontend Interface (Serves Data)
Frontend Interface ---&gt; Service Status (Displays)
Frontend Interface ---&gt; Response Times (Displays)
Frontend Interface ---&gt; System Resources (Displays)
User ---&gt; Frontend Interface (Views)
</pre></div>
</div>
</section>
<section id="health-api">
<h2>Health API<a class="headerlink" href="#health-api" title="Link to this heading"></a></h2>
<p>The Health API is responsible for collecting and serving health data. It is implemented using FastAPI and provides endpoints for retrieving health data.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>Health Data Collection</strong>: Collects health data from various components</p></li>
<li><p><strong>Health Data Serving</strong>: Serves health data to the frontend interface</p></li>
<li><p><strong>API Documentation</strong>: Provides Swagger UI for API documentation</p></li>
</ul>
<p><strong>API Endpoints</strong>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">/health</span></code>: Returns the health status of all services</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/api-docs</span></code>: Returns API documentation in JSON format</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/docs</span></code>: Serves the Health Dashboard and API documentation</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>The Health API is implemented in Python using the FastAPI framework. It collects health data from various components and stores it in memory.</p>
</section>
<section id="frontend-interface">
<h2>Frontend Interface<a class="headerlink" href="#frontend-interface" title="Link to this heading"></a></h2>
<p>The Frontend Interface is a web-based interface that displays health data. It is implemented using HTML, CSS, and JavaScript and is served by the Health API.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>Service Status Display</strong>: Displays the status of all services</p></li>
<li><p><strong>Response Time Display</strong>: Displays response times for all services</p></li>
<li><p><strong>Average Response Time Display</strong>: Displays average response times over time</p></li>
<li><p><strong>System Resource Display</strong>: Displays CPU, memory, and disk usage</p></li>
<li><p><strong>Dark Mode Support</strong>: Supports toggling between light and dark modes</p></li>
<li><p><strong>Auto-Refresh</strong>: Automatically refreshes data at configurable intervals</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>The Frontend Interface is implemented using HTML, CSS, and JavaScript. It uses fetch API to retrieve data from the Health API and updates the display accordingly.</p>
</section>
<section id="response-time-tracking">
<h2>Response Time Tracking<a class="headerlink" href="#response-time-tracking" title="Link to this heading"></a></h2>
<p>The Health Dashboard tracks response times for all services and calculates average response times over time. This provides valuable insights into service performance and helps identify performance issues.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>Response Time Measurement</strong>: Measures response times for all services</p></li>
<li><p><strong>Average Response Time Calculation</strong>: Calculates average response times over time</p></li>
<li><p><strong>Per-Service Response Time Tracking</strong>: Tracks response times for each service separately</p></li>
<li><p><strong>Visual Indicators</strong>: Provides visual indicators for response time trends</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>Response times are measured by the Health API when it collects health data from services. The response time is the time it takes for a service to respond to a health check request.</p>
<p>Average response times are calculated using a running average algorithm:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Calculate average response time</span>
<span class="kd">let</span><span class="w"> </span><span class="nx">totalResponseTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>
<span class="kd">let</span><span class="w"> </span><span class="nx">responseTimeCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>

<span class="c1">// Sum up all response times from this update</span>
<span class="nb">Object</span><span class="p">.</span><span class="nx">entries</span><span class="p">(</span><span class="nx">services</span><span class="p">).</span><span class="nx">forEach</span><span class="p">(([</span><span class="nx">serviceName</span><span class="p">,</span><span class="w"> </span><span class="nx">service</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">service</span><span class="p">.</span><span class="nx">response_time</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">totalResponseTime</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">service</span><span class="p">.</span><span class="nx">response_time</span><span class="p">;</span>
<span class="w">        </span><span class="nx">responseTimeCount</span><span class="o">++</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// Track per-service response times</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">])</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="nx">count</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span>
<span class="w">                </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span><span class="p">,</span>
<span class="w">                </span><span class="nx">average</span><span class="o">:</span><span class="w"> </span><span class="mf">0</span>
<span class="w">            </span><span class="p">};</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">].</span><span class="nx">total</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">service</span><span class="p">.</span><span class="nx">response_time</span><span class="p">;</span>
<span class="w">        </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">].</span><span class="nx">count</span><span class="o">++</span><span class="p">;</span>
<span class="w">        </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">].</span><span class="nx">average</span><span class="w"> </span><span class="o">=</span>
<span class="w">            </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">].</span><span class="nx">total</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">services</span><span class="p">[</span><span class="nx">serviceName</span><span class="p">].</span><span class="nx">count</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">});</span>

<span class="c1">// Update the running average</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">responseTimeCount</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">currentAvg</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">totalResponseTime</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">responseTimeCount</span><span class="p">;</span>
<span class="w">    </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">total</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">currentAvg</span><span class="p">;</span>
<span class="w">    </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">count</span><span class="o">++</span><span class="p">;</span>
<span class="w">    </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">average</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">total</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="nx">responseTimes</span><span class="p">.</span><span class="nx">count</span><span class="p">;</span>
<span class="p">}</span>
</pre></div>
</div>
<p>The average response time is displayed in the Health Dashboard for each service and for the overall system.</p>
</section>
<section id="system-resource-monitoring">
<h2>System Resource Monitoring<a class="headerlink" href="#system-resource-monitoring" title="Link to this heading"></a></h2>
<p>The Health Dashboard monitors system resources such as CPU, memory, and disk usage. This provides insights into system performance and helps identify resource constraints.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>CPU Usage Monitoring</strong>: Monitors CPU usage</p></li>
<li><p><strong>Memory Usage Monitoring</strong>: Monitors memory usage</p></li>
<li><p><strong>Disk Usage Monitoring</strong>: Monitors disk usage</p></li>
<li><p><strong>Visual Indicators</strong>: Provides visual indicators for resource usage</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>System resources are monitored by the Health API using the psutil library. The data is collected and served to the frontend interface, which displays it using progress bars and other visual indicators.</p>
</section>
<section id="dark-mode-support">
<h2>Dark Mode Support<a class="headerlink" href="#dark-mode-support" title="Link to this heading"></a></h2>
<p>The Health Dashboard supports toggling between light and dark modes for different viewing preferences.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>Light Mode</strong>: Default mode with light background and dark text</p></li>
<li><p><strong>Dark Mode</strong>: Alternative mode with dark background and light text</p></li>
<li><p><strong>System Preference Detection</strong>: Detects system preference for light or dark mode</p></li>
<li><p><strong>User Preference Storage</strong>: Stores user preference for light or dark mode</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>Dark mode is implemented using CSS variables and a toggle button. The user’s preference is stored in localStorage and applied when the dashboard loads.</p>
</section>
<section id="auto-refresh">
<h2>Auto-Refresh<a class="headerlink" href="#auto-refresh" title="Link to this heading"></a></h2>
<p>The Health Dashboard automatically refreshes data at configurable intervals to provide real-time monitoring.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>Configurable Refresh Interval</strong>: Allows configuring the refresh interval</p></li>
<li><p><strong>Manual Refresh</strong>: Allows manually refreshing the data</p></li>
<li><p><strong>Visual Indicators</strong>: Provides visual indicators for refresh status</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>Auto-refresh is implemented using JavaScript’s setInterval function. The refresh interval is configurable and defaults to 15 seconds.</p>
</section>
<section id="api-documentation">
<h2>API Documentation<a class="headerlink" href="#api-documentation" title="Link to this heading"></a></h2>
<p>The Health Dashboard includes integrated Swagger UI for API documentation.</p>
<p><strong>Key Features</strong>:</p>
<ul class="simple">
<li><p><strong>API Endpoint Documentation</strong>: Documents all API endpoints</p></li>
<li><p><strong>Request and Response Examples</strong>: Provides examples of requests and responses</p></li>
<li><p><strong>Interactive Testing</strong>: Allows testing API endpoints directly from the documentation</p></li>
</ul>
<p><strong>Implementation</strong>:</p>
<p>API documentation is implemented using Swagger UI and is served by the Health API. The documentation is generated from the API endpoints and their descriptions.</p>
</section>
<section id="usage">
<h2>Usage<a class="headerlink" href="#usage" title="Link to this heading"></a></h2>
<p>To access the Health Dashboard, navigate to the <cite>/docs</cite> endpoint of the Health API:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>http://localhost:8000/docs
</pre></div>
</div>
<p>The dashboard will display the health status of all services, response times, and system resource usage.</p>
<p>To toggle between light and dark modes, click the moon/sun icon in the top right corner of the dashboard.</p>
<p>To manually refresh the data, click the refresh button in the dashboard.</p>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>The Health Dashboard can be configured through environment variables:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">HEALTH_API_PORT</span></code>: Port for the Health API (default: 8000)</p></li>
</ul>
</section>
<section id="future-enhancements">
<h2>Future Enhancements<a class="headerlink" href="#future-enhancements" title="Link to this heading"></a></h2>
<p>The following enhancements are planned for future versions of the Health Dashboard:</p>
<p><strong>Historical Data</strong>:</p>
<p>Implement historical data storage and visualization to track performance over time.</p>
<p><strong>Alerting</strong>:</p>
<p>Add alerting capabilities to notify administrators of service issues.</p>
<p><strong>Customizable Dashboard</strong>:</p>
<p>Allow customizing the dashboard layout and displayed metrics.</p>
<p><strong>Service Dependencies</strong>:</p>
<p>Visualize service dependencies and their impact on system health.</p>
<p><strong>Performance Optimization</strong>:</p>
<p>Optimize data collection and display for improved performance.</p>
<p>For more information on contributing to these enhancements, see the <a class="reference internal" href="contributing.html"><span class="doc">Contributing</span></a> section.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="architecture.html" class="btn btn-neutral float-left" title="Architecture" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="contributing.html" class="btn btn-neutral float-right" title="Contributing" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
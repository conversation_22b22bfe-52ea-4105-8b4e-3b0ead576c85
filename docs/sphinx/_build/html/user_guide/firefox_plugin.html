

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Firefox Plugin &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=03e43079" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=f70ab148" />

  
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=8d563738"></script>
      <script src="../_static/doctools.js?v=9bcbadda"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=f281be69"></script>
      <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
      <script src="../_static/mermaid-init.js?v=fae81138"></script>
      <script src="../_static/dark-mode.js?v=a1be6e65"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Secure Browsing with bahtBrowse" href="browsing.html" />
    <link rel="prev" title="User Guide" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html">
            
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">User Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../getting_started/index.html">Getting Started with bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/installation.html">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#docker-compose-installation">Docker Compose Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#manual-installation">Manual Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#development-setup">Development Setup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#firefox-plugin-installation">Firefox Plugin Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#configuration">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#basic-configuration">Basic Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/installation.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/quick_start.html">Quick Start Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#starting-bahtbrowse">Starting bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#accessing-websites">Accessing Websites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-api-directly">Using the API Directly</a></li>
<li class="toctree-l4"><a class="reference internal" href="../getting_started/quick_start.html#using-the-firefox-plugin">Using the Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#understanding-the-workflow">Understanding the Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#browser-selection">Browser Selection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#session-management">Session Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../getting_started/quick_start.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#system-requirements">System Requirements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#quick-overview">Quick Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../getting_started/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="index.html">User Guide</a><ul class="current">
<li class="toctree-l2 current"><a class="current reference internal" href="#">Firefox Plugin</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#installation">Installation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#temporary-installation">Temporary Installation</a></li>
<li class="toctree-l4"><a class="reference internal" href="#permanent-installation">Permanent Installation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#configuration">Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#using-the-plugin">Using the Plugin</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#toolbar-button">Toolbar Button</a></li>
<li class="toctree-l4"><a class="reference internal" href="#context-menu">Context Menu</a></li>
<li class="toctree-l4"><a class="reference internal" href="#keyboard-shortcuts">Keyboard Shortcuts</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#connection-status">Connection Status</a></li>
<li class="toctree-l3"><a class="reference internal" href="#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l3"><a class="reference internal" href="#advanced-features">Advanced Features</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#browser-selection">Browser Selection</a></li>
<li class="toctree-l4"><a class="reference internal" href="#whitelist-and-blacklist">Whitelist and Blacklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#development">Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="browsing.html">Secure Browsing with bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#introduction">Introduction</a></li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#direct-api-access">Direct API Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#basic-usage">Basic Usage</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#advanced-parameters">Advanced Parameters</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#post-requests">POST Requests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#command-line-usage">Command Line Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#accessing-websites">Accessing Websites</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#testing-connection">Testing Connection</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#logging-console-messages">Logging Console Messages</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#managing-downloads">Managing Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#vnc-interface">VNC Interface</a><ul>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#navigation">Navigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#keyboard-and-mouse">Keyboard and Mouse</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#copy-and-paste">Copy and Paste</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#file-downloads">File Downloads</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#browser-selection">Browser Selection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#firefox">Firefox</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#chromium">Chromium</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#browsing-modes">Browsing Modes</a><ul>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#normal-mode">Normal Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="browsing.html#compatibility-mode">Compatibility Mode</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="browsing.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#basic-concepts">Basic Concepts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="index.html#remote-browser-isolation-rbi">Remote Browser Isolation (RBI)</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#containerization">Containerization</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="index.html#session-management">Session Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="index.html#getting-started">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#using-bahtbrowse">Using bahtBrowse</a></li>
<li class="toctree-l2"><a class="reference internal" href="index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../troubleshooting/index.html">Troubleshooting</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/common_issues.html">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#connection-issues">Connection Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#unable-to-connect-to-bahtbrowse-service">Unable to Connect to bahtBrowse Service</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#vnc-connection-fails">VNC Connection Fails</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-issues">Browser Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-fails-to-launch">Browser Fails to Launch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#browser-crashes">Browser Crashes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#website-compatibility-issues">Website Compatibility Issues</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#firefox-plugin-issues">Firefox Plugin Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-connecting">Plugin Not Connecting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#plugin-not-working-correctly">Plugin Not Working Correctly</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-browser-performance">Slow Browser Performance</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#slow-vnc-performance">Slow VNC Performance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#security-issues">Security Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#session-hijacking">Session Hijacking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#container-escape">Container Escape</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#logging-and-debugging">Logging and Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#viewing-logs">Viewing Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#enabling-debug-mode">Enabling Debug Mode</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/common_issues.html#debugging-firefox-plugin">Debugging Firefox Plugin</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/common_issues.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/faq.html">Frequently Asked Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#general-questions">General Questions</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-is-bahtbrowse">What is bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-work">How does bahtBrowse work?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#what-are-the-system-requirements-for-bahtbrowse">What are the system requirements for bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-free-to-use">Is bahtBrowse free to use?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-bahtbrowse">How do I install bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-install-the-firefox-plugin">How do I install the Firefox plugin?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-configure-bahtbrowse">How do I configure bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-windows-or-macos">Can I run bahtBrowse on Windows or macOS?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#usage">Usage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-access-websites-through-bahtbrowse">How do I access websites through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-browsers-other-than-firefox">Can I use bahtBrowse with browsers other than Firefox?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-download-files-through-bahtbrowse">How do I download files through bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-multiple-users">Can I use bahtBrowse for multiple users?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-use-bahtbrowse-with-a-proxy-server">How do I use bahtBrowse with a proxy server?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#security">Security</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-secure">Is bahtBrowse secure?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-bahtbrowse-protect-me-from-all-web-threats">Can bahtBrowse protect me from all web threats?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-for-sensitive-browsing">Can I use bahtBrowse for sensitive browsing?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#performance">Performance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-is-bahtbrowse-slow">Why is bahtBrowse slow?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-can-i-improve-bahtbrowse-performance">How can I improve bahtBrowse performance?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-run-bahtbrowse-on-low-end-hardware">Can I run bahtBrowse on low-end hardware?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-can-t-i-connect-to-bahtbrowse">Why can’t I connect to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-vnc-connection-fail">Why does the VNC connection fail?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#why-does-the-browser-crash">Why does the browser crash?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#development">Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-contribute-to-bahtbrowse">How do I contribute to bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-report-a-bug">How do I report a bug?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-request-a-feature">How do I request a feature?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-set-up-a-development-environment">How do I set up a development environment?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#miscellaneous">Miscellaneous</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-with-other-browsers">Can I use bahtBrowse with other browsers?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#is-bahtbrowse-suitable-for-enterprise-use">Is bahtBrowse suitable for enterprise use?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-does-bahtbrowse-compare-to-commercial-rbi-solutions">How does bahtBrowse compare to commercial RBI solutions?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#can-i-use-bahtbrowse-on-mobile-devices">Can I use bahtBrowse on mobile devices?</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/faq.html#getting-help">Getting Help</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#where-can-i-get-help-with-bahtbrowse">Where can I get help with bahtBrowse?</a></li>
<li class="toctree-l4"><a class="reference internal" href="../troubleshooting/faq.html#how-do-i-stay-updated-on-bahtbrowse-developments">How do I stay updated on bahtBrowse developments?</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#common-issues">Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#connection-issues">Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#errors">404 Errors</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#firefox-plugin-issues">Firefox Plugin Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../troubleshooting/index.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#logging-and-debugging">Logging and Debugging</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#frequently-asked-questions">Frequently Asked Questions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../troubleshooting/index.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Administrator Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/index.html">Administrator Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#deployment">Deployment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/index.html#maintenance">Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/deployment.html">Deployment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#single-server-deployment">Single Server Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#clone-the-repository">Clone the Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#build-and-start-the-containers">Build and Start the Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#verify-the-deployment">Verify the Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#distributed-deployment">Distributed Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#architecture">Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-deployment">Example Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#cloud-deployment">Cloud Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#aws-deployment">AWS Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#google-cloud-deployment">Google Cloud Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#azure-deployment">Azure Deployment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#kubernetes-deployment">Kubernetes Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#id2">Prerequisites</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#deployment-steps">Deployment Steps</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/deployment.html#example-manifests">Example Manifests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/deployment.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/configuration.html">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#docker-configuration">Docker Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-compose">Docker Compose</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#docker-images">Docker Images</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#network-configuration">Network Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-mode">Network Mode</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#port-configuration">Port Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#default-ports">Default Ports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-conflict-detection-and-resolution">Port Conflict Detection and Resolution</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#port-ranges">Port Ranges</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#using-the-port-conflict-resolution-system">Using the Port Conflict Resolution System</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#browser-configuration">Browser Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-types">Browser Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#browser-pool-configuration">Browser Pool Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#container-isolation">Container Isolation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#network-security">Network Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#performance-configuration">Performance Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#resource-limits">Resource Limits</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/configuration.html#scaling">Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/configuration.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../admin_guide/maintenance.html">Maintenance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#updating">Updating</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-bahtbrowse">Updating bahtBrowse</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#updating-dependencies">Updating Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#monitoring">Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#elk-stack">ELK Stack</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#backup">Backup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#redis-data">Redis Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#docker-volumes">Docker Volumes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#configuration-files">Configuration Files</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-issues">Container Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflicts">Port Conflicts</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#network-issues">Network Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-build-tests">Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#container-detection-tests">Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../admin_guide/maintenance.html#enhanced-tests">Enhanced Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../admin_guide/maintenance.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l4"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#system-architecture">System Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#codebase-structure">Codebase Structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#development-workflow">Development Workflow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#testing">Testing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#api-reference">API Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">System Architecture</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-diagram">Component Diagram</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#component-details">Component Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#docker-containers">Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#api-server">API Server</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#vnc-interface">VNC Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#firefox-plugin">Firefox Plugin</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#nginx-configuration">Nginx Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#sequence-diagrams">Sequence Diagrams</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/architecture.html#browser-session-creation">Browser Session Creation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#data-flow">Data Flow</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#design-decisions">Design Decisions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/architecture.html#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing to bahtBrowse</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#prerequisites">Prerequisites</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#setting-up-a-development-environment">Setting Up a Development Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-feature-branch">Creating a Feature Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#making-changes">Making Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#running-tests">Running Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#submitting-a-pull-request">Submitting a Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#coding-standards">Coding Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-code">Python Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#javascript-code">JavaScript Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-files">Docker Files</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#documentation">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#testing">Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-tests">Writing Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#id1">Running Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#docker-logs">Docker Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#api-server-logs">API Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#browser-console-logs">Browser Console Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#vnc-server-logs">VNC Server Logs</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#python-debugger">Python Debugger</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#firefox-plugin-debugging">Firefox Plugin Debugging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#id2">Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#building-documentation">Building Documentation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#writing-documentation">Writing Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#release-process">Release Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/contributing.html#creating-a-release">Creating a Release</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-framework">Test Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#installing-test-dependencies">Installing Test Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#running-tests">Running Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#unit-tests">Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-unit-tests">Writing Unit Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#mocking">Mocking</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#fixtures">Fixtures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#integration-tests">Integration Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-integration-tests">Writing Integration Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#docker-integration">Docker Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#end-to-end-tests">End-to-End Tests</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-build-tests">Container Build Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-build-tests">Writing Container Build Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-build-tests">Running Container Build Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#port-conflict-resolution-tests">Port Conflict Resolution Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-port-conflict-resolution-tests">Writing Port Conflict Resolution Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-port-conflict-resolution-tests">Running Port Conflict Resolution Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#container-detection-tests">Container Detection Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-container-detection-tests">Writing Container Detection Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-container-detection-tests">Running Container Detection Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#enhanced-tests">Enhanced Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#writing-end-to-end-tests">Writing End-to-End Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#playwright-tests">Playwright Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#running-playwright-tests">Running Playwright Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-coverage">Test Coverage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#current-coverage-metrics">Current Coverage Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-python-coverage-reports">Generating Python Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#generating-playwright-coverage-reports">Generating Playwright Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#interpreting-coverage-reports">Interpreting Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#visual-coverage-reports">Visual Coverage Reports</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#continuous-integration">Continuous Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-pipeline">CI Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#ci-configuration">CI Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#test-driven-development">Test Driven Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#tdd-workflow">TDD Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#benefits-of-tdd">Benefits of TDD</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#troubleshooting-tests">Troubleshooting Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#test-failures">Test Failures</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#slow-tests">Slow Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../developer_guide/testing.html#flaky-tests">Flaky Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../developer_guide/testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../api_reference/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/api_server.html">API Server</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#launch-browser-session">Launch Browser Session</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#request">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#response">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#test-connection">Test Connection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id4">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id5">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#log-console-messages">Log Console Messages</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id6">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id7">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#downloads-manager">Downloads Manager</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id8">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id9">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#list-downloaded-files">List Downloaded Files</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id10">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id11">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#delete-downloaded-file">Delete Downloaded File</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id12">Request</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#id13">Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#session-management">Session Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#browser-launching">Browser Launching</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#error-handling">Error Handling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#logging">Logging</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#security-considerations">Security Considerations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#api-client-examples">API Client Examples</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#python">Python</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#javascript">JavaScript</a></li>
<li class="toctree-l4"><a class="reference internal" href="../api_reference/api_server.html#curl">cURL</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/api_server.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/session_management.html">Session Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#overview">Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-manager">Session Manager</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-creation">Session Creation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validation">Session Validation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-cleanup">Session Cleanup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-validator">Session Validator</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-storage">Session Storage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-security">Session Security</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-api">Session Management API</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#example-usage">Example Usage</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#session-management-in-docker-containers">Session Management in Docker Containers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/session_management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#example-usage">Example Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#launch-a-browser-session">Launch a Browser Session</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#test-the-connection">Test the Connection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api_reference/index.html#log-console-messages">Log Console Messages</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#api-client-libraries">API Client Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api_reference/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="index.html">User Guide</a></li>
      <li class="breadcrumb-item active">Firefox Plugin</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/user_guide/firefox_plugin.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="firefox-plugin">
<span id="id1"></span><h1>Firefox Plugin<a class="headerlink" href="#firefox-plugin" title="Link to this heading"></a></h1>
<p>The bahtBrowse Firefox plugin provides a seamless way to access the bahtBrowse Remote Browser Isolation (RBI) service directly from your Firefox browser.</p>
<section id="installation">
<h2>Installation<a class="headerlink" href="#installation" title="Link to this heading"></a></h2>
<p>There are two ways to install the bahtBrowse Firefox plugin:</p>
<section id="temporary-installation">
<h3>Temporary Installation<a class="headerlink" href="#temporary-installation" title="Link to this heading"></a></h3>
<p>For testing or development purposes, you can install the plugin temporarily:</p>
<ol class="arabic simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:debugging</span></code></p></li>
<li><p>Click on “This Firefox”</p></li>
<li><p>Click on “Load Temporary Add-on…”</p></li>
<li><p>Select the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_bouncer.xpi</span></code> file from the <code class="docutils literal notranslate"><span class="pre">firefox_plugin/build</span></code> directory</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Temporary installations will be removed when Firefox is closed.</p>
</div>
</section>
<section id="permanent-installation">
<h3>Permanent Installation<a class="headerlink" href="#permanent-installation" title="Link to this heading"></a></h3>
<p>For regular use, you can install the plugin permanently:</p>
<ol class="arabic simple">
<li><p>Open Firefox</p></li>
<li><p>Navigate to <code class="docutils literal notranslate"><span class="pre">about:addons</span></code></p></li>
<li><p>Click the gear icon and select “Install Add-on From File…”</p></li>
<li><p>Select the <code class="docutils literal notranslate"><span class="pre">bahtbrowse_bouncer.xpi</span></code> file from the <code class="docutils literal notranslate"><span class="pre">firefox_plugin/build</span></code> directory</p></li>
</ol>
</section>
</section>
<section id="configuration">
<h2>Configuration<a class="headerlink" href="#configuration" title="Link to this heading"></a></h2>
<p>After installation, you need to configure the plugin to connect to your bahtBrowse service:</p>
<ol class="arabic simple">
<li><p>Click on the bahtBrowse icon in the Firefox toolbar</p></li>
<li><p>Click on “Options” (gear icon)</p></li>
<li><p>Enter the following settings:
- <strong>Host</strong>: The hostname or IP address of your bahtBrowse service (default: <code class="docutils literal notranslate"><span class="pre">localhost</span></code>)
- <strong>Port</strong>: The port number of your bahtBrowse API server (default: <code class="docutils literal notranslate"><span class="pre">8082</span></code>)
- <strong>Open in New Tab</strong>: Whether to open bahtBrowse sessions in a new tab (default: <code class="docutils literal notranslate"><span class="pre">true</span></code>)
- <strong>Whitelist/Blacklist</strong>: Configure which sites should be opened in bahtBrowse</p></li>
</ol>
<figure class="align-center" id="id2">
<img alt="Firefox Plugin Options" src="_static/firefox_plugin_options.png" />
<figcaption>
<p><span class="caption-text">Firefox Plugin Options</span><a class="headerlink" href="#id2" title="Link to this image"></a></p>
</figcaption>
</figure>
</section>
<section id="using-the-plugin">
<h2>Using the Plugin<a class="headerlink" href="#using-the-plugin" title="Link to this heading"></a></h2>
<p>The bahtBrowse Firefox plugin provides several ways to access websites through the bahtBrowse service:</p>
<section id="toolbar-button">
<h3>Toolbar Button<a class="headerlink" href="#toolbar-button" title="Link to this heading"></a></h3>
<p>The simplest way to use the plugin is through the toolbar button:</p>
<ol class="arabic simple">
<li><p>Click on the bahtBrowse icon in the Firefox toolbar</p></li>
<li><p>Enter the URL you want to visit or click “Browse Current Page Securely”</p></li>
<li><p>The page will open in a new tab, running in the isolated browser container</p></li>
</ol>
<figure class="align-center" id="id3">
<img alt="Firefox Plugin Popup" src="_static/firefox_plugin_popup.png" />
<figcaption>
<p><span class="caption-text">Firefox Plugin Popup</span><a class="headerlink" href="#id3" title="Link to this image"></a></p>
</figcaption>
</figure>
</section>
<section id="context-menu">
<h3>Context Menu<a class="headerlink" href="#context-menu" title="Link to this heading"></a></h3>
<p>You can also access bahtBrowse through the context menu:</p>
<ol class="arabic simple">
<li><p>Right-click on a link or anywhere on a page</p></li>
<li><p>Select “Open in 10baht Browse” from the context menu</p></li>
<li><p>The link or page will open in a new tab, running in the isolated browser container</p></li>
</ol>
<figure class="align-center" id="id4">
<img alt="Firefox Plugin Context Menu" src="_static/firefox_plugin_context_menu.png" />
<figcaption>
<p><span class="caption-text">Firefox Plugin Context Menu</span><a class="headerlink" href="#id4" title="Link to this image"></a></p>
</figcaption>
</figure>
</section>
<section id="keyboard-shortcuts">
<h3>Keyboard Shortcuts<a class="headerlink" href="#keyboard-shortcuts" title="Link to this heading"></a></h3>
<p>The plugin also provides keyboard shortcuts for quick access:</p>
<ul class="simple">
<li><p><strong>Ctrl+Shift+B</strong>: Open the current page in bahtBrowse</p></li>
<li><p><strong>Ctrl+Shift+L</strong>: Open the bahtBrowse popup</p></li>
</ul>
<p>You can customize these shortcuts in Firefox’s keyboard shortcuts settings.</p>
</section>
</section>
<section id="connection-status">
<h2>Connection Status<a class="headerlink" href="#connection-status" title="Link to this heading"></a></h2>
<p>The plugin icon in the toolbar indicates the connection status to the bahtBrowse service:</p>
<ul class="simple">
<li><p><strong>Blue Icon</strong>: The service is available</p></li>
<li><p><strong>Gray Icon</strong>: The service is not available</p></li>
</ul>
<p>You can click on the icon to check the connection status and troubleshoot any issues.</p>
</section>
<section id="troubleshooting">
<h2>Troubleshooting<a class="headerlink" href="#troubleshooting" title="Link to this heading"></a></h2>
<p>If you encounter issues with the Firefox plugin, try the following:</p>
<ol class="arabic simple">
<li><p><strong>Check Connection</strong>: Ensure that the bahtBrowse service is running and accessible</p></li>
<li><p><strong>Check Configuration</strong>: Verify that the host and port settings are correct</p></li>
<li><p><strong>Check Browser Console</strong>: Open the browser console (F12) and look for any error messages</p></li>
<li><p><strong>Reinstall Plugin</strong>: Try reinstalling the plugin</p></li>
</ol>
<p>For more troubleshooting tips, see the <a class="reference internal" href="../troubleshooting/common_issues.html"><span class="doc">Common Issues</span></a> section.</p>
</section>
<section id="advanced-features">
<h2>Advanced Features<a class="headerlink" href="#advanced-features" title="Link to this heading"></a></h2>
<p>The bahtBrowse Firefox plugin provides several advanced features:</p>
<section id="browser-selection">
<h3>Browser Selection<a class="headerlink" href="#browser-selection" title="Link to this heading"></a></h3>
<p>You can specify which browser to use for each session:</p>
<ol class="arabic simple">
<li><p>Click on the bahtBrowse icon in the Firefox toolbar</p></li>
<li><p>Click on “Options” (gear icon)</p></li>
<li><p>Select the default browser (Firefox or Chromium)</p></li>
<li><p>You can also override this setting for individual sessions by using the dropdown in the popup</p></li>
</ol>
</section>
<section id="whitelist-and-blacklist">
<h3>Whitelist and Blacklist<a class="headerlink" href="#whitelist-and-blacklist" title="Link to this heading"></a></h3>
<p>You can configure which sites should be opened in bahtBrowse:</p>
<ol class="arabic simple">
<li><p>Click on the bahtBrowse icon in the Firefox toolbar</p></li>
<li><p>Click on “Options” (gear icon)</p></li>
<li><p>Enable whitelist or blacklist</p></li>
<li><p>Add sites to the list (one per line)</p></li>
</ol>
<p>With whitelist enabled, only sites in the list will be opened in bahtBrowse. With blacklist enabled, sites in the list will not be opened in bahtBrowse.</p>
</section>
<section id="logging">
<h3>Logging<a class="headerlink" href="#logging" title="Link to this heading"></a></h3>
<p>The plugin logs various events to help with debugging:</p>
<ol class="arabic simple">
<li><p>Open the browser console (F12)</p></li>
<li><p>Filter for “bahtBrowse” to see plugin-related logs</p></li>
</ol>
<p>You can also enable verbose logging in the plugin options for more detailed logs.</p>
</section>
</section>
<section id="development">
<h2>Development<a class="headerlink" href="#development" title="Link to this heading"></a></h2>
<p>If you want to modify or contribute to the Firefox plugin, see the <a class="reference internal" href="../developer_guide/contributing.html"><span class="doc">Contributing to bahtBrowse</span></a> section.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="User Guide" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="browsing.html" class="btn btn-neutral float-right" title="Secure Browsing with bahtBrowse" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, bahtBrowse Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
/* Custom CSS for bahtBrowse documentation */

/* General styling */
body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    color: #333;
}

/* Header styling */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
}

/* Dark mode specific styles */
.dark {
    --body-color: #eeeeee;
    --content-background-color: #121212;
    --content-color: #eeeeee;
    --link-color: #8ab4f8;
    --link-color-hover: #aecbfa;
    --link-color-active: #8ab4f8;
    --link-color-visited: #c58af9;
    --hr-color: #555555;
    --table-border-color: #555555;
    --search-input-background-color: #121212;
    --search-input-color: #eeeeee;
    --search-match-color: #f6b26b;
    --search-match-background-color: #52565e;
    --search-active-color: #ffffff;
    --search-credits-color: #999999;
    --highlighted-color: #2a2a2a;
    --highlighted-background-color: #e9c062;
    --highlighted-border-color: #e9c062;
    --admonition-note-background-color: #2a2a2a;
    --admonition-note-color: #eeeeee;
    --admonition-note-title-background-color: #2980b9;
    --admonition-note-title-color: #ffffff;
    --admonition-attention-background-color: #2a2a2a;
    --admonition-attention-color: #eeeeee;
    --admonition-attention-title-background-color: #ff9800;
    --admonition-attention-title-color: #ffffff;
    --admonition-danger-background-color: #2a2a2a;
    --admonition-danger-color: #eeeeee;
    --admonition-danger-title-background-color: #e74c3c;
    --admonition-danger-title-color: #ffffff;
    --admonition-tip-background-color: #2a2a2a;
    --admonition-tip-color: #eeeeee;
    --admonition-tip-title-background-color: #2ecc71;
    --admonition-tip-title-color: #ffffff;
    --kbd-background-color: #2a2a2a;
    --kbd-outline-color: #555555;
    --kbd-shadow-color: #555555;
    --kbd-color: #eeeeee;
    --code-background-color: #2a2a2a;
    --code-border-color: #555555;
    --code-color: #eeeeee;
}

/* Link styling */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Code block styling */
pre {
    background-color: #f8f8f8;
    border: 1px solid #e1e4e5;
    border-radius: 4px;
    padding: 12px;
    font-size: 90%;
    overflow-x: auto;
}

code {
    background-color: #f8f8f8;
    border: 1px solid #e1e4e5;
    border-radius: 3px;
    padding: 0 5px;
    font-size: 90%;
}

/* Dark mode code block styling */
.dark pre {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    color: #eeeeee;
}

.dark code {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    color: #eeeeee;
}

/* Mermaid diagram styling for dark mode */
.dark .mermaid {
    background-color: #2a2a2a;
    color: #eeeeee;
}

.dark .mermaid .label {
    color: #eeeeee;
}

.dark .mermaid .node rect,
.dark .mermaid .node circle,
.dark .mermaid .node ellipse,
.dark .mermaid .node polygon,
.dark .mermaid .node path {
    fill: #2a2a2a;
    stroke: #aaaaaa;
}

.dark .mermaid .edgePath .path {
    stroke: #aaaaaa;
}

.dark .mermaid .edgeLabel {
    background-color: #2a2a2a;
    color: #eeeeee;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    border: 1px solid #e1e4e5;
    margin-bottom: 24px;
    width: 100%;
}

table.docutils td, table.docutils th {
    padding: 8px 16px;
    border: 1px solid #e1e4e5;
}

table.docutils th {
    background-color: #f3f6f6;
    font-weight: 600;
}

/* Dark mode table styling */
.dark table.docutils {
    border: 1px solid #555555;
}

.dark table.docutils td, .dark table.docutils th {
    border: 1px solid #555555;
}

.dark table.docutils th {
    background-color: #2a2a2a;
    color: #eeeeee;
}

.dark table.docutils tr {
    background-color: #1e1e1e;
    color: #eeeeee;
}

.dark table.docutils tr:nth-child(odd) {
    background-color: #262626;
}

/* Note and warning styling */
.admonition {
    padding: 12px;
    margin-bottom: 24px;
    border-radius: 4px;
}

.admonition.note {
    background-color: #e7f2fa;
    border-left: 6px solid #3498db;
}

.admonition.warning {
    background-color: #fcf3e7;
    border-left: 6px solid #e67e22;
}

.admonition.danger {
    background-color: #fae7e7;
    border-left: 6px solid #e74c3c;
}

.admonition-title {
    font-weight: 600;
    margin-top: 0;
}

/* Dark mode admonition styling */
.dark .admonition {
    color: #eeeeee;
}

.dark .admonition.note {
    background-color: #1a2733;
    border-left: 6px solid #3498db;
}

.dark .admonition.warning {
    background-color: #332a1a;
    border-left: 6px solid #e67e22;
}

.dark .admonition.danger {
    background-color: #331a1a;
    border-left: 6px solid #e74c3c;
}

.dark .admonition.tip {
    background-color: #1a331f;
    border-left: 6px solid #2ecc71;
}

.dark .admonition-title {
    color: #ffffff;
}

/* Sidebar styling */
.wy-nav-side {
    background-color: #2c3e50;
}

.wy-side-nav-search {
    background-color: #3498db;
}

.wy-menu-vertical a {
    color: #fff;
}

.wy-menu-vertical a:hover {
    background-color: #3498db;
}

/* Dark mode sidebar styling */
.dark .wy-nav-side {
    background-color: #1a1a1a;
}

.dark .wy-side-nav-search {
    background-color: #121212;
}

.dark .wy-nav-content-wrap {
    background-color: #121212;
}

.dark .wy-nav-content {
    background-color: #121212;
    color: #eeeeee;
}

.dark .wy-menu-vertical a {
    color: #eeeeee;
}

.dark .wy-menu-vertical a:hover {
    background-color: #2a2a2a;
}

.dark .wy-menu-vertical li.current {
    background-color: #2a2a2a;
}

.dark .wy-menu-vertical li.current > a {
    background-color: #2a2a2a;
    color: #eeeeee;
}

.dark .wy-menu-vertical li.current > a:hover {
    background-color: #3a3a3a;
}

.dark .wy-menu-vertical li.toctree-l1.current > a {
    border-color: #2a2a2a;
}

/* Mobile responsiveness */
@media screen and (max-width: 768px) {
    .wy-nav-content-wrap {
        margin-left: 0;
    }
}

/* Copy button styling */
button.copybtn {
    opacity: 0.5;
    transition: opacity 0.3s ease-in-out;
}

div.highlight:hover button.copybtn, button.copybtn:hover {
    opacity: 1;
}

/* API documentation styling */
dl.class, dl.function, dl.method, dl.attribute {
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #e1e4e5;
    border-radius: 4px;
}

dl.class > dt, dl.function > dt, dl.method > dt, dl.attribute > dt {
    background-color: #f3f6f6;
    padding: 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 100%;
}

/* Search results styling */
ul.search li {
    padding: 10px 0;
    border-bottom: 1px solid #e1e4e5;
}

ul.search li:last-child {
    border-bottom: none;
}

/* Footer styling */
footer {
    color: #999;
    font-size: 85%;
}

/* Dark Mode Toggle Button */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #3498db;
    color: white;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
    background-color: #2980b9;
}

.dark-mode-toggle.clicked {
    animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.dark .dark-mode-toggle {
    background-color: #f39c12;
}

.dark .dark-mode-toggle:hover {
    background-color: #e67e22;
}

/* Toggle switch inside the button */
.toggle-switch {
    position: relative;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-switch .sun,
.toggle-switch .moon {
    position: absolute;
    transition: opacity 0.3s ease, transform 0.5s ease;
}

.toggle-switch .sun {
    opacity: 1;
    transform: scale(1);
}

.toggle-switch .moon {
    opacity: 0;
    transform: scale(0);
}

.dark .toggle-switch .sun {
    opacity: 0;
    transform: scale(0);
}

.dark .toggle-switch .moon {
    opacity: 1;
    transform: scale(1);
}

/* Print styling */
@media print {
    .wy-nav-side {
        display: none;
    }

    .wy-nav-content-wrap {
        margin-left: 0;
    }

    .rst-footer-buttons {
        display: none;
    }

    .dark-mode-toggle {
        display: none;
    }
}

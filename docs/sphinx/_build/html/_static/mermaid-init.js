document.addEventListener('DOMContentLoaded', function() {
    if (typeof mermaid === 'undefined') {
        console.error('Mermaid library not loaded');
        return;
    }

    // Check if dark mode is active
    const isDarkMode = document.body.classList.contains('dark');

    mermaid.initialize({
        startOnLoad: true,
        theme: isDarkMode ? 'dark' : 'default',
        securityLevel: 'loose',
        flowchart: {
            useMaxWidth: false,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            useMaxWidth: false,
            showSequenceNumbers: true,
            mirrorActors: false,
            actorFontSize: 14,
            noteFontSize: 14,
            messageFontSize: 14
        }
    });

    // Listen for dark mode toggle events using MutationObserver
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const isDarkMode = document.body.classList.contains('dark');
                mermaid.initialize({
                    theme: isDarkMode ? 'dark' : 'default'
                });
                // Re-render all diagrams
                document.querySelectorAll('.mermaid').forEach(function(element) {
                    const content = element.textContent;
                    element.innerHTML = '';
                    element.textContent = content;
                });
                mermaid.init(undefined, '.mermaid');
            }
        });
    });

    observer.observe(document.body, { attributes: true });

    // Find all code blocks with class 'mermaid'
    document.querySelectorAll('pre.mermaid').forEach(function(element) {
        // Create a div with class 'mermaid'
        var div = document.createElement('div');
        div.className = 'mermaid';
        div.innerHTML = element.textContent;

        // Replace the pre element with the div
        element.parentNode.replaceChild(div, element);
    });

    // Find all Pygments-highlighted mermaid code blocks
    document.querySelectorAll('div.highlight-mermaid pre').forEach(function(element) {
        // Create a div with class 'mermaid'
        var div = document.createElement('div');
        div.className = 'mermaid';
        div.innerHTML = element.textContent;

        // Replace the pre element with the div
        element.parentNode.replaceChild(div, element);
    });

    // Initialize Mermaid
    mermaid.init(undefined, '.mermaid');
});

document.addEventListener('DOMContentLoaded', function() {
    // Create dark mode toggle button with sun and moon icons
    const toggleButton = document.createElement('button');
    toggleButton.className = 'dark-mode-toggle';
    toggleButton.title = 'Toggle Dark Mode';

    // Create toggle switch with sun and moon icons
    const toggleSwitch = document.createElement('div');
    toggleSwitch.className = 'toggle-switch';

    // Sun icon (SVG)
    const sunIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    sunIcon.setAttribute('class', 'sun');
    sunIcon.setAttribute('width', '24');
    sunIcon.setAttribute('height', '24');
    sunIcon.setAttribute('viewBox', '0 0 24 24');
    sunIcon.setAttribute('fill', 'none');
    sunIcon.setAttribute('stroke', 'currentColor');
    sunIcon.setAttribute('stroke-width', '2');
    sunIcon.setAttribute('stroke-linecap', 'round');
    sunIcon.setAttribute('stroke-linejoin', 'round');

    // Sun circle
    const sunCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    sunCircle.setAttribute('cx', '12');
    sunCircle.setAttribute('cy', '12');
    sunCircle.setAttribute('r', '5');
    sunIcon.appendChild(sunCircle);

    // Sun rays
    for (let i = 0; i < 8; i++) {
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        const angle = i * Math.PI / 4;
        const x1 = 12 + Math.cos(angle) * 7;
        const y1 = 12 + Math.sin(angle) * 7;
        const x2 = 12 + Math.cos(angle) * 9;
        const y2 = 12 + Math.sin(angle) * 9;

        line.setAttribute('x1', x1);
        line.setAttribute('y1', y1);
        line.setAttribute('x2', x2);
        line.setAttribute('y2', y2);

        sunIcon.appendChild(line);
    }

    // Moon icon (SVG)
    const moonIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    moonIcon.setAttribute('class', 'moon');
    moonIcon.setAttribute('width', '24');
    moonIcon.setAttribute('height', '24');
    moonIcon.setAttribute('viewBox', '0 0 24 24');
    moonIcon.setAttribute('fill', 'none');
    moonIcon.setAttribute('stroke', 'currentColor');
    moonIcon.setAttribute('stroke-width', '2');
    moonIcon.setAttribute('stroke-linecap', 'round');
    moonIcon.setAttribute('stroke-linejoin', 'round');

    // Moon path
    const moonPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    moonPath.setAttribute('d', 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z');
    moonIcon.appendChild(moonPath);

    // Add icons to toggle switch
    toggleSwitch.appendChild(sunIcon);
    toggleSwitch.appendChild(moonIcon);

    // Add toggle switch to button
    toggleButton.appendChild(toggleSwitch);

    // Add button to body
    document.body.appendChild(toggleButton);

    // Check for saved theme preference or use system preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark' || (savedTheme === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.body.classList.add('dark');
    }

    // Toggle dark mode on button click
    toggleButton.addEventListener('click', function() {
        document.body.classList.toggle('dark');
        const isDark = document.body.classList.contains('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');

        // Add animation effect
        toggleButton.classList.add('clicked');
        setTimeout(() => {
            toggleButton.classList.remove('clicked');
        }, 300);
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (localStorage.getItem('theme') === null) {
            if (e.matches) {
                document.body.classList.add('dark');
            } else {
                document.body.classList.remove('dark');
            }
        }
    });
});

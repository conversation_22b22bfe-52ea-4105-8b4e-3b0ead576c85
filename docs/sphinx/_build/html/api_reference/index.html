<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>API Reference &mdash; bahtBrowse 1.0.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=80d5e7a1" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />

  
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=8d563738"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="prev" title="Testing" href="../developer_guide/testing.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            bahtBrowse
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Developer Documentation</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/index.html">Developer Guide</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/architecture.html">Architecture</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/health_dashboard.html">Health Dashboard</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/contributing.html">Contributing</a></li>
<li class="toctree-l1"><a class="reference internal" href="../developer_guide/testing.html">Testing</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-endpoints">API Endpoints</a></li>
<li class="toctree-l2"><a class="reference internal" href="#health-api">Health API</a></li>
<li class="toctree-l2"><a class="reference internal" href="#browser-api">Browser API</a></li>
<li class="toctree-l2"><a class="reference internal" href="#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="#error-handling">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="#versioning">Versioning</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cors">CORS</a></li>
<li class="toctree-l2"><a class="reference internal" href="#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="#api-client-libraries">API Client Libraries</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">bahtBrowse</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">API Reference</li>
      <li class="wy-breadcrumbs-aside">
            <a href="../_sources/api_reference/index.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="api-reference">
<span id="id1"></span><h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<p>This document describes the bahtBrowse API.</p>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The bahtBrowse API provides programmatic access to bahtBrowse functionality. It allows you to create and manage browser sessions, access browser containers, and monitor system health.</p>
</section>
<section id="api-endpoints">
<h2>API Endpoints<a class="headerlink" href="#api-endpoints" title="Link to this heading"></a></h2>
<p>The bahtBrowse API provides the following endpoints:</p>
</section>
<section id="health-api">
<h2>Health API<a class="headerlink" href="#health-api" title="Link to this heading"></a></h2>
<p>The Health API provides endpoints for monitoring system health.</p>
<p><strong>Endpoints</strong>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">/health</span></code>: Returns the health status of all services</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/api-docs</span></code>: Returns API documentation in JSON format</p></li>
</ul>
<p><strong>Example</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8000/health
</pre></div>
</div>
<p><strong>Response</strong>:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;services&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;Central API&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.34</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Docker Queue API&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">23.45</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Redis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">34.56</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Celery Flower&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">45.67</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Firefox Alpine&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">56.78</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Chromium Alpine&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">67.89</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;VNC WebSocket&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;healthy&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service is running&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;response_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">78.90</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;system_info&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;cpu_percent&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">12.3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;memory_used&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">**********</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;memory_total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">**********1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;memory_percent&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">10.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;disk_used&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">**********1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;disk_total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">**********12</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;disk_percent&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">10.0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;python_version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;3.9.0&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-01-01T00:00:00Z&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="browser-api">
<h2>Browser API<a class="headerlink" href="#browser-api" title="Link to this heading"></a></h2>
<p>The Browser API provides endpoints for creating and managing browser sessions.</p>
<p><strong>Endpoints</strong>:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">/browse</span></code>: Creates a new browser session</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/browse/{session_id}</span></code>: Accesses an existing browser session</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/browse/{session_id}/status</span></code>: Returns the status of a browser session</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/browse/{session_id}/screenshot</span></code>: Takes a screenshot of a browser session</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">/browse/{session_id}/close</span></code>: Closes a browser session</p></li>
</ul>
<p><strong>Example</strong>:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8001/browse?url<span class="o">=</span>https://example.com
</pre></div>
</div>
<p><strong>Response</strong>:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;**********&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;browser&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;firefox&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;running&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;vnc_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://localhost:8001/vnc/?path=websockify/?token=**********&quot;</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="authentication">
<h2>Authentication<a class="headerlink" href="#authentication" title="Link to this heading"></a></h2>
<p>The bahtBrowse API supports authentication using API keys. To authenticate, include the API key in the <cite>Authorization</cite> header:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer YOUR_API_KEY&quot;</span><span class="w"> </span>http://localhost:8001/browse
</pre></div>
</div>
</section>
<section id="rate-limiting">
<h2>Rate Limiting<a class="headerlink" href="#rate-limiting" title="Link to this heading"></a></h2>
<p>The bahtBrowse API implements rate limiting to prevent abuse. Rate limits are applied per API key and are reset hourly.</p>
</section>
<section id="error-handling">
<h2>Error Handling<a class="headerlink" href="#error-handling" title="Link to this heading"></a></h2>
<p>The bahtBrowse API returns standard HTTP status codes to indicate success or failure. In case of an error, the response body will contain an error message:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid URL&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status_code&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">400</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="pagination">
<h2>Pagination<a class="headerlink" href="#pagination" title="Link to this heading"></a></h2>
<p>API endpoints that return multiple items support pagination. Use the <cite>page</cite> and <cite>per_page</cite> query parameters to control pagination:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8001/browse?page<span class="o">=</span><span class="m">1</span><span class="p">&amp;</span><span class="nv">per_page</span><span class="o">=</span><span class="m">10</span>
</pre></div>
</div>
<p>The response will include pagination metadata:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="err">...</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;per_page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="versioning">
<h2>Versioning<a class="headerlink" href="#versioning" title="Link to this heading"></a></h2>
<p>The bahtBrowse API is versioned. The current version is v1. To specify a version, include it in the URL:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>http://localhost:8001/v1/browse
</pre></div>
</div>
</section>
<section id="cors">
<h2>CORS<a class="headerlink" href="#cors" title="Link to this heading"></a></h2>
<p>The bahtBrowse API supports Cross-Origin Resource Sharing (CORS). CORS is enabled for all origins by default.</p>
</section>
<section id="webhooks">
<h2>Webhooks<a class="headerlink" href="#webhooks" title="Link to this heading"></a></h2>
<p>The bahtBrowse API supports webhooks for event notifications. To register a webhook, use the <cite>/webhooks</cite> endpoint:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span>-d<span class="w"> </span><span class="s1">&#39;{&quot;url&quot;: &quot;https://example.com/webhook&quot;, &quot;events&quot;: [&quot;session.created&quot;, &quot;session.closed&quot;]}&#39;</span><span class="w"> </span>http://localhost:8001/webhooks
</pre></div>
</div>
<p>When an event occurs, the API will send a POST request to the registered webhook URL with the event data:</p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;event&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;session.created&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;session_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;**********&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://example.com&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;browser&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;firefox&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;running&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;created_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-01-01T00:00:00Z&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="api-client-libraries">
<h2>API Client Libraries<a class="headerlink" href="#api-client-libraries" title="Link to this heading"></a></h2>
<p>The bahtBrowse API provides client libraries for various programming languages:</p>
<ul class="simple">
<li><p>Python: <cite>bahtbrowse-python</cite></p></li>
<li><p>JavaScript: <cite>bahtbrowse-js</cite></p></li>
<li><p>Ruby: <cite>bahtbrowse-ruby</cite></p></li>
<li><p>Go: <cite>bahtbrowse-go</cite></p></li>
<li><p>PHP: <cite>bahtbrowse-php</cite></p></li>
</ul>
<p>For more information on using the client libraries, see the client library documentation.</p>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../developer_guide/testing.html" class="btn btn-neutral float-left" title="Testing" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, 10Baht Security.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>
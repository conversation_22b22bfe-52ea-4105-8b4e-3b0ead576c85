Search.setIndex({"alltitles": {"API Client Libraries": [[0, "api-client-libraries"]], "API Documentation": [[3, "api-documentation"]], "API Endpoints": [[0, "api-endpoints"]], "API Reference": [[0, null], [4, "api-reference"]], "API Server": [[1, "api-server"]], "Administrator Guides": [[6, "administrator-guides"]], "Architecture": [[1, null]], "Authentication": [[0, "authentication"]], "Auto-Refresh": [[3, "auto-refresh"]], "Browser API": [[0, "browser-api"]], "CORS": [[0, "cors"]], "Code Review": [[2, "code-review"]], "Codebase Structure": [[4, "codebase-structure"]], "Coding Standards": [[2, "coding-standards"]], "Component Architecture": [[1, "component-architecture"], [3, "component-architecture"]], "Configuration": [[3, "configuration"]], "Continuous Integration": [[5, "continuous-integration"]], "Contributing": [[2, null]], "Dark Mode Support": [[3, "dark-mode-support"]], "Developer Guide": [[4, null]], "Developer Guides": [[6, "developer-guides"]], "Development Environment": [[2, "development-environment"], [4, "development-environment"]], "Development Workflow": [[4, "development-workflow"]], "Docker Containers": [[1, "docker-containers"]], "Documentation": [[2, "documentation"]], "End-to-End Tests": [[5, "end-to-end-tests"]], "Error Handling": [[0, "error-handling"]], "Firefox Plugin": [[1, "firefox-plugin"]], "Frontend Interface": [[3, "frontend-interface"]], "Future Enhancements": [[3, "future-enhancements"]], "Getting Started": [[2, "getting-started"]], "Health API": [[0, "health-api"], [3, "health-api"]], "Health Dashboard": [[1, "health-dashboard"], [3, null], [4, "health-dashboard"]], "Indices and tables": [[6, "indices-and-tables"]], "Integration Tests": [[5, "integration-tests"]], "Introduction": [[4, "introduction"]], "Key Features": [[3, "key-features"], [6, "key-features"]], "Mocking": [[5, "mocking"]], "Next Steps": [[4, "next-steps"]], "Nginx Configuration": [[1, "nginx-configuration"]], "Overview": [[0, "overview"], [1, "overview"], [3, "overview"], [5, "overview"]], "Pagination": [[0, "pagination"]], "Performance Tests": [[5, "performance-tests"]], "Pull Requests": [[2, "pull-requests"]], "Rate Limiting": [[0, "rate-limiting"]], "Releases": [[2, "releases"]], "Response Time Tracking": [[3, "response-time-tracking"]], "Running Tests": [[5, "running-tests"]], "System Architecture": [[4, "system-architecture"]], "System Resource Monitoring": [[3, "system-resource-monitoring"]], "Test Coverage": [[5, "test-coverage"]], "Test Data": [[5, "test-data"]], "Test Documentation": [[5, "test-documentation"]], "Test Environment": [[5, "test-environment"]], "Test Maintenance": [[5, "test-maintenance"]], "Testing": [[2, "testing"], [4, "testing"], [5, null]], "Unit Tests": [[5, "unit-tests"]], "Usage": [[3, "usage"]], "User Guides": [[6, "user-guides"]], "VNC Interface": [[1, "vnc-interface"]], "Versioning": [[0, "versioning"]], "Webhooks": [[0, "webhooks"]], "Welcome to bahtBrowse Documentation": [[6, null]], "Writing Tests": [[5, "writing-tests"]]}, "docnames": ["api_reference/index", "developer_guide/architecture", "developer_guide/contributing", "developer_guide/health_dashboard", "developer_guide/index", "developer_guide/testing", "index"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.viewcode": 1}, "filenames": ["api_reference/index.rst", "developer_guide/architecture.rst", "developer_guide/contributing.rst", "developer_guide/health_dashboard.rst", "developer_guide/index.rst", "developer_guide/testing.rst", "index.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": 3, "0": [0, 3], "00": 0, "00z": 0, "01": 0, "01t00": 0, "1": 0, "10": 0, "100": [0, 5], "10baht": [2, 4], "12": 0, "**********": 0, "**********1": 0, "**********12": 0, "15": 3, "2023": 0, "23": 0, "3": 0, "34": 0, "400": 0, "45": 0, "56": 0, "67": 0, "78": 0, "8": 2, "8000": [0, 3], "8001": 0, "89": 0, "9": 0, "90": 0, "A": [2, 3], "For": [0, 1, 2, 3, 4], "In": [0, 3], "It": [0, 1, 3, 6], "The": [0, 1, 3, 4, 5], "To": [0, 2, 3, 4, 5], "about": [4, 6], "abus": 0, "access": [0, 1, 3, 4, 6], "accord": 2, "accordingli": 3, "achiev": 5, "activ": [2, 4, 6], "add": 3, "adher": 2, "admin_guid": 6, "administr": 3, "alert": 3, "algorithm": 3, "all": [0, 1, 2, 3, 4, 5], "allow": [0, 1, 3], "along": 5, "alpin": 0, "altern": 3, "an": [0, 2, 4], "ani": 2, "api": 6, "app": 4, "appli": [0, 3], "applic": [0, 4], "appropri": 1, "ar": [0, 2, 3, 5], "architectur": 6, "aspect": [4, 5], "authent": 1, "author": 0, "auto": 4, "automat": 3, "averag": [3, 4], "background": [3, 4], "bahtbrows": [0, 1, 2, 3, 4, 5], "bar": 3, "base": 3, "bearer": 0, "befor": [2, 5], "being": 5, "between": 3, "bin": [2, 4], "bodi": 0, "branch": [2, 4], "break": 4, "brows": [0, 1, 4, 6], "browser": [1, 4, 6], "bug": 4, "build": [2, 4], "button": 3, "calcul": [3, 4], "can": [3, 5], "capabl": 3, "case": 0, "cd": [2, 4], "celeri": 0, "central": [0, 3], "chang": [2, 4, 5], "check": 3, "chromium": [0, 1, 4, 6], "clear": 2, "click": [1, 3], "clone": [2, 4], "close": 0, "code": [0, 4, 5], "collect": 3, "com": [0, 2, 4], "command": [4, 5], "commit": [2, 4], "common": 6, "compat": 2, "compon": [4, 5], "compos": [2, 4], "comprehens": [4, 5], "configur": [4, 6], "connect": 1, "consist": [1, 3, 4], "const": 3, "constraint": 3, "contain": [0, 2, 4, 6], "container": [1, 4, 6], "content": 0, "continu": 4, "contribut": [3, 4, 6], "control": 0, "copi": 4, "core": 2, "corner": 3, "count": 3, "cov": 5, "cover": [4, 5], "coverag": [2, 4], "cpu": [3, 4], "cpu_perc": 0, "creat": [0, 1, 2, 4], "created_at": 0, "critic": 5, "cross": 0, "css": [3, 4], "curl": 0, "current": 0, "currentavg": 3, "custom": 3, "customiz": 3, "d": [0, 2, 4], "dark": 4, "dashboard": 6, "data": [0, 3, 4], "default": [0, 3], "depend": [2, 3, 4, 5], "deploi": 6, "describ": [0, 1, 2, 3, 5], "descript": [2, 3], "design": 5, "detail": [3, 4], "detect": 3, "dev": [2, 4], "develop": 3, "differ": 3, "directli": 3, "directori": 5, "disk": [3, 4], "disk_perc": 0, "disk_tot": 0, "disk_us": 0, "displai": [3, 4], "doc": [0, 3, 4], "docker": [0, 2, 4, 6], "dockerfil": 4, "document": [0, 1, 4], "don": 4, "done": 5, "e": [2, 4], "each": [1, 3], "easi": 6, "easier": 3, "effect": 6, "enabl": [0, 1, 4], "end": 4, "endpoint": 3, "enhanc": 4, "ensur": 4, "enterpris": 6, "entir": 5, "entri": 3, "environ": [1, 3, 6], "event": 0, "exampl": [0, 3], "exist": [0, 4], "experi": [1, 4, 6], "explain": 5, "explan": 4, "explor": 4, "extend": 4, "extern": 5, "f": [2, 4], "failur": 0, "fast": 5, "fastapi": 3, "featur": [2, 4], "fetch": 3, "file": [1, 4], "firefox": [0, 4, 6], "firefox_plugin": 4, "fix": 4, "flower": 0, "focu": 2, "follow": [0, 2, 3, 4, 5], "foreach": 3, "fork": 2, "format": [0, 2, 3], "framework": 3, "from": [1, 3, 5, 6], "frontend": 4, "function": [0, 3, 4], "futur": 4, "gener": [3, 5], "get": [4, 6], "getting_start": 6, "git": [2, 4], "github": [2, 4], "go": 0, "goal": 5, "guid": 2, "h": 0, "ha": [4, 5], "handl": [1, 4], "header": 0, "health": 6, "health_api": 4, "health_api_port": 3, "healthi": 0, "help": [3, 4], "histor": 3, "hook": [2, 4], "host": 1, "hourli": 0, "how": [2, 4, 5, 6], "html": 3, "http": [0, 2, 3, 4], "i": [0, 1, 2, 3, 4, 5, 6], "icon": [3, 4], "identifi": 3, "impact": 3, "implement": [0, 3], "improv": 3, "includ": [0, 1, 2, 3, 4, 5], "index": 6, "indic": [0, 3], "individu": [5, 6], "inform": [0, 1, 2, 3, 4], "insight": 3, "instal": [2, 4], "integr": [1, 3, 4, 6], "interact": [1, 3, 5], "interfac": [4, 6], "interv": 3, "invalid": 0, "isol": [1, 4, 5, 6], "issu": [2, 3, 6], "item": 0, "its": 4, "j": [0, 4], "javascript": [0, 3, 4], "json": [0, 3, 4], "kei": [0, 4], "languag": 0, "layout": 3, "learn": [4, 6], "let": 3, "librari": 3, "light": 3, "load": 3, "local": 1, "localhost": [0, 3], "localstorag": 3, "longer": 5, "m": [2, 4], "mai": 5, "main": [2, 4], "maintain": 5, "mainten": 4, "make": [2, 3, 4], "manag": [0, 1, 2, 4, 6], "manifest": 4, "manual": 3, "measur": [3, 4, 5], "memori": [3, 4], "memory_perc": 0, "memory_tot": 0, "memory_us": 0, "merg": [2, 5], "messag": 0, "metadata": 0, "metric": 3, "minimum": 5, "mock": 4, "mode": 4, "modifi": 4, "modul": [4, 5, 6], "monitor": [0, 1, 4, 6], "moon": 3, "more": [0, 1, 2, 3, 4], "multipl": [0, 6], "must": 5, "navig": 3, "need": 4, "new": [0, 4], "nginx": 4, "notif": 0, "notifi": 3, "object": 3, "occur": 0, "onlin": 6, "open": [2, 4], "oper": 3, "optim": 3, "organ": 4, "origin": 0, "other": [1, 3], "over": [3, 4], "overal": 3, "overview": 4, "own": 4, "page": [0, 1, 6], "paramet": 0, "pass": [2, 5], "path": [0, 5], "pep": 2, "per": [0, 3], "per_pag": 0, "perform": [3, 4], "php": 0, "pip": [2, 4], "place": 5, "plan": 3, "plugin": [4, 6], "port": 3, "possibl": 5, "post": 0, "pre": [2, 4], "prefer": 3, "prevent": 0, "product": 5, "program": 0, "programmat": [0, 5, 6], "progress": 3, "project": 4, "protect": 6, "provid": [0, 1, 3, 4, 6], "proxi": [1, 4], "psutil": 3, "pull": 4, "py": [4, 5], "pytest": [4, 5], "python": [0, 2, 3, 4], "python3": [2, 4], "python_vers": 0, "qualiti": 2, "queri": 0, "queue": 0, "quickli": 6, "rbi": [4, 6], "real": [1, 3, 4], "redi": 0, "redirect": [1, 4], "refer": [2, 6], "reflect": 5, "refresh": 4, "regist": 0, "relat": [2, 4], "releas": 4, "remot": [1, 4, 6], "repositori": [2, 4, 5], "request": [0, 1, 3, 4], "reset": 0, "resourc": [0, 1, 4, 5], "respond": 3, "respons": [0, 1, 4, 5], "response_tim": [0, 3], "responsetim": 3, "responsetimecount": 3, "restructuredtext": 2, "retriev": 3, "return": [0, 3], "review": 4, "right": 3, "rout": 1, "rubi": 0, "run": [0, 1, 2, 3, 4, 6], "scalabl": 6, "screenshot": 0, "script": 4, "seamless": [1, 4, 6], "search": 6, "second": 3, "section": [3, 4], "secur": [1, 4, 6], "see": [0, 1, 2, 3, 4], "send": 0, "separ": 3, "serv": [1, 3, 4], "server": [4, 6], "servic": [0, 1, 3, 4, 5, 6], "servicenam": 3, "session": [0, 1, 4, 6], "session_id": 0, "session_manag": 4, "set": [2, 4], "setinterv": 3, "sever": [1, 3, 4], "share": 0, "should": [2, 5], "similar": 5, "singl": 1, "solv": 6, "sourc": [2, 4, 5], "specif": 5, "specifi": 0, "sphinx": [2, 4], "ssl": 1, "standard": [0, 4], "start": 4, "static": [1, 4], "statu": [0, 1, 3, 4, 6], "status_cod": 0, "step": 2, "storag": 3, "store": 3, "style": 2, "submit": [2, 4], "success": 0, "suit": [4, 5], "sum": 3, "sun": 3, "support": [0, 4, 6], "swagger": 3, "system": [0, 1, 5, 6], "system_info": 0, "t": 4, "take": [0, 3, 5], "team": 2, "termin": 1, "test": 3, "test_dock": 4, "test_fil": 5, "test_redirect": 4, "text": 3, "thei": [1, 5], "thi": [0, 1, 2, 3, 4, 5], "threat": 6, "through": 3, "throughput": 5, "time": [1, 4, 5], "timestamp": 0, "togeth": [1, 4], "toggl": 3, "token": 0, "top": 3, "total": [0, 3], "totalresponsetim": 3, "track": 4, "trend": 3, "troubleshoot": [3, 6], "type": 0, "typic": 4, "ui": 3, "understand": [4, 6], "unit": 4, "unittest": 5, "up": [2, 3, 4, 6], "updat": [3, 5], "url": [0, 1], "us": [0, 3, 4, 5, 6], "usag": [1, 4, 5], "user": [1, 3], "user_guid": 6, "v1": 0, "valuabl": 3, "variabl": 3, "variou": [0, 3, 4, 5], "venv": [2, 4], "version": 3, "view": 3, "virtual": [2, 4], "visual": 3, "vnc": [0, 4, 6], "vnc_url": 0, "want": 4, "web": [3, 4, 6], "websocket": 0, "websockifi": 0, "were": 1, "what": 5, "when": [0, 3, 5], "whether": 4, "which": 3, "while": 6, "who": 4, "why": 5, "work": [1, 4], "write": 4, "written": [2, 5], "x": 0, "yml": [2, 4], "you": [0, 4], "your": [2, 4, 6], "your_api_kei": 0}, "titles": ["API Reference", "Architecture", "Contributing", "Health Dashboard", "Developer Guide", "Testing", "Welcome to bahtBrowse Documentation"], "titleterms": {"administr": 6, "api": [0, 1, 3, 4], "architectur": [1, 3, 4], "authent": 0, "auto": 3, "bahtbrows": 6, "browser": 0, "client": 0, "code": 2, "codebas": 4, "compon": [1, 3], "configur": [1, 3], "contain": 1, "continu": 5, "contribut": 2, "cor": 0, "coverag": 5, "dark": 3, "dashboard": [1, 3, 4], "data": 5, "develop": [2, 4, 6], "docker": 1, "document": [2, 3, 5, 6], "end": 5, "endpoint": 0, "enhanc": 3, "environ": [2, 4, 5], "error": 0, "featur": [3, 6], "firefox": 1, "frontend": 3, "futur": 3, "get": 2, "guid": [4, 6], "handl": 0, "health": [0, 1, 3, 4], "indic": 6, "integr": 5, "interfac": [1, 3], "introduct": 4, "kei": [3, 6], "librari": 0, "limit": 0, "mainten": 5, "mock": 5, "mode": 3, "monitor": 3, "next": 4, "nginx": 1, "overview": [0, 1, 3, 5], "pagin": 0, "perform": 5, "plugin": 1, "pull": 2, "rate": 0, "refer": [0, 4], "refresh": 3, "releas": 2, "request": 2, "resourc": 3, "respons": 3, "review": 2, "run": 5, "server": 1, "standard": 2, "start": 2, "step": 4, "structur": 4, "support": 3, "system": [3, 4], "tabl": 6, "test": [2, 4, 5], "time": 3, "track": 3, "unit": 5, "usag": 3, "user": 6, "version": 0, "vnc": 1, "webhook": 0, "welcom": 6, "workflow": 4, "write": 5}})
%% Bookmarks and hyperlinks
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxoptionshyperref}[2021/01/27 hyperref]

% to make pdf with correct encoded bookmarks in Japanese
% this should precede the hyperref package
\ifx\kanjiskip\@undefined
% for non-Japanese: make sure bookmarks are ok also with lualatex
  \PassOptionsToPackage{pdfencoding=unicode}{hyperref}
\else
  \RequirePackage{atbegshi}
  \ifx\ucs\@undefined
    \ifnum 42146=\euc"A4A2
      \AtBeginShipoutFirst{\special{pdf:tounicode EUC-UCS2}}
    \else
      \AtBeginShipoutFirst{\special{pdf:tounicode 90ms-RKSJ-UCS2}}
    \fi
  \else
    \AtBeginShipoutFirst{\special{pdf:tounicode UTF8-UCS2}}
  \fi
\fi

\ifx\@jsc@uplatextrue\@undefined\else
  \PassOptionsToPackage{setpagesize=false}{hyperref}
\fi

% These options can be overridden inside  'hyperref' key
% or by later use of \hypersetup.
\PassOptionsToPackage{colorlinks,breaklinks,%
 linkcolor=InnerLinkColor,filecolor=OuterLinkColor,%
 menucolor=OuterLinkColor,urlcolor=OuterLinkColor,%
 citecolor=InnerLinkColor}{hyperref}

\endinput

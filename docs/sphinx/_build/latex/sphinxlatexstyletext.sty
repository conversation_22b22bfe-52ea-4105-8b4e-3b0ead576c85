%% TEXT STYLING
%
% change this info string if making any custom modification
\ProvidesPackage{sphinxlatexstyletext}[2024/07/28 v8.1.0 text styling]

% 7.4.0 has moved all that is related to admonitions to sphinxlatexadmonitions.sty
% 8.1.0 has moved topic/contents/sidebar to sphinxlatexshadowbox.sty
% Most everything left here consists of macros which are part of the latex markup
% produced by the Sphinx LaTeX writer.

% Some custom font markup commands.
\protected\def\sphinxstrong#1{\textbf{#1}}
\protected\def\sphinxcode#1{\texttt{#1}}
\protected\def\sphinxbfcode#1{\textbf{\sphinxcode{#1}}}
\protected\def\sphinxemail#1{\textsf{#1}}
\protected\def\sphinxtablecontinued#1{\textsf{#1}}
\protected\def\sphinxtitleref#1{\emph{#1}}
\protected\def\sphinxmenuselection#1{\emph{#1}}
\protected\def\sphinxguilabel#1{\emph{#1}}
\protected\def\sphinxkeyboard#1{\sphinxcode{#1}}
\protected\def\sphinxaccelerator#1{\underline{#1}}
\protected\def\sphinxcrossref#1{\emph{#1}}
\protected\def\sphinxtermref#1{\emph{#1}}
\protected\def\sphinxsamedocref#1{\emph{#1}}
\protected\def\sphinxparam#1{\emph{#1}}
\protected\def\sphinxtypeparam#1{\emph{#1}}
% \optional is used for ``[, arg]``, i.e. desc_optional nodes.
\long\protected\def\sphinxoptional#1{%
  {\sphinxoptionalhook\textnormal{\Large[}}{#1}\hspace{0.5mm}{\textnormal{\Large]}}}
\let\sphinxoptionalhook\empty

% additional customizable styling
\def\sphinxstyleindexentry   #1{\texttt{#1}}
\def\sphinxstyleindexextra   #1{ (\emph{#1})}
\def\sphinxstyleindexpageref #1{, \pageref{#1}}
\def\sphinxstyleindexpagemain#1{\textbf{#1}}
\def\spxentry{\@backslashchar spxentry}% let to \sphinxstyleindexentry in index
\def\spxextra{\@backslashchar spxextra}% let to \sphinxstyleindexextra in index
\def\sphinxstyleindexlettergroup #1%
    {{\Large\sffamily#1}\nopagebreak\vspace{1mm}}
\def\sphinxstyleindexlettergroupDefault #1%
    {{\Large\sffamily\sphinxnonalphabeticalgroupname}\nopagebreak\vspace{1mm}}
\protected\def\sphinxstyleothertitle   #1{\textbf{#1}}
% \text.. commands do not allow multiple paragraphs
% attention, this one is not self-delimiting
\protected\def\sphinxstyletheadfamily    {\sffamily}
\protected\def\sphinxstyleemphasis     #1{\emph{#1}}
\protected\def\sphinxstyleliteralemphasis#1{\emph{\sphinxcode{#1}}}
\protected\def\sphinxstylestrong       #1{\textbf{#1}}
\protected\def\sphinxstyleliteralstrong#1{\sphinxbfcode{#1}}
\protected\def\sphinxstyleabbreviation #1{\textsc{#1}}
\protected\def\sphinxstyleliteralintitle#1{\sphinxcode{#1}}
\newcommand*\sphinxstylecodecontinued[1]{{\footnotesize(#1)}}%
\newcommand*\sphinxstylecodecontinues[1]{{\footnotesize(#1)}}%
% figure legend comes after caption and may contain arbitrary body elements
\newenvironment{sphinxlegend}{\par\small}{\par}
% reduce hyperref "Token not allowed in a PDF string" warnings on PDF builds
\AtBeginDocument{\pdfstringdefDisableCommands{%
% all "protected" macros possibly ending up in section titles should be here
% TODO: examine if \sphinxhref, \sphinxurl, \sphinnolinkurl should be handled
    \let\sphinxstyleemphasis        \@firstofone
    \let\sphinxstyleliteralemphasis \@firstofone
    \let\sphinxstylestrong          \@firstofone
    \let\sphinxstyleliteralstrong   \@firstofone
    \let\sphinxstyleabbreviation    \@firstofone
    \let\sphinxstyleliteralintitle  \@firstofone
    \let\sphinxupquote  \@firstofone
    \let\sphinxstrong   \@firstofone
    \let\sphinxcode     \@firstofone
    \let\sphinxbfcode   \@firstofone
    \let\sphinxemail    \@firstofone
    \let\sphinxcrossref \@firstofone
    \let\sphinxtermref  \@firstofone
    \let\sphinxsamedocref\@firstofone
    \let\sphinxhyphen\sphinxhyphenforbookmarks
    \def\PYG#1#2{#2}% (can not yet appear in section titles, but perhaps in future)
}}

% Special characters
%
\def\sphinxparamcomma{, }% by default separate parameters with comma + space
% If the signature is rendered with one line per param, this wil be used
% instead (this \texttt makes the comma slightly more distinctive).
\def\sphinxparamcommaoneperline{\texttt{,}}
%
% The \kern\z@ is to prevent en-dash and em-dash TeX ligatures.
% A linebreak can occur after the dash in regular text (this is
% normal behaviour of "-" in TeX, it is not related to \kern\z@).
%
% Parsed-literals and inline literals also use the \sphinxhyphen
% but linebreaks there are prevented due to monospace font family.
% (xelatex needs a special addition, cf. sphinxlatexliterals.sty)
%
% Inside code-blocks, dashes are escaped via another macro, from
% Pygments latex output (search for \PYGZhy in sphinxlatexliterals.sty),
% and are configured to allow linebreaks despite the monospace font.
% (the #1 swallows the {} from \sphinxhyphen{} mark-up)
\protected\def\sphinxhyphen#1{-\kern\z@}
\protected\def\sphinxhyphennobreak#1{\mbox{-}}
% The {} from texescape mark-up is kept, else -- gives en-dash in PDF bookmark
\def\sphinxhyphenforbookmarks{-}

% For curly braces inside \index macro
\def\sphinxleftcurlybrace{\{}
\def\sphinxrightcurlybrace{\}}

% Declare Unicode characters used by linux tree command to pdflatex utf8/utf8x
\def\spx@bd#1#2{%
  \leavevmode
  \begingroup
  \ifx\spx@bd@height   \@undefined\def\spx@bd@height{\baselineskip}\fi
  \ifx\spx@bd@width    \@undefined\setbox0\hbox{0}\def\spx@bd@width{\wd0 }\fi
  \ifx\spx@bd@thickness\@undefined\def\spx@bd@thickness{.6\p@}\fi
  \ifx\spx@bd@lower    \@undefined\def\spx@bd@lower{\dp\strutbox}\fi
  \lower\spx@bd@lower#1{#2}%
  \endgroup
}%
\@namedef{sphinx@u2500}% BOX DRAWINGS LIGHT HORIZONTAL
  {\spx@bd{\vbox to\spx@bd@height}
          {\vss\hrule\@height\spx@bd@thickness
                     \@width\spx@bd@width\vss}}%
\@namedef{sphinx@u2502}% BOX DRAWINGS LIGHT VERTICAL
  {\spx@bd{\hb@xt@\spx@bd@width}
          {\hss\vrule\@height\spx@bd@height
                     \@width \spx@bd@thickness\hss}}%
\@namedef{sphinx@u2514}% BOX DRAWINGS LIGHT UP AND RIGHT
  {\spx@bd{\hb@xt@\spx@bd@width}
          {\hss\raise.5\spx@bd@height
           \hb@xt@\z@{\hss\vrule\@height.5\spx@bd@height
                                \@width \spx@bd@thickness\hss}%
           \vbox to\spx@bd@height{\vss\hrule\@height\spx@bd@thickness
                                            \@width.5\spx@bd@width\vss}}}%
\@namedef{sphinx@u251C}% BOX DRAWINGS LIGHT VERTICAL AND RIGHT
  {\spx@bd{\hb@xt@\spx@bd@width}
          {\hss
           \hb@xt@\z@{\hss\vrule\@height\spx@bd@height
                                \@width \spx@bd@thickness\hss}%
           \vbox to\spx@bd@height{\vss\hrule\@height\spx@bd@thickness
                                            \@width.5\spx@bd@width\vss}}}%
\protected\def\sphinxunichar#1{\@nameuse{sphinx@u#1}}%

% Tell TeX about pathological hyphenation cases:
\hyphenation{Base-HTTP-Re-quest-Hand-ler}

\endinput

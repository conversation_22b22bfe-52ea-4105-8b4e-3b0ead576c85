%% Generated by Sphinx.
\def\sphinxdocclass{report}
\documentclass[a4paper,11pt,english]{sphinxmanual}
\ifdefined\pdfpxdimen
   \let\sphinxpxdimen\pdfpxdimen\else\newdimen\sphinxpxdimen
\fi \sphinxpxdimen=.75bp\relax
\ifdefined\pdfimageresolution
    \pdfimageresolution= \numexpr \dimexpr1in\relax/\sphinxpxdimen\relax
\fi
%% let collapsible pdf bookmarks panel have high depth per default
\PassOptionsToPackage{bookmarksdepth=5}{hyperref}

\PassOptionsToPackage{booktabs}{sphinx}
\PassOptionsToPackage{colorrows}{sphinx}

\PassOptionsToPackage{warn}{textcomp}
\usepackage[utf8]{inputenc}
\ifdefined\DeclareUnicodeCharacter
% support both utf8 and utf8x syntaxes
  \ifdefined\DeclareUnicodeCharacterAsOptional
    \def\sphinxDUC#1{\DeclareUnicodeCharacter{"#1}}
  \else
    \let\sphinxDUC\DeclareUnicodeCharacter
  \fi
  \sphinxDUC{00A0}{\nobreakspace}
  \sphinxDUC{2500}{\sphinxunichar{2500}}
  \sphinxDUC{2502}{\sphinxunichar{2502}}
  \sphinxDUC{2514}{\sphinxunichar{2514}}
  \sphinxDUC{251C}{\sphinxunichar{251C}}
  \sphinxDUC{2572}{\textbackslash}
\fi
\usepackage{cmap}
\usepackage[T1]{fontenc}
\usepackage{amsmath,amssymb,amstext}
\usepackage{babel}



\usepackage{tgtermes}
\usepackage{tgheros}
\renewcommand{\ttdefault}{txtt}



\usepackage[Bjarne]{fncychap}
\usepackage{sphinx}

\fvset{fontsize=auto}
\usepackage{geometry}


% Include hyperref last.
\usepackage{hyperref}
% Fix anchor placement for figures with captions.
\usepackage{hypcap}% it must be loaded after hyperref.
% Set up styles of URL: it should be placed after hyperref.
\urlstyle{same}

\addto\captionsenglish{\renewcommand{\contentsname}{User Documentation}}

\usepackage{sphinxmessages}
\setcounter{tocdepth}{1}



\title{bahtBrowse Documentation}
\date{Jan 01, 1980}
\release{1.0.0}
\author{bahtBrowse Team}
\newcommand{\sphinxlogo}{\vbox{}}
\renewcommand{\releasename}{Release}
\makeindex
\begin{document}

\ifdefined\shorthandoff
  \ifnum\catcode`\=\string=\active\shorthandoff{=}\fi
  \ifnum\catcode`\"=\active\shorthandoff{"}\fi
\fi

\pagestyle{empty}
\sphinxmaketitle
\pagestyle{plain}
\sphinxtableofcontents
\pagestyle{normal}
\phantomsection\label{\detokenize{index::doc}}


\sphinxAtStartPar
\sphinxstylestrong{bahtBrowse} is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.

\noindent{\hspace*{\fill}\sphinxincludegraphics[width=400\sphinxpxdimen]{{_static/bahtbrowse_logo}.png}\hspace*{\fill}}


\chapter{Key Features}
\label{\detokenize{index:key-features}}\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Secure Isolation}: Browse the web in isolated Docker containers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Multiple Browser Support}: Firefox and Chromium support

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Seamless Integration}: Firefox plugin for easy access

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Interface}: Remote access to containerized browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: Manage browser sessions programmatically

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Scalable Architecture}: Deploy for individual users or enterprise environments

\end{itemize}


\chapter{User Guides}
\label{\detokenize{index:user-guides}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{getting_started/index::doc}]{\sphinxcrossref{\DUrole{doc}{Getting Started with bahtBrowse}}}}: Get up and running quickly

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{user_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{User Guide}}}}: Learn how to use bahtBrowse effectively

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{troubleshooting/index::doc}]{\sphinxcrossref{\DUrole{doc}{Troubleshooting}}}}: Solve common issues

\end{itemize}


\chapter{Administrator Guides}
\label{\detokenize{index:administrator-guides}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{admin_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{Administrator Guide}}}}: Deploy and manage bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}}: Configure bahtBrowse for your environment

\end{itemize}


\chapter{Developer Guides}
\label{\detokenize{index:developer-guides}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{Developer Guide}}}}: Understand the architecture and contribute

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{api_reference/index::doc}]{\sphinxcrossref{\DUrole{doc}{API Reference}}}}: API documentation for developers

\end{itemize}

\sphinxstepscope


\section{Getting Started with bahtBrowse}
\label{\detokenize{getting_started/index:getting-started-with-bahtbrowse}}\label{\detokenize{getting_started/index:getting-started}}\label{\detokenize{getting_started/index::doc}}
\sphinxAtStartPar
This section will help you get started with bahtBrowse, a secure Remote Browser Isolation (RBI) system.

\sphinxstepscope


\subsection{Installation Guide}
\label{\detokenize{getting_started/installation:installation-guide}}\label{\detokenize{getting_started/installation:installation}}\label{\detokenize{getting_started/installation::doc}}
\sphinxAtStartPar
This guide will walk you through the process of installing bahtBrowse on your system.


\subsubsection{Prerequisites}
\label{\detokenize{getting_started/installation:prerequisites}}
\sphinxAtStartPar
Before installing bahtBrowse, ensure your system meets the {\hyperref[\detokenize{getting_started/index:getting-started}]{\sphinxcrossref{\DUrole{std}{\DUrole{std-ref}{system requirements}}}}}.


\subsubsection{Installation Methods}
\label{\detokenize{getting_started/installation:installation-methods}}
\sphinxAtStartPar
There are several ways to install bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Compose} (Recommended)

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Manual Installation}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Development Setup}

\end{enumerate}


\paragraph{Docker Compose Installation}
\label{\detokenize{getting_started/installation:docker-compose-installation}}
\sphinxAtStartPar
The recommended way to install bahtBrowse is using Docker Compose, which simplifies the process of managing multiple containers.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build and start the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\sphinxAtStartPar
This will build the necessary Docker images and start the containers in detached mode.

\item {} 
\sphinxAtStartPar
Verify the installation:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see the following containers running:
\begin{itemize}
\item {} 
\sphinxAtStartPar
bahtbrowse\sphinxhyphen{}firefox

\item {} 
\sphinxAtStartPar
bahtbrowse\sphinxhyphen{}api

\end{itemize}

\item {} 
\sphinxAtStartPar
Test the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }http://localhost:8082/test\PYGZhy{}connection
\end{sphinxVerbatim}

\sphinxAtStartPar
You should receive a JSON response indicating that the service is available.

\end{enumerate}


\paragraph{Manual Installation}
\label{\detokenize{getting_started/installation:manual-installation}}
\sphinxAtStartPar
If you prefer to have more control over the installation process, you can install bahtBrowse manually.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build the Firefox container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }build\PYG{+w}{ }\PYGZhy{}t\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker/Dockerfile\PYG{+w}{ }.
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build the API server container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }build\PYG{+w}{ }\PYGZhy{}t\PYG{+w}{ }bahtbrowse\PYGZhy{}api\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker/Dockerfile.api\PYG{+w}{ }.
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Run the Firefox container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }run\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYGZhy{}\PYGZhy{}name\PYG{o}{=}bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }\PYGZhy{}p\PYG{+w}{ }\PYG{l+m}{6081}:6080\PYG{+w}{ }\PYGZhy{}p\PYG{+w}{ }\PYG{l+m}{8080}:80\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Run the API server container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }run\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYGZhy{}\PYGZhy{}name\PYG{o}{=}bahtbrowse\PYGZhy{}api\PYG{+w}{ }\PYGZhy{}p\PYG{+w}{ }\PYG{l+m}{8082}:8082\PYG{+w}{ }\PYGZhy{}\PYGZhy{}link\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }bahtbrowse\PYGZhy{}api
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Verify the installation:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see both containers running.

\end{enumerate}


\paragraph{Development Setup}
\label{\detokenize{getting_started/installation:development-setup}}
\sphinxAtStartPar
For development purposes, you may want to set up bahtBrowse with additional tools and configurations.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Create a Python virtual environment:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }\PYGZhy{}m\PYG{+w}{ }venv\PYG{+w}{ }venv
\PYG{n+nb}{source}\PYG{+w}{ }venv/bin/activate
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Install the development dependencies:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}e\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}.[dev]\PYGZdq{}}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Set up pre\sphinxhyphen{}commit hooks:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pre\PYGZhy{}commit\PYG{+w}{ }install
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build and run the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.yml\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.dev.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Run the tests to verify the setup:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\end{enumerate}


\subsubsection{Firefox Plugin Installation}
\label{\detokenize{getting_started/installation:firefox-plugin-installation}}
\sphinxAtStartPar
To use bahtBrowse with Firefox, you need to install the Firefox plugin.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Build the Firefox plugin:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }firefox\PYGZus{}plugin
./build.sh
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Install the plugin in Firefox:
\begin{enumerate}
\sphinxsetlistlabels{\alph}{enumii}{enumiii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:debugging}}

\item {} 
\sphinxAtStartPar
Click on “This Firefox”

\item {} 
\sphinxAtStartPar
Click on “Load Temporary Add\sphinxhyphen{}on…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}

\sphinxAtStartPar
Alternatively, you can install the plugin permanently:
\begin{enumerate}
\sphinxsetlistlabels{\alph}{enumii}{enumiii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Click the gear icon and select “Install Add\sphinxhyphen{}on From File…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}

\end{enumerate}


\subsubsection{Configuration}
\label{\detokenize{getting_started/installation:configuration}}
\sphinxAtStartPar
After installation, you may want to configure bahtBrowse to suit your needs.


\paragraph{Basic Configuration}
\label{\detokenize{getting_started/installation:basic-configuration}}
\sphinxAtStartPar
The basic configuration can be done through environment variables or a configuration file.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create a \sphinxcode{\sphinxupquote{.env}} file in the project root directory:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} API Server Configuration}
\PYG{n+nv}{API\PYGZus{}PORT}\PYG{o}{=}\PYG{l+m}{8082}
\PYG{n+nv}{API\PYGZus{}HOST}\PYG{o}{=}\PYG{l+m}{0}.0.0.0

\PYG{c+c1}{\PYGZsh{} VNC Configuration}
\PYG{n+nv}{VNC\PYGZus{}PORT}\PYG{o}{=}\PYG{l+m}{6080}

\PYG{c+c1}{\PYGZsh{} Browser Configuration}
\PYG{n+nv}{DEFAULT\PYGZus{}BROWSER}\PYG{o}{=}firefox

\PYG{c+c1}{\PYGZsh{} Logging Configuration}
\PYG{n+nv}{LOG\PYGZus{}LEVEL}\PYG{o}{=}INFO
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Restart the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }down
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}


\paragraph{Advanced Configuration}
\label{\detokenize{getting_started/installation:advanced-configuration}}
\sphinxAtStartPar
For advanced configuration, refer to the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.


\subsubsection{Troubleshooting}
\label{\detokenize{getting_started/installation:troubleshooting}}
\sphinxAtStartPar
If you encounter issues during installation, check the following:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Issues}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Ensure Docker is running: \sphinxcode{\sphinxupquote{systemctl status docker}}

\item {} 
\sphinxAtStartPar
Check Docker logs: \sphinxcode{\sphinxupquote{docker logs bahtbrowse\sphinxhyphen{}firefox}}

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Issues}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Verify port mappings: \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose ps}}

\item {} 
\sphinxAtStartPar
Check if ports are in use: \sphinxcode{\sphinxupquote{netstat \sphinxhyphen{}tuln | grep 8082}}

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Permission Issues}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Ensure you have permission to run Docker: \sphinxcode{\sphinxupquote{groups | grep docker}}

\item {} 
\sphinxAtStartPar
Check file permissions: \sphinxcode{\sphinxupquote{ls \sphinxhyphen{}la}}

\end{itemize}

\end{enumerate}

\sphinxAtStartPar
For more troubleshooting tips, see the {\hyperref[\detokenize{troubleshooting/index::doc}]{\sphinxcrossref{\DUrole{doc}{Troubleshooting}}}} section.


\subsubsection{Next Steps}
\label{\detokenize{getting_started/installation:next-steps}}
\sphinxAtStartPar
Now that you have installed bahtBrowse, you can:
\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{getting_started/quick_start::doc}]{\sphinxcrossref{\DUrole{doc}{Quick Start Guide}}}}: Learn how to use bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{user_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{User Guide}}}}: Explore the user guide

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}}: Configure bahtBrowse for your environment

\end{itemize}

\sphinxstepscope


\subsection{Quick Start Guide}
\label{\detokenize{getting_started/quick_start:quick-start-guide}}\label{\detokenize{getting_started/quick_start:quick-start}}\label{\detokenize{getting_started/quick_start::doc}}
\sphinxAtStartPar
This guide will help you quickly get started with bahtBrowse after installation.


\subsubsection{Overview}
\label{\detokenize{getting_started/quick_start:overview}}
\sphinxAtStartPar
bahtBrowse provides a secure way to browse the web by isolating browsers in Docker containers. This quick start guide will show you how to:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Start the bahtBrowse service

\item {} 
\sphinxAtStartPar
Access websites through bahtBrowse

\item {} 
\sphinxAtStartPar
Use the Firefox plugin

\item {} 
\sphinxAtStartPar
Understand the basic workflow

\end{enumerate}


\subsubsection{Starting bahtBrowse}
\label{\detokenize{getting_started/quick_start:starting-bahtbrowse}}
\sphinxAtStartPar
If you’ve followed the {\hyperref[\detokenize{getting_started/installation::doc}]{\sphinxcrossref{\DUrole{doc}{Installation Guide}}}} guide, you should have bahtBrowse installed on your system.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Start the bahtBrowse service:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Verify that the service is running:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see the containers running with status “Up”.

\item {} 
\sphinxAtStartPar
Test the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }http://localhost:8082/test\PYGZhy{}connection
\end{sphinxVerbatim}

\sphinxAtStartPar
You should receive a JSON response indicating that the service is available.

\end{enumerate}


\subsubsection{Accessing Websites}
\label{\detokenize{getting_started/quick_start:accessing-websites}}
\sphinxAtStartPar
There are several ways to access websites through bahtBrowse:


\paragraph{Using the API Directly}
\label{\detokenize{getting_started/quick_start:using-the-api-directly}}\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open a web browser and navigate to:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com
\end{sphinxVerbatim}

\sphinxAtStartPar
Replace \sphinxcode{\sphinxupquote{https://example.com}} with the URL you want to visit.

\item {} 
\sphinxAtStartPar
You will be redirected to the VNC interface where you can interact with the isolated browser.

\end{enumerate}


\paragraph{Using the Firefox Plugin}
\label{\detokenize{getting_started/quick_start:using-the-firefox-plugin}}
\sphinxAtStartPar
If you’ve installed the Firefox plugin as described in the {\hyperref[\detokenize{getting_started/installation::doc}]{\sphinxcrossref{\DUrole{doc}{Installation Guide}}}} guide, you can use it to access websites through bahtBrowse.
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Click on the bahtBrowse icon in the toolbar

\item {} 
\sphinxAtStartPar
Enter the URL you want to visit or click “Browse Current Page Securely”

\item {} 
\sphinxAtStartPar
The page will open in a new tab, running in the isolated browser container

\end{enumerate}

\begin{sphinxadmonition}{note}{Note:}
\sphinxAtStartPar
The Firefox plugin provides the most seamless experience for using bahtBrowse.
\end{sphinxadmonition}


\subsubsection{Understanding the Workflow}
\label{\detokenize{getting_started/quick_start:understanding-the-workflow}}
\sphinxAtStartPar
Here’s what happens when you access a website through bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Your request is sent to the bahtBrowse API server

\item {} 
\sphinxAtStartPar
The API server creates a new browser session in a Docker container

\item {} 
\sphinxAtStartPar
The URL is loaded in the containerized browser

\item {} 
\sphinxAtStartPar
You are redirected to a VNC interface where you can interact with the browser

\item {} 
\sphinxAtStartPar
All browsing activity is isolated in the container, protecting your main system

\end{enumerate}

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/workflow_diagram}.png}
\caption{bahtBrowse Workflow Diagram}\label{\detokenize{getting_started/quick_start:id1}}\end{figure}


\subsubsection{Browser Selection}
\label{\detokenize{getting_started/quick_start:browser-selection}}
\sphinxAtStartPar
bahtBrowse supports multiple browsers. You can specify which browser to use by adding the \sphinxcode{\sphinxupquote{browser}} parameter to the URL:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=firefox
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If no browser is specified, Firefox will be used by default.


\subsubsection{Session Management}
\label{\detokenize{getting_started/quick_start:session-management}}
\sphinxAtStartPar
Each browsing session is isolated in its own container. Sessions are automatically created when you access a URL through bahtBrowse.

\sphinxAtStartPar
Sessions are identified by a unique session ID, which is included in the URL when you are redirected to the VNC interface.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:6081/vnc1/run?session=12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc
\end{sphinxVerbatim}

\sphinxAtStartPar
Sessions are automatically cleaned up after a period of inactivity (default: 30 minutes).


\subsubsection{Next Steps}
\label{\detokenize{getting_started/quick_start:next-steps}}
\sphinxAtStartPar
Now that you’ve got the basics of bahtBrowse, you can:
\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{user_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{User Guide}}}}: Learn more about using bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}}: Configure bahtBrowse for your environment

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{Developer Guide}}}}: Understand the architecture and contribute to bahtBrowse

\end{itemize}


\subsection{Introduction}
\label{\detokenize{getting_started/index:introduction}}
\sphinxAtStartPar
bahtBrowse is a containerized browser isolation solution that provides secure web browsing by running browsers in isolated Docker containers. This approach protects your main system from web\sphinxhyphen{}based threats while providing a seamless browsing experience.


\subsection{System Requirements}
\label{\detokenize{getting_started/index:system-requirements}}
\sphinxAtStartPar
Before installing bahtBrowse, ensure your system meets the following requirements:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Operating System}: Linux (Ubuntu 20.04 or later recommended)

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker}: Docker 20.10 or later

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Memory}: At least 4GB RAM

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Storage}: At least 10GB free disk space

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network}: Stable internet connection

\end{itemize}

\sphinxAtStartPar
For Firefox plugin users:
* \sphinxstylestrong{Firefox}: Version 78 or later

\sphinxAtStartPar
For development:
* \sphinxstylestrong{Python}: 3.8 or later
* \sphinxstylestrong{Git}: 2.25 or later


\subsection{Quick Overview}
\label{\detokenize{getting_started/index:quick-overview}}
\sphinxAtStartPar
bahtBrowse consists of several components:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Containers}: Isolated environments running Firefox or Chromium browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: Manages browser sessions and handles requests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Interface}: Provides remote access to containerized browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Plugin}: Enables seamless integration with Firefox

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Configuration}: Handles proxying and serving static files

\end{enumerate}


\subsection{Next Steps}
\label{\detokenize{getting_started/index:next-steps}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{getting_started/installation::doc}]{\sphinxcrossref{\DUrole{doc}{Installation Guide}}}}: Detailed installation instructions

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{getting_started/quick_start::doc}]{\sphinxcrossref{\DUrole{doc}{Quick Start Guide}}}}: Quick start guide for first\sphinxhyphen{}time users

\end{itemize}

\sphinxstepscope


\section{User Guide}
\label{\detokenize{user_guide/index:user-guide}}\label{\detokenize{user_guide/index:id1}}\label{\detokenize{user_guide/index::doc}}
\sphinxAtStartPar
This user guide provides detailed information on how to use bahtBrowse effectively.

\sphinxstepscope


\subsection{Firefox Plugin}
\label{\detokenize{user_guide/firefox_plugin:firefox-plugin}}\label{\detokenize{user_guide/firefox_plugin:id1}}\label{\detokenize{user_guide/firefox_plugin::doc}}
\sphinxAtStartPar
The bahtBrowse Firefox plugin provides a seamless way to access the bahtBrowse Remote Browser Isolation (RBI) service directly from your Firefox browser.


\subsubsection{Installation}
\label{\detokenize{user_guide/firefox_plugin:installation}}
\sphinxAtStartPar
There are two ways to install the bahtBrowse Firefox plugin:


\paragraph{Temporary Installation}
\label{\detokenize{user_guide/firefox_plugin:temporary-installation}}
\sphinxAtStartPar
For testing or development purposes, you can install the plugin temporarily:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:debugging}}

\item {} 
\sphinxAtStartPar
Click on “This Firefox”

\item {} 
\sphinxAtStartPar
Click on “Load Temporary Add\sphinxhyphen{}on…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}

\begin{sphinxadmonition}{note}{Note:}
\sphinxAtStartPar
Temporary installations will be removed when Firefox is closed.
\end{sphinxadmonition}


\paragraph{Permanent Installation}
\label{\detokenize{user_guide/firefox_plugin:permanent-installation}}
\sphinxAtStartPar
For regular use, you can install the plugin permanently:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Click the gear icon and select “Install Add\sphinxhyphen{}on From File…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}


\subsubsection{Configuration}
\label{\detokenize{user_guide/firefox_plugin:configuration}}
\sphinxAtStartPar
After installation, you need to configure the plugin to connect to your bahtBrowse service:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Click on the bahtBrowse icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Click on “Options” (gear icon)

\item {} 
\sphinxAtStartPar
Enter the following settings:
\sphinxhyphen{} \sphinxstylestrong{Host}: The hostname or IP address of your bahtBrowse service (default: \sphinxcode{\sphinxupquote{localhost}})
\sphinxhyphen{} \sphinxstylestrong{Port}: The port number of your bahtBrowse API server (default: \sphinxcode{\sphinxupquote{8082}})
\sphinxhyphen{} \sphinxstylestrong{Open in New Tab}: Whether to open bahtBrowse sessions in a new tab (default: \sphinxcode{\sphinxupquote{true}})
\sphinxhyphen{} \sphinxstylestrong{Whitelist/Blacklist}: Configure which sites should be opened in bahtBrowse

\end{enumerate}

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/firefox_plugin_options}.png}
\caption{Firefox Plugin Options}\label{\detokenize{user_guide/firefox_plugin:id2}}\end{figure}


\subsubsection{Using the Plugin}
\label{\detokenize{user_guide/firefox_plugin:using-the-plugin}}
\sphinxAtStartPar
The bahtBrowse Firefox plugin provides several ways to access websites through the bahtBrowse service:


\paragraph{Toolbar Button}
\label{\detokenize{user_guide/firefox_plugin:toolbar-button}}
\sphinxAtStartPar
The simplest way to use the plugin is through the toolbar button:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Click on the bahtBrowse icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Enter the URL you want to visit or click “Browse Current Page Securely”

\item {} 
\sphinxAtStartPar
The page will open in a new tab, running in the isolated browser container

\end{enumerate}

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/firefox_plugin_popup}.png}
\caption{Firefox Plugin Popup}\label{\detokenize{user_guide/firefox_plugin:id3}}\end{figure}


\paragraph{Context Menu}
\label{\detokenize{user_guide/firefox_plugin:context-menu}}
\sphinxAtStartPar
You can also access bahtBrowse through the context menu:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Right\sphinxhyphen{}click on a link or anywhere on a page

\item {} 
\sphinxAtStartPar
Select “Open in 10baht Browse” from the context menu

\item {} 
\sphinxAtStartPar
The link or page will open in a new tab, running in the isolated browser container

\end{enumerate}

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/firefox_plugin_context_menu}.png}
\caption{Firefox Plugin Context Menu}\label{\detokenize{user_guide/firefox_plugin:id4}}\end{figure}


\paragraph{Keyboard Shortcuts}
\label{\detokenize{user_guide/firefox_plugin:keyboard-shortcuts}}
\sphinxAtStartPar
The plugin also provides keyboard shortcuts for quick access:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Ctrl+Shift+B}: Open the current page in bahtBrowse

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Ctrl+Shift+L}: Open the bahtBrowse popup

\end{itemize}

\sphinxAtStartPar
You can customize these shortcuts in Firefox’s keyboard shortcuts settings.


\subsubsection{Connection Status}
\label{\detokenize{user_guide/firefox_plugin:connection-status}}
\sphinxAtStartPar
The plugin icon in the toolbar indicates the connection status to the bahtBrowse service:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Blue Icon}: The service is available

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Gray Icon}: The service is not available

\end{itemize}

\sphinxAtStartPar
You can click on the icon to check the connection status and troubleshoot any issues.


\subsubsection{Troubleshooting}
\label{\detokenize{user_guide/firefox_plugin:troubleshooting}}
\sphinxAtStartPar
If you encounter issues with the Firefox plugin, try the following:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check Connection}: Ensure that the bahtBrowse service is running and accessible

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check Configuration}: Verify that the host and port settings are correct

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check Browser Console}: Open the browser console (F12) and look for any error messages

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reinstall Plugin}: Try reinstalling the plugin

\end{enumerate}

\sphinxAtStartPar
For more troubleshooting tips, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\subsubsection{Advanced Features}
\label{\detokenize{user_guide/firefox_plugin:advanced-features}}
\sphinxAtStartPar
The bahtBrowse Firefox plugin provides several advanced features:


\paragraph{Browser Selection}
\label{\detokenize{user_guide/firefox_plugin:browser-selection}}
\sphinxAtStartPar
You can specify which browser to use for each session:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Click on the bahtBrowse icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Click on “Options” (gear icon)

\item {} 
\sphinxAtStartPar
Select the default browser (Firefox or Chromium)

\item {} 
\sphinxAtStartPar
You can also override this setting for individual sessions by using the dropdown in the popup

\end{enumerate}


\paragraph{Whitelist and Blacklist}
\label{\detokenize{user_guide/firefox_plugin:whitelist-and-blacklist}}
\sphinxAtStartPar
You can configure which sites should be opened in bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Click on the bahtBrowse icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Click on “Options” (gear icon)

\item {} 
\sphinxAtStartPar
Enable whitelist or blacklist

\item {} 
\sphinxAtStartPar
Add sites to the list (one per line)

\end{enumerate}

\sphinxAtStartPar
With whitelist enabled, only sites in the list will be opened in bahtBrowse. With blacklist enabled, sites in the list will not be opened in bahtBrowse.


\paragraph{Logging}
\label{\detokenize{user_guide/firefox_plugin:logging}}
\sphinxAtStartPar
The plugin logs various events to help with debugging:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open the browser console (F12)

\item {} 
\sphinxAtStartPar
Filter for “bahtBrowse” to see plugin\sphinxhyphen{}related logs

\end{enumerate}

\sphinxAtStartPar
You can also enable verbose logging in the plugin options for more detailed logs.


\subsubsection{Development}
\label{\detokenize{user_guide/firefox_plugin:development}}
\sphinxAtStartPar
If you want to modify or contribute to the Firefox plugin, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.

\sphinxstepscope


\subsection{Secure Browsing with bahtBrowse}
\label{\detokenize{user_guide/browsing:secure-browsing-with-bahtbrowse}}\label{\detokenize{user_guide/browsing:browsing}}\label{\detokenize{user_guide/browsing::doc}}
\sphinxAtStartPar
This guide explains how to use bahtBrowse for secure web browsing, including direct API access and command\sphinxhyphen{}line usage.


\subsubsection{Introduction}
\label{\detokenize{user_guide/browsing:introduction}}
\sphinxAtStartPar
bahtBrowse provides a secure browsing experience by isolating web browsing activities in Docker containers. This approach protects your main system from web\sphinxhyphen{}based threats while providing a seamless browsing experience.

\sphinxAtStartPar
While the {\hyperref[\detokenize{user_guide/firefox_plugin::doc}]{\sphinxcrossref{\DUrole{doc}{Firefox Plugin}}}} provides the most user\sphinxhyphen{}friendly way to access bahtBrowse, you can also use the API directly or through the command line for more advanced use cases.


\subsubsection{Direct API Access}
\label{\detokenize{user_guide/browsing:direct-api-access}}
\sphinxAtStartPar
You can access websites through bahtBrowse by using the API directly.


\paragraph{Basic Usage}
\label{\detokenize{user_guide/browsing:basic-usage}}
\sphinxAtStartPar
To access a website through bahtBrowse, simply open the following URL in your browser:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com
\end{sphinxVerbatim}

\sphinxAtStartPar
Replace \sphinxcode{\sphinxupquote{https://example.com}} with the URL you want to visit.

\sphinxAtStartPar
This will:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create a new browser session in a Docker container

\item {} 
\sphinxAtStartPar
Load the specified URL in the containerized browser

\item {} 
\sphinxAtStartPar
Redirect you to the VNC interface where you can interact with the browser

\end{enumerate}


\paragraph{Advanced Parameters}
\label{\detokenize{user_guide/browsing:advanced-parameters}}
\sphinxAtStartPar
The API supports several parameters to customize your browsing experience:

\sphinxAtStartPar
Example with multiple parameters:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=chromium\PYGZam{}mode=compatibility
\end{sphinxVerbatim}


\paragraph{POST Requests}
\label{\detokenize{user_guide/browsing:post-requests}}
\sphinxAtStartPar
You can also use POST requests to access websites through bahtBrowse:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}url=https://example.com\PYGZam{}browser=firefox\PYGZdq{}}\PYG{+w}{ }http://localhost:8082/browse/
\end{sphinxVerbatim}

\sphinxAtStartPar
This is useful for scripts or applications that need to programmatically access websites through bahtBrowse.


\subsubsection{Command Line Usage}
\label{\detokenize{user_guide/browsing:command-line-usage}}
\sphinxAtStartPar
You can use the command line to interact with bahtBrowse for more advanced use cases.


\paragraph{Accessing Websites}
\label{\detokenize{user_guide/browsing:accessing-websites}}
\sphinxAtStartPar
To access a website through bahtBrowse from the command line:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }\PYGZhy{}L\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/browse/?url=https://example.com\PYGZdq{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{\sphinxhyphen{}L}} flag tells curl to follow redirects, which is necessary because the API will redirect to the VNC interface.


\paragraph{Testing Connection}
\label{\detokenize{user_guide/browsing:testing-connection}}
\sphinxAtStartPar
To test the connection to the bahtBrowse service:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }http://localhost:8082/test\PYGZhy{}connection
\end{sphinxVerbatim}

\sphinxAtStartPar
This will return a JSON response indicating whether the service is available.


\paragraph{Logging Console Messages}
\label{\detokenize{user_guide/browsing:logging-console-messages}}
\sphinxAtStartPar
To log browser console messages:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}H\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Content\PYGZhy{}Type: application/json\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}\PYGZob{}\PYGZdq{}level\PYGZdq{}: \PYGZdq{}info\PYGZdq{}, \PYGZdq{}message\PYGZdq{}: \PYGZdq{}Test message\PYGZdq{}, \PYGZdq{}url\PYGZdq{}: \PYGZdq{}https://example.com\PYGZdq{}, \PYGZdq{}session\PYGZus{}id\PYGZdq{}: \PYGZdq{}12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc\PYGZdq{}\PYGZcb{}\PYGZsq{}}\PYG{+w}{ }http://localhost:8082/log\PYGZhy{}console
\end{sphinxVerbatim}

\sphinxAtStartPar
This is useful for debugging or monitoring browser activity.


\paragraph{Managing Downloads}
\label{\detokenize{user_guide/browsing:managing-downloads}}
\sphinxAtStartPar
To list downloaded files:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }http://localhost:8082/downloads\PYGZhy{}api/list
\end{sphinxVerbatim}

\sphinxAtStartPar
To delete a downloaded file:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}H\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Content\PYGZhy{}Type: application/json\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}\PYGZob{}\PYGZdq{}filename\PYGZdq{}: \PYGZdq{}example.pdf\PYGZdq{}\PYGZcb{}\PYGZsq{}}\PYG{+w}{ }http://localhost:8082/downloads\PYGZhy{}api/delete
\end{sphinxVerbatim}


\subsubsection{VNC Interface}
\label{\detokenize{user_guide/browsing:vnc-interface}}
\sphinxAtStartPar
When you access a website through bahtBrowse, you are redirected to the VNC interface where you can interact with the containerized browser.

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/vnc_interface}.png}
\caption{VNC Interface}\label{\detokenize{user_guide/browsing:id1}}\end{figure}

\sphinxAtStartPar
The VNC interface provides a graphical interface to the browser running in the container. You can interact with it just like you would with a regular browser.


\paragraph{Navigation}
\label{\detokenize{user_guide/browsing:navigation}}
\sphinxAtStartPar
The VNC interface provides a toolbar at the top with navigation controls:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Back}: Go back to the previous page

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Forward}: Go forward to the next page

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Refresh}: Refresh the current page

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Home}: Go to the home page

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Address Bar}: Enter a URL to navigate to

\end{itemize}


\paragraph{Keyboard and Mouse}
\label{\detokenize{user_guide/browsing:keyboard-and-mouse}}
\sphinxAtStartPar
You can use your keyboard and mouse to interact with the browser in the VNC interface:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Mouse}: Click, double\sphinxhyphen{}click, right\sphinxhyphen{}click, scroll

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Keyboard}: Type, use keyboard shortcuts

\end{itemize}

\begin{sphinxadmonition}{note}{Note:}
\sphinxAtStartPar
Some keyboard shortcuts may be intercepted by your local browser or operating system. In such cases, you can use the on\sphinxhyphen{}screen keyboard provided by the VNC interface.
\end{sphinxadmonition}


\paragraph{Copy and Paste}
\label{\detokenize{user_guide/browsing:copy-and-paste}}
\sphinxAtStartPar
To copy text from the containerized browser:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Select the text in the containerized browser

\item {} 
\sphinxAtStartPar
Right\sphinxhyphen{}click and select “Copy”

\end{enumerate}

\sphinxAtStartPar
To paste text into the containerized browser:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Copy the text in your local environment

\item {} 
\sphinxAtStartPar
Right\sphinxhyphen{}click in the containerized browser and select “Paste”

\end{enumerate}

\begin{sphinxadmonition}{note}{Note:}
\sphinxAtStartPar
Copy and paste functionality may be limited for security reasons. If you encounter issues, try using the clipboard provided by the VNC interface.
\end{sphinxadmonition}


\paragraph{File Downloads}
\label{\detokenize{user_guide/browsing:file-downloads}}
\sphinxAtStartPar
When you download a file in the containerized browser, it is saved in the container. You can access downloaded files through the downloads manager:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{http://localhost:8082/downloads}}

\item {} 
\sphinxAtStartPar
You will see a list of downloaded files

\item {} 
\sphinxAtStartPar
Click on a file to download it to your local system

\end{enumerate}


\subsubsection{Browser Selection}
\label{\detokenize{user_guide/browsing:browser-selection}}
\sphinxAtStartPar
bahtBrowse supports multiple browsers. You can specify which browser to use by adding the \sphinxcode{\sphinxupquote{browser}} parameter to the URL:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=firefox
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If no browser is specified, Firefox will be used by default.

\sphinxAtStartPar
Each browser has its own advantages and limitations:


\paragraph{Firefox}
\label{\detokenize{user_guide/browsing:firefox}}\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Advantages}: Better compatibility with most websites, more features

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Limitations}: Slightly higher resource usage

\end{itemize}


\paragraph{Chromium}
\label{\detokenize{user_guide/browsing:chromium}}\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Advantages}: Faster startup, lower resource usage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Limitations}: May have compatibility issues with some websites

\end{itemize}


\subsubsection{Browsing Modes}
\label{\detokenize{user_guide/browsing:browsing-modes}}
\sphinxAtStartPar
bahtBrowse supports different browsing modes to optimize for different use cases:


\paragraph{Normal Mode}
\label{\detokenize{user_guide/browsing:normal-mode}}
\sphinxAtStartPar
The default mode, suitable for most websites:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}mode=normal
\end{sphinxVerbatim}


\paragraph{Compatibility Mode}
\label{\detokenize{user_guide/browsing:compatibility-mode}}
\sphinxAtStartPar
Optimized for complex websites that may have compatibility issues:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}mode=compatibility
\end{sphinxVerbatim}

\sphinxAtStartPar
This mode enables additional compatibility features and may improve performance on complex websites.


\subsubsection{Security Considerations}
\label{\detokenize{user_guide/browsing:security-considerations}}
\sphinxAtStartPar
While bahtBrowse provides a secure browsing experience by isolating browsers in containers, there are still some security considerations to keep in mind:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Traffic}: Traffic between your browser and the bahtBrowse service is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Isolation}: Each session is isolated, but sessions from the same user are not isolated from each other. Be cautious when browsing sensitive websites in multiple sessions.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Container Escape}: While unlikely, container escape vulnerabilities could potentially expose your system. Keep your Docker installation up to date.

\end{itemize}

\sphinxAtStartPar
For more information on security considerations, see the \DUrole{xref}{\DUrole{std}{\DUrole{std-doc}{../admin\_guide/security}}} section.


\subsubsection{Troubleshooting}
\label{\detokenize{user_guide/browsing:troubleshooting}}
\sphinxAtStartPar
If you encounter issues while browsing with bahtBrowse, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section for solutions to common problems.


\subsection{Introduction}
\label{\detokenize{user_guide/index:introduction}}
\sphinxAtStartPar
bahtBrowse is designed to provide a secure browsing experience by isolating web browsing activities in Docker containers. This approach protects your main system from web\sphinxhyphen{}based threats while providing a seamless browsing experience.

\sphinxAtStartPar
This user guide will help you understand how to use bahtBrowse effectively, including:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Using the Firefox plugin

\item {} 
\sphinxAtStartPar
Browsing websites securely

\item {} 
\sphinxAtStartPar
Managing browser sessions

\item {} 
\sphinxAtStartPar
Troubleshooting common issues

\end{itemize}


\subsection{Basic Concepts}
\label{\detokenize{user_guide/index:basic-concepts}}
\sphinxAtStartPar
Before diving into the details, it’s important to understand some basic concepts:


\subsubsection{Remote Browser Isolation (RBI)}
\label{\detokenize{user_guide/index:remote-browser-isolation-rbi}}
\sphinxAtStartPar
Remote Browser Isolation (RBI) is a security approach that separates the browsing process from the user’s device. In bahtBrowse, this is achieved by running browsers in Docker containers and providing access through a VNC interface.


\subsubsection{Containerization}
\label{\detokenize{user_guide/index:containerization}}
\sphinxAtStartPar
bahtBrowse uses Docker containers to isolate browsers from the host system. Each browser session runs in its own container, providing a clean and isolated environment for browsing.


\subsubsection{VNC Interface}
\label{\detokenize{user_guide/index:vnc-interface}}
\sphinxAtStartPar
The VNC (Virtual Network Computing) interface allows you to interact with the containerized browser. It provides a graphical interface to the browser running in the container.


\subsubsection{Session Management}
\label{\detokenize{user_guide/index:session-management}}
\sphinxAtStartPar
bahtBrowse manages browser sessions automatically. Each session is identified by a unique session ID and is isolated from other sessions.


\subsection{Getting Started}
\label{\detokenize{user_guide/index:getting-started}}
\sphinxAtStartPar
If you haven’t installed bahtBrowse yet, please refer to the {\hyperref[\detokenize{getting_started/installation::doc}]{\sphinxcrossref{\DUrole{doc}{Installation Guide}}}} guide.

\sphinxAtStartPar
For a quick introduction to using bahtBrowse, see the {\hyperref[\detokenize{getting_started/quick_start::doc}]{\sphinxcrossref{\DUrole{doc}{Quick Start Guide}}}} guide.


\subsection{Using bahtBrowse}
\label{\detokenize{user_guide/index:using-bahtbrowse}}
\sphinxAtStartPar
There are several ways to use bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Plugin}: The recommended way to use bahtBrowse is through the Firefox plugin, which provides a seamless integration with Firefox. See {\hyperref[\detokenize{user_guide/firefox_plugin::doc}]{\sphinxcrossref{\DUrole{doc}{Firefox Plugin}}}} for details.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Direct API Access}: You can access websites through bahtBrowse by using the API directly. See {\hyperref[\detokenize{user_guide/browsing::doc}]{\sphinxcrossref{\DUrole{doc}{Secure Browsing with bahtBrowse}}}} for details.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Command Line}: Advanced users can use the command line to interact with bahtBrowse. See {\hyperref[\detokenize{user_guide/browsing::doc}]{\sphinxcrossref{\DUrole{doc}{Secure Browsing with bahtBrowse}}}} for details.

\end{enumerate}


\subsection{Next Steps}
\label{\detokenize{user_guide/index:next-steps}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{user_guide/firefox_plugin::doc}]{\sphinxcrossref{\DUrole{doc}{Firefox Plugin}}}}: Learn how to use the Firefox plugin

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{user_guide/browsing::doc}]{\sphinxcrossref{\DUrole{doc}{Secure Browsing with bahtBrowse}}}}: Learn how to browse websites securely

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{troubleshooting/index::doc}]{\sphinxcrossref{\DUrole{doc}{Troubleshooting}}}}: Troubleshoot common issues

\end{itemize}

\sphinxstepscope


\section{Troubleshooting}
\label{\detokenize{troubleshooting/index:troubleshooting}}\label{\detokenize{troubleshooting/index:id1}}\label{\detokenize{troubleshooting/index::doc}}
\sphinxAtStartPar
This section provides solutions to common issues you might encounter when using bahtBrowse.

\sphinxstepscope


\subsection{Common Issues}
\label{\detokenize{troubleshooting/common_issues:common-issues}}\label{\detokenize{troubleshooting/common_issues:id1}}\label{\detokenize{troubleshooting/common_issues::doc}}
\sphinxAtStartPar
This document provides solutions to common issues you might encounter when using bahtBrowse.


\subsubsection{Connection Issues}
\label{\detokenize{troubleshooting/common_issues:connection-issues}}

\paragraph{Unable to Connect to bahtBrowse Service}
\label{\detokenize{troubleshooting/common_issues:unable-to-connect-to-bahtbrowse-service}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: You cannot connect to the bahtBrowse service.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Firefox plugin shows a disconnected icon
\sphinxhyphen{} Error message: “Unable to connect to bahtBrowse service”
\sphinxhyphen{} Browser redirects fail with connection errors

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the service is running}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the bahtbrowse\sphinxhyphen{}firefox and bahtbrowse\sphinxhyphen{}api containers are running.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check container logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}api
\end{sphinxVerbatim}

\sphinxAtStartPar
Look for any error messages that might indicate the cause of the issue.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check port mappings}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the ports are correctly mapped:
\sphinxhyphen{} 6081:6080 for VNC
\sphinxhyphen{} 8080:80 for HTTP
\sphinxhyphen{} 8082:8082 for API

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if ports are in use}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
netstat\PYG{+w}{ }\PYGZhy{}tuln\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }\PYG{l+m}{8082}
netstat\PYG{+w}{ }\PYGZhy{}tuln\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }\PYG{l+m}{6081}
netstat\PYG{+w}{ }\PYGZhy{}tuln\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }\PYG{l+m}{8080}
\end{sphinxVerbatim}

\sphinxAtStartPar
If the ports are in use by other services, you’ll need to stop those services or configure bahtBrowse to use different ports.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the service}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }down
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\sphinxAtStartPar
This will stop and restart the containers.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check firewall settings}:

\sphinxAtStartPar
Ensure that your firewall allows connections to the required ports.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check network configuration}:

\sphinxAtStartPar
If you’re running bahtBrowse on a remote server, ensure that the server is accessible from your network.

\end{enumerate}


\paragraph{VNC Connection Fails}
\label{\detokenize{troubleshooting/common_issues:vnc-connection-fails}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: You can connect to the bahtBrowse service, but the VNC connection fails.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Error message: “Unable to connect to VNC server”
\sphinxhyphen{} Black screen in the VNC interface
\sphinxhyphen{} VNC interface loads but shows an error

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the VNC server is running}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ps\PYG{+w}{ }aux\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }x11vnc
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see an x11vnc process running.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check VNC server logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/x11vnc.log
\end{sphinxVerbatim}

\sphinxAtStartPar
Look for any error messages that might indicate the cause of the issue.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the VNC port is accessible}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
telnet\PYG{+w}{ }localhost\PYG{+w}{ }\PYG{l+m}{6081}
\end{sphinxVerbatim}

\sphinxAtStartPar
If you can connect, the port is accessible.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check nginx configuration}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/etc/nginx/sites\PYGZhy{}enabled/proxy\PYGZhy{}server.conf
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the nginx configuration includes a location block for the VNC interface:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{location}\PYG{+w}{ }\PYG{l+s}{/vnc1/}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}pass}\PYG{+w}{ }\PYG{l+s}{http://localhost:6080/}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}http\PYGZus{}version}\PYG{+w}{ }\PYG{l+m+mi}{1}\PYG{l+s}{.1}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{Upgrade}\PYG{+w}{ }\PYG{n+nv}{\PYGZdl{}http\PYGZus{}upgrade}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{Connection}\PYG{+w}{ }\PYG{l+s}{\PYGZdq{}upgrade\PYGZdq{}}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{Host}\PYG{+w}{ }\PYG{n+nv}{\PYGZdl{}host}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{X\PYGZhy{}Real\PYGZhy{}IP}\PYG{+w}{ }\PYG{n+nv}{\PYGZdl{}remote\PYGZus{}addr}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{X\PYGZhy{}Forwarded\PYGZhy{}For}\PYG{+w}{ }\PYG{n+nv}{\PYGZdl{}proxy\PYGZus{}add\PYGZus{}x\PYGZus{}forwarded\PYGZus{}for}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}set\PYGZus{}header}\PYG{+w}{ }\PYG{l+s}{X\PYGZhy{}Forwarded\PYGZhy{}Proto}\PYG{+w}{ }\PYG{n+nv}{\PYGZdl{}scheme}\PYG{p}{;}

\PYG{+w}{    }\PYG{c+c1}{\PYGZsh{} WebSocket support}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}read\PYGZus{}timeout}\PYG{+w}{ }\PYG{l+s}{61s}\PYG{p}{;}
\PYG{+w}{    }\PYG{k+kn}{proxy\PYGZus{}buffering}\PYG{+w}{ }\PYG{n+no}{off}\PYG{p}{;}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the VNC server}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }pkill\PYG{+w}{ }x11vnc
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }x11vnc\PYG{+w}{ }\PYGZhy{}display\PYG{+w}{ }:1\PYG{+w}{ }\PYGZhy{}forever\PYG{+w}{ }\PYGZhy{}shared\PYG{+w}{ }\PYGZhy{}rfbport\PYG{+w}{ }\PYG{l+m}{6080}\PYG{+w}{ }\PYGZhy{}nopw\PYG{+w}{ }\PYGZhy{}listen\PYG{+w}{ }localhost\PYG{+w}{ }\PYGZhy{}xkb\PYG{+w}{ }\PYGZhy{}bg\PYG{+w}{ }\PYGZhy{}logfile\PYG{+w}{ }/tmp/x11vnc.log
\end{sphinxVerbatim}

\sphinxAtStartPar
This will stop and restart the VNC server.

\end{enumerate}


\subsubsection{Browser Issues}
\label{\detokenize{troubleshooting/common_issues:browser-issues}}

\paragraph{Browser Fails to Launch}
\label{\detokenize{troubleshooting/common_issues:browser-fails-to-launch}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The browser fails to launch in the container.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Error message: “Error launching browser”
\sphinxhyphen{} VNC interface shows a blank screen
\sphinxhyphen{} Browser process is not running in the container

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the browser is installed}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ls\PYG{+w}{ }\PYGZhy{}la\PYG{+w}{ }/tmp/firefox
\end{sphinxVerbatim}

\sphinxAtStartPar
For Firefox, you should see the Firefox executable.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ls\PYG{+w}{ }\PYGZhy{}la\PYG{+w}{ }/usr/bin/chromium\PYGZhy{}browser
\end{sphinxVerbatim}

\sphinxAtStartPar
For Chromium, you should see the Chromium executable.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check browser logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/browser.log
\end{sphinxVerbatim}

\sphinxAtStartPar
Look for any error messages that might indicate the cause of the issue.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the browser process is running}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ps\PYG{+w}{ }aux\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }firefox
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ps\PYG{+w}{ }aux\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see a browser process running.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the X server is running}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }ps\PYG{+w}{ }aux\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }Xvfb
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see an Xvfb process running.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the browser}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }pkill\PYG{+w}{ }firefox
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }\PYG{n+nv}{DISPLAY}\PYG{o}{=}:1\PYG{+w}{ }/tmp/firefox/firefox\PYG{+w}{ }\PYGZhy{}\PYGZhy{}new\PYGZhy{}instance\PYG{+w}{ }\PYGZhy{}\PYGZhy{}url\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}https://example.com\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}\PYGZhy{}kiosk\PYG{+w}{ }\PYG{p}{\PYGZam{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
This will stop and restart the browser.

\end{enumerate}


\paragraph{Browser Crashes}
\label{\detokenize{troubleshooting/common_issues:browser-crashes}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The browser crashes during use.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Browser window disappears
\sphinxhyphen{} Error message: “Browser has crashed”
\sphinxhyphen{} VNC interface shows a blank screen

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check browser logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/browser.log
\end{sphinxVerbatim}

\sphinxAtStartPar
Look for any error messages that might indicate the cause of the crash.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check system resources}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }stats\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the container has enough CPU and memory resources.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try a different browser}:

\sphinxAtStartPar
If Firefox is crashing, try Chromium:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If Chromium is crashing, try Firefox:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try compatibility mode}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{mode}\PYG{o}{=}compatibility
\end{sphinxVerbatim}

\sphinxAtStartPar
Compatibility mode enables additional features that may help with complex websites.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Disable JavaScript execution}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{disable\PYGZus{}js\PYGZus{}execution}\PYG{o}{=}\PYG{n+nb}{true}
\end{sphinxVerbatim}

\sphinxAtStartPar
This can help if the crash is caused by JavaScript.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Increase JavaScript timeout}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{js\PYGZus{}timeout}\PYG{o}{=}\PYG{l+m}{30000}
\end{sphinxVerbatim}

\sphinxAtStartPar
This increases the JavaScript execution timeout to 30 seconds.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the container}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }restart\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\sphinxAtStartPar
This will restart the container.

\end{enumerate}


\paragraph{Website Compatibility Issues}
\label{\detokenize{troubleshooting/common_issues:website-compatibility-issues}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: Some websites don’t work correctly in bahtBrowse.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Website layout is broken
\sphinxhyphen{} Interactive elements don’t work
\sphinxhyphen{} Website shows an error message

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try compatibility mode}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{mode}\PYG{o}{=}compatibility
\end{sphinxVerbatim}

\sphinxAtStartPar
Compatibility mode enables additional features that may help with complex websites.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try a different browser}:

\sphinxAtStartPar
If Firefox is having issues, try Chromium:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If Chromium is having issues, try Firefox:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if JavaScript is enabled}:

\sphinxAtStartPar
Some websites require JavaScript to function correctly. Ensure that JavaScript is enabled:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{disable\PYGZus{}js\PYGZus{}execution}\PYG{o}{=}\PYG{n+nb}{false}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the website requires cookies}:

\sphinxAtStartPar
Some websites require cookies to function correctly. Ensure that cookies are enabled in the browser.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the website requires authentication}:

\sphinxAtStartPar
Some websites require authentication to access. Ensure that you’re logged in to the website.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the website requires a specific locale}:

\sphinxAtStartPar
Some websites require a specific locale to function correctly. Try setting a different locale:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{locale}\PYG{o}{=}en\PYGZhy{}US
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{locale}\PYG{o}{=}de\PYGZhy{}DE
\end{sphinxVerbatim}

\end{enumerate}


\subsubsection{Firefox Plugin Issues}
\label{\detokenize{troubleshooting/common_issues:firefox-plugin-issues}}

\paragraph{Plugin Not Connecting}
\label{\detokenize{troubleshooting/common_issues:plugin-not-connecting}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The Firefox plugin cannot connect to the bahtBrowse service.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Plugin icon shows a disconnected state
\sphinxhyphen{} Error message: “Unable to connect to bahtBrowse service”
\sphinxhyphen{} Clicking the plugin button does nothing

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check plugin settings}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Click on the plugin icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Click on “Options” (gear icon)

\item {} 
\sphinxAtStartPar
Verify that the host and port settings are correct:
\sphinxhyphen{} Host: \sphinxcode{\sphinxupquote{localhost}} (or the IP address of the server)
\sphinxhyphen{} Port: \sphinxcode{\sphinxupquote{8082}}

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the service is running}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the bahtbrowse\sphinxhyphen{}firefox and bahtbrowse\sphinxhyphen{}api containers are running.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the API server is accessible}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }http://localhost:8082/test\PYGZhy{}connection
\end{sphinxVerbatim}

\sphinxAtStartPar
You should receive a JSON response indicating that the service is available.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check browser console for errors}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Press F12 to open the developer tools

\item {} 
\sphinxAtStartPar
Go to the Console tab

\item {} 
\sphinxAtStartPar
Look for any error messages related to the plugin

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reinstall the plugin}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and click “Remove”

\item {} 
\sphinxAtStartPar
Install the plugin again

\end{itemize}

\end{enumerate}


\paragraph{Plugin Not Working Correctly}
\label{\detokenize{troubleshooting/common_issues:plugin-not-working-correctly}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The Firefox plugin is not working correctly.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Plugin icon is visible but clicking it does nothing
\sphinxhyphen{} Right\sphinxhyphen{}click context menu items are missing
\sphinxhyphen{} Plugin settings cannot be saved

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check browser console for errors}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Press F12 to open the developer tools

\item {} 
\sphinxAtStartPar
Go to the Console tab

\item {} 
\sphinxAtStartPar
Look for any error messages related to the plugin

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check plugin permissions}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and click “Permissions”

\item {} 
\sphinxAtStartPar
Ensure that the plugin has the necessary permissions

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check if the plugin is enabled}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and ensure it’s enabled

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart Firefox}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Close Firefox

\item {} 
\sphinxAtStartPar
Open Firefox again

\item {} 
\sphinxAtStartPar
Check if the plugin works correctly

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reinstall the plugin}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and click “Remove”

\item {} 
\sphinxAtStartPar
Install the plugin again

\end{itemize}

\end{enumerate}


\subsubsection{Performance Issues}
\label{\detokenize{troubleshooting/common_issues:performance-issues}}

\paragraph{Slow Browser Performance}
\label{\detokenize{troubleshooting/common_issues:slow-browser-performance}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The browser in bahtBrowse is slow or unresponsive.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Browser takes a long time to load pages
\sphinxhyphen{} Browser is unresponsive to user input
\sphinxhyphen{} Browser animations are choppy

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check system resources}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }stats\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\sphinxAtStartPar
Ensure that the container has enough CPU and memory resources.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try a different browser}:

\sphinxAtStartPar
If Firefox is slow, try Chromium:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If Chromium is slow, try Firefox:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{browser}\PYG{o}{=}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Try compatibility mode}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{mode}\PYG{o}{=}compatibility
\end{sphinxVerbatim}

\sphinxAtStartPar
Compatibility mode enables additional features that may help with complex websites.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Disable JavaScript execution}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url\PYG{o}{=}https://example.com\PYG{p}{\PYGZam{}}\PYG{n+nv}{disable\PYGZus{}js\PYGZus{}execution}\PYG{o}{=}\PYG{n+nb}{true}
\end{sphinxVerbatim}

\sphinxAtStartPar
This can help if the slowness is caused by JavaScript.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Increase container resources}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to allocate more CPU and memory to the container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{deploy}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{resources}\PYG{p}{:}
\PYG{+w}{        }\PYG{n+nt}{limits}\PYG{p}{:}
\PYG{+w}{          }\PYG{n+nt}{cpus}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s}{\PYGZsq{}}\PYG{l+s}{2}\PYG{l+s}{\PYGZsq{}}
\PYG{+w}{          }\PYG{n+nt}{memory}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{2G}
\end{sphinxVerbatim}

\sphinxAtStartPar
This allocates 2 CPU cores and 2GB of memory to the container.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Optimize browser settings}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Disable browser extensions

\item {} 
\sphinxAtStartPar
Disable browser animations

\item {} 
\sphinxAtStartPar
Disable browser hardware acceleration

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the container}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }restart\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\sphinxAtStartPar
This will restart the container.

\end{enumerate}


\paragraph{Slow VNC Performance}
\label{\detokenize{troubleshooting/common_issues:slow-vnc-performance}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The VNC interface is slow or unresponsive.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} VNC interface takes a long time to load
\sphinxhyphen{} VNC interface is unresponsive to user input
\sphinxhyphen{} VNC interface animations are choppy

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check network bandwidth}:

\sphinxAtStartPar
Ensure that you have sufficient network bandwidth for VNC.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reduce VNC quality}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to reduce the VNC quality:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{VNC\PYGZus{}QUALITY=50}
\end{sphinxVerbatim}

\sphinxAtStartPar
This reduces the VNC quality to 50\%.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reduce VNC resolution}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to reduce the VNC resolution:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{VNC\PYGZus{}RESOLUTION=1024x768}
\end{sphinxVerbatim}

\sphinxAtStartPar
This reduces the VNC resolution to 1024x768.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a different VNC client}:

\sphinxAtStartPar
Instead of using the web\sphinxhyphen{}based VNC client, you can use a native VNC client:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
vncviewer\PYG{+w}{ }localhost:6081
\end{sphinxVerbatim}

\sphinxAtStartPar
This may provide better performance.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Restart the VNC server}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }pkill\PYG{+w}{ }x11vnc
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }x11vnc\PYG{+w}{ }\PYGZhy{}display\PYG{+w}{ }:1\PYG{+w}{ }\PYGZhy{}forever\PYG{+w}{ }\PYGZhy{}shared\PYG{+w}{ }\PYGZhy{}rfbport\PYG{+w}{ }\PYG{l+m}{6080}\PYG{+w}{ }\PYGZhy{}nopw\PYG{+w}{ }\PYGZhy{}listen\PYG{+w}{ }localhost\PYG{+w}{ }\PYGZhy{}xkb\PYG{+w}{ }\PYGZhy{}bg\PYG{+w}{ }\PYGZhy{}logfile\PYG{+w}{ }/tmp/x11vnc.log
\end{sphinxVerbatim}

\sphinxAtStartPar
This will stop and restart the VNC server.

\end{enumerate}


\subsubsection{Security Issues}
\label{\detokenize{troubleshooting/common_issues:security-issues}}

\paragraph{Session Hijacking}
\label{\detokenize{troubleshooting/common_issues:session-hijacking}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: Someone else can access your browser session.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} You see unexpected activity in your browser session
\sphinxhyphen{} Your browser session is being used by someone else
\sphinxhyphen{} You are redirected to unexpected websites

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use HTTPS}:

\sphinxAtStartPar
Configure bahtBrowse to use HTTPS to encrypt traffic between the client and server.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Enable session validation}:

\sphinxAtStartPar
Ensure that session validation is enabled in the API server.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a firewall}:

\sphinxAtStartPar
Configure a firewall to restrict access to the bahtBrowse service.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a VPN}:

\sphinxAtStartPar
Use a VPN to encrypt traffic between the client and server.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Implement authentication}:

\sphinxAtStartPar
Implement authentication to restrict access to the bahtBrowse service.

\end{enumerate}


\paragraph{Container Escape}
\label{\detokenize{troubleshooting/common_issues:container-escape}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: A malicious website can escape the container and affect the host system.

\sphinxAtStartPar
\sphinxstylestrong{Symptoms}:
\sphinxhyphen{} Unexpected files or processes on the host system
\sphinxhyphen{} Host system resources are being used by unknown processes
\sphinxhyphen{} Host system is compromised

\sphinxAtStartPar
\sphinxstylestrong{Solutions}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Keep Docker up to date}:

\sphinxAtStartPar
Ensure that Docker is up to date to benefit from security fixes.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use security options}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to add security options:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{security\PYGZus{}opt}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{no\PYGZhy{}new\PYGZhy{}privileges:true}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{seccomp=unconfined}
\end{sphinxVerbatim}

\sphinxAtStartPar
This prevents the container from gaining new privileges and uses the default seccomp profile.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use read\sphinxhyphen{}only file systems}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to make the file system read\sphinxhyphen{}only:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{read\PYGZus{}only}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{true}
\PYG{+w}{    }\PYG{n+nt}{tmpfs}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{/tmp}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{/var/run}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{/var/log}
\end{sphinxVerbatim}

\sphinxAtStartPar
This makes the file system read\sphinxhyphen{}only, with temporary file systems for directories that need to be writable.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use user namespaces}:

\sphinxAtStartPar
Configure Docker to use user namespaces to map container users to host users.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use AppArmor or SELinux}:

\sphinxAtStartPar
Configure AppArmor or SELinux to restrict container capabilities.

\end{enumerate}


\subsubsection{Logging and Debugging}
\label{\detokenize{troubleshooting/common_issues:logging-and-debugging}}

\paragraph{Viewing Logs}
\label{\detokenize{troubleshooting/common_issues:viewing-logs}}
\sphinxAtStartPar
To view logs for the bahtBrowse service:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}api
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Container Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Console Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/browser\PYGZus{}console.log
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Server Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/x11vnc.log
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/var/log/nginx/access.log
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/var/log/nginx/error.log
\end{sphinxVerbatim}

\end{enumerate}


\paragraph{Enabling Debug Mode}
\label{\detokenize{troubleshooting/common_issues:enabling-debug-mode}}
\sphinxAtStartPar
To enable debug mode for the bahtBrowse service:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server Debug Mode}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to enable debug mode for the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{api}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}api}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{DEBUG=true}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Debug Mode}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to enable debug mode for the browser:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{BROWSER\PYGZus{}DEBUG=true}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Debug Mode}:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file to enable debug mode for the VNC server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}firefox}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{VNC\PYGZus{}DEBUG=true}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Debug Mode}:

\sphinxAtStartPar
Edit the nginx configuration to enable debug mode:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{error\PYGZus{}log}\PYG{+w}{ }\PYG{l+s}{/var/log/nginx/error.log}\PYG{+w}{ }\PYG{l+s}{debug}\PYG{p}{;}
\end{sphinxVerbatim}

\sphinxAtStartPar
Then restart nginx:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }service\PYG{+w}{ }nginx\PYG{+w}{ }restart
\end{sphinxVerbatim}

\end{enumerate}


\paragraph{Debugging Firefox Plugin}
\label{\detokenize{troubleshooting/common_issues:debugging-firefox-plugin}}
\sphinxAtStartPar
To debug the Firefox plugin:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Enable Browser Console}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Press Ctrl+Shift+J to open the Browser Console

\item {} 
\sphinxAtStartPar
Look for messages related to the plugin

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Enable Plugin Debugging}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:debugging}}

\item {} 
\sphinxAtStartPar
Click on “This Firefox”

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and click “Inspect”

\item {} 
\sphinxAtStartPar
Use the browser developer tools to debug the plugin

\end{itemize}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Enable Verbose Logging}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Click on the plugin icon in the Firefox toolbar

\item {} 
\sphinxAtStartPar
Click on “Options” (gear icon)

\item {} 
\sphinxAtStartPar
Enable “Verbose Logging”

\item {} 
\sphinxAtStartPar
Check the Browser Console for detailed logs

\end{itemize}

\end{enumerate}


\subsubsection{Getting Help}
\label{\detokenize{troubleshooting/common_issues:getting-help}}
\sphinxAtStartPar
If you’re still experiencing issues after trying the solutions in this guide, you can:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check the FAQ}:

\sphinxAtStartPar
See the {\hyperref[\detokenize{troubleshooting/faq::doc}]{\sphinxcrossref{\DUrole{doc}{Frequently Asked Questions}}}} for answers to frequently asked questions.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Search for Similar Issues}:

\sphinxAtStartPar
Search for similar issues in the GitHub repository.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Report the Issue}:

\sphinxAtStartPar
Report the issue on GitHub following the guidelines in {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}}.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Contact the Maintainers}:

\sphinxAtStartPar
Contact the maintainers for help with complex issues.

\end{enumerate}

\sphinxstepscope


\subsection{Frequently Asked Questions}
\label{\detokenize{troubleshooting/faq:frequently-asked-questions}}\label{\detokenize{troubleshooting/faq:faq}}\label{\detokenize{troubleshooting/faq::doc}}
\sphinxAtStartPar
This document provides answers to frequently asked questions about bahtBrowse.


\subsubsection{General Questions}
\label{\detokenize{troubleshooting/faq:general-questions}}

\paragraph{What is bahtBrowse?}
\label{\detokenize{troubleshooting/faq:what-is-bahtbrowse}}
\sphinxAtStartPar
bahtBrowse is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. It isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.


\paragraph{How does bahtBrowse work?}
\label{\detokenize{troubleshooting/faq:how-does-bahtbrowse-work}}
\sphinxAtStartPar
bahtBrowse works by running browsers (Firefox or Chromium) in isolated Docker containers. When you access a website through bahtBrowse, the system:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Creates a new browser session in a Docker container

\item {} 
\sphinxAtStartPar
Loads the specified URL in the containerized browser

\item {} 
\sphinxAtStartPar
Provides access to the browser through a VNC interface

\item {} 
\sphinxAtStartPar
Isolates the browsing activity from your local system

\end{enumerate}

\sphinxAtStartPar
This approach protects your local system from web\sphinxhyphen{}based threats, as any malicious code is contained within the isolated container.


\paragraph{What are the system requirements for bahtBrowse?}
\label{\detokenize{troubleshooting/faq:what-are-the-system-requirements-for-bahtbrowse}}
\sphinxAtStartPar
To run bahtBrowse, you need:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Operating System}: Linux (Ubuntu 20.04 or later recommended)

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker}: Docker 20.10 or later

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Memory}: At least 4GB RAM

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Storage}: At least 10GB free disk space

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network}: Stable internet connection

\end{itemize}

\sphinxAtStartPar
For Firefox plugin users:
\sphinxhyphen{} \sphinxstylestrong{Firefox}: Version 78 or later

\sphinxAtStartPar
For development:
\sphinxhyphen{} \sphinxstylestrong{Python}: 3.8 or later
\sphinxhyphen{} \sphinxstylestrong{Git}: 2.25 or later


\paragraph{Is bahtBrowse free to use?}
\label{\detokenize{troubleshooting/faq:is-bahtbrowse-free-to-use}}
\sphinxAtStartPar
Yes, bahtBrowse is open\sphinxhyphen{}source software and free to use. It is licensed under the MIT License, which allows you to use, modify, and distribute the software freely.


\subsubsection{Installation and Setup}
\label{\detokenize{troubleshooting/faq:installation-and-setup}}

\paragraph{How do I install bahtBrowse?}
\label{\detokenize{troubleshooting/faq:how-do-i-install-bahtbrowse}}
\sphinxAtStartPar
To install bahtBrowse, follow these steps:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build and start the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
For detailed installation instructions, see the {\hyperref[\detokenize{getting_started/installation::doc}]{\sphinxcrossref{\DUrole{doc}{Installation Guide}}}} guide.


\paragraph{How do I install the Firefox plugin?}
\label{\detokenize{troubleshooting/faq:how-do-i-install-the-firefox-plugin}}
\sphinxAtStartPar
To install the Firefox plugin:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Build the Firefox plugin:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }firefox\PYGZus{}plugin
./build.sh
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Install the plugin in Firefox:
\begin{enumerate}
\sphinxsetlistlabels{\alph}{enumii}{enumiii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:debugging}}

\item {} 
\sphinxAtStartPar
Click on “This Firefox”

\item {} 
\sphinxAtStartPar
Click on “Load Temporary Add\sphinxhyphen{}on…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}

\sphinxAtStartPar
Alternatively, you can install the plugin permanently:
\begin{enumerate}
\sphinxsetlistlabels{\alph}{enumii}{enumiii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:addons}}

\item {} 
\sphinxAtStartPar
Click the gear icon and select “Install Add\sphinxhyphen{}on From File…”

\item {} 
\sphinxAtStartPar
Select the \sphinxcode{\sphinxupquote{bahtbrowse\_bouncer.xpi}} file from the \sphinxcode{\sphinxupquote{firefox\_plugin/build}} directory

\end{enumerate}

\end{enumerate}

\sphinxAtStartPar
For more information on the Firefox plugin, see the {\hyperref[\detokenize{user_guide/firefox_plugin::doc}]{\sphinxcrossref{\DUrole{doc}{Firefox Plugin}}}} guide.


\paragraph{How do I configure bahtBrowse?}
\label{\detokenize{troubleshooting/faq:how-do-i-configure-bahtbrowse}}
\sphinxAtStartPar
You can configure bahtBrowse through environment variables or a configuration file. To create a basic configuration:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create a \sphinxcode{\sphinxupquote{.env}} file in the project root directory:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} API Server Configuration}
\PYG{n+nv}{API\PYGZus{}PORT}\PYG{o}{=}\PYG{l+m}{8082}
\PYG{n+nv}{API\PYGZus{}HOST}\PYG{o}{=}\PYG{l+m}{0}.0.0.0

\PYG{c+c1}{\PYGZsh{} VNC Configuration}
\PYG{n+nv}{VNC\PYGZus{}PORT}\PYG{o}{=}\PYG{l+m}{6080}

\PYG{c+c1}{\PYGZsh{} Browser Configuration}
\PYG{n+nv}{DEFAULT\PYGZus{}BROWSER}\PYG{o}{=}firefox

\PYG{c+c1}{\PYGZsh{} Logging Configuration}
\PYG{n+nv}{LOG\PYGZus{}LEVEL}\PYG{o}{=}INFO
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Restart the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }down
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
For more advanced configuration options, see the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.


\paragraph{Can I run bahtBrowse on Windows or macOS?}
\label{\detokenize{troubleshooting/faq:can-i-run-bahtbrowse-on-windows-or-macos}}
\sphinxAtStartPar
While bahtBrowse is primarily designed for Linux, you can run it on Windows or macOS using Docker Desktop. However, performance may be lower compared to running on Linux.

\sphinxAtStartPar
To run bahtBrowse on Windows or macOS:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Install Docker Desktop for your operating system

\item {} 
\sphinxAtStartPar
Follow the standard installation instructions

\item {} 
\sphinxAtStartPar
Note that some features may not work as expected on non\sphinxhyphen{}Linux systems

\end{enumerate}


\subsubsection{Usage}
\label{\detokenize{troubleshooting/faq:usage}}

\paragraph{How do I access websites through bahtBrowse?}
\label{\detokenize{troubleshooting/faq:how-do-i-access-websites-through-bahtbrowse}}
\sphinxAtStartPar
There are several ways to access websites through bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Plugin}: The recommended way to use bahtBrowse is through the Firefox plugin. Simply click on the plugin icon in the Firefox toolbar and enter the URL you want to visit.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Direct API Access}: You can access websites through bahtBrowse by opening the following URL in your browser:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com
\end{sphinxVerbatim}

\sphinxAtStartPar
Replace \sphinxcode{\sphinxupquote{https://example.com}} with the URL you want to visit.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Command Line}: You can use the command line to access websites through bahtBrowse:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
curl\PYG{+w}{ }\PYGZhy{}L\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/browse/?url=https://example.com\PYGZdq{}}
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
For more information on using bahtBrowse, see the {\hyperref[\detokenize{user_guide/browsing::doc}]{\sphinxcrossref{\DUrole{doc}{Secure Browsing with bahtBrowse}}}} guide.


\paragraph{Can I use bahtBrowse with browsers other than Firefox?}
\label{\detokenize{troubleshooting/faq:can-i-use-bahtbrowse-with-browsers-other-than-firefox}}
\sphinxAtStartPar
Yes, bahtBrowse supports both Firefox and Chromium browsers. You can specify which browser to use by adding the \sphinxcode{\sphinxupquote{browser}} parameter to the URL:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=firefox
http://localhost:8082/browse/?url=https://example.com\PYGZam{}browser=chromium
\end{sphinxVerbatim}

\sphinxAtStartPar
If no browser is specified, Firefox will be used by default.


\paragraph{How do I download files through bahtBrowse?}
\label{\detokenize{troubleshooting/faq:how-do-i-download-files-through-bahtbrowse}}
\sphinxAtStartPar
When you download a file in the containerized browser, it is saved in the container. You can access downloaded files through the downloads manager:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{http://localhost:8082/downloads}}

\item {} 
\sphinxAtStartPar
You will see a list of downloaded files

\item {} 
\sphinxAtStartPar
Click on a file to download it to your local system

\end{enumerate}

\sphinxAtStartPar
Note that downloaded files are stored in the container and will be lost when the container is restarted. To persist downloads, you can mount a volume to the container.


\paragraph{Can I use bahtBrowse for multiple users?}
\label{\detokenize{troubleshooting/faq:can-i-use-bahtbrowse-for-multiple-users}}
\sphinxAtStartPar
Yes, bahtBrowse can be used by multiple users simultaneously. Each user will have their own isolated browser session.

\sphinxAtStartPar
However, the default configuration does not include user authentication or session management. For multi\sphinxhyphen{}user environments, you should implement:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authentication}: Add user authentication to restrict access to the bahtBrowse service

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Management}: Implement session management to associate sessions with users

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Resource Limits}: Set resource limits to prevent resource exhaustion

\end{enumerate}

\sphinxAtStartPar
For more information on multi\sphinxhyphen{}user setups, see the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.


\paragraph{How do I use bahtBrowse with a proxy server?}
\label{\detokenize{troubleshooting/faq:how-do-i-use-bahtbrowse-with-a-proxy-server}}
\sphinxAtStartPar
To use bahtBrowse with a proxy server, you can configure the proxy settings in the browser:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
For Firefox:

\sphinxAtStartPar
Edit the \sphinxcode{\sphinxupquote{user.js}} file in the Firefox profile directory to include proxy settings:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.type\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+m+mf}{1}\PYG{p}{)}\PYG{p}{;}
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.http\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}proxy.example.com\PYGZdq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.http\PYGZus{}port\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+m+mf}{8080}\PYG{p}{)}\PYG{p}{;}
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.ssl\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}proxy.example.com\PYGZdq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.ssl\PYGZus{}port\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+m+mf}{8080}\PYG{p}{)}\PYG{p}{;}
\PYG{n+nx}{user\PYGZus{}pref}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}network.proxy.no\PYGZus{}proxies\PYGZus{}on\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}localhost, 127.0.0.1\PYGZdq{}}\PYG{p}{)}\PYG{p}{;}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
For Chromium:

\sphinxAtStartPar
Launch Chromium with proxy settings:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
chromium\PYGZhy{}browser\PYG{+w}{ }\PYGZhy{}\PYGZhy{}proxy\PYGZhy{}server\PYG{o}{=}\PYG{l+s+s2}{\PYGZdq{}http://proxy.example.com:8080\PYGZdq{}}
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
Alternatively, you can configure the proxy at the Docker level by setting the \sphinxcode{\sphinxupquote{HTTP\_PROXY}} and \sphinxcode{\sphinxupquote{HTTPS\_PROXY}} environment variables in the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.yml}} file.


\subsubsection{Security}
\label{\detokenize{troubleshooting/faq:security}}

\paragraph{Is bahtBrowse secure?}
\label{\detokenize{troubleshooting/faq:is-bahtbrowse-secure}}
\sphinxAtStartPar
bahtBrowse is designed with security in mind. It isolates web browsing activities in Docker containers, protecting your local system from web\sphinxhyphen{}based threats.

\sphinxAtStartPar
However, no security solution is perfect, and there are some security considerations to keep in mind:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Container Isolation}: While Docker provides good isolation, it is not as strong as virtual machines. Container escape vulnerabilities could potentially expose your system.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Traffic}: Traffic between your browser and the bahtBrowse service is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Management}: Sessions are identified by unique IDs, but there is no authentication by default. Consider implementing authentication for multi\sphinxhyphen{}user environments.

\end{itemize}

\sphinxAtStartPar
For more information on security considerations, see the {\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}} section.


\paragraph{Can bahtBrowse protect me from all web threats?}
\label{\detokenize{troubleshooting/faq:can-bahtbrowse-protect-me-from-all-web-threats}}
\sphinxAtStartPar
No security solution can protect you from all threats. While bahtBrowse provides good protection against many web\sphinxhyphen{}based threats, it is not a substitute for good security practices.

\sphinxAtStartPar
To maximize your security:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Keep Software Updated}: Keep bahtBrowse, Docker, and your operating system up to date

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use Strong Passwords}: Use strong, unique passwords for all your accounts

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Enable Two\sphinxhyphen{}Factor Authentication}: Enable two\sphinxhyphen{}factor authentication where available

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Be Cautious}: Be cautious about the websites you visit and the files you download

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use Multiple Security Layers}: Use bahtBrowse as part of a comprehensive security strategy

\end{enumerate}


\paragraph{Can I use bahtBrowse for sensitive browsing?}
\label{\detokenize{troubleshooting/faq:can-i-use-bahtbrowse-for-sensitive-browsing}}
\sphinxAtStartPar
bahtBrowse can be used for sensitive browsing, but you should take additional precautions:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use HTTPS}: Configure bahtBrowse to use HTTPS to encrypt traffic between your browser and the bahtBrowse service

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Implement Authentication}: Add user authentication to restrict access to the bahtBrowse service

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a VPN}: Use a VPN to encrypt traffic between your device and the bahtBrowse service

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Secure the Host}: Ensure that the host running bahtBrowse is secure

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Regular Security Audits}: Conduct regular security audits of your bahtBrowse installation

\end{enumerate}

\sphinxAtStartPar
For more information on securing bahtBrowse, see the \DUrole{xref}{\DUrole{std}{\DUrole{std-doc}{../admin\_guide/security}}} section.


\subsubsection{Performance}
\label{\detokenize{troubleshooting/faq:performance}}

\paragraph{Why is bahtBrowse slow?}
\label{\detokenize{troubleshooting/faq:why-is-bahtbrowse-slow}}
\sphinxAtStartPar
There are several reasons why bahtBrowse might be slow:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Resource Constraints}: The container might not have enough CPU or memory resources

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Latency}: High network latency can affect the VNC connection

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Performance}: The browser in the container might be slow due to the website or browser configuration

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Performance}: The VNC connection might be slow due to network conditions or VNC settings

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Container Overhead}: Running browsers in containers adds some overhead

\end{enumerate}

\sphinxAtStartPar
To improve performance, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\paragraph{How can I improve bahtBrowse performance?}
\label{\detokenize{troubleshooting/faq:how-can-i-improve-bahtbrowse-performance}}
\sphinxAtStartPar
To improve bahtBrowse performance:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Increase Container Resources}: Allocate more CPU and memory to the container

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Optimize VNC Settings}: Reduce VNC quality and resolution

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a Different Browser}: Try using Chromium instead of Firefox, or vice versa

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use Compatibility Mode}: Try using compatibility mode for complex websites

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Optimize Browser Settings}: Disable browser extensions and animations

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a Faster Network}: Use a faster network connection

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use a Native VNC Client}: Use a native VNC client instead of the web\sphinxhyphen{}based client

\end{enumerate}

\sphinxAtStartPar
For more detailed performance optimization tips, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\paragraph{Can I run bahtBrowse on low\sphinxhyphen{}end hardware?}
\label{\detokenize{troubleshooting/faq:can-i-run-bahtbrowse-on-low-end-hardware}}
\sphinxAtStartPar
Yes, bahtBrowse can run on low\sphinxhyphen{}end hardware, but performance may be limited. To run bahtBrowse on low\sphinxhyphen{}end hardware:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reduce Container Resources}: Allocate fewer resources to the container

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use Chromium}: Chromium generally uses fewer resources than Firefox

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Disable JavaScript}: Disable JavaScript execution for better performance

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Reduce VNC Quality}: Reduce VNC quality and resolution

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Limit Concurrent Sessions}: Limit the number of concurrent browser sessions

\end{enumerate}

\sphinxAtStartPar
For more information on running bahtBrowse on low\sphinxhyphen{}end hardware, see the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.


\subsubsection{Troubleshooting}
\label{\detokenize{troubleshooting/faq:troubleshooting}}

\paragraph{Why can’t I connect to bahtBrowse?}
\label{\detokenize{troubleshooting/faq:why-can-t-i-connect-to-bahtbrowse}}
\sphinxAtStartPar
If you can’t connect to bahtBrowse, check the following:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Service Running}: Ensure that the bahtBrowse service is running

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Port Mapping}: Ensure that the ports are correctly mapped

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firewall}: Ensure that your firewall allows connections to the required ports

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Configuration}: Ensure that the server is accessible from your network

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Plugin Configuration}: Ensure that the Firefox plugin is configured correctly

\end{enumerate}

\sphinxAtStartPar
For more detailed troubleshooting steps, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\paragraph{Why does the VNC connection fail?}
\label{\detokenize{troubleshooting/faq:why-does-the-vnc-connection-fail}}
\sphinxAtStartPar
If the VNC connection fails, check the following:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Server Running}: Ensure that the VNC server is running in the container

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Port Accessibility}: Ensure that the VNC port is accessible

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Configuration}: Ensure that the nginx configuration includes a location block for the VNC interface

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{WebSocket Support}: Ensure that WebSocket support is enabled in nginx

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Support}: Ensure that your browser supports WebSockets

\end{enumerate}

\sphinxAtStartPar
For more detailed troubleshooting steps, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\paragraph{Why does the browser crash?}
\label{\detokenize{troubleshooting/faq:why-does-the-browser-crash}}
\sphinxAtStartPar
If the browser crashes, check the following:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Logs}: Check the browser logs for error messages

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{System Resources}: Ensure that the container has enough CPU and memory resources

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Compatibility}: Try a different browser (Firefox or Chromium)

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Website Compatibility}: Try compatibility mode for complex websites

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{JavaScript Issues}: Try disabling JavaScript execution

\end{enumerate}

\sphinxAtStartPar
For more detailed troubleshooting steps, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\subsubsection{Development}
\label{\detokenize{troubleshooting/faq:development}}

\paragraph{How do I contribute to bahtBrowse?}
\label{\detokenize{troubleshooting/faq:how-do-i-contribute-to-bahtbrowse}}
\sphinxAtStartPar
To contribute to bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Fork the Repository}: Fork the bahtBrowse repository on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a Feature Branch}: Create a branch for your feature or bug fix

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Make Changes}: Make your changes to the codebase

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run Tests}: Run the test suite to ensure your changes don’t break existing functionality

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Submit a Pull Request}: Submit a pull request to the main repository

\end{enumerate}

\sphinxAtStartPar
For more information on contributing to bahtBrowse, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\paragraph{How do I report a bug?}
\label{\detokenize{troubleshooting/faq:how-do-i-report-a-bug}}
\sphinxAtStartPar
To report a bug:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check Existing Issues}: Check if the bug has already been reported

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a New Issue}: If the bug hasn’t been reported, create a new issue on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Provide Details}: Provide as much detail as possible about the bug, including:
\sphinxhyphen{} Steps to reproduce
\sphinxhyphen{} Expected behavior
\sphinxhyphen{} Actual behavior
\sphinxhyphen{} Error messages
\sphinxhyphen{} System information

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Include Logs}: Include relevant logs from the container

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Attach Screenshots}: If applicable, attach screenshots illustrating the issue

\end{enumerate}

\sphinxAtStartPar
For more information on reporting bugs, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\paragraph{How do I request a feature?}
\label{\detokenize{troubleshooting/faq:how-do-i-request-a-feature}}
\sphinxAtStartPar
To request a feature:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check Existing Requests}: Check if the feature has already been requested

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a New Issue}: If the feature hasn’t been requested, create a new issue on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Provide Details}: Provide as much detail as possible about the feature, including:
\sphinxhyphen{} Description of the feature
\sphinxhyphen{} Use cases
\sphinxhyphen{} Benefits
\sphinxhyphen{} Potential implementation approaches

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Label as Enhancement}: Label the issue as an enhancement

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Discuss with the Community}: Discuss the feature with the community to gather feedback

\end{enumerate}

\sphinxAtStartPar
For more information on requesting features, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\paragraph{How do I set up a development environment?}
\label{\detokenize{troubleshooting/faq:how-do-i-set-up-a-development-environment}}
\sphinxAtStartPar
To set up a development environment for bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Clone the Repository}: Clone the bahtBrowse repository

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a Virtual Environment}: Create a Python virtual environment

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Install Dependencies}: Install the development dependencies

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Set Up Pre\sphinxhyphen{}commit Hooks}: Set up pre\sphinxhyphen{}commit hooks for code quality

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Build and Run Containers}: Build and run the containers in development mode

\end{enumerate}

\sphinxAtStartPar
For detailed instructions on setting up a development environment, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\subsubsection{Miscellaneous}
\label{\detokenize{troubleshooting/faq:miscellaneous}}

\paragraph{Can I use bahtBrowse with other browsers?}
\label{\detokenize{troubleshooting/faq:can-i-use-bahtbrowse-with-other-browsers}}
\sphinxAtStartPar
Currently, bahtBrowse supports Firefox and Chromium browsers. Support for other browsers may be added in the future.

\sphinxAtStartPar
If you need to use a different browser, you can:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Modify the Docker Image}: Create a custom Docker image with the browser of your choice

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Update the API Server}: Modify the API server to support the new browser

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Contribute the Changes}: Contribute your changes back to the project

\end{enumerate}

\sphinxAtStartPar
For more information on extending bahtBrowse, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\paragraph{Is bahtBrowse suitable for enterprise use?}
\label{\detokenize{troubleshooting/faq:is-bahtbrowse-suitable-for-enterprise-use}}
\sphinxAtStartPar
Yes, bahtBrowse can be used in enterprise environments. However, for enterprise use, you should consider:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authentication}: Implement user authentication to restrict access to the bahtBrowse service

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authorization}: Implement authorization to control what users can do

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Logging and Monitoring}: Set up logging and monitoring to track usage and detect issues

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{High Availability}: Set up a high\sphinxhyphen{}availability configuration to ensure service continuity

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Scalability}: Implement load balancing to distribute browser sessions across multiple servers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Security Hardening}: Apply additional security measures to protect the service

\end{enumerate}

\sphinxAtStartPar
For more information on enterprise deployments, see the {\hyperref[\detokenize{admin_guide/deployment::doc}]{\sphinxcrossref{\DUrole{doc}{Deployment}}}} section.


\paragraph{How does bahtBrowse compare to commercial RBI solutions?}
\label{\detokenize{troubleshooting/faq:how-does-bahtbrowse-compare-to-commercial-rbi-solutions}}
\sphinxAtStartPar
bahtBrowse is an open\sphinxhyphen{}source alternative to commercial Remote Browser Isolation (RBI) solutions. Compared to commercial solutions, bahtBrowse:

\sphinxAtStartPar
\sphinxstylestrong{Advantages}:
\sphinxhyphen{} \sphinxstylestrong{Cost}: Free and open\sphinxhyphen{}source
\sphinxhyphen{} \sphinxstylestrong{Customizability}: Can be customized to meet specific requirements
\sphinxhyphen{} \sphinxstylestrong{Transparency}: Source code is available for review
\sphinxhyphen{} \sphinxstylestrong{Community Support}: Supported by the open\sphinxhyphen{}source community

\sphinxAtStartPar
\sphinxstylestrong{Disadvantages}:
\sphinxhyphen{} \sphinxstylestrong{Features}: May have fewer features than commercial solutions
\sphinxhyphen{} \sphinxstylestrong{Support}: No commercial support (though community support is available)
\sphinxhyphen{} \sphinxstylestrong{Integration}: May require more effort to integrate with existing systems
\sphinxhyphen{} \sphinxstylestrong{Documentation}: Documentation may be less comprehensive

\sphinxAtStartPar
For a detailed comparison with specific commercial solutions, see the \DUrole{xref}{\DUrole{std}{\DUrole{std-doc}{../admin\_guide/comparison}}} section.


\paragraph{Can I use bahtBrowse on mobile devices?}
\label{\detokenize{troubleshooting/faq:can-i-use-bahtbrowse-on-mobile-devices}}
\sphinxAtStartPar
bahtBrowse is primarily designed for desktop use, but you can access it from mobile devices through a web browser. However, the user experience may not be optimized for mobile devices.

\sphinxAtStartPar
To use bahtBrowse on mobile devices:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Access via Browser}: Open your mobile browser and navigate to the bahtBrowse URL

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Interface}: Use the VNC interface to interact with the containerized browser

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Mobile Limitations}: Be aware of mobile\sphinxhyphen{}specific limitations, such as screen size and touch input

\end{enumerate}

\sphinxAtStartPar
For a better mobile experience, consider using a mobile\sphinxhyphen{}optimized VNC client.


\subsubsection{Getting Help}
\label{\detokenize{troubleshooting/faq:getting-help}}

\paragraph{Where can I get help with bahtBrowse?}
\label{\detokenize{troubleshooting/faq:where-can-i-get-help-with-bahtbrowse}}
\sphinxAtStartPar
If you need help with bahtBrowse, you can:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Check the Documentation}: Read the documentation for answers to common questions

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Search for Similar Issues}: Search for similar issues on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Ask the Community}: Ask the community for help on GitHub Discussions or other forums

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Report an Issue}: If you’ve found a bug, report it on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Contact the Maintainers}: Contact the maintainers for help with complex issues

\end{enumerate}

\sphinxAtStartPar
For more information on getting help, see the {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}} section.


\paragraph{How do I stay updated on bahtBrowse developments?}
\label{\detokenize{troubleshooting/faq:how-do-i-stay-updated-on-bahtbrowse-developments}}
\sphinxAtStartPar
To stay updated on bahtBrowse developments:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Watch the Repository}: Watch the bahtBrowse repository on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Star the Repository}: Star the repository to show your interest

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Follow the Maintainers}: Follow the maintainers on GitHub

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Join the Community}: Join the bahtBrowse community on GitHub Discussions or other forums

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Subscribe to Releases}: Subscribe to release notifications on GitHub

\end{enumerate}

\sphinxAtStartPar
For more information on staying updated, see the project’s GitHub repository.


\subsection{Introduction}
\label{\detokenize{troubleshooting/index:introduction}}
\sphinxAtStartPar
Even with careful setup and configuration, you might encounter issues when using bahtBrowse. This troubleshooting guide aims to help you identify and resolve common problems.

\sphinxAtStartPar
If you can’t find a solution to your issue in this guide, please check the {\hyperref[\detokenize{troubleshooting/faq::doc}]{\sphinxcrossref{\DUrole{doc}{Frequently Asked Questions}}}} or consider {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} to report the issue.


\subsection{Common Issues}
\label{\detokenize{troubleshooting/index:common-issues}}
\sphinxAtStartPar
Here are some common issues you might encounter when using bahtBrowse:


\subsubsection{Connection Issues}
\label{\detokenize{troubleshooting/index:connection-issues}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: Unable to connect to the bahtBrowse service.

\sphinxAtStartPar
\sphinxstylestrong{Solution}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check if the bahtBrowse service is running:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Verify that the ports are correctly mapped:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }ps
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Check if the ports are in use by other services:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
netstat\PYG{+w}{ }\PYGZhy{}tuln\PYG{+w}{ }\PYG{p}{|}\PYG{+w}{ }grep\PYG{+w}{ }\PYG{l+m}{8082}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Restart the service:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }down
docker\PYGZhy{}compose\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
For more details, see {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}}.


\subsubsection{404 Errors}
\label{\detokenize{troubleshooting/index:errors}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: Receiving 404 errors when trying to access a URL through bahtBrowse.

\sphinxAtStartPar
\sphinxstylestrong{Solution}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check if the URL is valid and accessible directly from your browser.

\item {} 
\sphinxAtStartPar
Verify that the API server is running and accessible.

\item {} 
\sphinxAtStartPar
Check the logs for any error messages:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Ensure that the nginx configuration is correct and that the novnc\_proxy.html file is being served correctly.

\end{enumerate}

\sphinxAtStartPar
For more details, see {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}}.


\subsubsection{Firefox Plugin Issues}
\label{\detokenize{troubleshooting/index:firefox-plugin-issues}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The Firefox plugin is not working correctly.

\sphinxAtStartPar
\sphinxstylestrong{Solution}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check if the plugin is installed correctly:
\sphinxhyphen{} Open Firefox
\sphinxhyphen{} Navigate to \sphinxcode{\sphinxupquote{about:addons}}
\sphinxhyphen{} Verify that the bahtBrowse plugin is listed and enabled

\item {} 
\sphinxAtStartPar
Check if the plugin is configured correctly:
\sphinxhyphen{} Click on the plugin icon in the toolbar
\sphinxhyphen{} Click on “Options”
\sphinxhyphen{} Verify that the host and port settings are correct

\item {} 
\sphinxAtStartPar
Check the browser console for any error messages:
\sphinxhyphen{} Press F12 to open the developer tools
\sphinxhyphen{} Go to the Console tab
\sphinxhyphen{} Look for any error messages related to the plugin

\end{enumerate}

\sphinxAtStartPar
For more details, see {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}}.


\subsubsection{Performance Issues}
\label{\detokenize{troubleshooting/index:performance-issues}}
\sphinxAtStartPar
\sphinxstylestrong{Issue}: The bahtBrowse service is slow or unresponsive.

\sphinxAtStartPar
\sphinxstylestrong{Solution}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check the system resources:
\sphinxhyphen{} CPU usage: \sphinxcode{\sphinxupquote{top}}
\sphinxhyphen{} Memory usage: \sphinxcode{\sphinxupquote{free \sphinxhyphen{}h}}
\sphinxhyphen{} Disk usage: \sphinxcode{\sphinxupquote{df \sphinxhyphen{}h}}

\item {} 
\sphinxAtStartPar
Check the Docker container resources:
\sphinxhyphen{} \sphinxcode{\sphinxupquote{docker stats}}

\item {} 
\sphinxAtStartPar
Consider increasing the resources allocated to Docker.

\item {} 
\sphinxAtStartPar
Optimize the browser settings for performance.

\end{enumerate}

\sphinxAtStartPar
For more details, see {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}}.


\subsection{Logging and Debugging}
\label{\detokenize{troubleshooting/index:logging-and-debugging}}
\sphinxAtStartPar
bahtBrowse provides several logging mechanisms to help you diagnose issues:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}api
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Container Logs}:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Console Logs}:

\sphinxAtStartPar
These are logged to \sphinxcode{\sphinxupquote{/tmp/browser\_console.log}} inside the container.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Server Logs}:

\sphinxAtStartPar
These are logged to \sphinxcode{\sphinxupquote{/tmp/x11vnc.log}} inside the container.

\end{enumerate}

\sphinxAtStartPar
For more advanced debugging techniques, see {\hyperref[\detokenize{troubleshooting/common_issues::doc}]{\sphinxcrossref{\DUrole{doc}{Common Issues}}}}.


\subsection{Frequently Asked Questions}
\label{\detokenize{troubleshooting/index:frequently-asked-questions}}
\sphinxAtStartPar
For answers to frequently asked questions, see {\hyperref[\detokenize{troubleshooting/faq::doc}]{\sphinxcrossref{\DUrole{doc}{Frequently Asked Questions}}}}.


\subsection{Getting Help}
\label{\detokenize{troubleshooting/index:getting-help}}
\sphinxAtStartPar
If you can’t find a solution to your issue in this guide, you can:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check the {\hyperref[\detokenize{troubleshooting/faq::doc}]{\sphinxcrossref{\DUrole{doc}{Frequently Asked Questions}}}} for answers to common questions.

\item {} 
\sphinxAtStartPar
Search for similar issues in the GitHub repository.

\item {} 
\sphinxAtStartPar
Report the issue on GitHub following the guidelines in {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}}.

\end{enumerate}

\sphinxstepscope


\section{Administrator Guide}
\label{\detokenize{admin_guide/index:administrator-guide}}\label{\detokenize{admin_guide/index:admin-guide}}\label{\detokenize{admin_guide/index::doc}}
\sphinxAtStartPar
This guide provides information for administrators who are deploying and managing bahtBrowse.


\subsection{Overview}
\label{\detokenize{admin_guide/index:overview}}
\sphinxAtStartPar
The Administrator Guide covers the following topics:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Deployment}: How to deploy bahtBrowse in various environments

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Configuration}: How to configure bahtBrowse for your specific needs

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Maintenance}: How to maintain and update bahtBrowse

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Monitoring}: How to monitor bahtBrowse performance and usage

\end{itemize}


\subsection{Deployment}
\label{\detokenize{admin_guide/index:deployment}}
\sphinxAtStartPar
See the {\hyperref[\detokenize{admin_guide/deployment::doc}]{\sphinxcrossref{\DUrole{doc}{Deployment}}}} section for information on deploying bahtBrowse.


\subsection{Configuration}
\label{\detokenize{admin_guide/index:configuration}}
\sphinxAtStartPar
See the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section for information on configuring bahtBrowse.


\subsection{Maintenance}
\label{\detokenize{admin_guide/index:maintenance}}
\sphinxAtStartPar
See the {\hyperref[\detokenize{admin_guide/maintenance::doc}]{\sphinxcrossref{\DUrole{doc}{Maintenance}}}} section for information on maintaining bahtBrowse.

\sphinxstepscope


\subsubsection{Deployment}
\label{\detokenize{admin_guide/deployment:deployment}}\label{\detokenize{admin_guide/deployment:id1}}\label{\detokenize{admin_guide/deployment::doc}}
\sphinxAtStartPar
This guide provides information on how to deploy bahtBrowse in various environments.


\paragraph{Overview}
\label{\detokenize{admin_guide/deployment:overview}}
\sphinxAtStartPar
bahtBrowse can be deployed in various environments, from a single server to a distributed cluster. This guide covers the following deployment topics:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Prerequisites}: What you need before deploying bahtBrowse

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Single Server Deployment}: How to deploy bahtBrowse on a single server

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Distributed Deployment}: How to deploy bahtBrowse across multiple servers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Cloud Deployment}: How to deploy bahtBrowse in the cloud

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Kubernetes Deployment}: How to deploy bahtBrowse on Kubernetes

\end{itemize}


\paragraph{Prerequisites}
\label{\detokenize{admin_guide/deployment:prerequisites}}
\sphinxAtStartPar
Before deploying bahtBrowse, you need to have the following prerequisites:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker}: bahtBrowse uses Docker to run its containers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Compose}: bahtBrowse uses Docker Compose to manage its containers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Python 3.9+}: bahtBrowse requires Python 3.9 or later

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Git}: To clone the bahtBrowse repository

\end{itemize}


\paragraph{Single Server Deployment}
\label{\detokenize{admin_guide/deployment:single-server-deployment}}
\sphinxAtStartPar
The simplest way to deploy bahtBrowse is on a single server.


\subparagraph{Clone the Repository}
\label{\detokenize{admin_guide/deployment:clone-the-repository}}
\sphinxAtStartPar
First, clone the bahtBrowse repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}


\subparagraph{Build and Start the Containers}
\label{\detokenize{admin_guide/deployment:build-and-start-the-containers}}
\sphinxAtStartPar
Use Docker Compose to build and start the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\sphinxAtStartPar
This will build the Docker images and start the containers in the background.


\subparagraph{Verify the Deployment}
\label{\detokenize{admin_guide/deployment:verify-the-deployment}}
\sphinxAtStartPar
Verify that the containers are running:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }ps
\end{sphinxVerbatim}

\sphinxAtStartPar
You should see all the containers running.


\paragraph{Distributed Deployment}
\label{\detokenize{admin_guide/deployment:distributed-deployment}}
\sphinxAtStartPar
For larger deployments, you can distribute bahtBrowse across multiple servers.


\subparagraph{Architecture}
\label{\detokenize{admin_guide/deployment:architecture}}
\sphinxAtStartPar
A distributed deployment of bahtBrowse typically consists of the following components:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Servers}: Handle API requests and manage browser sessions

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Redis Servers}: Store session data and coordinate between components

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Worker Servers}: Manage browser containers and handle background tasks

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Servers}: Run the actual browsers (Firefox, Chromium, etc.)

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Monitoring Servers}: Run monitoring tools like Elasticsearch, Kibana, and Logstash

\end{itemize}

\sphinxAtStartPar
Each component can be deployed on a separate server or group of servers.


\subparagraph{Example Deployment}
\label{\detokenize{admin_guide/deployment:example-deployment}}
\sphinxAtStartPar
Here’s an example of a distributed deployment:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Server 1}: API Servers and Redis

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Server 2}: Worker Servers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Server 3}: Browser Servers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Server 4}: Monitoring Servers

\end{itemize}


\paragraph{Cloud Deployment}
\label{\detokenize{admin_guide/deployment:cloud-deployment}}
\sphinxAtStartPar
bahtBrowse can be deployed in the cloud using various cloud providers.


\subparagraph{AWS Deployment}
\label{\detokenize{admin_guide/deployment:aws-deployment}}
\sphinxAtStartPar
To deploy bahtBrowse on AWS:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create EC2 instances for each component

\item {} 
\sphinxAtStartPar
Install Docker and Docker Compose on each instance

\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository on each instance

\item {} 
\sphinxAtStartPar
Configure the Docker Compose file for the distributed deployment

\item {} 
\sphinxAtStartPar
Start the containers on each instance

\end{enumerate}


\subparagraph{Google Cloud Deployment}
\label{\detokenize{admin_guide/deployment:google-cloud-deployment}}
\sphinxAtStartPar
To deploy bahtBrowse on Google Cloud:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create Compute Engine instances for each component

\item {} 
\sphinxAtStartPar
Install Docker and Docker Compose on each instance

\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository on each instance

\item {} 
\sphinxAtStartPar
Configure the Docker Compose file for the distributed deployment

\item {} 
\sphinxAtStartPar
Start the containers on each instance

\end{enumerate}


\subparagraph{Azure Deployment}
\label{\detokenize{admin_guide/deployment:azure-deployment}}
\sphinxAtStartPar
To deploy bahtBrowse on Azure:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create Virtual Machines for each component

\item {} 
\sphinxAtStartPar
Install Docker and Docker Compose on each VM

\item {} 
\sphinxAtStartPar
Clone the bahtBrowse repository on each VM

\item {} 
\sphinxAtStartPar
Configure the Docker Compose file for the distributed deployment

\item {} 
\sphinxAtStartPar
Start the containers on each VM

\end{enumerate}


\paragraph{Kubernetes Deployment}
\label{\detokenize{admin_guide/deployment:kubernetes-deployment}}
\sphinxAtStartPar
bahtBrowse can be deployed on Kubernetes for better scalability and management.


\subparagraph{Prerequisites}
\label{\detokenize{admin_guide/deployment:id2}}
\sphinxAtStartPar
Before deploying bahtBrowse on Kubernetes, you need to have the following:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Kubernetes Cluster}: A running Kubernetes cluster

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{kubectl}: The Kubernetes command\sphinxhyphen{}line tool

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Helm}: The Kubernetes package manager (optional)

\end{itemize}


\subparagraph{Deployment Steps}
\label{\detokenize{admin_guide/deployment:deployment-steps}}
\sphinxAtStartPar
To deploy bahtBrowse on Kubernetes:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Create Kubernetes manifests for each component

\item {} 
\sphinxAtStartPar
Apply the manifests to the Kubernetes cluster

\item {} 
\sphinxAtStartPar
Verify that the pods are running

\item {} 
\sphinxAtStartPar
Configure ingress for external access

\end{enumerate}


\subparagraph{Example Manifests}
\label{\detokenize{admin_guide/deployment:example-manifests}}
\sphinxAtStartPar
Here’s an example of a Kubernetes manifest for the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{apiVersion}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{apps/v1}
\PYG{n+nt}{kind}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Deployment}
\PYG{n+nt}{metadata}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{n+nt}{spec}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{replicas}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{3}
\PYG{+w}{  }\PYG{n+nt}{selector}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{matchLabels}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{app}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{+w}{  }\PYG{n+nt}{template}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{metadata}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{labels}\PYG{p}{:}
\PYG{+w}{        }\PYG{n+nt}{app}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{+w}{    }\PYG{n+nt}{spec}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{containers}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{+w}{        }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse/api:latest}
\PYG{+w}{        }\PYG{n+nt}{ports}\PYG{p}{:}
\PYG{+w}{        }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{containerPort}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{8082}
\PYG{+w}{        }\PYG{n+nt}{env}\PYG{p}{:}
\PYG{+w}{        }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{REDIS\PYGZus{}HOST}
\PYG{+w}{          }\PYG{n+nt}{value}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{redis}
\PYG{n+nn}{\PYGZhy{}\PYGZhy{}\PYGZhy{}}
\PYG{n+nt}{apiVersion}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{v1}
\PYG{n+nt}{kind}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Service}
\PYG{n+nt}{metadata}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{n+nt}{spec}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{selector}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{app}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{api}
\PYG{+w}{  }\PYG{n+nt}{ports}\PYG{p}{:}
\PYG{+w}{  }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{port}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{8082}
\PYG{+w}{    }\PYG{n+nt}{targetPort}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{8082}
\PYG{+w}{  }\PYG{n+nt}{type}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{ClusterIP}
\end{sphinxVerbatim}


\paragraph{Conclusion}
\label{\detokenize{admin_guide/deployment:conclusion}}
\sphinxAtStartPar
This guide covered the various deployment options for bahtBrowse. By following these guidelines, you can deploy bahtBrowse in the environment that best suits your needs.

\sphinxAtStartPar
For more information on configuring bahtBrowse, see the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.

\sphinxAtStartPar
For more information on maintaining bahtBrowse, see the {\hyperref[\detokenize{admin_guide/maintenance::doc}]{\sphinxcrossref{\DUrole{doc}{Maintenance}}}} section.

\sphinxstepscope


\subsubsection{Configuration}
\label{\detokenize{admin_guide/configuration:configuration}}\label{\detokenize{admin_guide/configuration:id1}}\label{\detokenize{admin_guide/configuration::doc}}
\sphinxAtStartPar
This guide provides information on how to configure bahtBrowse for your specific needs.


\paragraph{Overview}
\label{\detokenize{admin_guide/configuration:overview}}
\sphinxAtStartPar
bahtBrowse can be configured in various ways to suit your specific requirements. This guide covers the following configuration topics:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Configuration}: How to configure Docker containers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Configuration}: How to configure network settings

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Port Configuration}: How to configure port settings and handle port conflicts

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Configuration}: How to configure browser settings

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Security Configuration}: How to configure security settings

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Performance Configuration}: How to configure performance settings

\end{itemize}


\paragraph{Docker Configuration}
\label{\detokenize{admin_guide/configuration:docker-configuration}}
\sphinxAtStartPar
Docker containers are the core of bahtBrowse. Each browser instance runs in its own Docker container, providing isolation and security.


\subparagraph{Docker Compose}
\label{\detokenize{admin_guide/configuration:docker-compose}}
\sphinxAtStartPar
bahtBrowse uses Docker Compose to manage its containers. The Docker Compose configuration is defined in the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.fixed.yml}} file.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{version}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s}{\PYGZsq{}}\PYG{l+s}{3}\PYG{l+s}{\PYGZsq{}}
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{api}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{build}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{context}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{.}
\PYG{+w}{      }\PYG{n+nt}{dockerfile}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{docker/Dockerfile.api}
\PYG{+w}{    }\PYG{n+nt}{ports}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+s}{\PYGZdq{}}\PYG{l+s}{8082:8082}\PYG{l+s}{\PYGZdq{}}
\PYG{+w}{    }\PYG{n+nt}{volumes}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{./files:/app/files}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{REDIS\PYGZus{}HOST=redis}
\PYG{+w}{    }\PYG{n+nt}{depends\PYGZus{}on}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{redis}
\PYG{+w}{  }\PYG{n+nt}{redis}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{image}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{redis:alpine}
\PYG{+w}{    }\PYG{n+nt}{ports}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+s}{\PYGZdq{}}\PYG{l+s}{6379:6379}\PYG{l+s}{\PYGZdq{}}
\PYG{+w}{  }\PYG{n+nt}{worker}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{build}\PYG{p}{:}
\PYG{+w}{      }\PYG{n+nt}{context}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{.}
\PYG{+w}{      }\PYG{n+nt}{dockerfile}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{docker/Dockerfile.worker}
\PYG{+w}{    }\PYG{n+nt}{volumes}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{/var/run/docker.sock:/var/run/docker.sock}
\PYG{+w}{    }\PYG{n+nt}{environment}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{REDIS\PYGZus{}HOST=redis}
\PYG{+w}{    }\PYG{n+nt}{depends\PYGZus{}on}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{redis}
\end{sphinxVerbatim}


\subparagraph{Docker Images}
\label{\detokenize{admin_guide/configuration:docker-images}}
\sphinxAtStartPar
bahtBrowse uses several Docker images:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: Handles API requests and manages browser sessions

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Redis}: Stores session data and coordinates between components

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Worker}: Manages browser containers and handles background tasks

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Browser Containers}: Run the actual browsers (Firefox, Chromium, etc.)

\end{itemize}


\paragraph{Network Configuration}
\label{\detokenize{admin_guide/configuration:network-configuration}}
\sphinxAtStartPar
bahtBrowse uses Docker’s networking capabilities to connect its containers.


\subparagraph{Network Mode}
\label{\detokenize{admin_guide/configuration:network-mode}}
\sphinxAtStartPar
By default, bahtBrowse uses the \sphinxcode{\sphinxupquote{bridge}} network mode, which creates a virtual network for the containers.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{api}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}network}
\PYG{+w}{  }\PYG{n+nt}{redis}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bahtbrowse\PYGZhy{}network}

\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{bahtbrowse\PYGZhy{}network}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{driver}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bridge}
\end{sphinxVerbatim}


\paragraph{Port Configuration}
\label{\detokenize{admin_guide/configuration:port-configuration}}
\sphinxAtStartPar
bahtBrowse uses several ports for its services. These ports can be configured to avoid conflicts with other services.


\subparagraph{Default Ports}
\label{\detokenize{admin_guide/configuration:default-ports}}
\sphinxAtStartPar
The default ports used by bahtBrowse are:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: 8082

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Redis}: 6379

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Flower}: 5555

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx}: 80

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Admin Dashboard}: 9001

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Elasticsearch}: 9200

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Kibana}: 5601

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Logstash}: 9600

\end{itemize}


\subparagraph{Port Conflict Detection and Resolution}
\label{\detokenize{admin_guide/configuration:port-conflict-detection-and-resolution}}
\sphinxAtStartPar
bahtBrowse includes a port conflict detection and resolution system that automatically detects when default ports are already in use and finds alternative ports. This makes the system more resilient when running in environments where the default ports might already be in use by other applications.

\sphinxAtStartPar
The port conflict detection and resolution system is implemented in the \sphinxcode{\sphinxupquote{bahtbrowse\_cli.py}} file and provides the following features:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Automatic detection of port conflicts}: The system checks if the default ports are already in use.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Dynamic port allocation}: If a default port is in use, the system finds an available port in the appropriate range.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Visual indicators}: Services using non\sphinxhyphen{}default ports are highlighted in the status table.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Configurable port ranges}: Port ranges can be configured for different service categories.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Seamless communication}: Services can still communicate with each other when using alternative ports.

\end{itemize}


\subparagraph{Port Ranges}
\label{\detokenize{admin_guide/configuration:port-ranges}}
\sphinxAtStartPar
The port conflict resolution system defines the following port ranges for different service categories:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Web Services}: 8000\sphinxhyphen{}8100

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Monitoring Services}: 5500\sphinxhyphen{}5700

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Infrastructure Services}: 6300\sphinxhyphen{}6400

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Core Services}: 8000\sphinxhyphen{}8100

\end{itemize}

\sphinxAtStartPar
These ranges can be configured in the \sphinxcode{\sphinxupquote{PORT\_RANGES}} dictionary in the \sphinxcode{\sphinxupquote{bahtbrowse\_cli.py}} file.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n}{PORT\PYGZus{}RANGES} \PYG{o}{=} \PYG{p}{\PYGZob{}}
    \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{web}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{p}{(}\PYG{l+m+mi}{8000}\PYG{p}{,} \PYG{l+m+mi}{8100}\PYG{p}{)}\PYG{p}{,}           \PYG{c+c1}{\PYGZsh{} Web services}
    \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{monitoring}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{p}{(}\PYG{l+m+mi}{5500}\PYG{p}{,} \PYG{l+m+mi}{5700}\PYG{p}{)}\PYG{p}{,}    \PYG{c+c1}{\PYGZsh{} Monitoring services}
    \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{infrastructure}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{p}{(}\PYG{l+m+mi}{6300}\PYG{p}{,} \PYG{l+m+mi}{6400}\PYG{p}{)}\PYG{p}{,} \PYG{c+c1}{\PYGZsh{} Infrastructure services}
    \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{core}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{p}{(}\PYG{l+m+mi}{8000}\PYG{p}{,} \PYG{l+m+mi}{8100}\PYG{p}{)}           \PYG{c+c1}{\PYGZsh{} Core services}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}


\subparagraph{Using the Port Conflict Resolution System}
\label{\detokenize{admin_guide/configuration:using-the-port-conflict-resolution-system}}
\sphinxAtStartPar
The port conflict resolution system is integrated into the bahtBrowse CLI tool. When you start services using the CLI, it automatically checks if the default ports are available and finds alternative ports if needed.

\sphinxAtStartPar
To start all services with automatic port conflict resolution:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./bahtbrowse\PYGZus{}cli.py\PYG{+w}{ }\PYGZhy{}\PYGZhy{}start\PYGZhy{}all
\end{sphinxVerbatim}

\sphinxAtStartPar
To start a specific service:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./bahtbrowse\PYGZus{}cli.py\PYG{+w}{ }\PYGZhy{}\PYGZhy{}start\PYG{+w}{ }\PYGZlt{}service\PYGZus{}name\PYGZgt{}
\end{sphinxVerbatim}

\sphinxAtStartPar
To view the status of all services, including their port assignments:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./bahtbrowse\PYGZus{}cli.py\PYG{+w}{ }\PYGZhy{}\PYGZhy{}status
\end{sphinxVerbatim}

\sphinxAtStartPar
In the status table, services using non\sphinxhyphen{}default ports will have their port numbers highlighted in yellow.


\paragraph{Browser Configuration}
\label{\detokenize{admin_guide/configuration:browser-configuration}}
\sphinxAtStartPar
bahtBrowse supports multiple browsers, each with its own configuration options.


\subparagraph{Browser Types}
\label{\detokenize{admin_guide/configuration:browser-types}}
\sphinxAtStartPar
bahtBrowse supports the following browser types:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox}: Mozilla Firefox browser

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Chromium}: Chromium browser

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Tor Browser}: Tor Browser for anonymous browsing

\end{itemize}


\subparagraph{Browser Pool Configuration}
\label{\detokenize{admin_guide/configuration:browser-pool-configuration}}
\sphinxAtStartPar
bahtBrowse includes a browser pool configuration feature that allows you to configure how many browsers of each type should be kept ready. This feature is implemented in the \sphinxcode{\sphinxupquote{docker\_queue/api/queue\_config\_routes.py}} file.

\sphinxAtStartPar
The browser pool configuration includes the following settings:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Min Pool Size}: The minimum number of browser containers to keep ready

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Max Pool Size}: The maximum number of browser containers to allow

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Idle Timeout}: How long to keep idle containers before removing them

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Per\sphinxhyphen{}Browser Type Settings}: Configure settings for each browser type

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}global\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}min\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{2}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}max\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{10}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}idle\PYGZus{}timeout\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{300}
\PYG{+w}{  }\PYG{p}{\PYGZcb{},}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}firefox\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}min\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{1}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}max\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{5}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}idle\PYGZus{}timeout\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{300}
\PYG{+w}{  }\PYG{p}{\PYGZcb{},}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}chromium\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}min\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{1}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}max\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{5}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}idle\PYGZus{}timeout\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{300}
\PYG{+w}{  }\PYG{p}{\PYGZcb{},}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}tor\PYGZhy{}browser\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}min\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{0}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}max\PYGZus{}pool\PYGZus{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{2}\PYG{p}{,}
\PYG{+w}{    }\PYG{n+nt}{\PYGZdq{}idle\PYGZus{}timeout\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{600}
\PYG{+w}{  }\PYG{p}{\PYGZcb{}}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}


\paragraph{Security Configuration}
\label{\detokenize{admin_guide/configuration:security-configuration}}
\sphinxAtStartPar
bahtBrowse includes several security features that can be configured.


\subparagraph{Container Isolation}
\label{\detokenize{admin_guide/configuration:container-isolation}}
\sphinxAtStartPar
Browser containers are isolated from each other and from the host system. This isolation can be configured in the Docker Compose file.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{security\PYGZus{}opt}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{seccomp=seccomp\PYGZhy{}firefox.json}
\PYG{+w}{    }\PYG{n+nt}{cap\PYGZus{}drop}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{ALL}
\PYG{+w}{    }\PYG{n+nt}{cap\PYGZus{}add}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{NET\PYGZus{}ADMIN}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{SYS\PYGZus{}ADMIN}
\end{sphinxVerbatim}


\subparagraph{Network Security}
\label{\detokenize{admin_guide/configuration:network-security}}
\sphinxAtStartPar
bahtBrowse includes network security features that can be configured.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{nginx}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{frontend}
\PYG{+w}{  }\PYG{n+nt}{api}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{frontend}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{backend}
\PYG{+w}{  }\PYG{n+nt}{redis}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{      }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{backend}

\PYG{n+nt}{networks}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{frontend}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{driver}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bridge}
\PYG{+w}{  }\PYG{n+nt}{backend}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{driver}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{bridge}
\PYG{+w}{    }\PYG{n+nt}{internal}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{true}
\end{sphinxVerbatim}


\paragraph{Performance Configuration}
\label{\detokenize{admin_guide/configuration:performance-configuration}}
\sphinxAtStartPar
bahtBrowse includes several performance features that can be configured.


\subparagraph{Resource Limits}
\label{\detokenize{admin_guide/configuration:resource-limits}}
\sphinxAtStartPar
Docker containers can have resource limits configured in the Docker Compose file.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{services}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{firefox}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{mem\PYGZus{}limit}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{2g}
\PYG{+w}{    }\PYG{n+nt}{cpus}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{2}
\PYG{+w}{  }\PYG{n+nt}{chromium}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{mem\PYGZus{}limit}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{2g}
\PYG{+w}{    }\PYG{n+nt}{cpus}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{2}
\PYG{+w}{  }\PYG{n+nt}{tor\PYGZhy{}browser}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{mem\PYGZus{}limit}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{1g}
\PYG{+w}{    }\PYG{n+nt}{cpus}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{1}
\end{sphinxVerbatim}


\subparagraph{Scaling}
\label{\detokenize{admin_guide/configuration:scaling}}
\sphinxAtStartPar
bahtBrowse can be scaled to handle more users by increasing the number of worker containers.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYGZhy{}\PYGZhy{}scale\PYG{+w}{ }\PYG{n+nv}{worker}\PYG{o}{=}\PYG{l+m}{3}
\end{sphinxVerbatim}


\paragraph{Conclusion}
\label{\detokenize{admin_guide/configuration:conclusion}}
\sphinxAtStartPar
This guide covered the various configuration options available in bahtBrowse. By configuring these options, you can customize bahtBrowse to suit your specific needs.

\sphinxAtStartPar
For more information on deploying bahtBrowse, see the {\hyperref[\detokenize{admin_guide/deployment::doc}]{\sphinxcrossref{\DUrole{doc}{Deployment}}}} section.

\sphinxAtStartPar
For more information on maintaining bahtBrowse, see the {\hyperref[\detokenize{admin_guide/maintenance::doc}]{\sphinxcrossref{\DUrole{doc}{Maintenance}}}} section.

\sphinxstepscope


\subsubsection{Maintenance}
\label{\detokenize{admin_guide/maintenance:maintenance}}\label{\detokenize{admin_guide/maintenance:id1}}\label{\detokenize{admin_guide/maintenance::doc}}
\sphinxAtStartPar
This guide provides information on how to maintain and update bahtBrowse.


\paragraph{Overview}
\label{\detokenize{admin_guide/maintenance:overview}}
\sphinxAtStartPar
Maintaining bahtBrowse involves several tasks, including:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Updating}: Keeping bahtBrowse and its dependencies up to date

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Monitoring}: Monitoring the health and performance of bahtBrowse

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Backup}: Backing up bahtBrowse data

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Troubleshooting}: Troubleshooting common issues

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Testing}: Testing bahtBrowse to ensure it’s working correctly

\end{itemize}


\paragraph{Updating}
\label{\detokenize{admin_guide/maintenance:updating}}
\sphinxAtStartPar
Keeping bahtBrowse up to date is important for security and performance.


\subparagraph{Updating bahtBrowse}
\label{\detokenize{admin_guide/maintenance:updating-bahtbrowse}}
\sphinxAtStartPar
To update bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Pull the latest changes from the repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
git\PYG{+w}{ }pull
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Rebuild the Docker images:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }build
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Restart the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}


\subparagraph{Updating Dependencies}
\label{\detokenize{admin_guide/maintenance:updating-dependencies}}
\sphinxAtStartPar
bahtBrowse depends on several external components, including Docker, Docker Compose, and Python packages.

\sphinxAtStartPar
To update Docker:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
sudo\PYG{+w}{ }apt\PYGZhy{}get\PYG{+w}{ }update
sudo\PYG{+w}{ }apt\PYGZhy{}get\PYG{+w}{ }upgrade\PYG{+w}{ }docker\PYGZhy{}ce
\end{sphinxVerbatim}

\sphinxAtStartPar
To update Docker Compose:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
sudo\PYG{+w}{ }curl\PYG{+w}{ }\PYGZhy{}L\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://github.com/docker/compose/releases/download/latest/docker\PYGZhy{}compose\PYGZhy{}}\PYG{k}{\PYGZdl{}(}uname\PYG{+w}{ }\PYGZhy{}s\PYG{k}{)}\PYG{l+s+s2}{\PYGZhy{}}\PYG{k}{\PYGZdl{}(}uname\PYG{+w}{ }\PYGZhy{}m\PYG{k}{)}\PYG{l+s+s2}{\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}o\PYG{+w}{ }/usr/local/bin/docker\PYGZhy{}compose
sudo\PYG{+w}{ }chmod\PYG{+w}{ }+x\PYG{+w}{ }/usr/local/bin/docker\PYGZhy{}compose
\end{sphinxVerbatim}

\sphinxAtStartPar
To update Python packages:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}U\PYG{+w}{ }\PYGZhy{}r\PYG{+w}{ }requirements.txt
\end{sphinxVerbatim}


\paragraph{Monitoring}
\label{\detokenize{admin_guide/maintenance:monitoring}}
\sphinxAtStartPar
Monitoring bahtBrowse is important for ensuring it’s running correctly and efficiently.


\subparagraph{Health Checks}
\label{\detokenize{admin_guide/maintenance:health-checks}}
\sphinxAtStartPar
bahtBrowse includes health checks for its services. You can use the CLI tool to check the health of all services:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./bahtbrowse\PYGZus{}cli.py\PYG{+w}{ }\PYGZhy{}\PYGZhy{}status
\end{sphinxVerbatim}

\sphinxAtStartPar
This will show the status and health of all services.

\sphinxAtStartPar
Logs
\textasciitilde{}\textasciitilde{}\textasciitilde{}

\sphinxAtStartPar
You can view the logs of bahtBrowse services using Docker Compose:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }logs
\end{sphinxVerbatim}

\sphinxAtStartPar
To view the logs of a specific service:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }logs\PYG{+w}{ }\PYGZlt{}service\PYGZus{}name\PYGZgt{}
\end{sphinxVerbatim}

\sphinxAtStartPar
For example, to view the logs of the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }logs\PYG{+w}{ }api
\end{sphinxVerbatim}


\subparagraph{ELK Stack}
\label{\detokenize{admin_guide/maintenance:elk-stack}}
\sphinxAtStartPar
bahtBrowse includes an ELK (Elasticsearch, Logstash, Kibana) stack for advanced monitoring and logging.

\sphinxAtStartPar
To access Kibana:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open a web browser

\item {} 
\sphinxAtStartPar
Navigate to \sphinxurl{http://localhost:5601}

\item {} 
\sphinxAtStartPar
Log in with the default credentials (username: elastic, password: changeme)

\end{enumerate}

\sphinxAtStartPar
Kibana provides a web interface for searching, analyzing, and visualizing logs.


\paragraph{Backup}
\label{\detokenize{admin_guide/maintenance:backup}}
\sphinxAtStartPar
Backing up bahtBrowse data is important for disaster recovery.


\subparagraph{Redis Data}
\label{\detokenize{admin_guide/maintenance:redis-data}}
\sphinxAtStartPar
Redis data can be backed up using the Redis CLI:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }bahtbrowse\PYGZus{}redis\PYGZus{}1\PYG{+w}{ }redis\PYGZhy{}cli\PYG{+w}{ }SAVE
\end{sphinxVerbatim}

\sphinxAtStartPar
This will create a snapshot of the Redis data.

\sphinxAtStartPar
To back up the Redis data to a file:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }bahtbrowse\PYGZus{}redis\PYGZus{}1\PYG{+w}{ }redis\PYGZhy{}cli\PYG{+w}{ }SAVE
docker\PYG{+w}{ }cp\PYG{+w}{ }bahtbrowse\PYGZus{}redis\PYGZus{}1:/data/dump.rdb\PYG{+w}{ }/backup/redis\PYGZhy{}dump.rdb
\end{sphinxVerbatim}


\subparagraph{Docker Volumes}
\label{\detokenize{admin_guide/maintenance:docker-volumes}}
\sphinxAtStartPar
Docker volumes can be backed up using the docker cp command:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }cp\PYG{+w}{ }bahtbrowse\PYGZus{}redis\PYGZus{}1:/data\PYG{+w}{ }/backup/redis\PYGZhy{}data
\end{sphinxVerbatim}


\subparagraph{Configuration Files}
\label{\detokenize{admin_guide/maintenance:configuration-files}}
\sphinxAtStartPar
Configuration files can be backed up using standard file system tools:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
cp\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }/backup/docker\PYGZhy{}compose.fixed.yml
\end{sphinxVerbatim}


\paragraph{Troubleshooting}
\label{\detokenize{admin_guide/maintenance:troubleshooting}}
\sphinxAtStartPar
This section covers common issues and their solutions.


\subparagraph{Container Issues}
\label{\detokenize{admin_guide/maintenance:container-issues}}
\sphinxAtStartPar
If a container is not starting:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check the container logs:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }logs\PYG{+w}{ }\PYGZlt{}service\PYGZus{}name\PYGZgt{}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Check the container status:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }ps\PYG{+w}{ }\PYGZlt{}service\PYGZus{}name\PYGZgt{}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Try restarting the container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }restart\PYG{+w}{ }\PYGZlt{}service\PYGZus{}name\PYGZgt{}
\end{sphinxVerbatim}

\end{enumerate}


\subparagraph{Port Conflicts}
\label{\detokenize{admin_guide/maintenance:port-conflicts}}
\sphinxAtStartPar
If a port conflict occurs, bahtBrowse will automatically detect it and find an alternative port. You can view the port assignments using the CLI tool:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./bahtbrowse\PYGZus{}cli.py\PYG{+w}{ }\PYGZhy{}\PYGZhy{}status
\end{sphinxVerbatim}

\sphinxAtStartPar
In the status table, services using non\sphinxhyphen{}default ports will have their port numbers highlighted in yellow.

\sphinxAtStartPar
If you need to manually configure ports, you can edit the \sphinxcode{\sphinxupquote{docker\sphinxhyphen{}compose.fixed.yml}} file and change the port mappings.


\subparagraph{Network Issues}
\label{\detokenize{admin_guide/maintenance:network-issues}}
\sphinxAtStartPar
If services cannot communicate with each other:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Check that all services are running:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.fixed.yml\PYG{+w}{ }ps
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Check the network configuration:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }network\PYG{+w}{ }ls
docker\PYG{+w}{ }network\PYG{+w}{ }inspect\PYG{+w}{ }bahtbrowse\PYGZus{}default
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Try restarting the Docker daemon:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
sudo\PYG{+w}{ }systemctl\PYG{+w}{ }restart\PYG{+w}{ }docker
\end{sphinxVerbatim}

\end{enumerate}


\paragraph{Testing}
\label{\detokenize{admin_guide/maintenance:testing}}
\sphinxAtStartPar
Testing bahtBrowse is important for ensuring it’s working correctly.


\subparagraph{Container Build Tests}
\label{\detokenize{admin_guide/maintenance:container-build-tests}}
\sphinxAtStartPar
bahtBrowse includes a container build test suite that verifies all Docker containers can be built successfully. To run the container build tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./tests/run\PYGZus{}container\PYGZus{}build\PYGZus{}tests.sh
\end{sphinxVerbatim}

\sphinxAtStartPar
This will attempt to build each Dockerfile in the project and report any failures.


\subparagraph{Port Conflict Resolution Tests}
\label{\detokenize{admin_guide/maintenance:port-conflict-resolution-tests}}
\sphinxAtStartPar
bahtBrowse includes a port conflict resolution test suite that verifies the port conflict detection and resolution system works correctly. To run the port conflict resolution tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }tests/test\PYGZus{}port\PYGZus{}conflict\PYGZus{}resolution.py
\end{sphinxVerbatim}

\sphinxAtStartPar
This will test the port conflict detection and resolution system.


\subparagraph{Container Detection Tests}
\label{\detokenize{admin_guide/maintenance:container-detection-tests}}
\sphinxAtStartPar
bahtBrowse includes a container detection test suite that verifies the container detection functionality in the CLI tool works correctly. To run the container detection tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }tests/test\PYGZus{}container\PYGZus{}detection.py
\end{sphinxVerbatim}

\sphinxAtStartPar
This will test the container detection functionality.


\subparagraph{Enhanced Tests}
\label{\detokenize{admin_guide/maintenance:enhanced-tests}}
\sphinxAtStartPar
bahtBrowse includes an enhanced test suite that runs all the above tests. To run the enhanced tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./tests/run\PYGZus{}enhanced\PYGZus{}tests.sh
\end{sphinxVerbatim}

\sphinxAtStartPar
This will run the container build tests, port conflict resolution tests, and container detection tests, and save the results to a file.


\paragraph{Conclusion}
\label{\detokenize{admin_guide/maintenance:conclusion}}
\sphinxAtStartPar
This guide covered the various maintenance tasks for bahtBrowse. By following these guidelines, you can keep bahtBrowse running smoothly and efficiently.

\sphinxAtStartPar
For more information on deploying bahtBrowse, see the {\hyperref[\detokenize{admin_guide/deployment::doc}]{\sphinxcrossref{\DUrole{doc}{Deployment}}}} section.

\sphinxAtStartPar
For more information on configuring bahtBrowse, see the {\hyperref[\detokenize{admin_guide/configuration::doc}]{\sphinxcrossref{\DUrole{doc}{Configuration}}}} section.

\sphinxstepscope


\section{Developer Guide}
\label{\detokenize{developer_guide/index:developer-guide}}\label{\detokenize{developer_guide/index:id1}}\label{\detokenize{developer_guide/index::doc}}
\sphinxAtStartPar
This developer guide provides detailed information for developers who want to understand, modify, or contribute to bahtBrowse.

\sphinxstepscope


\subsection{System Architecture}
\label{\detokenize{developer_guide/architecture:system-architecture}}\label{\detokenize{developer_guide/architecture:architecture}}\label{\detokenize{developer_guide/architecture::doc}}
\sphinxAtStartPar
This document provides a detailed overview of the bahtBrowse system architecture, including components, interactions, and design decisions.


\subsubsection{Overview}
\label{\detokenize{developer_guide/architecture:overview}}
\sphinxAtStartPar
bahtBrowse is a Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. The system isolates web browsing activities in Docker containers, protecting users from online threats while providing a seamless browsing experience.

\begin{figure}[htbp]
\centering
\capstart

\noindent\sphinxincludegraphics{{_static/architecture_overview}.png}
\caption{bahtBrowse Architecture Overview}\label{\detokenize{developer_guide/architecture:id1}}\end{figure}

\sphinxAtStartPar
The architecture consists of several key components:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Containers}: Isolated environments running Firefox or Chromium browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: Manages browser sessions and handles requests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Interface}: Provides remote access to containerized browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Plugin}: Enables seamless integration with Firefox

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Configuration}: Handles proxying and serving static files

\end{enumerate}


\subsubsection{Component Diagram}
\label{\detokenize{developer_guide/architecture:component-diagram}}
\sphinxAtStartPar
The following diagram shows the main components of the bahtBrowse system and their interactions:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
+\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
|                 |     |                |     |                |
|  Firefox Plugin |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|   API Server   |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}| Docker Engine  |
|                 |     |                |     |                |
+\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
                                |                      |
                                v                      v
                        +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
                        |                |     |                |
                        |  Nginx Proxy   |     | Browser        |
                        |                |     | Containers     |
                        +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     |                |
                                |             +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
                                v                      |
                        +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+             |
                        |                |             |
                        |  VNC Interface |\PYGZlt{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
                        |                |
                        +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
\end{sphinxVerbatim}


\subsubsection{Component Details}
\label{\detokenize{developer_guide/architecture:component-details}}

\paragraph{Docker Containers}
\label{\detokenize{developer_guide/architecture:docker-containers}}
\sphinxAtStartPar
The Docker containers are the core of the bahtBrowse system. Each container runs a browser (Firefox or Chromium) in an isolated environment.

\sphinxAtStartPar
\sphinxstylestrong{Key Features}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Isolation}: Each browser session runs in its own container, providing isolation from the host system and other sessions.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Statelessness}: Containers are designed to be stateless, with no persistent storage between sessions.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Resource Limits}: Containers have resource limits to prevent resource exhaustion.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Container Types}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Container}: Runs Firefox browser with custom profile and settings.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Chromium Container}: Runs Chromium browser with custom profile and settings.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Container Lifecycle}:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Container is created when a user requests a new browser session.

\item {} 
\sphinxAtStartPar
Browser is launched with the specified URL.

\item {} 
\sphinxAtStartPar
User interacts with the browser through the VNC interface.

\item {} 
\sphinxAtStartPar
Container is destroyed when the session ends or times out.

\end{enumerate}


\paragraph{API Server}
\label{\detokenize{developer_guide/architecture:api-server}}
\sphinxAtStartPar
The API server is responsible for managing browser sessions and handling requests from users.

\sphinxAtStartPar
\sphinxstylestrong{Key Features}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Management}: Creates and manages browser sessions.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Request Handling}: Processes requests to launch browsers and access websites.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authentication}: (Optional) Authenticates users and enforces access controls.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Logging}: Logs browser console messages and other events.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{API Endpoints}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/browse/}}: Launch a browser session with the specified URL.

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/test\sphinxhyphen{}connection}}: Test the connection to the service.

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/log\sphinxhyphen{}console}}: Log browser console messages.

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads}}: Access the downloads manager.

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/list}}: List downloaded files.

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/delete}}: Delete a downloaded file.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Implementation}:

\sphinxAtStartPar
The API server is implemented in Python using the aiohttp framework. It communicates with the Docker Engine to create and manage containers.


\paragraph{VNC Interface}
\label{\detokenize{developer_guide/architecture:vnc-interface}}
\sphinxAtStartPar
The VNC interface provides remote access to the containerized browsers.

\sphinxAtStartPar
\sphinxstylestrong{Key Features}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Remote Access}: Allows users to interact with the containerized browsers.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Graphical Interface}: Provides a graphical interface to the browser.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Keyboard and Mouse Input}: Captures keyboard and mouse input from the user.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Clipboard Support}: Provides clipboard support for copy and paste operations.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Implementation}:

\sphinxAtStartPar
The VNC interface is implemented using noVNC, a JavaScript VNC client. It communicates with the VNC server running in the container.


\paragraph{Firefox Plugin}
\label{\detokenize{developer_guide/architecture:firefox-plugin}}
\sphinxAtStartPar
The Firefox plugin provides seamless integration with Firefox, allowing users to access bahtBrowse directly from their browser.

\sphinxAtStartPar
\sphinxstylestrong{Key Features}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Toolbar Button}: Provides a toolbar button for quick access to bahtBrowse.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Context Menu}: Adds context menu items for opening links in bahtBrowse.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Configuration}: Allows users to configure the plugin settings.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Connection Status}: Indicates the connection status to the bahtBrowse service.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Implementation}:

\sphinxAtStartPar
The Firefox plugin is implemented using the WebExtensions API. It communicates with the API server to launch browser sessions.


\paragraph{Nginx Configuration}
\label{\detokenize{developer_guide/architecture:nginx-configuration}}
\sphinxAtStartPar
The Nginx configuration handles proxying and serving static files.

\sphinxAtStartPar
\sphinxstylestrong{Key Features}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Proxying}: Proxies requests to the API server and VNC interface.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Static Files}: Serves static files such as HTML, CSS, and JavaScript.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{WebSocket Support}: Provides WebSocket support for the VNC interface.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Caching}: Implements caching for improved performance.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Implementation}:

\sphinxAtStartPar
The Nginx configuration is implemented using Nginx configuration files. It is configured to proxy requests to the appropriate services.


\subsubsection{Sequence Diagrams}
\label{\detokenize{developer_guide/architecture:sequence-diagrams}}
\sphinxAtStartPar
The following sequence diagrams illustrate the interactions between components for common operations.


\paragraph{Browser Session Creation}
\label{\detokenize{developer_guide/architecture:browser-session-creation}}
\sphinxAtStartPar
This diagram shows the sequence of events when a user creates a new browser session:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
User        Firefox Plugin        API Server        Docker Engine        Browser Container        VNC Interface
 |                |                   |                   |                       |                    |
 |                |                   |                   |                       |                    |
 |  Request URL   |                   |                   |                       |                    |
 |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|                   |                   |                       |                    |
 |                |                   |                   |                       |                    |
 |                |  POST /browse/    |                   |                       |                    |
 |                |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|                   |                       |                    |
 |                |                   |                   |                       |                    |
 |                |                   |  Create Container |                       |                    |
 |                |                   |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|                       |                    |
 |                |                   |                   |                       |                    |
 |                |                   |                   |  Start Container      |                    |
 |                |                   |                   |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|                    |
 |                |                   |                   |                       |                    |
 |                |                   |                   |                       |  Start VNC Server  |
 |                |                   |                   |                       |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|
 |                |                   |                   |                       |                    |
 |                |                   |                   |                       |  Launch Browser    |
 |                |                   |                   |                       |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|
 |                |                   |                   |                       |                    |
 |                |                   |  Return Session ID|                       |                    |
 |                |                   |\PYGZlt{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|                       |                    |
 |                |                   |                   |                       |                    |
 |                |  Redirect to VNC  |                   |                       |                    |
 |                |\PYGZlt{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|                   |                       |                    |
 |                |                   |                   |                       |                    |
 |  Redirect to VNC                   |                   |                       |                    |
 |\PYGZlt{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|                   |                   |                       |                    |
 |                |                   |                   |                       |                    |
 |  Connect to VNC|                   |                   |                       |                    |
 |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|
 |                |                   |                   |                       |                    |
 |  Interact with Browser             |                   |                       |                    |
 |\PYGZlt{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|
 |                |                   |                   |                       |                    |
\end{sphinxVerbatim}


\subsubsection{Data Flow}
\label{\detokenize{developer_guide/architecture:data-flow}}
\sphinxAtStartPar
The following diagram illustrates the data flow through the bahtBrowse system:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
+\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
|                |     |                |     |                |
|    User        |\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|  Firefox Plugin|\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZgt{}|   API Server   |
|                |     |                |     |                |
+\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+     +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       \PYGZca{}                                             |
       |                                             v
       |                                      +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       |                                      |                |
       |                                      | Docker Engine  |
       |                                      |                |
       |                                      +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       |                                             |
       |                                             v
       |                                      +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       |                                      |                |
       |                                      | Browser        |
       |                                      | Container      |
       |                                      +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       |                                             |
       |                                             v
       |                                      +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
       |                                      |                |
       +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}|  VNC Interface |
                                             |                |
                                             +\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}+
\end{sphinxVerbatim}


\subsubsection{Security Considerations}
\label{\detokenize{developer_guide/architecture:security-considerations}}
\sphinxAtStartPar
The bahtBrowse system is designed with security in mind. Here are some key security considerations:

\sphinxAtStartPar
\sphinxstylestrong{Container Isolation}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Containers are isolated from the host system and from each other.

\item {} 
\sphinxAtStartPar
Containers have limited privileges and capabilities.

\item {} 
\sphinxAtStartPar
Containers are designed to be stateless, with no persistent storage between sessions.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Network Security}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Traffic between components is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.

\item {} 
\sphinxAtStartPar
The API server and VNC interface should be protected by a firewall and not exposed to the public internet.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{User Authentication}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
The system does not include user authentication by default. Consider implementing authentication for multi\sphinxhyphen{}user environments.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Browser Security}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Browsers are configured with security settings to minimize the risk of exploits.

\item {} 
\sphinxAtStartPar
JavaScript execution can be disabled for high\sphinxhyphen{}security environments.

\end{itemize}

\sphinxAtStartPar
\sphinxstylestrong{Session Management}:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Sessions are identified by unique session IDs.

\item {} 
\sphinxAtStartPar
Sessions are automatically cleaned up after a period of inactivity.

\end{itemize}


\subsubsection{Design Decisions}
\label{\detokenize{developer_guide/architecture:design-decisions}}
\sphinxAtStartPar
This section explains some of the key design decisions made during the development of bahtBrowse.

\sphinxAtStartPar
\sphinxstylestrong{Docker Containers vs. Virtual Machines}:

\sphinxAtStartPar
Docker containers were chosen over virtual machines for their lower resource usage and faster startup times. While virtual machines provide stronger isolation, containers provide sufficient isolation for most use cases.

\sphinxAtStartPar
\sphinxstylestrong{Firefox and Chromium Support}:

\sphinxAtStartPar
Both Firefox and Chromium are supported to provide flexibility and compatibility with different websites. Firefox is the default browser due to its better compatibility with most websites.

\sphinxAtStartPar
\sphinxstylestrong{VNC vs. Other Remote Access Protocols}:

\sphinxAtStartPar
VNC was chosen for its wide support and compatibility with web browsers through noVNC. Other protocols like RDP or X11 forwarding were considered but rejected due to compatibility issues or higher resource usage.

\sphinxAtStartPar
\sphinxstylestrong{API Server Implementation}:

\sphinxAtStartPar
The API server is implemented in Python using the aiohttp framework for its simplicity, performance, and async support. Other frameworks like Flask or Django were considered but rejected due to their synchronous nature or higher overhead.

\sphinxAtStartPar
\sphinxstylestrong{Nginx as Proxy}:

\sphinxAtStartPar
Nginx was chosen as the proxy server for its performance, reliability, and feature set. It provides WebSocket support, caching, and other features needed for the VNC interface.


\subsubsection{Future Enhancements}
\label{\detokenize{developer_guide/architecture:future-enhancements}}
\sphinxAtStartPar
The following enhancements are planned for future versions of bahtBrowse:

\sphinxAtStartPar
\sphinxstylestrong{Container Pooling}:

\sphinxAtStartPar
Implement container pooling to reduce startup times by maintaining a pool of pre\sphinxhyphen{}created containers.

\sphinxAtStartPar
\sphinxstylestrong{User Authentication}:

\sphinxAtStartPar
Add user authentication and authorization to support multi\sphinxhyphen{}user environments.

\sphinxAtStartPar
\sphinxstylestrong{HTTPS Support}:

\sphinxAtStartPar
Add HTTPS support for secure communication between components.

\sphinxAtStartPar
\sphinxstylestrong{Load Balancing}:

\sphinxAtStartPar
Implement load balancing to distribute browser sessions across multiple servers.

\sphinxAtStartPar
\sphinxstylestrong{Monitoring and Metrics}:

\sphinxAtStartPar
Add monitoring and metrics collection to track system performance and usage.

\sphinxAtStartPar
For more information on contributing to these enhancements, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.

\sphinxstepscope


\subsection{Contributing to bahtBrowse}
\label{\detokenize{developer_guide/contributing:contributing-to-bahtbrowse}}\label{\detokenize{developer_guide/contributing:contributing}}\label{\detokenize{developer_guide/contributing::doc}}
\sphinxAtStartPar
This guide provides information on how to contribute to the bahtBrowse project, including setting up a development environment, coding standards, and the contribution workflow.


\subsubsection{Getting Started}
\label{\detokenize{developer_guide/contributing:getting-started}}
\sphinxAtStartPar
Thank you for your interest in contributing to bahtBrowse! This guide will help you get started with contributing to the project.


\paragraph{Prerequisites}
\label{\detokenize{developer_guide/contributing:prerequisites}}
\sphinxAtStartPar
Before you begin, ensure you have the following installed:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Git}: For version control

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker}: For running containers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Python 3.8+}: For development and testing

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Node.js}: For Firefox plugin development

\end{itemize}


\subsubsection{Setting Up a Development Environment}
\label{\detokenize{developer_guide/contributing:setting-up-a-development-environment}}
\sphinxAtStartPar
Follow these steps to set up a development environment for bahtBrowse:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Create a Python virtual environment:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }\PYGZhy{}m\PYG{+w}{ }venv\PYG{+w}{ }venv
\PYG{n+nb}{source}\PYG{+w}{ }venv/bin/activate
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Install the development dependencies:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}e\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}.[dev]\PYGZdq{}}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Set up pre\sphinxhyphen{}commit hooks:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pre\PYGZhy{}commit\PYG{+w}{ }install
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build and run the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.yml\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.dev.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Run the tests to verify the setup:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\end{enumerate}


\subsubsection{Development Workflow}
\label{\detokenize{developer_guide/contributing:development-workflow}}
\sphinxAtStartPar
The typical development workflow for bahtBrowse is as follows:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a Feature Branch}: Create a new branch for your feature or bug fix.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Make Changes}: Make your changes to the codebase.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run Tests}: Run the test suite to ensure your changes don’t break existing functionality.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Submit a Pull Request}: Submit a pull request to the main repository.

\end{enumerate}


\paragraph{Creating a Feature Branch}
\label{\detokenize{developer_guide/contributing:creating-a-feature-branch}}
\sphinxAtStartPar
To create a feature branch:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }checkout\PYG{+w}{ }\PYGZhy{}b\PYG{+w}{ }feature/your\PYGZhy{}feature\PYGZhy{}name
\end{sphinxVerbatim}

\sphinxAtStartPar
Use a descriptive name for your branch that reflects the feature or bug fix you’re working on.


\paragraph{Making Changes}
\label{\detokenize{developer_guide/contributing:making-changes}}
\sphinxAtStartPar
Make your changes to the codebase. Follow the coding standards and best practices described in the next section.


\paragraph{Running Tests}
\label{\detokenize{developer_guide/contributing:running-tests}}
\sphinxAtStartPar
Before submitting a pull request, run the test suite to ensure your changes don’t break existing functionality:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\sphinxAtStartPar
You can also run specific tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }tests/test\PYGZus{}docker.py
pytest\PYG{+w}{ }tests/test\PYGZus{}redirect.py
\end{sphinxVerbatim}


\paragraph{Submitting a Pull Request}
\label{\detokenize{developer_guide/contributing:submitting-a-pull-request}}
\sphinxAtStartPar
To submit a pull request:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Push your changes to your fork of the repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }push\PYG{+w}{ }origin\PYG{+w}{ }feature/your\PYGZhy{}feature\PYGZhy{}name
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Go to the GitHub repository and create a pull request from your branch to the main branch.

\item {} 
\sphinxAtStartPar
Provide a clear description of the changes and any related issues.

\item {} 
\sphinxAtStartPar
Wait for the CI/CD pipeline to run and verify that all tests pass.

\item {} 
\sphinxAtStartPar
Address any feedback from the code review.

\end{enumerate}


\subsubsection{Coding Standards}
\label{\detokenize{developer_guide/contributing:coding-standards}}
\sphinxAtStartPar
The bahtBrowse project follows these coding standards:


\paragraph{Python Code}
\label{\detokenize{developer_guide/contributing:python-code}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Follow PEP 8 style guide.

\item {} 
\sphinxAtStartPar
Use type hints for function parameters and return values.

\item {} 
\sphinxAtStartPar
Write docstrings for all functions, classes, and modules.

\item {} 
\sphinxAtStartPar
Use meaningful variable and function names.

\item {} 
\sphinxAtStartPar
Keep functions and methods small and focused.

\item {} 
\sphinxAtStartPar
Write unit tests for all new functionality.

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{calculate\PYGZus{}total}\PYG{p}{(}\PYG{n}{items}\PYG{p}{:} \PYG{n}{List}\PYG{p}{[}\PYG{n}{Item}\PYG{p}{]}\PYG{p}{)} \PYG{o}{\PYGZhy{}}\PYG{o}{\PYGZgt{}} \PYG{n+nb}{float}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}}
\PYG{l+s+sd}{    Calculate the total price of a list of items.}

\PYG{l+s+sd}{    Args:}
\PYG{l+s+sd}{        items: A list of Item objects.}

\PYG{l+s+sd}{    Returns:}
\PYG{l+s+sd}{        The total price of all items.}
\PYG{l+s+sd}{    \PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{k}{return} \PYG{n+nb}{sum}\PYG{p}{(}\PYG{n}{item}\PYG{o}{.}\PYG{n}{price} \PYG{k}{for} \PYG{n}{item} \PYG{o+ow}{in} \PYG{n}{items}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{JavaScript Code}
\label{\detokenize{developer_guide/contributing:javascript-code}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Follow the Airbnb JavaScript Style Guide.

\item {} 
\sphinxAtStartPar
Use ES6+ features where appropriate.

\item {} 
\sphinxAtStartPar
Use meaningful variable and function names.

\item {} 
\sphinxAtStartPar
Keep functions and methods small and focused.

\item {} 
\sphinxAtStartPar
Write unit tests for all new functionality.

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+cm}{/**}
\PYG{c+cm}{ * Calculate the total price of a list of items.}
\PYG{c+cm}{ * @param \PYGZob{}Array\PYGZcb{} items \PYGZhy{} A list of item objects.}
\PYG{c+cm}{ * @returns \PYGZob{}number\PYGZcb{} The total price of all items.}
\PYG{c+cm}{ */}
\PYG{k+kd}{function}\PYG{+w}{ }\PYG{n+nx}{calculateTotal}\PYG{p}{(}\PYG{n+nx}{items}\PYG{p}{)}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{k}{return}\PYG{+w}{ }\PYG{n+nx}{items}\PYG{p}{.}\PYG{n+nx}{reduce}\PYG{p}{(}\PYG{p}{(}\PYG{n+nx}{total}\PYG{p}{,}\PYG{+w}{ }\PYG{n+nx}{item}\PYG{p}{)}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{n+nx}{total}\PYG{+w}{ }\PYG{o}{+}\PYG{+w}{ }\PYG{n+nx}{item}\PYG{p}{.}\PYG{n+nx}{price}\PYG{p}{,}\PYG{+w}{ }\PYG{l+m+mf}{0}\PYG{p}{)}\PYG{p}{;}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}


\paragraph{Docker Files}
\label{\detokenize{developer_guide/contributing:docker-files}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Use official base images where possible.

\item {} 
\sphinxAtStartPar
Use multi\sphinxhyphen{}stage builds to minimize image size.

\item {} 
\sphinxAtStartPar
Include only necessary files in the image.

\item {} 
\sphinxAtStartPar
Set appropriate permissions for files and directories.

\item {} 
\sphinxAtStartPar
Use environment variables for configuration.

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{FROM}\PYG{+w}{ }\PYG{l+s}{python:3.9\PYGZhy{}slim}\PYG{+w}{ }\PYG{k}{AS}\PYG{+w}{ }\PYG{l+s}{builder}

\PYG{k}{WORKDIR}\PYG{+w}{ }\PYG{l+s}{/app}

\PYG{k}{COPY}\PYG{+w}{ }requirements.txt\PYG{+w}{ }.
\PYG{k}{RUN}\PYG{+w}{ }pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}\PYGZhy{}no\PYGZhy{}cache\PYGZhy{}dir\PYG{+w}{ }\PYGZhy{}r\PYG{+w}{ }requirements.txt

\PYG{k}{FROM}\PYG{+w}{ }\PYG{l+s}{python:3.9\PYGZhy{}slim}

\PYG{k}{WORKDIR}\PYG{+w}{ }\PYG{l+s}{/app}

\PYG{k}{COPY}\PYG{+w}{ }\PYGZhy{}\PYGZhy{}from\PYG{o}{=}builder\PYG{+w}{ }/usr/local/lib/python3.9/site\PYGZhy{}packages\PYG{+w}{ }/usr/local/lib/python3.9/site\PYGZhy{}packages
\PYG{k}{COPY}\PYG{+w}{ }.\PYG{+w}{ }.

\PYG{k}{CMD}\PYG{+w}{ }\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}python\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}app.py\PYGZdq{}}\PYG{p}{]}
\end{sphinxVerbatim}


\paragraph{Documentation}
\label{\detokenize{developer_guide/contributing:documentation}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Write clear and concise documentation.

\item {} 
\sphinxAtStartPar
Use reStructuredText for documentation files.

\item {} 
\sphinxAtStartPar
Include examples and code snippets where appropriate.

\item {} 
\sphinxAtStartPar
Keep documentation up to date with code changes.

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{..} \PYG{n+nt}{\PYGZus{}example:}

\PYG{g+gh}{Example}
\PYG{g+gh}{=======}

This is an example of reStructuredText documentation.

\PYG{p}{..} \PYG{o+ow}{code\PYGZhy{}block}\PYG{p}{::} \PYG{k}{python}

   \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{example\PYGZus{}function}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
   \PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}This is an example function.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
       \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Hello, world!}\PYG{l+s+s2}{\PYGZdq{}}
\end{sphinxVerbatim}


\subsubsection{Testing}
\label{\detokenize{developer_guide/contributing:testing}}
\sphinxAtStartPar
The bahtBrowse project uses pytest for testing. All new functionality should be covered by tests.


\paragraph{Writing Tests}
\label{\detokenize{developer_guide/contributing:writing-tests}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Write unit tests for all new functionality.

\item {} 
\sphinxAtStartPar
Use pytest fixtures for test setup and teardown.

\item {} 
\sphinxAtStartPar
Use mocking to isolate the code being tested.

\item {} 
\sphinxAtStartPar
Write clear and descriptive test names.

\end{itemize}

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}calculate\PYGZus{}total}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that calculate\PYGZus{}total returns the correct total.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{n}{items} \PYG{o}{=} \PYG{p}{[}\PYG{n}{Item}\PYG{p}{(}\PYG{n}{price}\PYG{o}{=}\PYG{l+m+mi}{10}\PYG{p}{)}\PYG{p}{,} \PYG{n}{Item}\PYG{p}{(}\PYG{n}{price}\PYG{o}{=}\PYG{l+m+mi}{20}\PYG{p}{)}\PYG{p}{,} \PYG{n}{Item}\PYG{p}{(}\PYG{n}{price}\PYG{o}{=}\PYG{l+m+mi}{30}\PYG{p}{)}\PYG{p}{]}
    \PYG{k}{assert} \PYG{n}{calculate\PYGZus{}total}\PYG{p}{(}\PYG{n}{items}\PYG{p}{)} \PYG{o}{==} \PYG{l+m+mi}{60}
\end{sphinxVerbatim}


\paragraph{Running Tests}
\label{\detokenize{developer_guide/contributing:id1}}
\sphinxAtStartPar
To run the tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\sphinxAtStartPar
To run specific tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }tests/test\PYGZus{}docker.py
pytest\PYG{+w}{ }tests/test\PYGZus{}redirect.py
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests with coverage:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }\PYGZhy{}\PYGZhy{}cov\PYG{o}{=}bahtbrowse
\end{sphinxVerbatim}


\subsubsection{Debugging}
\label{\detokenize{developer_guide/contributing:debugging}}
\sphinxAtStartPar
The bahtBrowse project provides several tools for debugging:


\paragraph{Docker Logs}
\label{\detokenize{developer_guide/contributing:docker-logs}}
\sphinxAtStartPar
To view the logs of a Docker container:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox
docker\PYG{+w}{ }logs\PYG{+w}{ }bahtbrowse\PYGZhy{}api
\end{sphinxVerbatim}


\paragraph{API Server Logs}
\label{\detokenize{developer_guide/contributing:api-server-logs}}
\sphinxAtStartPar
The API server logs are stored in \sphinxcode{\sphinxupquote{/tmp/bahtbrowse\_server.log}} inside the container. You can view them with:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}api\PYG{+w}{ }cat\PYG{+w}{ }/tmp/bahtbrowse\PYGZus{}server.log
\end{sphinxVerbatim}


\paragraph{Browser Console Logs}
\label{\detokenize{developer_guide/contributing:browser-console-logs}}
\sphinxAtStartPar
Browser console logs are stored in \sphinxcode{\sphinxupquote{/tmp/browser\_console.log}} inside the container. You can view them with:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/browser\PYGZus{}console.log
\end{sphinxVerbatim}


\paragraph{VNC Server Logs}
\label{\detokenize{developer_guide/contributing:vnc-server-logs}}
\sphinxAtStartPar
VNC server logs are stored in \sphinxcode{\sphinxupquote{/tmp/x11vnc.log}} inside the container. You can view them with:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYG{+w}{ }\PYG{n+nb}{exec}\PYG{+w}{ }\PYGZhy{}it\PYG{+w}{ }bahtbrowse\PYGZhy{}firefox\PYG{+w}{ }cat\PYG{+w}{ }/tmp/x11vnc.log
\end{sphinxVerbatim}


\paragraph{Python Debugger}
\label{\detokenize{developer_guide/contributing:python-debugger}}
\sphinxAtStartPar
You can use the Python debugger (pdb) to debug Python code:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{pdb}
\PYG{n}{pdb}\PYG{o}{.}\PYG{n}{set\PYGZus{}trace}\PYG{p}{(}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Firefox Plugin Debugging}
\label{\detokenize{developer_guide/contributing:firefox-plugin-debugging}}
\sphinxAtStartPar
To debug the Firefox plugin:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Open Firefox

\item {} 
\sphinxAtStartPar
Navigate to \sphinxcode{\sphinxupquote{about:debugging}}

\item {} 
\sphinxAtStartPar
Click on “This Firefox”

\item {} 
\sphinxAtStartPar
Find the bahtBrowse plugin and click “Inspect”

\item {} 
\sphinxAtStartPar
Use the browser developer tools to debug the plugin

\end{enumerate}


\subsubsection{Documentation}
\label{\detokenize{developer_guide/contributing:id2}}
\sphinxAtStartPar
The bahtBrowse project uses Sphinx for documentation. All new functionality should be documented.


\paragraph{Building Documentation}
\label{\detokenize{developer_guide/contributing:building-documentation}}
\sphinxAtStartPar
To build the documentation:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }docs/sphinx
./build.sh
\end{sphinxVerbatim}

\sphinxAtStartPar
The documentation will be built in the \sphinxcode{\sphinxupquote{docs/sphinx/build/html}} directory.


\paragraph{Writing Documentation}
\label{\detokenize{developer_guide/contributing:writing-documentation}}\begin{itemize}
\item {} 
\sphinxAtStartPar
Use reStructuredText for documentation files.

\item {} 
\sphinxAtStartPar
Include examples and code snippets where appropriate.

\item {} 
\sphinxAtStartPar
Keep documentation up to date with code changes.

\item {} 
\sphinxAtStartPar
Follow the existing documentation structure.

\end{itemize}

\sphinxAtStartPar
For more information on reStructuredText, see the \sphinxhref{https://www.sphinx-doc.org/en/master/usage/restructuredtext/basics.html}{reStructuredText Primer}.


\subsubsection{Release Process}
\label{\detokenize{developer_guide/contributing:release-process}}
\sphinxAtStartPar
The bahtBrowse project follows a semantic versioning scheme (MAJOR.MINOR.PATCH).


\paragraph{Creating a Release}
\label{\detokenize{developer_guide/contributing:creating-a-release}}
\sphinxAtStartPar
To create a release:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Update the version number in \sphinxcode{\sphinxupquote{setup.py}} and \sphinxcode{\sphinxupquote{package.json}}.

\item {} 
\sphinxAtStartPar
Update the changelog with the changes in the new version.

\item {} 
\sphinxAtStartPar
Create a new tag with the version number:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }tag\PYG{+w}{ }\PYGZhy{}a\PYG{+w}{ }v1.0.0\PYG{+w}{ }\PYGZhy{}m\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Version 1.0.0\PYGZdq{}}
git\PYG{+w}{ }push\PYG{+w}{ }origin\PYG{+w}{ }v1.0.0
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Create a new release on GitHub with the tag.

\item {} 
\sphinxAtStartPar
Build and publish the packages:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python\PYG{+w}{ }setup.py\PYG{+w}{ }sdist\PYG{+w}{ }bdist\PYGZus{}wheel
twine\PYG{+w}{ }upload\PYG{+w}{ }dist/*
\end{sphinxVerbatim}

\end{enumerate}


\subsubsection{Getting Help}
\label{\detokenize{developer_guide/contributing:getting-help}}
\sphinxAtStartPar
If you need help with contributing to bahtBrowse, you can:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Open an issue on GitHub

\item {} 
\sphinxAtStartPar
Contact the maintainers

\item {} 
\sphinxAtStartPar
Join the community chat

\end{itemize}

\sphinxAtStartPar
Thank you for contributing to bahtBrowse!

\sphinxstepscope


\subsection{Testing Guide}
\label{\detokenize{developer_guide/testing:testing-guide}}\label{\detokenize{developer_guide/testing:testing}}\label{\detokenize{developer_guide/testing::doc}}
\sphinxAtStartPar
This guide provides information on how to test the bahtBrowse system, including unit tests, integration tests, and end\sphinxhyphen{}to\sphinxhyphen{}end tests.


\subsubsection{Overview}
\label{\detokenize{developer_guide/testing:overview}}
\sphinxAtStartPar
Testing is a critical part of the bahtBrowse development process. The project uses a combination of unit tests, integration tests, and end\sphinxhyphen{}to\sphinxhyphen{}end tests to ensure the quality and reliability of the codebase.

\sphinxAtStartPar
The test suite is organized as follows:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Unit Tests}: Test individual components in isolation

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Integration Tests}: Test the interaction between components

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{End\sphinxhyphen{}to\sphinxhyphen{}End Tests}: Test the entire system from a user’s perspective

\end{itemize}


\subsubsection{Test Framework}
\label{\detokenize{developer_guide/testing:test-framework}}
\sphinxAtStartPar
The bahtBrowse project uses pytest as the primary test framework. Pytest provides a simple and flexible way to write and run tests.


\paragraph{Installing Test Dependencies}
\label{\detokenize{developer_guide/testing:installing-test-dependencies}}
\sphinxAtStartPar
To install the test dependencies:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}e\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}.[dev]\PYGZdq{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
This will install pytest and other test dependencies.


\subsubsection{Running Tests}
\label{\detokenize{developer_guide/testing:running-tests}}
\sphinxAtStartPar
To run the entire test suite:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\sphinxAtStartPar
To run specific tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }tests/test\PYGZus{}docker.py
pytest\PYG{+w}{ }tests/test\PYGZus{}redirect.py
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests with coverage:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }\PYGZhy{}\PYGZhy{}cov\PYG{o}{=}bahtbrowse
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests with verbose output:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }\PYGZhy{}v
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests in parallel:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }\PYGZhy{}xvs\PYG{+w}{ }\PYGZhy{}n\PYG{+w}{ }auto
\end{sphinxVerbatim}


\subsubsection{Unit Tests}
\label{\detokenize{developer_guide/testing:unit-tests}}
\sphinxAtStartPar
Unit tests focus on testing individual components in isolation. They are fast, reliable, and provide immediate feedback on the correctness of the code.


\paragraph{Writing Unit Tests}
\label{\detokenize{developer_guide/testing:writing-unit-tests}}
\sphinxAtStartPar
Unit tests should be small, focused, and independent. They should test a single function or method in isolation.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}session\PYGZus{}manager\PYGZus{}create\PYGZus{}session}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that session\PYGZus{}manager.create\PYGZus{}session creates a valid session.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{n}{session\PYGZus{}manager} \PYG{o}{=} \PYG{n}{SessionManager}\PYG{p}{(}\PYG{p}{)}
    \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{create\PYGZus{}session}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{127.0.0.1}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
    \PYG{k}{assert} \PYG{n}{session\PYGZus{}id} \PYG{o+ow}{is} \PYG{o+ow}{not} \PYG{k+kc}{None}
    \PYG{k}{assert} \PYG{n+nb}{len}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{)} \PYG{o}{==} \PYG{l+m+mi}{36}  \PYG{c+c1}{\PYGZsh{} UUID length}
\end{sphinxVerbatim}


\paragraph{Mocking}
\label{\detokenize{developer_guide/testing:mocking}}
\sphinxAtStartPar
Unit tests often use mocking to isolate the code being tested from its dependencies. The bahtBrowse project uses the unittest.mock module for mocking.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{from}\PYG{+w}{ }\PYG{n+nn}{unittest}\PYG{n+nn}{.}\PYG{n+nn}{mock}\PYG{+w}{ }\PYG{k+kn}{import} \PYG{n}{patch}

\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}api\PYGZus{}server\PYGZus{}handle\PYGZus{}request}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that api\PYGZus{}server.handle\PYGZus{}request handles requests correctly.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{k}{with} \PYG{n}{patch}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api\PYGZus{}server.session\PYGZus{}manager.create\PYGZus{}session}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)} \PYG{k}{as} \PYG{n}{mock\PYGZus{}create\PYGZus{}session}\PYG{p}{:}
        \PYG{n}{mock\PYGZus{}create\PYGZus{}session}\PYG{o}{.}\PYG{n}{return\PYGZus{}value} \PYG{o}{=} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{test\PYGZhy{}session\PYGZhy{}id}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{n}{result} \PYG{o}{=} \PYG{n}{handle\PYGZus{}request}\PYG{p}{(}\PYG{p}{\PYGZob{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{url}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{\PYGZcb{}}\PYG{p}{)}
        \PYG{k}{assert} \PYG{n}{result}\PYG{o}{.}\PYG{n}{status\PYGZus{}code} \PYG{o}{==} \PYG{l+m+mi}{302}
        \PYG{k}{assert} \PYG{n}{result}\PYG{o}{.}\PYG{n}{headers}\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Location}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{]} \PYG{o}{==} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{/vnc?session=test\PYGZhy{}session\PYGZhy{}id}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{n}{mock\PYGZus{}create\PYGZus{}session}\PYG{o}{.}\PYG{n}{assert\PYGZus{}called\PYGZus{}once\PYGZus{}with}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{127.0.0.1}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Fixtures}
\label{\detokenize{developer_guide/testing:fixtures}}
\sphinxAtStartPar
Pytest fixtures provide a way to set up and tear down test dependencies. They can be used to create test data, initialize objects, or set up the test environment.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{pytest}

\PYG{n+nd}{@pytest}\PYG{o}{.}\PYG{n}{fixture}
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{session\PYGZus{}manager}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Create a session manager for testing.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{k}{return} \PYG{n}{SessionManager}\PYG{p}{(}\PYG{p}{)}

\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}session\PYGZus{}manager\PYGZus{}create\PYGZus{}session}\PYG{p}{(}\PYG{n}{session\PYGZus{}manager}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that session\PYGZus{}manager.create\PYGZus{}session creates a valid session.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{create\PYGZus{}session}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{127.0.0.1}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
    \PYG{k}{assert} \PYG{n}{session\PYGZus{}id} \PYG{o+ow}{is} \PYG{o+ow}{not} \PYG{k+kc}{None}
    \PYG{k}{assert} \PYG{n+nb}{len}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{)} \PYG{o}{==} \PYG{l+m+mi}{36}  \PYG{c+c1}{\PYGZsh{} UUID length}
\end{sphinxVerbatim}


\subsubsection{Integration Tests}
\label{\detokenize{developer_guide/testing:integration-tests}}
\sphinxAtStartPar
Integration tests focus on testing the interaction between components. They ensure that the components work together correctly.


\paragraph{Writing Integration Tests}
\label{\detokenize{developer_guide/testing:writing-integration-tests}}
\sphinxAtStartPar
Integration tests should focus on the interaction between components. They should test the integration points and ensure that the components work together correctly.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}api\PYGZus{}server\PYGZus{}docker\PYGZus{}integration}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that the API server can create Docker containers.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Start the API server}
    \PYG{n}{api\PYGZus{}server} \PYG{o}{=} \PYG{n}{APIServer}\PYG{p}{(}\PYG{p}{)}
    \PYG{n}{api\PYGZus{}server}\PYG{o}{.}\PYG{n}{start}\PYG{p}{(}\PYG{p}{)}

    \PYG{k}{try}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Send a request to the API server}
        \PYG{n}{response} \PYG{o}{=} \PYG{n}{requests}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{http://localhost:8082/browse/?url=https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Verify that the API server created a Docker container}
        \PYG{k}{assert} \PYG{n}{response}\PYG{o}{.}\PYG{n}{status\PYGZus{}code} \PYG{o}{==} \PYG{l+m+mi}{302}
        \PYG{k}{assert} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{session}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{response}\PYG{o}{.}\PYG{n}{headers}\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Location}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{]}

        \PYG{c+c1}{\PYGZsh{} Verify that the Docker container is running}
        \PYG{n}{docker\PYGZus{}client} \PYG{o}{=} \PYG{n}{docker}\PYG{o}{.}\PYG{n}{from\PYGZus{}env}\PYG{p}{(}\PYG{p}{)}
        \PYG{n}{containers} \PYG{o}{=} \PYG{n}{docker\PYGZus{}client}\PYG{o}{.}\PYG{n}{containers}\PYG{o}{.}\PYG{n}{list}\PYG{p}{(}\PYG{p}{)}
        \PYG{k}{assert} \PYG{n+nb}{any}\PYG{p}{(}\PYG{n}{container}\PYG{o}{.}\PYG{n}{name}\PYG{o}{.}\PYG{n}{startswith}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{bahtbrowse\PYGZhy{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)} \PYG{k}{for} \PYG{n}{container} \PYG{o+ow}{in} \PYG{n}{containers}\PYG{p}{)}
    \PYG{k}{finally}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Stop the API server}
        \PYG{n}{api\PYGZus{}server}\PYG{o}{.}\PYG{n}{stop}\PYG{p}{(}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Docker Integration}
\label{\detokenize{developer_guide/testing:docker-integration}}
\sphinxAtStartPar
Many integration tests involve Docker containers. The bahtBrowse project provides utilities for working with Docker containers in tests.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{from}\PYG{+w}{ }\PYG{n+nn}{bahtbrowse}\PYG{n+nn}{.}\PYG{n+nn}{docker\PYGZus{}utils}\PYG{+w}{ }\PYG{k+kn}{import} \PYG{n}{create\PYGZus{}container}\PYG{p}{,} \PYG{n}{remove\PYGZus{}container}

\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}docker\PYGZus{}container\PYGZus{}creation}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test that Docker containers can be created and removed.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Create a container}
    \PYG{n}{container} \PYG{o}{=} \PYG{n}{create\PYGZus{}container}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{firefox}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

    \PYG{k}{try}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Verify that the container is running}
        \PYG{k}{assert} \PYG{n}{container}\PYG{o}{.}\PYG{n}{status} \PYG{o}{==} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{running}\PYG{l+s+s2}{\PYGZdq{}}

        \PYG{c+c1}{\PYGZsh{} Verify that the browser is running in the container}
        \PYG{n}{logs} \PYG{o}{=} \PYG{n}{container}\PYG{o}{.}\PYG{n}{logs}\PYG{p}{(}\PYG{p}{)}\PYG{o}{.}\PYG{n}{decode}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{utf\PYGZhy{}8}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{k}{assert} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Firefox}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{logs}
    \PYG{k}{finally}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Remove the container}
        \PYG{n}{remove\PYGZus{}container}\PYG{p}{(}\PYG{n}{container}\PYG{p}{)}
\end{sphinxVerbatim}


\subsubsection{End\sphinxhyphen{}to\sphinxhyphen{}End Tests}
\label{\detokenize{developer_guide/testing:end-to-end-tests}}
\sphinxAtStartPar
End\sphinxhyphen{}to\sphinxhyphen{}end tests focus on testing the entire system from a user’s perspective. They ensure that the system works correctly as a whole.


\subsubsection{Container Build Tests}
\label{\detokenize{developer_guide/testing:container-build-tests}}
\sphinxAtStartPar
Container build tests focus on testing the Docker container build process. They ensure that all Docker containers can be built successfully.


\paragraph{Writing Container Build Tests}
\label{\detokenize{developer_guide/testing:writing-container-build-tests}}
\sphinxAtStartPar
Container build tests should focus on the Docker container build process. They should test that all Dockerfiles in the project can be built successfully.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}build\PYGZus{}all\PYGZus{}containers}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test building all Docker containers.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{k}{with} \PYG{n}{Progress}\PYG{p}{(}
        \PYG{n}{SpinnerColumn}\PYG{p}{(}\PYG{p}{)}\PYG{p}{,}
        \PYG{n}{TextColumn}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{[bold blue]}\PYG{l+s+si}{\PYGZob{}task.description\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}\PYG{p}{,}
        \PYG{n}{BarColumn}\PYG{p}{(}\PYG{p}{)}\PYG{p}{,}
        \PYG{n}{TimeElapsedColumn}\PYG{p}{(}\PYG{p}{)}\PYG{p}{,}
        \PYG{n}{console}\PYG{o}{=}\PYG{n}{console}
    \PYG{p}{)} \PYG{k}{as} \PYG{n}{progress}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Create a task for each Dockerfile}
        \PYG{n}{tasks} \PYG{o}{=} \PYG{p}{\PYGZob{}}\PYG{p}{\PYGZcb{}}
        \PYG{k}{for} \PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{n}{filename} \PYG{o+ow}{in} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{dockerfiles}\PYG{p}{:}
            \PYG{n}{tag} \PYG{o}{=} \PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{bahtbrowse\PYGZhy{}test\PYGZhy{}}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{filename}\PYG{o}{.}\PYG{n}{replace}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{Dockerfile}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{replace}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{.}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{lower}\PYG{p}{(}\PYG{p}{)}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}
            \PYG{k}{if} \PYG{o+ow}{not} \PYG{n}{tag}\PYG{o}{.}\PYG{n}{endswith}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{\PYGZhy{}test}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}\PYG{p}{:}
                \PYG{n}{tag} \PYG{o}{+}\PYG{o}{=} \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{\PYGZhy{}test}\PYG{l+s+s1}{\PYGZsq{}}

            \PYG{n}{task\PYGZus{}id} \PYG{o}{=} \PYG{n}{progress}\PYG{o}{.}\PYG{n}{add\PYGZus{}task}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Building }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{filename}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{total}\PYG{o}{=}\PYG{l+m+mi}{100}\PYG{p}{)}
            \PYG{n}{tasks}\PYG{p}{[}\PYG{n}{task\PYGZus{}id}\PYG{p}{]} \PYG{o}{=} \PYG{p}{(}\PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{n}{filename}\PYG{p}{,} \PYG{n}{tag}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Build each Docker image}
        \PYG{n}{results} \PYG{o}{=} \PYG{p}{\PYGZob{}}\PYG{p}{\PYGZcb{}}
        \PYG{n}{success\PYGZus{}count} \PYG{o}{=} \PYG{l+m+mi}{0}
        \PYG{n}{failure\PYGZus{}count} \PYG{o}{=} \PYG{l+m+mi}{0}

        \PYG{k}{for} \PYG{n}{task\PYGZus{}id}\PYG{p}{,} \PYG{p}{(}\PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{n}{filename}\PYG{p}{,} \PYG{n}{tag}\PYG{p}{)} \PYG{o+ow}{in} \PYG{n}{tasks}\PYG{o}{.}\PYG{n}{items}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
            \PYG{n}{progress}\PYG{o}{.}\PYG{n}{update}\PYG{p}{(}\PYG{n}{task\PYGZus{}id}\PYG{p}{,} \PYG{n}{description}\PYG{o}{=}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Building }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{filename}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{...}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{completed}\PYG{o}{=}\PYG{l+m+mi}{10}\PYG{p}{)}

            \PYG{n}{success}\PYG{p}{,} \PYG{n}{output} \PYG{o}{=} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{build\PYGZus{}docker\PYGZus{}image}\PYG{p}{(}\PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{n}{tag}\PYG{p}{)}
            \PYG{n}{results}\PYG{p}{[}\PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{]} \PYG{o}{=} \PYG{p}{(}\PYG{n}{success}\PYG{p}{,} \PYG{n}{output}\PYG{p}{)}

            \PYG{k}{if} \PYG{n}{success}\PYG{p}{:}
                \PYG{n}{success\PYGZus{}count} \PYG{o}{+}\PYG{o}{=} \PYG{l+m+mi}{1}
                \PYG{n}{progress}\PYG{o}{.}\PYG{n}{update}\PYG{p}{(}\PYG{n}{task\PYGZus{}id}\PYG{p}{,} \PYG{n}{description}\PYG{o}{=}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Built }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{filename}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{ successfully}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{completed}\PYG{o}{=}\PYG{l+m+mi}{100}\PYG{p}{)}
            \PYG{k}{else}\PYG{p}{:}
                \PYG{n}{failure\PYGZus{}count} \PYG{o}{+}\PYG{o}{=} \PYG{l+m+mi}{1}
                \PYG{n}{progress}\PYG{o}{.}\PYG{n}{update}\PYG{p}{(}\PYG{n}{task\PYGZus{}id}\PYG{p}{,} \PYG{n}{description}\PYG{o}{=}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Failed to build }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{filename}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{completed}\PYG{o}{=}\PYG{l+m+mi}{100}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Create a table to show the results}
        \PYG{n}{table} \PYG{o}{=} \PYG{n}{Table}\PYG{p}{(}\PYG{n}{title}\PYG{o}{=}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Docker Build Results}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{table}\PYG{o}{.}\PYG{n}{add\PYGZus{}column}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Path}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{style}\PYG{o}{=}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{cyan}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{table}\PYG{o}{.}\PYG{n}{add\PYGZus{}column}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Status}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{justify}\PYG{o}{=}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{center}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{table}\PYG{o}{.}\PYG{n}{add\PYGZus{}column}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Details}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{style}\PYG{o}{=}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{dim}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Add results to the table}
        \PYG{k}{for} \PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{p}{(}\PYG{n}{success}\PYG{p}{,} \PYG{n}{output}\PYG{p}{)} \PYG{o+ow}{in} \PYG{n}{results}\PYG{o}{.}\PYG{n}{items}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
            \PYG{n}{status} \PYG{o}{=} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{[green]Success[/green]}\PYG{l+s+s2}{\PYGZdq{}} \PYG{k}{if} \PYG{n}{success} \PYG{k}{else} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{[red]Failed[/red]}\PYG{l+s+s2}{\PYGZdq{}}
            \PYG{n}{details} \PYG{o}{=} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Build completed successfully}\PYG{l+s+s2}{\PYGZdq{}} \PYG{k}{if} \PYG{n}{success} \PYG{k}{else} \PYG{n}{output}\PYG{o}{.}\PYG{n}{split}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+se}{\PYGZbs{}n}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}\PYG{p}{[}\PYG{l+m+mi}{0}\PYG{p}{]} \PYG{k}{if} \PYG{n}{output} \PYG{k}{else} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Unknown error}\PYG{l+s+s2}{\PYGZdq{}}
            \PYG{n}{table}\PYG{o}{.}\PYG{n}{add\PYGZus{}row}\PYG{p}{(}\PYG{n}{dockerfile\PYGZus{}path}\PYG{p}{,} \PYG{n}{status}\PYG{p}{,} \PYG{n}{details}\PYG{p}{)}

        \PYG{n}{console}\PYG{o}{.}\PYG{n}{print}\PYG{p}{(}\PYG{n}{table}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Print summary}
        \PYG{n}{console}\PYG{o}{.}\PYG{n}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+se}{\PYGZbs{}n}\PYG{l+s+s2}{[bold]Build Summary:[/bold]}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{console}\PYG{o}{.}\PYG{n}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Total Dockerfiles: }\PYG{l+s+si}{\PYGZob{}}\PYG{n+nb}{len}\PYG{p}{(}\PYG{n}{results}\PYG{p}{)}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{console}\PYG{o}{.}\PYG{n}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Successful builds: [green]}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{success\PYGZus{}count}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{[/green]}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{console}\PYG{o}{.}\PYG{n}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Failed builds: [red]}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{failure\PYGZus{}count}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{[/red]}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} If we have at least one successful build, consider the test a success}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{assertGreater}\PYG{p}{(}\PYG{n}{success\PYGZus{}count}\PYG{p}{,} \PYG{l+m+mi}{0}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{No Docker builds were successful. See the table above for details.}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Running Container Build Tests}
\label{\detokenize{developer_guide/testing:running-container-build-tests}}
\sphinxAtStartPar
To run the container build tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./tests/run\PYGZus{}container\PYGZus{}build\PYGZus{}tests.sh
\end{sphinxVerbatim}

\sphinxAtStartPar
This will attempt to build each Dockerfile in the project and report any failures.


\subsubsection{Port Conflict Resolution Tests}
\label{\detokenize{developer_guide/testing:port-conflict-resolution-tests}}
\sphinxAtStartPar
Port conflict resolution tests focus on testing the port conflict detection and resolution system. They ensure that the system can detect port conflicts and find alternative ports when needed.


\paragraph{Writing Port Conflict Resolution Tests}
\label{\detokenize{developer_guide/testing:writing-port-conflict-resolution-tests}}
\sphinxAtStartPar
Port conflict resolution tests should focus on the port conflict detection and resolution system. They should test that the system can detect port conflicts and find alternative ports when needed.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}get\PYGZus{}dynamic\PYGZus{}port\PYGZus{}default\PYGZus{}in\PYGZus{}use}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test get\PYGZus{}dynamic\PYGZus{}port when the default port is in use.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Mock is\PYGZus{}port\PYGZus{}in\PYGZus{}use to return True for the default port (port is in use)}
    \PYG{c+c1}{\PYGZsh{} and False for other ports}
    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{mock\PYGZus{}is\PYGZus{}port\PYGZus{}in\PYGZus{}use}\PYG{p}{(}\PYG{n}{port}\PYG{p}{)}\PYG{p}{:}
        \PYG{n}{service\PYGZus{}id} \PYG{o}{=} \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}
        \PYG{n}{default\PYGZus{}port} \PYG{o}{=} \PYG{n}{SERVICES}\PYG{p}{[}\PYG{n}{service\PYGZus{}id}\PYG{p}{]}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{port}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}
        \PYG{k}{return} \PYG{n}{port} \PYG{o}{==} \PYG{n}{default\PYGZus{}port}

    \PYG{k}{with} \PYG{n}{patch}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{bahtbrowse\PYGZus{}cli.is\PYGZus{}port\PYGZus{}in\PYGZus{}use}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,} \PYG{n}{side\PYGZus{}effect}\PYG{o}{=}\PYG{n}{mock\PYGZus{}is\PYGZus{}port\PYGZus{}in\PYGZus{}use}\PYG{p}{)}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Mock find\PYGZus{}available\PYGZus{}port to return a specific port}
        \PYG{n}{available\PYGZus{}port} \PYG{o}{=} \PYG{l+m+mi}{8090}
        \PYG{k}{with} \PYG{n}{patch}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{bahtbrowse\PYGZus{}cli.find\PYGZus{}available\PYGZus{}port}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,} \PYG{n}{return\PYGZus{}value}\PYG{o}{=}\PYG{n}{available\PYGZus{}port}\PYG{p}{)}\PYG{p}{:}
            \PYG{c+c1}{\PYGZsh{} Test with a service that has a default port}
            \PYG{n}{service\PYGZus{}id} \PYG{o}{=} \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}

            \PYG{n}{port} \PYG{o}{=} \PYG{n}{get\PYGZus{}dynamic\PYGZus{}port}\PYG{p}{(}\PYG{n}{service\PYGZus{}id}\PYG{p}{)}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{assertEqual}\PYG{p}{(}\PYG{n}{port}\PYG{p}{,} \PYG{n}{available\PYGZus{}port}\PYG{p}{,} \PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Should return the available port }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{available\PYGZus{}port}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{assertEqual}\PYG{p}{(}\PYG{n}{DYNAMIC\PYGZus{}PORTS}\PYG{p}{[}\PYG{n}{service\PYGZus{}id}\PYG{p}{]}\PYG{p}{,} \PYG{n}{available\PYGZus{}port}\PYG{p}{,} \PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Should store the available port }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{available\PYGZus{}port}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{ in DYNAMIC\PYGZus{}PORTS}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Running Port Conflict Resolution Tests}
\label{\detokenize{developer_guide/testing:running-port-conflict-resolution-tests}}
\sphinxAtStartPar
To run the port conflict resolution tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }tests/test\PYGZus{}port\PYGZus{}conflict\PYGZus{}resolution.py
\end{sphinxVerbatim}

\sphinxAtStartPar
This will test the port conflict detection and resolution system.


\subsubsection{Container Detection Tests}
\label{\detokenize{developer_guide/testing:container-detection-tests}}
\sphinxAtStartPar
Container detection tests focus on testing the container detection functionality in the CLI tool. They ensure that the system can detect containers with different naming patterns.


\paragraph{Writing Container Detection Tests}
\label{\detokenize{developer_guide/testing:writing-container-detection-tests}}
\sphinxAtStartPar
Container detection tests should focus on the container detection functionality in the CLI tool. They should test that the system can detect containers with different naming patterns.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}update\PYGZus{}service\PYGZus{}status\PYGZus{}unknown\PYGZus{}with\PYGZus{}container\PYGZus{}check\PYGZus{}alternative\PYGZus{}name}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test update\PYGZus{}service\PYGZus{}status when docker\PYGZhy{}compose ps returns an unknown status but container check finds the container with an alternative name.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Mock run\PYGZus{}command to return an unknown status from docker\PYGZhy{}compose ps}
    \PYG{c+c1}{\PYGZsh{} but a running status from the container check with an alternative name}
    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{mock\PYGZus{}run\PYGZus{}command}\PYG{p}{(}\PYG{n}{command}\PYG{p}{)}\PYG{p}{:}
        \PYG{k}{if} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker\PYGZhy{}compose}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command} \PYG{o+ow}{and} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{ps}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Name                   Command               State                    Ports                  }\PYG{l+s+se}{\PYGZbs{}n}\PYG{l+s+s2}{\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}\PYGZhy{}}\PYG{l+s+se}{\PYGZbs{}n}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{elif} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker ps \PYGZhy{}a \PYGZhy{}\PYGZhy{}filter name=bahtbrowse\PYGZus{}api\PYGZus{}1}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{elif} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker ps \PYGZhy{}a \PYGZhy{}\PYGZhy{}filter name=bahtbrowse\PYGZhy{}api\PYGZhy{}1}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Up 2 hours}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{elif} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker ps \PYGZhy{}a \PYGZhy{}\PYGZhy{}filter name=bahtbrowse\PYGZus{}api}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{elif} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker ps \PYGZhy{}a \PYGZhy{}\PYGZhy{}filter name=bahtbrowse\PYGZhy{}api}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{elif} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{docker ps \PYGZhy{}a \PYGZhy{}\PYGZhy{}filter name=api}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{command}\PYG{p}{:}
            \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{\PYGZdq{}}
        \PYG{k}{return} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{\PYGZdq{}}

    \PYG{k}{with} \PYG{n}{patch}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{bahtbrowse\PYGZus{}cli.run\PYGZus{}command}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,} \PYG{n}{side\PYGZus{}effect}\PYG{o}{=}\PYG{n}{mock\PYGZus{}run\PYGZus{}command}\PYG{p}{)}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Mock the health check to return True}
        \PYG{k}{with} \PYG{n}{patch}\PYG{o}{.}\PYG{n}{dict}\PYG{p}{(}\PYG{n}{SERVICES}\PYG{p}{,} \PYG{p}{\PYGZob{}}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{p}{\PYGZob{}}\PYG{o}{*}\PYG{o}{*}\PYG{n}{SERVICES}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}\PYG{p}{,} \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{health\PYGZus{}check}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{k}{lambda}\PYG{p}{:} \PYG{k+kc}{True}\PYG{p}{\PYGZcb{}}\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{:}
            \PYG{c+c1}{\PYGZsh{} Update the status of the api service}
            \PYG{n}{update\PYGZus{}service\PYGZus{}status}\PYG{p}{(}\PYG{p}{)}

            \PYG{c+c1}{\PYGZsh{} Check that the status was updated correctly}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{assertEqual}\PYG{p}{(}\PYG{n}{SERVICE\PYGZus{}STATUS}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{status}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{running}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Status should be }\PYG{l+s+s2}{\PYGZsq{}}\PYG{l+s+s2}{running}\PYG{l+s+s2}{\PYGZsq{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{assertEqual}\PYG{p}{(}\PYG{n}{SERVICE\PYGZus{}STATUS}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{api}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{health}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{healthy}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Health should be }\PYG{l+s+s2}{\PYGZsq{}}\PYG{l+s+s2}{healthy}\PYG{l+s+s2}{\PYGZsq{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Running Container Detection Tests}
\label{\detokenize{developer_guide/testing:running-container-detection-tests}}
\sphinxAtStartPar
To run the container detection tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }tests/test\PYGZus{}container\PYGZus{}detection.py
\end{sphinxVerbatim}

\sphinxAtStartPar
This will test the container detection functionality.


\subsubsection{Enhanced Tests}
\label{\detokenize{developer_guide/testing:enhanced-tests}}
\sphinxAtStartPar
bahtBrowse includes an enhanced test suite that runs all the above tests. To run the enhanced tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
./tests/run\PYGZus{}enhanced\PYGZus{}tests.sh
\end{sphinxVerbatim}

\sphinxAtStartPar
This will run the container build tests, port conflict resolution tests, and container detection tests, and save the results to a file.


\paragraph{Writing End\sphinxhyphen{}to\sphinxhyphen{}End Tests}
\label{\detokenize{developer_guide/testing:writing-end-to-end-tests}}
\sphinxAtStartPar
End\sphinxhyphen{}to\sphinxhyphen{}end tests should simulate user interactions with the system. They should test the entire flow from start to finish.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{test\PYGZus{}end\PYGZus{}to\PYGZus{}end\PYGZus{}firefox\PYGZus{}plugin}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Test the end\PYGZhy{}to\PYGZhy{}end flow with the Firefox plugin.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Start the API server}
    \PYG{n}{api\PYGZus{}server} \PYG{o}{=} \PYG{n}{APIServer}\PYG{p}{(}\PYG{p}{)}
    \PYG{n}{api\PYGZus{}server}\PYG{o}{.}\PYG{n}{start}\PYG{p}{(}\PYG{p}{)}

    \PYG{k}{try}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Start Firefox with the plugin installed}
        \PYG{n}{firefox} \PYG{o}{=} \PYG{n}{webdriver}\PYG{o}{.}\PYG{n}{Firefox}\PYG{p}{(}\PYG{p}{)}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{about:addons}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Install the plugin}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{find\PYGZus{}element\PYGZus{}by\PYGZus{}id}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{install\PYGZhy{}from\PYGZhy{}file}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{click}\PYG{p}{(}\PYG{p}{)}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{find\PYGZus{}element\PYGZus{}by\PYGZus{}id}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{file\PYGZhy{}input}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{send\PYGZus{}keys}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{/path/to/bahtbrowse\PYGZus{}bouncer.xpi}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{find\PYGZus{}element\PYGZus{}by\PYGZus{}id}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{install\PYGZhy{}button}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{click}\PYG{p}{(}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Navigate to a website}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Click the plugin button}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{find\PYGZus{}element\PYGZus{}by\PYGZus{}id}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{bahtbrowse\PYGZhy{}button}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}\PYG{o}{.}\PYG{n}{click}\PYG{p}{(}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Verify that the website opens in bahtBrowse}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{switch\PYGZus{}to}\PYG{o}{.}\PYG{n}{window}\PYG{p}{(}\PYG{n}{firefox}\PYG{o}{.}\PYG{n}{window\PYGZus{}handles}\PYG{p}{[}\PYG{o}{\PYGZhy{}}\PYG{l+m+mi}{1}\PYG{p}{]}\PYG{p}{)}
        \PYG{k}{assert} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{bahtBrowse}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{title}
        \PYG{k}{assert} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{example.com}\PYG{l+s+s2}{\PYGZdq{}} \PYG{o+ow}{in} \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{current\PYGZus{}url}
    \PYG{k}{finally}\PYG{p}{:}
        \PYG{c+c1}{\PYGZsh{} Close Firefox}
        \PYG{n}{firefox}\PYG{o}{.}\PYG{n}{quit}\PYG{p}{(}\PYG{p}{)}

        \PYG{c+c1}{\PYGZsh{} Stop the API server}
        \PYG{n}{api\PYGZus{}server}\PYG{o}{.}\PYG{n}{stop}\PYG{p}{(}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{Playwright Tests}
\label{\detokenize{developer_guide/testing:playwright-tests}}
\sphinxAtStartPar
The bahtBrowse project uses Playwright for comprehensive testing of the web interface and API. Playwright provides a way to automate browser interactions and test the application from a user’s perspective.

\sphinxAtStartPar
The Playwright test suite includes:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{UI Tests}:
\sphinxhyphen{} \sphinxstylestrong{Main Interface Tests}: Test basic functionality, URL input, browser selection, etc.
\sphinxhyphen{} \sphinxstylestrong{Accessibility Tests}: Test keyboard navigation, focus management, ARIA attributes, etc.
\sphinxhyphen{} \sphinxstylestrong{Error Handling Tests}: Test network errors, invalid URLs, timeouts, etc.
\sphinxhyphen{} \sphinxstylestrong{Theme Tests}: Test theme switching, system preference detection, etc.
\sphinxhyphen{} \sphinxstylestrong{Visual Regression Tests}: Test visual appearance of UI components across different viewports and themes.
\sphinxhyphen{} \sphinxstylestrong{Internationalization Tests}: Test language switching, RTL support, localized content, etc.
\sphinxhyphen{} \sphinxstylestrong{Security Tests}: Test XSS protection, CSP, clickjacking prevention, etc.
\sphinxhyphen{} \sphinxstylestrong{End\sphinxhyphen{}to\sphinxhyphen{}End Workflow Tests}: Test complete user workflows from start to finish.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Tests}:
\sphinxhyphen{} \sphinxstylestrong{Error Handling Tests}: Test invalid parameters, non\sphinxhyphen{}existent endpoints, server errors, etc.
\sphinxhyphen{} \sphinxstylestrong{Authentication Tests}: Test authentication requirements, token validation, etc.
\sphinxhyphen{} \sphinxstylestrong{Performance Tests}: Test response times, concurrent requests, etc.
\sphinxhyphen{} \sphinxstylestrong{Data Validation Tests}: Test input validation, output format, schema compliance, etc.
\sphinxhyphen{} \sphinxstylestrong{Load Tests}: Test performance under load, sustained load, large payloads, etc.

\end{enumerate}

\sphinxAtStartPar
Example UI Test:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{// @ts\PYGZhy{}check}
\PYG{k+kd}{const}\PYG{+w}{ }\PYG{p}{\PYGZob{}}\PYG{+w}{ }\PYG{n+nx}{test}\PYG{p}{,}\PYG{+w}{ }\PYG{n+nx}{expect}\PYG{+w}{ }\PYG{p}{\PYGZcb{}}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{n+nx}{require}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}@playwright/test\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{k+kd}{const}\PYG{+w}{ }\PYG{n+nx}{BrowserPage}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{n+nx}{require}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}./page\PYGZhy{}objects/BrowserPage\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}

\PYG{n+nx}{test}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}should navigate to a URL\PYGZsq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{k}{async}\PYG{+w}{ }\PYG{p}{(}\PYG{p}{\PYGZob{}}\PYG{+w}{ }\PYG{n+nx}{page}\PYG{+w}{ }\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{k+kd}{const}\PYG{+w}{ }\PYG{n+nx}{browserPage}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{o+ow}{new}\PYG{+w}{ }\PYG{n+nx}{BrowserPage}\PYG{p}{(}\PYG{n+nx}{page}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{  }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{browserPage}\PYG{p}{.}\PYG{k+kr}{goto}\PYG{p}{(}\PYG{p}{)}\PYG{p}{;}

\PYG{+w}{  }\PYG{c+c1}{// Enter a URL and navigate to it}
\PYG{+w}{  }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{browserPage}\PYG{p}{.}\PYG{n+nx}{navigateTo}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}https://example.com\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}

\PYG{+w}{  }\PYG{c+c1}{// Verify that the URL was loaded}
\PYG{+w}{  }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{browserPage}\PYG{p}{.}\PYG{n+nx}{urlInput}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toHaveValue}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}https://example.com\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}

\PYG{+w}{  }\PYG{c+c1}{// Verify that the page content is visible}
\PYG{+w}{  }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{page}\PYG{p}{.}\PYG{n+nx}{frameLocator}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}\PYGZsh{}browser\PYGZhy{}frame\PYGZsq{}}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{locator}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}body\PYGZsq{}}\PYG{p}{)}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toContainText}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}Example Domain\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{;}
\end{sphinxVerbatim}

\sphinxAtStartPar
Example API Test:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{// @ts\PYGZhy{}check}
\PYG{k+kd}{const}\PYG{+w}{ }\PYG{p}{\PYGZob{}}\PYG{+w}{ }\PYG{n+nx}{test}\PYG{p}{,}\PYG{+w}{ }\PYG{n+nx}{expect}\PYG{+w}{ }\PYG{p}{\PYGZcb{}}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{n+nx}{require}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}@playwright/test\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}

\PYG{n+nx}{test}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}should handle invalid request parameters\PYGZsq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{k}{async}\PYG{+w}{ }\PYG{p}{(}\PYG{p}{\PYGZob{}}\PYG{+w}{ }\PYG{n+nx}{request}\PYG{+w}{ }\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{c+c1}{// Test with missing required parameters}
\PYG{+w}{  }\PYG{k+kd}{const}\PYG{+w}{ }\PYG{n+nx}{response}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{request}\PYG{p}{.}\PYG{n+nx}{post}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}/api/containers/request\PYGZsq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nx}{data}\PYG{o}{:}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{      }\PYG{c+c1}{// Missing user\PYGZus{}id}
\PYG{+w}{      }\PYG{n+nx}{priority}\PYG{o}{:}\PYG{+w}{ }\PYG{l+m+mf}{5}
\PYG{+w}{    }\PYG{p}{\PYGZcb{}}
\PYG{+w}{  }\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{;}

\PYG{+w}{  }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{response}\PYG{p}{.}\PYG{n+nx}{status}\PYG{p}{(}\PYG{p}{)}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toBe}\PYG{p}{(}\PYG{l+m+mf}{400}\PYG{p}{)}\PYG{p}{;}

\PYG{+w}{  }\PYG{k+kd}{const}\PYG{+w}{ }\PYG{n+nx}{data}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{k}{await}\PYG{+w}{ }\PYG{n+nx}{response}\PYG{p}{.}\PYG{n+nx}{json}\PYG{p}{(}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{  }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{data}\PYG{p}{.}\PYG{n+nx}{status}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toBe}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}error\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{  }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{data}\PYG{p}{.}\PYG{n+nx}{message}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toBeDefined}\PYG{p}{(}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{  }\PYG{n+nx}{expect}\PYG{p}{(}\PYG{n+nx}{data}\PYG{p}{.}\PYG{n+nx}{message}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{toContain}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}user\PYGZus{}id\PYGZsq{}}\PYG{p}{)}\PYG{p}{;}
\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{;}
\end{sphinxVerbatim}


\paragraph{Running Playwright Tests}
\label{\detokenize{developer_guide/testing:running-playwright-tests}}
\sphinxAtStartPar
To run all Playwright tests:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }tests/playwright
npm\PYG{+w}{ }\PYG{n+nb}{test}
\end{sphinxVerbatim}

\sphinxAtStartPar
To run specific test categories:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Run UI tests only}
npm\PYG{+w}{ }run\PYG{+w}{ }test:ui\PYGZhy{}only

\PYG{c+c1}{\PYGZsh{} Run API tests only}
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}only

\PYG{c+c1}{\PYGZsh{} Run specific test categories}
npm\PYG{+w}{ }run\PYG{+w}{ }test:accessibility
npm\PYG{+w}{ }run\PYG{+w}{ }test:error\PYGZhy{}handling
npm\PYG{+w}{ }run\PYG{+w}{ }test:theme
npm\PYG{+w}{ }run\PYG{+w}{ }test:visual
npm\PYG{+w}{ }run\PYG{+w}{ }test:i18n
npm\PYG{+w}{ }run\PYG{+w}{ }test:security
npm\PYG{+w}{ }run\PYG{+w}{ }test:workflow
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}error
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}auth
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}performance
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}validation
npm\PYG{+w}{ }run\PYG{+w}{ }test:api\PYGZhy{}load
\end{sphinxVerbatim}

\sphinxAtStartPar
To run test suites:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Run smoke tests}
npm\PYG{+w}{ }run\PYG{+w}{ }test:smoke

\PYG{c+c1}{\PYGZsh{} Run regression tests}
npm\PYG{+w}{ }run\PYG{+w}{ }test:regression

\PYG{c+c1}{\PYGZsh{} Run critical path tests}
npm\PYG{+w}{ }run\PYG{+w}{ }test:critical\PYGZhy{}path
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests with a browser UI:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
npm\PYG{+w}{ }run\PYG{+w}{ }test:headed
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests in debug mode:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
npm\PYG{+w}{ }run\PYG{+w}{ }test:debug
\end{sphinxVerbatim}

\sphinxAtStartPar
To run tests with coverage:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
npm\PYG{+w}{ }run\PYG{+w}{ }test:coverage
\end{sphinxVerbatim}


\subsubsection{Test Coverage}
\label{\detokenize{developer_guide/testing:test-coverage}}
\sphinxAtStartPar
The bahtBrowse project uses both pytest\sphinxhyphen{}cov and Playwright’s coverage tools to measure test coverage. Test coverage reports show which parts of the codebase are covered by tests.


\paragraph{Current Coverage Metrics}
\label{\detokenize{developer_guide/testing:current-coverage-metrics}}
\sphinxAtStartPar
The project maintains high test coverage across all components:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{UI Components}: 95\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Endpoints}: 95\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Error Handling}: 90\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authentication}: 85\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Performance}: 85\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Internationalization}: 80\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Security}: 85\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Visual Regression}: 90\% coverage

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{End\sphinxhyphen{}to\sphinxhyphen{}End Workflows}: 90\% coverage

\end{itemize}


\paragraph{Generating Python Coverage Reports}
\label{\detokenize{developer_guide/testing:generating-python-coverage-reports}}
\sphinxAtStartPar
To generate a Python coverage report:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest\PYG{+w}{ }\PYGZhy{}\PYGZhy{}cov\PYG{o}{=}bahtbrowse\PYG{+w}{ }\PYGZhy{}\PYGZhy{}cov\PYGZhy{}report\PYG{o}{=}html
\end{sphinxVerbatim}

\sphinxAtStartPar
This will generate an HTML coverage report in the \sphinxcode{\sphinxupquote{htmlcov}} directory.


\paragraph{Generating Playwright Coverage Reports}
\label{\detokenize{developer_guide/testing:generating-playwright-coverage-reports}}
\sphinxAtStartPar
To generate a Playwright coverage report:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nb}{cd}\PYG{+w}{ }tests/playwright
npm\PYG{+w}{ }run\PYG{+w}{ }test:coverage
\end{sphinxVerbatim}

\sphinxAtStartPar
This will generate a coverage report in the \sphinxcode{\sphinxupquote{coverage}} directory.


\paragraph{Interpreting Coverage Reports}
\label{\detokenize{developer_guide/testing:interpreting-coverage-reports}}
\sphinxAtStartPar
Coverage reports show which lines of code are covered by tests. Lines that are not covered are highlighted in red.

\sphinxAtStartPar
The coverage report includes:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Coverage percentage}: The percentage of lines covered by tests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Missing lines}: Lines that are not covered by tests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Excluded lines}: Lines that are excluded from coverage measurement

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Function coverage}: The percentage of functions covered by tests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Branch coverage}: The percentage of branches covered by tests

\end{itemize}


\paragraph{Visual Coverage Reports}
\label{\detokenize{developer_guide/testing:visual-coverage-reports}}
\sphinxAtStartPar
The Playwright tests also generate visual coverage reports that show which parts of the UI are covered by tests. These reports include screenshots of the UI with highlighted areas that are covered by tests.


\subsubsection{Continuous Integration}
\label{\detokenize{developer_guide/testing:continuous-integration}}
\sphinxAtStartPar
The bahtBrowse project uses GitHub Actions for continuous integration. The CI pipeline runs the test suite on every pull request and push to the main branch.


\paragraph{CI Pipeline}
\label{\detokenize{developer_guide/testing:ci-pipeline}}
\sphinxAtStartPar
The CI pipeline includes the following steps:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Checkout}: Check out the code from the repository

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Setup}: Set up the Python environment and install dependencies

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Lint}: Run linting checks

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test}: Run the test suite
\sphinxhyphen{} Run Python unit tests
\sphinxhyphen{} Run Playwright UI tests
\sphinxhyphen{} Run Playwright API tests
\sphinxhyphen{} Run Playwright visual regression tests
\sphinxhyphen{} Run Playwright accessibility tests
\sphinxhyphen{} Run Playwright security tests
\sphinxhyphen{} Run Playwright internationalization tests
\sphinxhyphen{} Run Playwright end\sphinxhyphen{}to\sphinxhyphen{}end workflow tests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Coverage}: Generate coverage reports
\sphinxhyphen{} Generate Python coverage report
\sphinxhyphen{} Generate Playwright coverage report
\sphinxhyphen{} Generate visual coverage report

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Build}: Build the Docker images

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Deploy}: Deploy the Docker images (on main branch only)

\end{enumerate}


\paragraph{CI Configuration}
\label{\detokenize{developer_guide/testing:ci-configuration}}
\sphinxAtStartPar
The CI pipeline is configured in the \sphinxcode{\sphinxupquote{.github/workflows/ci.yml}} file. This file defines the steps, triggers, and environment for the CI pipeline.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{CI}

\PYG{n+nt}{on}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{push}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{branches}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{[}\PYG{+w}{ }\PYG{n+nv}{main}\PYG{+w}{ }\PYG{p+pIndicator}{]}
\PYG{+w}{  }\PYG{n+nt}{pull\PYGZus{}request}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{branches}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{[}\PYG{+w}{ }\PYG{n+nv}{main}\PYG{+w}{ }\PYG{p+pIndicator}{]}

\PYG{n+nt}{jobs}\PYG{p}{:}
\PYG{+w}{  }\PYG{n+nt}{python\PYGZhy{}tests}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{runs\PYGZhy{}on}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{ubuntu\PYGZhy{}latest}
\PYG{+w}{    }\PYG{n+nt}{steps}\PYG{p}{:}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{actions/checkout@v2}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Set}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{up}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{Python}
\PYG{+w}{      }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{actions/setup\PYGZhy{}python@v2}
\PYG{+w}{      }\PYG{n+nt}{with}\PYG{p}{:}
\PYG{+w}{        }\PYG{n+nt}{python\PYGZhy{}version}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{3.9}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Install}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{dependencies}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{python \PYGZhy{}m pip install \PYGZhy{}\PYGZhy{}upgrade pip}
\PYG{+w}{        }\PYG{n+no}{pip install \PYGZhy{}e \PYGZdq{}.[dev]\PYGZdq{}}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Lint}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{flake8}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Test}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{pytest \PYGZhy{}\PYGZhy{}cov=bahtbrowse}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Upload}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{coverage}
\PYG{+w}{      }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{codecov/codecov\PYGZhy{}action@v1}

\PYG{+w}{  }\PYG{n+nt}{playwright\PYGZhy{}tests}\PYG{p}{:}
\PYG{+w}{    }\PYG{n+nt}{runs\PYGZhy{}on}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{ubuntu\PYGZhy{}latest}
\PYG{+w}{    }\PYG{n+nt}{steps}\PYG{p}{:}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{actions/checkout@v2}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Set}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{up}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{Node.js}
\PYG{+w}{      }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{actions/setup\PYGZhy{}node@v2}
\PYG{+w}{      }\PYG{n+nt}{with}\PYG{p}{:}
\PYG{+w}{        }\PYG{n+nt}{node\PYGZhy{}version}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s}{\PYGZsq{}}\PYG{l+s}{16}\PYG{l+s}{\PYGZsq{}}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Install}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{dependencies}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{cd tests/playwright}
\PYG{+w}{        }\PYG{n+no}{npm ci}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Install}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{Playwright}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{browsers}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{cd tests/playwright}
\PYG{+w}{        }\PYG{n+no}{npx playwright install}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Start}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{test}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{environment}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{docker compose up \PYGZhy{}d}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Run}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{UI}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{tests}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{cd tests/playwright}
\PYG{+w}{        }\PYG{n+no}{npm run test:ui\PYGZhy{}only}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Run}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{API}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{tests}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{cd tests/playwright}
\PYG{+w}{        }\PYG{n+no}{npm run test:api\PYGZhy{}only}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Run}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{coverage}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{cd tests/playwright}
\PYG{+w}{        }\PYG{n+no}{npm run test:coverage}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Upload}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{coverage}
\PYG{+w}{      }\PYG{n+nt}{uses}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{actions/upload\PYGZhy{}artifact@v2}
\PYG{+w}{      }\PYG{n+nt}{with}\PYG{p}{:}
\PYG{+w}{        }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{playwright\PYGZhy{}coverage}
\PYG{+w}{        }\PYG{n+nt}{path}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{tests/playwright/coverage/}
\PYG{+w}{    }\PYG{p+pIndicator}{\PYGZhy{}}\PYG{+w}{ }\PYG{n+nt}{name}\PYG{p}{:}\PYG{+w}{ }\PYG{l+lScalar+lScalarPlain}{Stop}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{test}\PYG{l+lScalar+lScalarPlain}{ }\PYG{l+lScalar+lScalarPlain}{environment}
\PYG{+w}{      }\PYG{n+nt}{run}\PYG{p}{:}\PYG{+w}{ }\PYG{p+pIndicator}{|}
\PYG{+w}{        }\PYG{n+no}{docker compose down}
\end{sphinxVerbatim}


\subsubsection{Test Driven Development}
\label{\detokenize{developer_guide/testing:test-driven-development}}
\sphinxAtStartPar
The bahtBrowse project encourages test\sphinxhyphen{}driven development (TDD). TDD is a development process where tests are written before the code.


\paragraph{TDD Workflow}
\label{\detokenize{developer_guide/testing:tdd-workflow}}
\sphinxAtStartPar
The TDD workflow consists of the following steps:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Write a test}: Write a test that defines a function or improvement of a function

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run the test}: Verify that the test fails

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Write the code}: Write the minimum amount of code to make the test pass

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run the test}: Verify that the test passes

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Refactor}: Refactor the code to improve its quality

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Repeat}: Repeat the process for the next function or improvement

\end{enumerate}


\paragraph{Benefits of TDD}
\label{\detokenize{developer_guide/testing:benefits-of-tdd}}
\sphinxAtStartPar
TDD provides several benefits:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Improved code quality}: TDD encourages writing clean, modular code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Better test coverage}: TDD ensures that all code is covered by tests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Faster feedback}: TDD provides immediate feedback on the correctness of the code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Documentation}: Tests serve as documentation for the code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Confidence}: TDD gives confidence that the code works as expected

\end{itemize}


\subsubsection{Troubleshooting Tests}
\label{\detokenize{developer_guide/testing:troubleshooting-tests}}
\sphinxAtStartPar
If you encounter issues with tests, here are some common problems and solutions:


\paragraph{Test Failures}
\label{\detokenize{developer_guide/testing:test-failures}}
\sphinxAtStartPar
If a test fails, check the following:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test environment}: Ensure that the test environment is set up correctly

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test dependencies}: Ensure that all test dependencies are installed

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test data}: Ensure that the test data is correct

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test code}: Ensure that the test code is correct

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Code under test}: Ensure that the code under test is correct

\end{itemize}


\paragraph{Slow Tests}
\label{\detokenize{developer_guide/testing:slow-tests}}
\sphinxAtStartPar
If tests are slow, consider the following:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Mocking}: Use mocking to avoid slow operations like network requests or database queries

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Parallelism}: Run tests in parallel using pytest\sphinxhyphen{}xdist

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test scope}: Reduce the scope of tests to focus on specific functionality

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test data}: Use smaller test data sets

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test fixtures}: Use fixtures to share setup and teardown code

\end{itemize}


\paragraph{Flaky Tests}
\label{\detokenize{developer_guide/testing:flaky-tests}}
\sphinxAtStartPar
If tests are flaky (sometimes pass, sometimes fail), consider the following:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Determinism}: Ensure that tests are deterministic

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Isolation}: Ensure that tests are isolated from each other

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Timing}: Add delays or retries for timing\sphinxhyphen{}sensitive operations

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Resources}: Ensure that tests clean up resources properly

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Dependencies}: Ensure that tests don’t depend on external services

\end{itemize}


\subsubsection{Best Practices}
\label{\detokenize{developer_guide/testing:best-practices}}
\sphinxAtStartPar
Here are some best practices for testing the bahtBrowse system:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Write tests first}: Follow the TDD approach and write tests before code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Keep tests small}: Write small, focused tests that test a single aspect of the code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use descriptive names}: Use descriptive names for tests that explain what they test

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Use fixtures}: Use fixtures to share setup and teardown code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Mock dependencies}: Use mocking to isolate the code being tested

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Test edge cases}: Test edge cases and error conditions

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Maintain tests}: Keep tests up to date with code changes

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run tests often}: Run tests frequently to catch issues early

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Measure coverage}: Use coverage reports to identify untested code

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Automate testing}: Use CI to automate testing

\end{itemize}


\subsubsection{Conclusion}
\label{\detokenize{developer_guide/testing:conclusion}}
\sphinxAtStartPar
Testing is a critical part of the bahtBrowse development process. By following the guidelines in this guide, you can ensure that your contributions to the project are well\sphinxhyphen{}tested and reliable.

\sphinxAtStartPar
For more information on contributing to bahtBrowse, see the {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}} section.


\subsection{Introduction}
\label{\detokenize{developer_guide/index:introduction}}
\sphinxAtStartPar
bahtBrowse is an open\sphinxhyphen{}source Remote Browser Isolation (RBI) system that provides secure, containerized web browsing. This developer guide will help you understand the architecture, codebase, and development workflow of bahtBrowse.

\sphinxAtStartPar
Whether you want to contribute to the project, extend its functionality, or integrate it into your own systems, this guide will provide the information you need.


\subsection{System Architecture}
\label{\detokenize{developer_guide/index:system-architecture}}
\sphinxAtStartPar
bahtBrowse consists of several components that work together to provide a secure browsing experience:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Docker Containers}: Isolated environments running Firefox or Chromium browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{API Server}: Manages browser sessions and handles requests

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{VNC Interface}: Provides remote access to containerized browsers

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Firefox Plugin}: Enables seamless integration with Firefox

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Nginx Configuration}: Handles proxying and serving static files

\end{enumerate}

\sphinxAtStartPar
For a detailed explanation of the architecture, see {\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}}.


\subsection{Codebase Structure}
\label{\detokenize{developer_guide/index:codebase-structure}}
\sphinxAtStartPar
The bahtBrowse codebase is organized as follows:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
bahtbrowse/
├── docker/                 \PYGZsh{} Docker\PYGZhy{}related files
│   ├── Dockerfile          \PYGZsh{} Main Dockerfile for Firefox container
│   ├── Dockerfile.api      \PYGZsh{} Dockerfile for API server
│   └── ...
├── files/                  \PYGZsh{} Files to be copied into containers
│   ├── app.py              \PYGZsh{} API server application
│   ├── session\PYGZus{}manager.py  \PYGZsh{} Session management module
│   └── ...
├── firefox\PYGZus{}plugin/         \PYGZsh{} Firefox plugin source code
│   ├── background.js       \PYGZsh{} Background script
│   ├── manifest.json       \PYGZsh{} Plugin manifest
│   └── ...
├── docs/                   \PYGZsh{} Documentation
│   ├── sphinx/             \PYGZsh{} Sphinx documentation
│   └── ...
├── tests/                  \PYGZsh{} Test suite
│   ├── test\PYGZus{}docker.py      \PYGZsh{} Docker\PYGZhy{}related tests
│   ├── test\PYGZus{}redirect.py    \PYGZsh{} Redirection tests
│   └── ...
├── docker\PYGZhy{}compose.yml      \PYGZsh{} Docker Compose configuration
└── ...
\end{sphinxVerbatim}


\subsection{Development Environment}
\label{\detokenize{developer_guide/index:development-environment}}
\sphinxAtStartPar
To set up a development environment for bahtBrowse, follow these steps:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Clone the repository:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
git\PYG{+w}{ }clone\PYG{+w}{ }https://github.com/10Baht/bahtbrowse.git
\PYG{n+nb}{cd}\PYG{+w}{ }bahtbrowse
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Create a Python virtual environment:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
python3\PYG{+w}{ }\PYGZhy{}m\PYG{+w}{ }venv\PYG{+w}{ }venv
\PYG{n+nb}{source}\PYG{+w}{ }venv/bin/activate
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Install the development dependencies:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pip\PYG{+w}{ }install\PYG{+w}{ }\PYGZhy{}e\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}.[dev]\PYGZdq{}}
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Set up pre\sphinxhyphen{}commit hooks:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pre\PYGZhy{}commit\PYG{+w}{ }install
\end{sphinxVerbatim}

\item {} 
\sphinxAtStartPar
Build and run the containers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
docker\PYGZhy{}compose\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.yml\PYG{+w}{ }\PYGZhy{}f\PYG{+w}{ }docker\PYGZhy{}compose.dev.yml\PYG{+w}{ }up\PYG{+w}{ }\PYGZhy{}d
\end{sphinxVerbatim}

\end{enumerate}

\sphinxAtStartPar
For more detailed information on setting up a development environment, see {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}}.


\subsection{Development Workflow}
\label{\detokenize{developer_guide/index:development-workflow}}
\sphinxAtStartPar
The typical development workflow for bahtBrowse is as follows:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Create a Feature Branch}: Create a new branch for your feature or bug fix.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Make Changes}: Make your changes to the codebase.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Run Tests}: Run the test suite to ensure your changes don’t break existing functionality.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Submit a Pull Request}: Submit a pull request to the main repository.

\end{enumerate}

\sphinxAtStartPar
For more detailed information on the development workflow, see {\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}}.


\subsection{Testing}
\label{\detokenize{developer_guide/index:testing}}
\sphinxAtStartPar
bahtBrowse has a comprehensive test suite that covers various aspects of the system. To run the tests, use the following command:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
pytest
\end{sphinxVerbatim}

\sphinxAtStartPar
For more detailed information on testing, see {\hyperref[\detokenize{developer_guide/testing::doc}]{\sphinxcrossref{\DUrole{doc}{Testing Guide}}}}.


\subsection{API Reference}
\label{\detokenize{developer_guide/index:api-reference}}
\sphinxAtStartPar
For detailed information on the bahtBrowse API, see the {\hyperref[\detokenize{api_reference/index::doc}]{\sphinxcrossref{\DUrole{doc}{API Reference}}}} section.


\subsection{Next Steps}
\label{\detokenize{developer_guide/index:next-steps}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}}: Learn about the architecture of bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/contributing::doc}]{\sphinxcrossref{\DUrole{doc}{Contributing to bahtBrowse}}}}: Learn how to contribute to bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/testing::doc}]{\sphinxcrossref{\DUrole{doc}{Testing Guide}}}}: Learn how to test bahtBrowse

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{api_reference/index::doc}]{\sphinxcrossref{\DUrole{doc}{API Reference}}}}: Explore the API reference

\end{itemize}

\sphinxstepscope


\section{API Reference}
\label{\detokenize{api_reference/index:api-reference}}\label{\detokenize{api_reference/index:id1}}\label{\detokenize{api_reference/index::doc}}
\sphinxAtStartPar
This API reference provides detailed information about the bahtBrowse API, including endpoints, request parameters, and response formats.

\sphinxstepscope


\subsection{API Server}
\label{\detokenize{api_reference/api_server:api-server}}\label{\detokenize{api_reference/api_server:id1}}\label{\detokenize{api_reference/api_server::doc}}
\sphinxAtStartPar
The bahtBrowse API server provides a RESTful API for launching and managing browser sessions. This document provides detailed information about the API endpoints, request parameters, and response formats.


\subsubsection{Overview}
\label{\detokenize{api_reference/api_server:overview}}
\sphinxAtStartPar
The API server is implemented in Python using the aiohttp framework. It provides endpoints for launching browser sessions, testing the connection, logging browser console messages, and managing downloads.

\sphinxAtStartPar
The API server is designed to be simple and easy to use, with a focus on providing a seamless browsing experience.


\subsubsection{API Endpoints}
\label{\detokenize{api_reference/api_server:api-endpoints}}
\sphinxAtStartPar
The API server provides the following endpoints:


\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
\centering
\begin{tabulary}{\linewidth}[t]{TTT}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
Endpoint
&\sphinxstyletheadfamily 
\sphinxAtStartPar
Description
&\sphinxstyletheadfamily 
\sphinxAtStartPar
Methods
\\
\sphinxmidrule
\sphinxtableatstartofbodyhook
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/browse/}}
&
\sphinxAtStartPar
Launch a browser session
&
\sphinxAtStartPar
GET, POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/test\sphinxhyphen{}connection}}
&
\sphinxAtStartPar
Test the connection to the service
&
\sphinxAtStartPar
GET, POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/log\sphinxhyphen{}console}}
&
\sphinxAtStartPar
Log browser console messages
&
\sphinxAtStartPar
POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads}}
&
\sphinxAtStartPar
Access the downloads manager
&
\sphinxAtStartPar
GET
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/list}}
&
\sphinxAtStartPar
List downloaded files
&
\sphinxAtStartPar
GET
\\
\sphinxhline\sphinxstartmulticolumn{2}%
\begin{varwidth}[t]{\sphinxcolwidth{2}{3}}
\sphinxAtStartPar
{\color{red}\bfseries{}\textasciigrave{}\textasciigrave{}}/downloads\sphinxhyphen{}api/delete\textasciigrave{}\textasciigrave{}| Delete a downloaded file
\par
\vskip-\baselineskip\vbox{\hbox{\strut}}\end{varwidth}%
\sphinxstopmulticolumn
&
\sphinxAtStartPar
POST
\\
\sphinxbottomrule
\end{tabulary}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}


\subsubsection{Launch Browser Session}
\label{\detokenize{api_reference/api_server:launch-browser-session}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/browse/}} endpoint is used to launch a browser session with the specified URL.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:request}}
\sphinxAtStartPar
\sphinxstylestrong{GET /browse/}

\sphinxAtStartPar
Parameters:

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
GET /browse/?url=https://example.com\PYGZam{}browser=firefox\PYGZam{}mode=normal HTTP/1.1
Host: localhost:8082
\end{sphinxVerbatim}

\sphinxAtStartPar
\sphinxstylestrong{POST /browse/}

\sphinxAtStartPar
Parameters:

\sphinxAtStartPar
The same parameters as the GET request, but sent in the request body as form data.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
POST /browse/ HTTP/1.1
Host: localhost:8082
Content\PYGZhy{}Type: application/x\PYGZhy{}www\PYGZhy{}form\PYGZhy{}urlencoded

url=https://example.com\PYGZam{}browser=firefox\PYGZam{}mode=normal
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:response}}
\sphinxAtStartPar
The response is a redirect to the VNC interface with the session ID.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{302 Found}}

\sphinxAtStartPar
Headers:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
Location: http://localhost:6081/vnc1/run?session=12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc
\end{sphinxVerbatim}

\sphinxAtStartPar
Error Responses:


\subsubsection{Test Connection}
\label{\detokenize{api_reference/api_server:test-connection}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/test\sphinxhyphen{}connection}} endpoint is used to test the connection to the service.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:id4}}
\sphinxAtStartPar
\sphinxstylestrong{GET /test\sphinxhyphen{}connection}

\sphinxAtStartPar
No parameters required.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
GET /test\PYGZhy{}connection HTTP/1.1
Host: localhost:8082
\end{sphinxVerbatim}

\sphinxAtStartPar
\sphinxstylestrong{POST /test\sphinxhyphen{}connection}

\sphinxAtStartPar
No parameters required.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
POST /test\PYGZhy{}connection HTTP/1.1
Host: localhost:8082
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:id5}}
\sphinxAtStartPar
The response is a JSON object with information about the service.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{200 OK}}

\sphinxAtStartPar
Body:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}status\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}success\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}message\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}BahtBrowse service is available\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}timestamp\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}2025\PYGZhy{}04\PYGZhy{}27T20:00:00\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}client\PYGZus{}ip\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}127.0.0.1\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}client\PYGZus{}info\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Firefox Plugin\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}request\PYGZus{}source\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}background\PYGZhy{}script\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}plugin\PYGZus{}version\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}1.0.0\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}host\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}localhost\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}port\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}8082\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}server\PYGZus{}version\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}1.1.0\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}compatibility\PYGZus{}mode\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{k+kc}{true}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}supported\PYGZus{}browsers\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}firefox\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}chromium\PYGZdq{}}\PYG{p}{],}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}supported\PYGZus{}sites\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}amazon.com\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}amazon.*\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}amzn.*\PYGZdq{}}\PYG{p}{],}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}server\PYGZus{}time\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}2025\PYGZhy{}04\PYGZhy{}27T20:00:00.000Z\PYGZdq{}}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}


\subsubsection{Log Console Messages}
\label{\detokenize{api_reference/api_server:log-console-messages}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/log\sphinxhyphen{}console}} endpoint is used to log browser console messages.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:id6}}
\sphinxAtStartPar
\sphinxstylestrong{POST /log\sphinxhyphen{}console}

\sphinxAtStartPar
Parameters (JSON):

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
POST /log\PYGZhy{}console HTTP/1.1
Host: localhost:8082
Content\PYGZhy{}Type: application/json

\PYGZob{}
  \PYGZdq{}level\PYGZdq{}: \PYGZdq{}info\PYGZdq{},
  \PYGZdq{}message\PYGZdq{}: \PYGZdq{}Test message\PYGZdq{},
  \PYGZdq{}url\PYGZdq{}: \PYGZdq{}https://example.com\PYGZdq{},
  \PYGZdq{}session\PYGZus{}id\PYGZdq{}: \PYGZdq{}12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc\PYGZdq{},
  \PYGZdq{}line\PYGZdq{}: 42,
  \PYGZdq{}column\PYGZdq{}: 10,
  \PYGZdq{}stack\PYGZdq{}: \PYGZdq{}Error: Test error\PYGZbs{}n    at test.js:42:10\PYGZdq{}
\PYGZcb{}
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:id7}}
\sphinxAtStartPar
The response is a JSON object indicating success or failure.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{200 OK}}

\sphinxAtStartPar
Body:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}status\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}success\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}message\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Console log received\PYGZdq{}}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
Error Responses:


\subsubsection{Downloads Manager}
\label{\detokenize{api_reference/api_server:downloads-manager}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/downloads}} endpoint is used to access the downloads manager.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:id8}}
\sphinxAtStartPar
\sphinxstylestrong{GET /downloads}

\sphinxAtStartPar
No parameters required.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
GET /downloads HTTP/1.1
Host: localhost:8082
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:id9}}
\sphinxAtStartPar
The response is an HTML page with the downloads manager.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{200 OK}}

\sphinxAtStartPar
Content\sphinxhyphen{}Type: \sphinxcode{\sphinxupquote{text/html}}


\subsubsection{List Downloaded Files}
\label{\detokenize{api_reference/api_server:list-downloaded-files}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/list}} endpoint is used to list downloaded files.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:id10}}
\sphinxAtStartPar
\sphinxstylestrong{GET /downloads\sphinxhyphen{}api/list}

\sphinxAtStartPar
No parameters required.

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
GET /downloads\PYGZhy{}api/list HTTP/1.1
Host: localhost:8082
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:id11}}
\sphinxAtStartPar
The response is a JSON object with the list of downloaded files.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{200 OK}}

\sphinxAtStartPar
Body:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}success\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{k+kc}{true}\PYG{p}{,}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}files\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{p}{[}
\PYG{+w}{    }\PYG{p}{\PYGZob{}}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}name\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}example.pdf\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{12345}\PYG{p}{,}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}mtime\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{1619539200000}
\PYG{+w}{    }\PYG{p}{\PYGZcb{},}
\PYG{+w}{    }\PYG{p}{\PYGZob{}}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}name\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}image.jpg\PYGZdq{}}\PYG{p}{,}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}size\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{67890}\PYG{p}{,}
\PYG{+w}{      }\PYG{n+nt}{\PYGZdq{}mtime\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{l+m+mi}{1619539100000}
\PYG{+w}{    }\PYG{p}{\PYGZcb{}}
\PYG{+w}{  }\PYG{p}{]}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
Error Responses:


\subsubsection{Delete Downloaded File}
\label{\detokenize{api_reference/api_server:delete-downloaded-file}}
\sphinxAtStartPar
The \sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/delete}} endpoint is used to delete a downloaded file.


\paragraph{Request}
\label{\detokenize{api_reference/api_server:id12}}
\sphinxAtStartPar
\sphinxstylestrong{POST /downloads\sphinxhyphen{}api/delete}

\sphinxAtStartPar
Parameters (JSON):

\sphinxAtStartPar
Example:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
POST /downloads\PYGZhy{}api/delete HTTP/1.1
Host: localhost:8082
Content\PYGZhy{}Type: application/json

\PYGZob{}
  \PYGZdq{}filename\PYGZdq{}: \PYGZdq{}example.pdf\PYGZdq{}
\PYGZcb{}
\end{sphinxVerbatim}


\paragraph{Response}
\label{\detokenize{api_reference/api_server:id13}}
\sphinxAtStartPar
The response is a JSON object indicating success or failure.

\sphinxAtStartPar
Status Code: \sphinxcode{\sphinxupquote{200 OK}}

\sphinxAtStartPar
Body:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{p}{\PYGZob{}}
\PYG{+w}{  }\PYG{n+nt}{\PYGZdq{}success\PYGZdq{}}\PYG{p}{:}\PYG{+w}{ }\PYG{k+kc}{true}
\PYG{p}{\PYGZcb{}}
\end{sphinxVerbatim}

\sphinxAtStartPar
Error Responses:


\subsubsection{Implementation Details}
\label{\detokenize{api_reference/api_server:implementation-details}}
\sphinxAtStartPar
The API server is implemented in Python using the aiohttp framework. It communicates with the Docker Engine to create and manage containers.


\paragraph{Session Management}
\label{\detokenize{api_reference/api_server:session-management}}
\sphinxAtStartPar
The API server uses a session manager to create and manage browser sessions. Each session is identified by a unique session ID (UUID).

\sphinxAtStartPar
The session manager is responsible for:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Creating new sessions

\item {} 
\sphinxAtStartPar
Validating session IDs

\item {} 
\sphinxAtStartPar
Cleaning up expired sessions

\end{itemize}


\paragraph{Browser Launching}
\label{\detokenize{api_reference/api_server:browser-launching}}
\sphinxAtStartPar
The API server launches browsers in Docker containers. It supports both Firefox and Chromium browsers.

\sphinxAtStartPar
The browser launching process involves:
\begin{enumerate}
\sphinxsetlistlabels{\arabic}{enumi}{enumii}{}{.}%
\item {} 
\sphinxAtStartPar
Creating a new Docker container

\item {} 
\sphinxAtStartPar
Starting the VNC server in the container

\item {} 
\sphinxAtStartPar
Launching the browser with the specified URL

\item {} 
\sphinxAtStartPar
Redirecting the user to the VNC interface

\end{enumerate}


\paragraph{Error Handling}
\label{\detokenize{api_reference/api_server:error-handling}}
\sphinxAtStartPar
The API server includes comprehensive error handling to ensure a smooth user experience. It handles errors such as:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Invalid request parameters

\item {} 
\sphinxAtStartPar
Browser launch failures

\item {} 
\sphinxAtStartPar
Container creation failures

\item {} 
\sphinxAtStartPar
VNC server failures

\end{itemize}


\paragraph{Logging}
\label{\detokenize{api_reference/api_server:logging}}
\sphinxAtStartPar
The API server logs all requests and errors to help with debugging and monitoring. It logs:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Request parameters

\item {} 
\sphinxAtStartPar
Response status codes

\item {} 
\sphinxAtStartPar
Error messages

\item {} 
\sphinxAtStartPar
Browser console messages

\end{itemize}


\subsubsection{Security Considerations}
\label{\detokenize{api_reference/api_server:security-considerations}}
\sphinxAtStartPar
The API server is designed with security in mind. However, there are some security considerations to keep in mind:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Authentication}: The API server does not include authentication by default. Consider implementing authentication for multi\sphinxhyphen{}user environments.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Input Validation}: The API server validates all input parameters to prevent injection attacks.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Container Isolation}: Browsers run in isolated Docker containers to prevent malicious websites from affecting the host system.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Network Security}: Traffic between components is not encrypted by default. Consider using HTTPS or a VPN for sensitive browsing.

\end{itemize}

\sphinxAtStartPar
For more information on security considerations, see the {\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}} section.


\subsubsection{API Client Examples}
\label{\detokenize{api_reference/api_server:api-client-examples}}
\sphinxAtStartPar
Here are some examples of how to use the API from different programming languages:


\paragraph{Python}
\label{\detokenize{api_reference/api_server:python}}
\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{requests}

\PYG{c+c1}{\PYGZsh{} Launch a browser session}
\PYG{n}{response} \PYG{o}{=} \PYG{n}{requests}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{http://localhost:8082/browse/}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{params}\PYG{o}{=}\PYG{p}{\PYGZob{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{url}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{\PYGZcb{}}\PYG{p}{)}

\PYG{c+c1}{\PYGZsh{} Follow the redirect}
\PYG{k}{if} \PYG{n}{response}\PYG{o}{.}\PYG{n}{status\PYGZus{}code} \PYG{o}{==} \PYG{l+m+mi}{302}\PYG{p}{:}
    \PYG{n}{redirect\PYGZus{}url} \PYG{o}{=} \PYG{n}{response}\PYG{o}{.}\PYG{n}{headers}\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Location}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{]}
    \PYG{n+nb}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Redirected to: }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{redirect\PYGZus{}url}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\paragraph{JavaScript}
\label{\detokenize{api_reference/api_server:javascript}}
\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{// Launch a browser session}
\PYG{n+nx}{fetch}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/browse/?url=https://example.com\PYGZdq{}}\PYG{p}{)}
\PYG{+w}{  }\PYG{p}{.}\PYG{n+nx}{then}\PYG{p}{(}\PYG{n+nx}{response}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{k}{if}\PYG{+w}{ }\PYG{p}{(}\PYG{n+nx}{response}\PYG{p}{.}\PYG{n+nx}{redirected}\PYG{p}{)}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{      }\PYG{n+nb}{window}\PYG{p}{.}\PYG{n+nx}{location}\PYG{p}{.}\PYG{n+nx}{href}\PYG{+w}{ }\PYG{o}{=}\PYG{+w}{ }\PYG{n+nx}{response}\PYG{p}{.}\PYG{n+nx}{url}\PYG{p}{;}
\PYG{+w}{    }\PYG{p}{\PYGZcb{}}\PYG{+w}{ }\PYG{k}{else}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{      }\PYG{k}{return}\PYG{+w}{ }\PYG{n+nx}{response}\PYG{p}{.}\PYG{n+nx}{text}\PYG{p}{(}\PYG{p}{)}\PYG{p}{.}\PYG{n+nx}{then}\PYG{p}{(}\PYG{n+nx}{text}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{        }\PYG{k}{throw}\PYG{+w}{ }\PYG{o+ow}{new}\PYG{+w}{ }\PYG{n+ne}{Error}\PYG{p}{(}\PYG{l+s+sb}{`}\PYG{l+s+sb}{Error: }\PYG{l+s+si}{\PYGZdl{}\PYGZob{}}\PYG{n+nx}{text}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+sb}{`}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{      }\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{    }\PYG{p}{\PYGZcb{}}
\PYG{+w}{  }\PYG{p}{\PYGZcb{}}\PYG{p}{)}
\PYG{+w}{  }\PYG{p}{.}\PYG{k}{catch}\PYG{p}{(}\PYG{n+nx}{error}\PYG{+w}{ }\PYG{p}{=\PYGZgt{}}\PYG{+w}{ }\PYG{p}{\PYGZob{}}
\PYG{+w}{    }\PYG{n+nx}{console}\PYG{p}{.}\PYG{n+nx}{error}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}Error:\PYGZdq{}}\PYG{p}{,}\PYG{+w}{ }\PYG{n+nx}{error}\PYG{p}{)}\PYG{p}{;}
\PYG{+w}{  }\PYG{p}{\PYGZcb{}}\PYG{p}{)}\PYG{p}{;}
\end{sphinxVerbatim}


\paragraph{cURL}
\label{\detokenize{api_reference/api_server:curl}}
\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Launch a browser session}
curl\PYG{+w}{ }\PYGZhy{}L\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/browse/?url=https://example.com\PYGZdq{}}

\PYG{c+c1}{\PYGZsh{} Test the connection}
curl\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/test\PYGZhy{}connection\PYGZdq{}}

\PYG{c+c1}{\PYGZsh{} Log a console message}
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}H\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Content\PYGZhy{}Type: application/json\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}\PYGZob{}\PYGZdq{}level\PYGZdq{}: \PYGZdq{}info\PYGZdq{}, \PYGZdq{}message\PYGZdq{}: \PYGZdq{}Test message\PYGZdq{}, \PYGZdq{}url\PYGZdq{}: \PYGZdq{}https://example.com\PYGZdq{}, \PYGZdq{}session\PYGZus{}id\PYGZdq{}: \PYGZdq{}12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc\PYGZdq{}\PYGZcb{}\PYGZsq{}}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/log\PYGZhy{}console\PYGZdq{}}

\PYG{c+c1}{\PYGZsh{} List downloaded files}
curl\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/downloads\PYGZhy{}api/list\PYGZdq{}}

\PYG{c+c1}{\PYGZsh{} Delete a downloaded file}
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}H\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Content\PYGZhy{}Type: application/json\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}\PYGZob{}\PYGZdq{}filename\PYGZdq{}: \PYGZdq{}example.pdf\PYGZdq{}\PYGZcb{}\PYGZsq{}}\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/downloads\PYGZhy{}api/delete\PYGZdq{}}
\end{sphinxVerbatim}


\subsubsection{Conclusion}
\label{\detokenize{api_reference/api_server:conclusion}}
\sphinxAtStartPar
The bahtBrowse API server provides a simple and easy\sphinxhyphen{}to\sphinxhyphen{}use API for launching and managing browser sessions. It is designed to be flexible and extensible, allowing for a wide range of use cases.

\sphinxAtStartPar
For more information on the bahtBrowse architecture, see the {\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}} section.

\sphinxstepscope


\subsection{Session Management}
\label{\detokenize{api_reference/session_management:session-management}}\label{\detokenize{api_reference/session_management:id1}}\label{\detokenize{api_reference/session_management::doc}}
\sphinxAtStartPar
This document provides detailed information about the session management system in bahtBrowse, including session creation, validation, and cleanup.


\subsubsection{Overview}
\label{\detokenize{api_reference/session_management:overview}}
\sphinxAtStartPar
The session management system is responsible for creating and managing browser sessions. Each session is identified by a unique session ID (UUID) and is associated with a specific client IP address.

\sphinxAtStartPar
The session manager ensures that:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Each session has a unique ID

\item {} 
\sphinxAtStartPar
Sessions are isolated from each other

\item {} 
\sphinxAtStartPar
Sessions are cleaned up after a period of inactivity

\item {} 
\sphinxAtStartPar
Session IDs are validated before use

\end{itemize}


\subsubsection{Session Manager}
\label{\detokenize{api_reference/session_management:session-manager}}
\sphinxAtStartPar
The session manager is implemented in the \sphinxcode{\sphinxupquote{session\_manager.py}} file. It provides methods for creating, validating, and cleaning up sessions.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{class}\PYG{+w}{ }\PYG{n+nc}{SessionManager}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Manage browser sessions.\PYGZdq{}\PYGZdq{}\PYGZdq{}}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf+fm}{\PYGZus{}\PYGZus{}init\PYGZus{}\PYGZus{}}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Initialize the session manager.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions} \PYG{o}{=} \PYG{p}{\PYGZob{}}\PYG{p}{\PYGZcb{}}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout} \PYG{o}{=} \PYG{l+m+mi}{1800}  \PYG{c+c1}{\PYGZsh{} 30 minutes}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{create\PYGZus{}session}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Create a new session for the given client IP.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n+nb}{str}\PYG{p}{(}\PYG{n}{uuid}\PYG{o}{.}\PYG{n}{uuid4}\PYG{p}{(}\PYG{p}{)}\PYG{p}{)}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions}\PYG{p}{[}\PYG{n}{session\PYGZus{}id}\PYG{p}{]} \PYG{o}{=} \PYG{p}{\PYGZob{}}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{client\PYGZus{}ip}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{client\PYGZus{}ip}\PYG{p}{,}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{created\PYGZus{}at}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}\PYG{p}{,}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{last\PYGZus{}accessed}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}
        \PYG{p}{\PYGZcb{}}
        \PYG{k}{return} \PYG{n}{session\PYGZus{}id}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{validate\PYGZus{}session}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Validate the session ID for the given client IP.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{k}{if} \PYG{n}{session\PYGZus{}id} \PYG{o+ow}{not} \PYG{o+ow}{in} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions}\PYG{p}{:}
            \PYG{k}{return} \PYG{k+kc}{False}

        \PYG{n}{session} \PYG{o}{=} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions}\PYG{p}{[}\PYG{n}{session\PYGZus{}id}\PYG{p}{]}
        \PYG{k}{if} \PYG{n}{session}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{client\PYGZus{}ip}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]} \PYG{o}{!=} \PYG{n}{client\PYGZus{}ip}\PYG{p}{:}
            \PYG{k}{return} \PYG{k+kc}{False}

        \PYG{c+c1}{\PYGZsh{} Update last accessed time}
        \PYG{n}{session}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{last\PYGZus{}accessed}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]} \PYG{o}{=} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}
        \PYG{k}{return} \PYG{k+kc}{True}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{cleanup\PYGZus{}sessions}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Clean up expired sessions.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{now} \PYG{o}{=} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}
        \PYG{n}{expired\PYGZus{}sessions} \PYG{o}{=} \PYG{p}{[}\PYG{p}{]}

        \PYG{k}{for} \PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{session} \PYG{o+ow}{in} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions}\PYG{o}{.}\PYG{n}{items}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
            \PYG{k}{if} \PYG{n}{now} \PYG{o}{\PYGZhy{}} \PYG{n}{session}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{last\PYGZus{}accessed}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]} \PYG{o}{\PYGZgt{}} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout}\PYG{p}{:}
                \PYG{n}{expired\PYGZus{}sessions}\PYG{o}{.}\PYG{n}{append}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{)}

        \PYG{k}{for} \PYG{n}{session\PYGZus{}id} \PYG{o+ow}{in} \PYG{n}{expired\PYGZus{}sessions}\PYG{p}{:}
            \PYG{k}{del} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{sessions}\PYG{p}{[}\PYG{n}{session\PYGZus{}id}\PYG{p}{]}
\end{sphinxVerbatim}


\subsubsection{Session Creation}
\label{\detokenize{api_reference/session_management:session-creation}}
\sphinxAtStartPar
Sessions are created when a user requests a new browser session through the API server. The session manager creates a new session with a unique ID and associates it with the client IP address.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Create a new session}
\PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{create\PYGZus{}session}\PYG{p}{(}\PYG{n}{client\PYGZus{}ip}\PYG{p}{)}
\end{sphinxVerbatim}

\sphinxAtStartPar
The session ID is a UUID (Universally Unique Identifier) that is generated using the \sphinxcode{\sphinxupquote{uuid.uuid4()}} function. This ensures that each session has a unique ID.

\sphinxAtStartPar
The session is stored in the session manager’s \sphinxcode{\sphinxupquote{sessions}} dictionary, with the session ID as the key and a dictionary containing the client IP address, creation time, and last accessed time as the value.


\subsubsection{Session Validation}
\label{\detokenize{api_reference/session_management:session-validation}}
\sphinxAtStartPar
Before a session is used, it is validated to ensure that it exists and is associated with the correct client IP address. This prevents unauthorized access to sessions.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Validate a session}
\PYG{n}{is\PYGZus{}valid} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{validate\PYGZus{}session}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}
\end{sphinxVerbatim}

\sphinxAtStartPar
The validation process checks that:
\begin{itemize}
\item {} 
\sphinxAtStartPar
The session ID exists in the session manager’s \sphinxcode{\sphinxupquote{sessions}} dictionary

\item {} 
\sphinxAtStartPar
The session is associated with the correct client IP address

\end{itemize}

\sphinxAtStartPar
If the session is valid, the last accessed time is updated to the current time. This ensures that active sessions are not cleaned up.


\subsubsection{Session Cleanup}
\label{\detokenize{api_reference/session_management:session-cleanup}}
\sphinxAtStartPar
Sessions are automatically cleaned up after a period of inactivity to prevent resource exhaustion. The default timeout is 30 minutes (1800 seconds).

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Clean up expired sessions}
\PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{cleanup\PYGZus{}sessions}\PYG{p}{(}\PYG{p}{)}
\end{sphinxVerbatim}

\sphinxAtStartPar
The cleanup process checks each session’s last accessed time. If the session has not been accessed for longer than the timeout period, it is removed from the session manager’s \sphinxcode{\sphinxupquote{sessions}} dictionary.

\sphinxAtStartPar
The cleanup process is typically run periodically, such as once per minute, to ensure that expired sessions are removed in a timely manner.


\subsubsection{Session Validator}
\label{\detokenize{api_reference/session_management:session-validator}}
\sphinxAtStartPar
The session validator is a middleware component that validates session IDs in incoming requests. It is implemented in the \sphinxcode{\sphinxupquote{session\_validator.py}} file.

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k}{class}\PYG{+w}{ }\PYG{n+nc}{SessionValidator}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Validate session IDs in incoming requests.\PYGZdq{}\PYGZdq{}\PYGZdq{}}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf+fm}{\PYGZus{}\PYGZus{}init\PYGZus{}\PYGZus{}}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{session\PYGZus{}manager}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Initialize the session validator.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}manager} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}

    \PYG{k}{async} \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{validate}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{request}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Validate the session ID in the request.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{request}\PYG{o}{.}\PYG{n}{query}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{session}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}
        \PYG{k}{if} \PYG{o+ow}{not} \PYG{n}{session\PYGZus{}id}\PYG{p}{:}
            \PYG{k}{return} \PYG{k+kc}{False}

        \PYG{n}{client\PYGZus{}ip} \PYG{o}{=} \PYG{n}{request}\PYG{o}{.}\PYG{n}{remote}
        \PYG{k}{return} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{validate\PYGZus{}session}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}
\end{sphinxVerbatim}

\sphinxAtStartPar
The session validator is used by the API server to validate session IDs in incoming requests. It extracts the session ID from the request query parameters and validates it using the session manager.


\subsubsection{Session Storage}
\label{\detokenize{api_reference/session_management:session-storage}}
\sphinxAtStartPar
Sessions are stored in memory in the session manager’s \sphinxcode{\sphinxupquote{sessions}} dictionary. This provides fast access to session data but means that sessions are lost if the server is restarted.

\sphinxAtStartPar
For production environments, it is recommended to use a persistent storage backend, such as Redis or a database, to store session data. This ensures that sessions are not lost if the server is restarted.

\sphinxAtStartPar
Here’s an example of how to implement a Redis\sphinxhyphen{}based session manager:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{redis}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{uuid}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{time}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{json}

\PYG{k}{class}\PYG{+w}{ }\PYG{n+nc}{RedisSessionManager}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Manage browser sessions using Redis.\PYGZdq{}\PYGZdq{}\PYGZdq{}}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf+fm}{\PYGZus{}\PYGZus{}init\PYGZus{}\PYGZus{}}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{redis\PYGZus{}url}\PYG{o}{=}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{redis://localhost:6379/0}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Initialize the session manager.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{redis} \PYG{o}{=} \PYG{n}{redis}\PYG{o}{.}\PYG{n}{from\PYGZus{}url}\PYG{p}{(}\PYG{n}{redis\PYGZus{}url}\PYG{p}{)}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout} \PYG{o}{=} \PYG{l+m+mi}{1800}  \PYG{c+c1}{\PYGZsh{} 30 minutes}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{create\PYGZus{}session}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Create a new session for the given client IP.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n+nb}{str}\PYG{p}{(}\PYG{n}{uuid}\PYG{o}{.}\PYG{n}{uuid4}\PYG{p}{(}\PYG{p}{)}\PYG{p}{)}
        \PYG{n}{session\PYGZus{}data} \PYG{o}{=} \PYG{p}{\PYGZob{}}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{client\PYGZus{}ip}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{client\PYGZus{}ip}\PYG{p}{,}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{created\PYGZus{}at}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}\PYG{p}{,}
            \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{last\PYGZus{}accessed}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{:} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}
        \PYG{p}{\PYGZcb{}}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{redis}\PYG{o}{.}\PYG{n}{setex}\PYG{p}{(}
            \PYG{l+s+sa}{f}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{session:}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{session\PYGZus{}id}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout}\PYG{p}{,}
            \PYG{n}{json}\PYG{o}{.}\PYG{n}{dumps}\PYG{p}{(}\PYG{n}{session\PYGZus{}data}\PYG{p}{)}
        \PYG{p}{)}
        \PYG{k}{return} \PYG{n}{session\PYGZus{}id}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{validate\PYGZus{}session}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Validate the session ID for the given client IP.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{session\PYGZus{}key} \PYG{o}{=} \PYG{l+s+sa}{f}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{session:}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{session\PYGZus{}id}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s1}{\PYGZsq{}}
        \PYG{n}{session\PYGZus{}data\PYGZus{}str} \PYG{o}{=} \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{redis}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{n}{session\PYGZus{}key}\PYG{p}{)}

        \PYG{k}{if} \PYG{o+ow}{not} \PYG{n}{session\PYGZus{}data\PYGZus{}str}\PYG{p}{:}
            \PYG{k}{return} \PYG{k+kc}{False}

        \PYG{n}{session\PYGZus{}data} \PYG{o}{=} \PYG{n}{json}\PYG{o}{.}\PYG{n}{loads}\PYG{p}{(}\PYG{n}{session\PYGZus{}data\PYGZus{}str}\PYG{p}{)}
        \PYG{k}{if} \PYG{n}{session\PYGZus{}data}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{client\PYGZus{}ip}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]} \PYG{o}{!=} \PYG{n}{client\PYGZus{}ip}\PYG{p}{:}
            \PYG{k}{return} \PYG{k+kc}{False}

        \PYG{c+c1}{\PYGZsh{} Update last accessed time}
        \PYG{n}{session\PYGZus{}data}\PYG{p}{[}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{last\PYGZus{}accessed}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{]} \PYG{o}{=} \PYG{n}{time}\PYG{o}{.}\PYG{n}{time}\PYG{p}{(}\PYG{p}{)}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{redis}\PYG{o}{.}\PYG{n}{setex}\PYG{p}{(}
            \PYG{n}{session\PYGZus{}key}\PYG{p}{,}
            \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout}\PYG{p}{,}
            \PYG{n}{json}\PYG{o}{.}\PYG{n}{dumps}\PYG{p}{(}\PYG{n}{session\PYGZus{}data}\PYG{p}{)}
        \PYG{p}{)}
        \PYG{k}{return} \PYG{k+kc}{True}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{cleanup\PYGZus{}sessions}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Clean up expired sessions.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{c+c1}{\PYGZsh{} Redis automatically expires keys based on the timeout}
        \PYG{k}{pass}
\end{sphinxVerbatim}


\subsubsection{Session Security}
\label{\detokenize{api_reference/session_management:session-security}}
\sphinxAtStartPar
The session management system includes several security features to prevent unauthorized access to sessions:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session IDs}: Session IDs are UUIDs, which are cryptographically secure and difficult to guess.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Client IP Binding}: Sessions are bound to the client IP address, preventing session hijacking.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Timeout}: Sessions expire after a period of inactivity, limiting the window of opportunity for attacks.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Session Validation}: Session IDs are validated before use, preventing unauthorized access.

\end{itemize}

\sphinxAtStartPar
For additional security, consider implementing:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxstylestrong{HTTPS}: Use HTTPS to encrypt traffic between the client and server, preventing eavesdropping.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{CSRF Protection}: Implement Cross\sphinxhyphen{}Site Request Forgery (CSRF) protection to prevent unauthorized requests.

\item {} 
\sphinxAtStartPar
\sphinxstylestrong{Rate Limiting}: Implement rate limiting to prevent brute force attacks on session IDs.

\end{itemize}


\subsubsection{Session Management API}
\label{\detokenize{api_reference/session_management:session-management-api}}
\sphinxAtStartPar
The session manager provides the following API for managing sessions:


\subsubsection{Example Usage}
\label{\detokenize{api_reference/session_management:example-usage}}
\sphinxAtStartPar
Here’s an example of how to use the session manager in the API server:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{from}\PYG{+w}{ }\PYG{n+nn}{session\PYGZus{}manager}\PYG{+w}{ }\PYG{k+kn}{import} \PYG{n}{SessionManager}

\PYG{c+c1}{\PYGZsh{} Initialize the session manager}
\PYG{n}{session\PYGZus{}manager} \PYG{o}{=} \PYG{n}{SessionManager}\PYG{p}{(}\PYG{p}{)}

\PYG{k}{async} \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{handle\PYGZus{}request}\PYG{p}{(}\PYG{n}{request}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Handle a request to launch a browser session.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Get the client IP address}
    \PYG{n}{client\PYGZus{}ip} \PYG{o}{=} \PYG{n}{request}\PYG{o}{.}\PYG{n}{remote}

    \PYG{c+c1}{\PYGZsh{} Create a new session}
    \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{create\PYGZus{}session}\PYG{p}{(}\PYG{n}{client\PYGZus{}ip}\PYG{p}{)}

    \PYG{c+c1}{\PYGZsh{} Redirect to the VNC interface with the session ID}
    \PYG{k}{return} \PYG{n}{web}\PYG{o}{.}\PYG{n}{HTTPFound}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{/vnc?session=}\PYG{l+s+si}{\PYGZob{}}\PYG{n}{session\PYGZus{}id}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}

\PYG{k}{async} \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{handle\PYGZus{}vnc\PYGZus{}request}\PYG{p}{(}\PYG{n}{request}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Handle a request to access the VNC interface.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{c+c1}{\PYGZsh{} Get the session ID from the request}
    \PYG{n}{session\PYGZus{}id} \PYG{o}{=} \PYG{n}{request}\PYG{o}{.}\PYG{n}{query}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{session}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}
    \PYG{k}{if} \PYG{o+ow}{not} \PYG{n}{session\PYGZus{}id}\PYG{p}{:}
        \PYG{k}{return} \PYG{n}{web}\PYG{o}{.}\PYG{n}{Response}\PYG{p}{(}\PYG{n}{status}\PYG{o}{=}\PYG{l+m+mi}{400}\PYG{p}{,} \PYG{n}{text}\PYG{o}{=}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{Session ID is required}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}

    \PYG{c+c1}{\PYGZsh{} Get the client IP address}
    \PYG{n}{client\PYGZus{}ip} \PYG{o}{=} \PYG{n}{request}\PYG{o}{.}\PYG{n}{remote}

    \PYG{c+c1}{\PYGZsh{} Validate the session}
    \PYG{k}{if} \PYG{o+ow}{not} \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{validate\PYGZus{}session}\PYG{p}{(}\PYG{n}{session\PYGZus{}id}\PYG{p}{,} \PYG{n}{client\PYGZus{}ip}\PYG{p}{)}\PYG{p}{:}
        \PYG{k}{return} \PYG{n}{web}\PYG{o}{.}\PYG{n}{Response}\PYG{p}{(}\PYG{n}{status}\PYG{o}{=}\PYG{l+m+mi}{403}\PYG{p}{,} \PYG{n}{text}\PYG{o}{=}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{Invalid session ID}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}

    \PYG{c+c1}{\PYGZsh{} Serve the VNC interface}
    \PYG{k}{return} \PYG{n}{web}\PYG{o}{.}\PYG{n}{FileResponse}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{/path/to/vnc.html}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}

\PYG{c+c1}{\PYGZsh{} Set up a periodic task to clean up expired sessions}
\PYG{k}{async} \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf}{cleanup\PYGZus{}task}\PYG{p}{(}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Periodically clean up expired sessions.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
    \PYG{k}{while} \PYG{k+kc}{True}\PYG{p}{:}
        \PYG{n}{session\PYGZus{}manager}\PYG{o}{.}\PYG{n}{cleanup\PYGZus{}sessions}\PYG{p}{(}\PYG{p}{)}
        \PYG{k}{await} \PYG{n}{asyncio}\PYG{o}{.}\PYG{n}{sleep}\PYG{p}{(}\PYG{l+m+mi}{60}\PYG{p}{)}  \PYG{c+c1}{\PYGZsh{} Clean up every minute}

\PYG{c+c1}{\PYGZsh{} Start the cleanup task}
\PYG{n}{asyncio}\PYG{o}{.}\PYG{n}{create\PYGZus{}task}\PYG{p}{(}\PYG{n}{cleanup\PYGZus{}task}\PYG{p}{(}\PYG{p}{)}\PYG{p}{)}
\end{sphinxVerbatim}


\subsubsection{Session Management in Docker Containers}
\label{\detokenize{api_reference/session_management:session-management-in-docker-containers}}
\sphinxAtStartPar
When using Docker containers, session management becomes more complex because each container has its own session manager. To ensure that sessions are consistent across containers, you can use a shared storage backend, such as Redis.

\sphinxAtStartPar
Here’s an example of how to configure the Redis session manager in a Docker environment:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} In docker\PYGZhy{}compose.yml}
\PYG{n}{services}\PYG{p}{:}
  \PYG{n}{api}\PYG{p}{:}
    \PYG{n}{image}\PYG{p}{:} \PYG{n}{bahtbrowse}\PYG{o}{\PYGZhy{}}\PYG{n}{api}
    \PYG{n}{environment}\PYG{p}{:}
      \PYG{o}{\PYGZhy{}} \PYG{n}{REDIS\PYGZus{}URL}\PYG{o}{=}\PYG{n}{redis}\PYG{p}{:}\PYG{o}{/}\PYG{o}{/}\PYG{n}{redis}\PYG{p}{:}\PYG{l+m+mi}{6379}\PYG{o}{/}\PYG{l+m+mi}{0}
    \PYG{n}{depends\PYGZus{}on}\PYG{p}{:}
      \PYG{o}{\PYGZhy{}} \PYG{n}{redis}
  \PYG{n}{redis}\PYG{p}{:}
    \PYG{n}{image}\PYG{p}{:} \PYG{n}{redis}\PYG{p}{:}\PYG{l+m+mf}{6.0}
    \PYG{n}{volumes}\PYG{p}{:}
      \PYG{o}{\PYGZhy{}} \PYG{n}{redis}\PYG{o}{\PYGZhy{}}\PYG{n}{data}\PYG{p}{:}\PYG{o}{/}\PYG{n}{data}

\PYG{c+c1}{\PYGZsh{} In session\PYGZus{}manager.py}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{os}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{redis}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{uuid}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{time}
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{json}

\PYG{k}{class}\PYG{+w}{ }\PYG{n+nc}{RedisSessionManager}\PYG{p}{:}
\PYG{+w}{    }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Manage browser sessions using Redis.\PYGZdq{}\PYGZdq{}\PYGZdq{}}

    \PYG{k}{def}\PYG{+w}{ }\PYG{n+nf+fm}{\PYGZus{}\PYGZus{}init\PYGZus{}\PYGZus{}}\PYG{p}{(}\PYG{n+nb+bp}{self}\PYG{p}{,} \PYG{n}{redis\PYGZus{}url}\PYG{o}{=}\PYG{k+kc}{None}\PYG{p}{)}\PYG{p}{:}
\PYG{+w}{        }\PYG{l+s+sd}{\PYGZdq{}\PYGZdq{}\PYGZdq{}Initialize the session manager.\PYGZdq{}\PYGZdq{}\PYGZdq{}}
        \PYG{n}{redis\PYGZus{}url} \PYG{o}{=} \PYG{n}{redis\PYGZus{}url} \PYG{o+ow}{or} \PYG{n}{os}\PYG{o}{.}\PYG{n}{environ}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{REDIS\PYGZus{}URL}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{,} \PYG{l+s+s1}{\PYGZsq{}}\PYG{l+s+s1}{redis://localhost:6379/0}\PYG{l+s+s1}{\PYGZsq{}}\PYG{p}{)}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{redis} \PYG{o}{=} \PYG{n}{redis}\PYG{o}{.}\PYG{n}{from\PYGZus{}url}\PYG{p}{(}\PYG{n}{redis\PYGZus{}url}\PYG{p}{)}
        \PYG{n+nb+bp}{self}\PYG{o}{.}\PYG{n}{session\PYGZus{}timeout} \PYG{o}{=} \PYG{l+m+mi}{1800}  \PYG{c+c1}{\PYGZsh{} 30 minutes}

    \PYG{c+c1}{\PYGZsh{} ... rest of the implementation ...}
\end{sphinxVerbatim}


\subsubsection{Conclusion}
\label{\detokenize{api_reference/session_management:conclusion}}
\sphinxAtStartPar
The session management system is a critical component of the bahtBrowse system. It ensures that browser sessions are isolated, secure, and properly managed.

\sphinxAtStartPar
For more information on the bahtBrowse architecture, see the {\hyperref[\detokenize{developer_guide/architecture::doc}]{\sphinxcrossref{\DUrole{doc}{System Architecture}}}} section.


\subsection{Introduction}
\label{\detokenize{api_reference/index:introduction}}
\sphinxAtStartPar
The bahtBrowse API allows you to interact with the bahtBrowse system programmatically. You can use the API to:
\begin{itemize}
\item {} 
\sphinxAtStartPar
Launch browser sessions

\item {} 
\sphinxAtStartPar
Manage browser sessions

\item {} 
\sphinxAtStartPar
Check the status of the service

\item {} 
\sphinxAtStartPar
Log browser console messages

\end{itemize}


\subsection{API Endpoints}
\label{\detokenize{api_reference/index:api-endpoints}}
\sphinxAtStartPar
The bahtBrowse API provides the following endpoints:


\begin{savenotes}\sphinxattablestart
\sphinxthistablewithglobalstyle
\centering
\begin{tabulary}{\linewidth}[t]{TTT}
\sphinxtoprule
\sphinxstyletheadfamily 
\sphinxAtStartPar
Endpoint
&\sphinxstyletheadfamily 
\sphinxAtStartPar
Description
&\sphinxstyletheadfamily 
\sphinxAtStartPar
Methods
\\
\sphinxmidrule
\sphinxtableatstartofbodyhook
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/browse/}}
&
\sphinxAtStartPar
Launch a browser session
&
\sphinxAtStartPar
GET, POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/test\sphinxhyphen{}connection}}
&
\sphinxAtStartPar
Test the connection to the service
&
\sphinxAtStartPar
GET, POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/log\sphinxhyphen{}console}}
&
\sphinxAtStartPar
Log browser console messages
&
\sphinxAtStartPar
POST
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads}}
&
\sphinxAtStartPar
Access the downloads manager
&
\sphinxAtStartPar
GET
\\
\sphinxhline
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{/downloads\sphinxhyphen{}api/list}}
&
\sphinxAtStartPar
List downloaded files
&
\sphinxAtStartPar
GET
\\
\sphinxhline\sphinxstartmulticolumn{2}%
\begin{varwidth}[t]{\sphinxcolwidth{2}{3}}
\sphinxAtStartPar
{\color{red}\bfseries{}\textasciigrave{}\textasciigrave{}}/downloads\sphinxhyphen{}api/delete\textasciigrave{}\textasciigrave{}| Delete a downloaded file
\par
\vskip-\baselineskip\vbox{\hbox{\strut}}\end{varwidth}%
\sphinxstopmulticolumn
&
\sphinxAtStartPar
POST
\\
\sphinxbottomrule
\end{tabulary}
\sphinxtableafterendhook\par
\sphinxattableend\end{savenotes}

\sphinxAtStartPar
For detailed information about each endpoint, see {\hyperref[\detokenize{api_reference/api_server::doc}]{\sphinxcrossref{\DUrole{doc}{API Server}}}}.


\subsection{Authentication}
\label{\detokenize{api_reference/index:authentication}}
\sphinxAtStartPar
The bahtBrowse API does not currently require authentication. However, it is recommended to secure the API if you are deploying it in a production environment.


\subsection{Rate Limiting}
\label{\detokenize{api_reference/index:rate-limiting}}
\sphinxAtStartPar
The bahtBrowse API does not currently implement rate limiting. However, it is recommended to implement rate limiting if you are deploying it in a production environment.


\subsection{Error Handling}
\label{\detokenize{api_reference/index:error-handling}}
\sphinxAtStartPar
The bahtBrowse API returns standard HTTP status codes to indicate the success or failure of a request:
\begin{itemize}
\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{200 OK}}: The request was successful

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{302 Found}}: The request was successful and a redirect is provided

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{400 Bad Request}}: The request was invalid

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{404 Not Found}}: The requested resource was not found

\item {} 
\sphinxAtStartPar
\sphinxcode{\sphinxupquote{500 Internal Server Error}}: An error occurred on the server

\end{itemize}

\sphinxAtStartPar
Error responses include a message in the response body explaining the error.


\subsection{Example Usage}
\label{\detokenize{api_reference/index:example-usage}}
\sphinxAtStartPar
Here are some examples of how to use the bahtBrowse API:


\subsubsection{Launch a Browser Session}
\label{\detokenize{api_reference/index:launch-a-browser-session}}
\sphinxAtStartPar
To launch a browser session, send a GET or POST request to the \sphinxcode{\sphinxupquote{/browse/}} endpoint with the \sphinxcode{\sphinxupquote{url}} parameter:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Using curl with GET}
curl\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}http://localhost:8082/browse/?url=https://example.com\PYGZdq{}}

\PYG{c+c1}{\PYGZsh{} Using curl with POST}
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}url=https://example.com\PYGZdq{}}\PYG{+w}{ }http://localhost:8082/browse/
\end{sphinxVerbatim}


\subsubsection{Test the Connection}
\label{\detokenize{api_reference/index:test-the-connection}}
\sphinxAtStartPar
To test the connection to the service, send a GET or POST request to the \sphinxcode{\sphinxupquote{/test\sphinxhyphen{}connection}} endpoint:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Using curl}
curl\PYG{+w}{ }http://localhost:8082/test\PYGZhy{}connection
\end{sphinxVerbatim}


\subsubsection{Log Console Messages}
\label{\detokenize{api_reference/index:log-console-messages}}
\sphinxAtStartPar
To log browser console messages, send a POST request to the \sphinxcode{\sphinxupquote{/log\sphinxhyphen{}console}} endpoint with the message details:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{c+c1}{\PYGZsh{} Using curl}
curl\PYG{+w}{ }\PYGZhy{}X\PYG{+w}{ }POST\PYG{+w}{ }\PYGZhy{}H\PYG{+w}{ }\PYG{l+s+s2}{\PYGZdq{}Content\PYGZhy{}Type: application/json\PYGZdq{}}\PYG{+w}{ }\PYGZhy{}d\PYG{+w}{ }\PYG{l+s+s1}{\PYGZsq{}\PYGZob{}\PYGZdq{}level\PYGZdq{}: \PYGZdq{}info\PYGZdq{}, \PYGZdq{}message\PYGZdq{}: \PYGZdq{}Test message\PYGZdq{}, \PYGZdq{}url\PYGZdq{}: \PYGZdq{}https://example.com\PYGZdq{}, \PYGZdq{}session\PYGZus{}id\PYGZdq{}: \PYGZdq{}12345678\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}1234\PYGZhy{}123456789abc\PYGZdq{}\PYGZcb{}\PYGZsq{}}\PYG{+w}{ }http://localhost:8082/log\PYGZhy{}console
\end{sphinxVerbatim}


\subsection{API Client Libraries}
\label{\detokenize{api_reference/index:api-client-libraries}}
\sphinxAtStartPar
There are currently no official client libraries for the bahtBrowse API. However, you can use any HTTP client library to interact with the API.

\sphinxAtStartPar
Here’s an example using Python’s \sphinxcode{\sphinxupquote{requests}} library:

\begin{sphinxVerbatim}[commandchars=\\\{\}]
\PYG{k+kn}{import}\PYG{+w}{ }\PYG{n+nn}{requests}

\PYG{c+c1}{\PYGZsh{} Launch a browser session}
\PYG{n}{response} \PYG{o}{=} \PYG{n}{requests}\PYG{o}{.}\PYG{n}{get}\PYG{p}{(}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{http://localhost:8082/browse/}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{,} \PYG{n}{params}\PYG{o}{=}\PYG{p}{\PYGZob{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{url}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{:} \PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{https://example.com}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{\PYGZcb{}}\PYG{p}{)}

\PYG{c+c1}{\PYGZsh{} Follow the redirect}
\PYG{k}{if} \PYG{n}{response}\PYG{o}{.}\PYG{n}{status\PYGZus{}code} \PYG{o}{==} \PYG{l+m+mi}{302}\PYG{p}{:}
    \PYG{n}{redirect\PYGZus{}url} \PYG{o}{=} \PYG{n}{response}\PYG{o}{.}\PYG{n}{headers}\PYG{p}{[}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Location}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{]}
    \PYG{n+nb}{print}\PYG{p}{(}\PYG{l+s+sa}{f}\PYG{l+s+s2}{\PYGZdq{}}\PYG{l+s+s2}{Redirected to: }\PYG{l+s+si}{\PYGZob{}}\PYG{n}{redirect\PYGZus{}url}\PYG{l+s+si}{\PYGZcb{}}\PYG{l+s+s2}{\PYGZdq{}}\PYG{p}{)}
\end{sphinxVerbatim}


\subsection{Next Steps}
\label{\detokenize{api_reference/index:next-steps}}\begin{itemize}
\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{api_reference/api_server::doc}]{\sphinxcrossref{\DUrole{doc}{API Server}}}}: Learn about the API server endpoints

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{api_reference/session_management::doc}]{\sphinxcrossref{\DUrole{doc}{Session Management}}}}: Learn about session management

\item {} 
\sphinxAtStartPar
{\hyperref[\detokenize{developer_guide/index::doc}]{\sphinxcrossref{\DUrole{doc}{Developer Guide}}}}: Explore the developer guide

\end{itemize}


\chapter{Indices and tables}
\label{\detokenize{index:indices-and-tables}}\begin{itemize}
\item {} 
\sphinxAtStartPar
\DUrole{xref}{\DUrole{std}{\DUrole{std-ref}{genindex}}}

\item {} 
\sphinxAtStartPar
\DUrole{xref}{\DUrole{std}{\DUrole{std-ref}{modindex}}}

\item {} 
\sphinxAtStartPar
\DUrole{xref}{\DUrole{std}{\DUrole{std-ref}{search}}}

\end{itemize}



\renewcommand{\indexname}{Index}
\printindex
\end{document}
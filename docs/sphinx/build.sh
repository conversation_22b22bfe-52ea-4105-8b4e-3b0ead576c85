#!/bin/bash

# Script to build the Sphinx documentation

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Ensure script is run from the sphinx directory
if [[ $(basename "$(pwd)") != "sphinx" ]]; then
    echo "This script must be run from the sphinx directory."
    echo "Current directory: $(pwd)"
    echo "Please change to the sphinx directory and try again."
    exit 1
fi

# Function to print section headers
print_header() {
    echo -e "\n${BLUE}======================================================================${NC}"
    echo -e "${BLUE}${BOLD} $1 ${NC}"
    echo -e "${BLUE}======================================================================${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}${BOLD}✓ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}${BOLD}✗ $1${NC}"
}

# Function to print info messages
print_info() {
    echo -e "${YELLOW}${BOLD}ℹ $1${NC}"
}

# Check if Sphinx is installed
print_header "Checking dependencies"
if ! command -v sphinx-build &> /dev/null; then
    print_error "Sphinx is not installed. Please install it with: pip install sphinx"
    exit 1
fi
print_success "Sphinx is installed"

# Check if required extensions are installed
required_extensions=(
    "sphinx_rtd_theme"
    "myst_parser"
    "sphinxcontrib.mermaid"
    "sphinx_copybutton"
)

for ext in "${required_extensions[@]}"; do
    if ! pip list | grep -q "$ext"; then
        print_error "$ext is not installed. Please install it with: pip install $ext"
        exit 1
    fi
    print_success "$ext is installed"
done

# Create necessary directories
print_header "Creating directories"
mkdir -p build/html
mkdir -p build/pdf
mkdir -p source/_static
mkdir -p source/_templates

# Check if logo exists, if not create a placeholder
if [ ! -f "source/_static/bahtbrowse_logo.png" ]; then
    print_info "Logo not found. Creating a placeholder logo description."
    if [ ! -f "source/_static/logo_description.txt" ]; then
        echo "A logo for bahtBrowse should be placed here." > source/_static/logo_description.txt
    fi
fi

# Create a custom.css file if it doesn't exist
if [ ! -f "source/_static/custom.css" ]; then
    print_info "custom.css not found. Creating a basic custom.css file."
    cat > source/_static/custom.css << EOF
/* Custom CSS for bahtBrowse documentation */
body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
EOF
fi

# Build HTML documentation
print_header "Building HTML documentation"
sphinx-build -b html source build/html

if [ $? -eq 0 ]; then
    print_success "HTML documentation built successfully"
else
    print_error "Failed to build HTML documentation"
    exit 1
fi

# Build PDF documentation if LaTeX is installed
print_header "Building PDF documentation"
if command -v pdflatex &> /dev/null; then
    sphinx-build -b latex source build/pdf
    if [ $? -eq 0 ]; then
        print_success "LaTeX files generated successfully"
        
        # Change to the PDF build directory
        cd build/pdf
        
        # Build the PDF
        make
        
        if [ $? -eq 0 ]; then
            print_success "PDF documentation built successfully"
            # Move the PDF to the html directory for easy access
            mv bahtBrowse.pdf ../html/
            print_info "PDF documentation moved to build/html/bahtBrowse.pdf"
        else
            print_error "Failed to build PDF documentation"
        fi
        
        # Change back to the original directory
        cd ../..
    else
        print_error "Failed to generate LaTeX files"
    fi
else
    print_info "LaTeX is not installed. Skipping PDF generation."
    print_info "To build PDF documentation, install LaTeX and run this script again."
fi

# Print success message
print_header "Build completed"
print_success "Documentation built successfully"
print_info "HTML documentation is available at: $(pwd)/build/html/index.html"
if [ -f "build/html/bahtBrowse.pdf" ]; then
    print_info "PDF documentation is available at: $(pwd)/build/html/bahtBrowse.pdf"
fi

# Open the documentation in a browser if possible
if command -v xdg-open &> /dev/null; then
    print_info "Opening documentation in browser..."
    xdg-open "$(pwd)/build/html/index.html"
elif command -v open &> /dev/null; then
    print_info "Opening documentation in browser..."
    open "$(pwd)/build/html/index.html"
else
    print_info "To view the documentation, open the following file in your browser:"
    print_info "$(pwd)/build/html/index.html"
fi

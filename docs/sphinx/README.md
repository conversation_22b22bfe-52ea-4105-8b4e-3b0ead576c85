# bahtBrowse Sphinx Documentation

This directory contains the Sphinx documentation for the bahtBrowse project.

## Overview

The documentation is organized into the following sections:

- **Getting Started**: Installation and quick start guides
- **User Guide**: Detailed information for users
- **Administrator Guide**: Deployment and configuration guides
- **Developer Guide**: Architecture and contribution guidelines
- **API Reference**: API documentation
- **Troubleshooting**: Common issues and solutions

## Building the Documentation

To build the documentation, run the following command from the `docs/sphinx` directory:

```bash
./build.sh
```

This will build both HTML and PDF versions of the documentation (if LaTeX is installed).

## Requirements

The following packages are required to build the documentation:

- Sphinx
- sphinx_rtd_theme
- myst_parser
- sphinxcontrib.mermaid
- sphinx_copybutton

You can install these packages with pip:

```bash
pip install sphinx sphinx_rtd_theme myst_parser sphinxcontrib-mermaid sphinx-copybutton
```

For PDF generation, you also need LaTeX:

```bash
# Ubuntu/Debian
sudo apt-get install texlive-latex-recommended texlive-fonts-recommended texlive-latex-extra latexmk

# Fedora
sudo dnf install texlive-scheme-medium

# macOS
brew install --cask mactex
```

## Directory Structure

```
sphinx/
├── build/              # Build output
│   ├── html/           # HTML output
│   └── pdf/            # PDF output
├── source/             # Documentation source files
│   ├── _static/        # Static files (CSS, images, etc.)
│   ├── _templates/     # Custom templates
│   ├── getting_started/ # Getting started guides
│   ├── user_guide/     # User guides
│   ├── admin_guide/    # Administrator guides
│   ├── developer_guide/ # Developer guides
│   ├── api_reference/  # API reference
│   ├── troubleshooting/ # Troubleshooting guides
│   ├── conf.py         # Sphinx configuration
│   └── index.rst       # Documentation home page
├── build.sh            # Build script
└── README.md           # This file
```

## Contributing to the Documentation

If you want to contribute to the documentation, please follow these guidelines:

1. Use reStructuredText (`.rst`) for documentation files
2. Follow the existing structure and style
3. Build the documentation locally to verify your changes
4. Submit a pull request with your changes

## License

The documentation is licensed under the same license as the bahtBrowse project.

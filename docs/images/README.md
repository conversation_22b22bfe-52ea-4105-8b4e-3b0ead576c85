# Screenshot Directory

This directory contains screenshots for documentation.

## benchmark-cli.png

This file should be a screenshot of the Rich CLI interface showing the benchmark results. 

To capture this screenshot:

1. Run the benchmark test:
   ```bash
   python -m tests.load_testing.benchmark_runner --scenario low --all
   ```

2. Take a screenshot of the terminal showing the Rich CLI interface with all four browser-OS panels and the winner panel.

3. Save the screenshot as `benchmark-cli.png` in this directory.

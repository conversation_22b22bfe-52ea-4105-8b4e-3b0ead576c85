# Git Workflow

This document illustrates the branching strategy used in the BahtBrowse project.

## Actual Branching Model

```mermaid
gitGraph
    commit
    commit
    branch refactor/cw17-pre-integration
    checkout refactor/cw17-pre-integration
    commit id: "Fix redirect loop"
    commit id: "Improve nginx config"
    commit id: "Add user guides"
    commit id: "Add BahtBrowse submodule"
    commit id: "LEAF: All critical components integrated"
    checkout main
    commit
    branch refactor/get-firefox-working-again-cw19
    checkout refactor/get-firefox-working-again-cw19
    commit id: "Fix Firefox on Ubuntu"
    commit id: "Add Firefox extension"
    commit id: "Add health dashboard"
    commit id: "Add health dashboard with icons and improved UI"
    commit id: "Add inactivity timeout"
    commit id: "Merge fixes from cw17-pre-integration"
    commit id: "Add VNC test script updates"
    commit id: "Add roadmap and issue templates"
    commit id: "Add production deployment script"
    commit id: "LEAF: Successfully integrated all critical components from cw17-pre-integration"
    commit id: "Cleanup: Removed obsolete branches"
```

## Branch Naming Conventions

- `feature/cwXX`: For new feature development where XX is a number
- `refactor/cwXX-description`: For code refactoring and pre-integration work
- `testing/cwXX-description`: For testing-related work
- `hotfix/cwXX`: For urgent fixes that need to be applied directly to main

## Workflow Guidelines

1. Create a new branch from `develop` for features or refactoring
2. Make commits to your branch
3. When complete, merge back to `develop`
4. Periodically merge `develop` into `main` for stable releases
5. Create hotfix branches directly from `main` when needed

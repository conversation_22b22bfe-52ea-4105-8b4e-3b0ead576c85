# Firefox Plugin Consolidation

## Overview

As part of the project structure cleanup, we have identified two directories containing Firefox plugin code:

1. `firefox_plugin/`
2. `browser_plugins/firefox/`

This document explains the consolidation strategy for these directories.

## Comparison of Directories

Both directories contain similar files:

- Background scripts
- Manifest files
- Build scripts
- Icons
- Options pages
- Popup pages

The `browser_plugins/firefox/` directory appears to be more complete and up-to-date, with additional files like `web-ext-artifacts/` and slightly different versions of some files.

## Consolidation Strategy

1. Keep the `browser_plugins/firefox/` directory as the primary location for Firefox plugin code
2. Archive the `firefox_plugin/` directory by moving it to `archive/firefox_plugin_legacy/`
3. Document any significant differences between the two implementations
4. Update all references to use the consolidated location

## Implementation Notes

### File Differences

| File | Differences | Action |
|------|-------------|--------|
| `background.js` | Different sizes (22101 vs 28704 bytes) | Keep newer version in `browser_plugins/firefox/` |
| `manifest.json` | Different sizes (1287 vs 1309 bytes) | Keep newer version in `browser_plugins/firefox/` |
| Other files | Similar sizes and timestamps | Keep versions in `browser_plugins/firefox/` |

### Directory Structure

The consolidated Firefox plugin code will be located at `browser_plugins/firefox/` with the following structure:

```
browser_plugins/
└── firefox/
    ├── build/
    ├── icons/
    ├── options/
    ├── popup/
    ├── web-ext-artifacts/
    ├── background.js
    ├── manifest.json
    └── ... (other files)
```

## References to Update

The following references need to be updated to point to the new location:

1. Documentation in README.md
2. Build scripts
3. Test scripts
4. Any import statements in code

## Conclusion

By consolidating the Firefox plugin code into a single location, we improve maintainability and reduce confusion for developers. The `browser_plugins/` directory is the logical place for all browser extension code, allowing for future expansion to other browsers like Chrome.

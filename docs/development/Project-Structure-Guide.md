# Project Structure Guide

## Overview

This document explains the bahtBrowse project structure after the cleanup and reorganisation. The goal of the restructuring was to create a more maintainable, organised codebase that follows Python best practices.

## Directory Structure

```
bahtbrowse/
├── browser_plugins/          # All browser extension code
│   ├── firefox/              # Firefox plugin files
│   └── chrome/               # Future Chrome plugin files (placeholder)
├── core/                     # Core application code
│   ├── api/                  # API server code
│   ├── browser/              # Browser management code
│   ├── container/            # Container management code
│   └── utils/                # Utility functions
├── docker_queue/             # Queue management system
│   ├── api/                  # API endpoints
│   ├── config/               # Configuration
│   ├── dashboard/            # Dashboard UI
│   ├── performance/          # Performance tools
│   └── tasks/                # Celery tasks
├── docs/                     # Documentation
│   ├── architecture/         # Architecture diagrams and docs
│   ├── development/          # Development guides
│   ├── images/               # Images for documentation
│   └── user/                 # User guides
├── elk/                      # ELK stack configuration
├── scripts/                  # Utility scripts
│   ├── build/                # Build scripts
│   ├── deployment/           # Deployment scripts
│   └── test/                 # Test scripts
├── tests/                    # All test files
│   ├── core/                 # Tests for core functionality
│   ├── docker_queue/         # Tests for docker queue
│   ├── integration/          # Integration tests
│   ├── load_testing/         # Load testing scripts
│   └── unit/                 # Unit tests
├── archive/                  # Archived code (for reference)
├── .github/                  # GitHub workflows and templates
├── .gitignore                # Git ignore file
├── CHANGELOG.md              # Changelog
├── docker-compose.yml        # Main Docker Compose file
├── Dockerfile                # Main Dockerfile
├── LICENSE                   # License file
├── pyproject.toml            # Python project configuration
├── README.md                 # Main README
└── requirements.txt          # Dependencies
```

## Core Modules

### core/

The `core/` directory contains the central functionality of bahtBrowse:

- **api/**: API server code for handling requests
- **browser/**: Browser management code for controlling browser instances
- **container/**: Container management code for Docker containers
- **utils/**: Utility functions used across the codebase

### docker_queue/

The `docker_queue/` directory contains the queue management system:

- **api/**: API endpoints for the queue system
- **config/**: Configuration files for the queue system
- **dashboard/**: Dashboard UI for monitoring the queue
- **performance/**: Performance tools for optimising the queue
- **tasks/**: Celery tasks for asynchronous processing

### browser_plugins/

The `browser_plugins/` directory contains all browser extension code:

- **firefox/**: Firefox plugin files
- **chrome/**: Future Chrome plugin files (placeholder)

## Testing

All tests are located in the `tests/` directory, organised by type:

- **core/**: Tests for core functionality
- **docker_queue/**: Tests for the queue management system
- **integration/**: Integration tests for the entire system
- **load_testing/**: Load testing scripts for performance testing
- **unit/**: Unit tests for individual components

To run tests:

```bash
# Run all tests
pytest

# Run specific test category
pytest tests/core/
pytest tests/integration/

# Run with coverage
pytest --cov=core
```

## Documentation

Documentation is located in the `docs/` directory:

- **architecture/**: Architecture diagrams and documentation
- **development/**: Development guides (like this one)
- **images/**: Images for documentation
- **user/**: User guides and tutorials

## Scripts

Utility scripts are located in the `scripts/` directory:

- **build/**: Scripts for building the project
- **deployment/**: Scripts for deploying the project
- **test/**: Scripts for testing the project

## Configuration

Project configuration is managed through:

- **pyproject.toml**: Python project configuration
- **docker-compose.yml**: Docker Compose configuration
- **Dockerfile**: Docker configuration

## Development Workflow

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt -r requirements-dev.txt`
3. Run tests to ensure everything works: `pytest`
4. Make changes in the appropriate directory
5. Run tests to verify changes: `pytest`
6. Update documentation if necessary
7. Commit changes and create a pull request

## Import Conventions

When importing modules, use the following conventions:

```python
# Importing from core
from core.api.server import app
from core.browser.manager import BrowserManager
from core.utils.helpers import get_random_id

# Importing from docker_queue
from docker_queue.api.routes import router
from docker_queue.tasks.container_management import create_container
```

## Conclusion

This new structure provides a more organised and maintainable codebase. By following Python best practices and providing clear separation of concerns, we make it easier for developers to understand and contribute to the project.

# BahtBrowse Data Storage Architecture

This document outlines the data storage architecture used in the BahtBrowse system.

## Overview

BahtBrowse uses a simple, file-based storage approach for all data persistence needs. This design choice prioritizes simplicity, reliability, and compatibility with containerized environments.

## Storage Components

### 1. Session Data

Session data is stored in the local filesystem within the Docker container:

- **Location**: `/tmp/sessions/[session_id]/`
- **Content**: 
  - Session metadata (creation time, URL, settings)
  - Firefox profile data
  - Session-specific configuration

### 2. Logging

BahtBrowse implements a comprehensive logging system using file-based logs:

- **System Logs**: `/tmp/bahtbrowse_server.log`
  - API requests
  - Container lifecycle events
  - Error conditions
  
- **Browser Console Logs**: `/tmp/browser_console.log`
  - JavaScript errors and warnings
  - Page load events
  - User interactions

### 3. Firefox Profile Data

Firefox profiles are stored in the filesystem:

- **Base Profile**: `/tmp/firefox_profile/`
  - Default Firefox settings
  - Base user.js configuration
  
- **Session-Specific Profiles**: `/tmp/firefox_profile_[session_id]/`
  - Cloned from base profile
  - Modified with session-specific settings

## Data Lifecycle

1. **Creation**: When a new browsing session is requested, a unique session ID is generated and a directory structure is created.
2. **Usage**: During the session, logs and temporary data are written to the filesystem.
3. **Cleanup**: When a session ends or times out, the associated directory is removed.

## Advantages of File-Based Storage

1. **Simplicity**: No additional services or dependencies required
2. **Containerization**: Works well within Docker's ephemeral filesystem
3. **Performance**: Fast local I/O for session data
4. **Debugging**: Easy access to logs and session data for troubleshooting
5. **Isolation**: Each session's data is contained in its own directory

## Future Enhancements

While the current file-based approach meets the project's needs, future versions may consider:

1. **Structured Logging**: Enhanced log formatting and indexing
2. **Optimized Storage**: More efficient use of filesystem resources
3. **Optional Persistence**: Configurable retention of session data
4. **Backup Mechanisms**: Automated backup of critical configuration

## Implementation Details

The storage implementation is primarily handled in the `files/app.py` module, which:

1. Creates session directories
2. Manages Firefox profiles
3. Writes log entries
4. Handles cleanup of expired sessions

No external database or caching services are required for the current implementation.

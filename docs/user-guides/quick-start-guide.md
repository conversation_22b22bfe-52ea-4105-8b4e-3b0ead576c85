# BahtBrowse Quick Start Guide

This guide provides a quick overview of how to get started with BahtBrowse.

## What is BahtBrowse?

BahtBrowse is a secure browsing solution that allows you to browse the web in an isolated environment. It runs websites in a containerized browser, protecting your local system from potential threats.

## Installation

### Server Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/tenbahtsecurity/bahtbrowse.git
   cd bahtbrowse
   ```

2. Deploy to production:
   ```bash
   ./scripts/utils/prepare_production.sh
   ./scripts/utils/deploy_production.sh
   ```

### Firefox Plugin Installation

1. Download the latest `bahtbrowse_bouncer.xpi` file from the [releases page](https://github.com/tenbahtsecurity/bahtbrowse/releases)
2. Open Firefox and go to `about:addons`
3. Click the gear icon and select "Install Add-on From File..."
4. Navigate to the downloaded `.xpi` file and select it
5. Follow the prompts to complete the installation

## Basic Usage

### Using the Web Interface

1. Open your web browser and navigate to the BahtBrowse server URL (default: `http://localhost:8081`)
2. Enter the URL you want to browse in the input field
3. Click the "Browse" button or press Enter
4. The website will be loaded in a secure container

### Using the Firefox Plugin

1. Navigate to a website in Firefox
2. Click the BahtBrowse icon in the Firefox toolbar
3. The website will be opened in BahtBrowse

## Key Features

- **Isolated Environment**: Each browsing session runs in an isolated container
- **No Persistence**: Browser data is not saved between sessions
- **Network Isolation**: The browser container has limited network access
- **Automatic Cleanup**: Containers are automatically cleaned up after use
- **Firefox Integration**: Seamless integration with Firefox through the BahtBrowse Bouncer plugin

## Next Steps

- Read the [Web Interface User Guide](web-interface-user-guide.md) for detailed information on using the BahtBrowse web interface
- Read the [Firefox Plugin User Guide](firefox-plugin-user-guide.md) for detailed information on using the BahtBrowse Firefox plugin
- Check the [Production Deployment Guide](../production-deployment-guide.md) for information on deploying BahtBrowse to a production environment

## Feedback and Support

If you encounter any issues or have suggestions for improving BahtBrowse, please [open an issue on GitHub](https://github.com/tenbahtsecurity/bahtbrowse/issues).

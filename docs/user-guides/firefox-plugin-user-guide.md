# BahtBrowse Firefox Plugin User Guide

This guide provides instructions for installing and using the BahtBrowse Firefox plugin.

## Installation

### From Firefox Add-ons Store (Recommended)

1. Open Firefox and go to the [BahtBrowse Bouncer page on the Firefox Add-ons store](https://addons.mozilla.org/en-US/firefox/addon/bahtbrowse-bouncer/)
2. Click the "Add to Firefox" button
3. Follow the prompts to complete the installation

### Manual Installation

1. Download the latest `bahtbrowse_bouncer.xpi` file from the [releases page](https://github.com/tenbahtsecurity/bahtbrowse/releases)
2. Open Firefox and go to `about:addons`
3. Click the gear icon and select "Install Add-on From File..."
4. Navigate to the downloaded `.xpi` file and select it
5. Follow the prompts to complete the installation

## Configuration

1. After installation, click the BahtBrowse icon in the Firefox toolbar
2. Click the "Settings" button to open the preferences page
3. Configure the following settings:
   - **BahtBrowse Server URL**: The URL of your BahtBrowse server (default: `http://localhost:8081`)
   - **Connection Timeout**: The timeout for connections to the BahtBrowse server (default: 5000ms)
   - **Dark Mode**: Enable or disable dark mode for the plugin interface
   - **Debug Mode**: Enable or disable debug logging (for troubleshooting)

4. Click "Save" to apply your changes

## Usage

### Basic Usage

1. Navigate to a website you want to view in BahtBrowse
2. Click the BahtBrowse icon in the Firefox toolbar
3. The current page will be opened in BahtBrowse

### Context Menu

You can also right-click on any link and select "Open Link in BahtBrowse" to open the link directly in BahtBrowse.

### Keyboard Shortcut

Press `Ctrl+Shift+B` (or `Cmd+Shift+B` on macOS) to open the current page in BahtBrowse.

## Testing the Connection

1. Click the BahtBrowse icon in the Firefox toolbar
2. Click the "Test Connection" button
3. A notification will appear indicating whether the connection was successful or not

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the BahtBrowse server:

1. Ensure the BahtBrowse server is running
2. Check that the server URL in the plugin settings is correct
3. Verify that your network allows connections to the BahtBrowse server
4. Try increasing the connection timeout in the plugin settings

### Debug Logs

If you need to troubleshoot issues with the plugin:

1. Enable Debug Mode in the plugin settings
2. Open the Firefox Developer Tools (F12)
3. Go to the Console tab
4. Look for messages starting with "BahtBrowse:"

## Uninstalling

1. Open Firefox and go to `about:addons`
2. Find the BahtBrowse Bouncer plugin
3. Click the three dots next to the plugin and select "Remove"
4. Confirm the removal when prompted

## Feedback and Support

If you encounter any issues or have suggestions for improving the plugin, please [open an issue on GitHub](https://github.com/tenbahtsecurity/bahtbrowse/issues).

# BahtBrowse Web Interface User Guide

This guide provides instructions for using the BahtBrowse web interface.

## Accessing the Web Interface

1. Open your web browser and navigate to the BahtBrowse server URL (default: `http://localhost:8081`)
2. You will be presented with the BahtBrowse landing page

## Browsing Websites

### Direct URL Entry

1. On the BahtBrowse landing page, enter the URL you want to browse in the input field
2. Click the "Browse" button or press Enter
3. The website will be loaded in a secure container

### Using the Firefox Plugin

If you have the BahtBrowse Firefox plugin installed:

1. Navigate to a website in Firefox
2. Click the BahtBrowse icon in the Firefox toolbar
3. The website will be opened in BahtBrowse

## VNC Interface

The BahtBrowse web interface uses noVNC to provide a virtual browser environment.

### Navigation Controls

- **Clipboard**: Click the clipboard icon to copy/paste text between your local system and the virtual browser
- **Keyboard**: Click the keyboard icon to send special key combinations to the virtual browser
- **Power**: Click the power icon to disconnect from the VNC session
- **Fullscreen**: Click the fullscreen icon to toggle fullscreen mode
- **Settings**: Click the settings icon to adjust VNC settings

### VNC Settings

Click the settings icon to access the following settings:

- **Scaling Mode**: Adjust how the VNC display is scaled in your browser window
- **Clipboard**: Configure clipboard behavior
- **View-only**: Enable/disable view-only mode
- **Compression**: Adjust compression level for better performance
- **Quality**: Adjust image quality
- **Shared Mode**: Enable/disable shared mode
- **Show Dot Cursor**: Show a dot cursor when the remote cursor is not visible
- **Hostname**: The hostname of the VNC server
- **Path**: The WebSocket path
- **Reconnect**: Enable/disable automatic reconnection
- **Reconnect Delay**: Set the delay before reconnecting

## Security Features

BahtBrowse provides several security features:

- **Isolated Environment**: Each browsing session runs in an isolated container
- **No Persistence**: Browser data is not saved between sessions
- **Network Isolation**: The browser container has limited network access
- **Automatic Cleanup**: Containers are automatically cleaned up after use

## Performance Optimization

For optimal performance:

1. Adjust the compression level and quality settings in the VNC settings
2. Use a wired network connection if possible
3. Close unused browser tabs and applications
4. Consider increasing the VNC resolution if you have a high-resolution display

## Troubleshooting

### Connection Issues

If you're having trouble connecting to the BahtBrowse server:

1. Ensure the BahtBrowse server is running
2. Check that you're using the correct URL
3. Verify that your network allows connections to the BahtBrowse server
4. Try clearing your browser cache and cookies

### Display Issues

If you're experiencing display issues:

1. Try adjusting the scaling mode in the VNC settings
2. Refresh the page
3. Try a different web browser
4. Check if your browser is up to date

### Performance Issues

If you're experiencing performance issues:

1. Adjust the compression level and quality settings in the VNC settings
2. Close unused browser tabs and applications
3. Try a different web browser
4. Check your network connection

## Feedback and Support

If you encounter any issues or have suggestions for improving the BahtBrowse web interface, please [open an issue on GitHub](https://github.com/tenbahtsecurity/bahtbrowse/issues).

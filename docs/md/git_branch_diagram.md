```mermaid
%%{init: { 'logLevel': 'debug', 'theme': 'dark' } }%%
gitGraph
   commit id: "Initial commits"
   commit id: "Add Firefox plugin"
   commit id: "Add Docker queue"
   commit id: "Add UI Dashboard"
   commit id: "b1ffcfc" tag: "Add retro theme"
   commit id: "e7db353" tag: "UI improvements"

   branch retro-green-terminal-theme
   checkout retro-green-terminal-theme

   branch fix-container-security-and-locale
   checkout fix-container-security-and-locale
   commit id: "9ba42b9" tag: "Fix security & locale"
   commit id: "4d6b6c1" tag: "Enhance locale handling"
   commit id: "e9864f9" tag: "Secure downloads"
   commit id: "Various fixes"
   commit id: "Testing improvements"
   commit id: "db4ac91" tag: "Move Downloads button"
   commit id: "d5961e1" tag: "Add session-based auth"

   checkout retro-green-terminal-theme
   merge fix-container-security-and-locale

   branch fix-dependency-vulnerabilities
   checkout fix-dependency-vulnerabilities
   commit id: "aa38b8f" tag: "Fix vulnerabilities"

   checkout retro-green-terminal-theme
   merge fix-dependency-vulnerabilities

   branch chromium-testing
   checkout chromium-testing
   commit id: "82da1b9" tag: "Add Chromium tests"

   branch feature/container-pool-management-and-dark-mode
   checkout feature/container-pool-management-and-dark-mode
   commit id: "ff4df1c" tag: "Browser pool management"
   commit id: "6b1e9f7" tag: "Dark mode for Flower"
   commit id: "bff692c" tag: "Add tests"
   commit id: "ee66493" tag: "Update docs"

   checkout chromium-testing
   branch new-features/looking-good
   checkout new-features/looking-good
   commit id: "099e3b7" tag: "Squashed features"

   checkout chromium-testing
   branch feature/load-testing-suite
   checkout feature/load-testing-suite
   commit id: "8cbb36a" tag: "Add load testing suite"
   commit id: "d6d7a86" tag: "Add demo script"
```

## Branch Descriptions

### chromium-testing
Base functionality with container-compatible Chromium tests.

### feature/container-pool-management-and-dark-mode
Detailed implementation of browser-specific container pool management and dark mode features.

### new-features/looking-good
Clean, squashed version of the container pool management and dark mode features.

### feature/load-testing-suite
Load testing suite implementation with:
- Comprehensive framework for browser session testing
- Support for different load scenarios (low, medium, high)
- Rich CLI visualization for real-time monitoring
- Detailed metrics collection and reporting

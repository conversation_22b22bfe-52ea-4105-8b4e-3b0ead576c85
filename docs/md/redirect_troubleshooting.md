# BahtBrowse Redirect Loop Troubleshooting

## Problem Description

BahtBrowse had a "nasty loop redirect issue" where the system was stuck in a redirection loop instead of properly displaying the VNC interface. The flow should be:

1. User requests a URL via the API (`/browse/?url=example.com`)
2. <PERSON> launches a browser and redirects to `/novnc_proxy.html?session=<session_id>`
3. The proxy page loads and embeds the VNC interface correctly

However, the system was experiencing redirect loops, likely between steps 2 and 3.

## Initial Investigation

We examined the following components:

1. **nginx configuration**: The main configuration was found at `/etc/nginx/sites-available/default` with a symlink in `/etc/nginx/sites-enabled/`
2. **novnc_proxy.html**: Found in the root directory, serves as an intermediate page that should load the VNC interface
3. **app.py**: Handles the initial request and redirects to the proxy page

We found that the API was correctly redirecting to `http://localhost:8083/novnc_proxy.html?session=<session_id>`, but there were issues with how the proxy page was being served and how it was loading the VNC interface.

## Attempted Solutions

### 1. Created an improved novnc_proxy.html

**What we did**: 
- Created a new version of `novnc_proxy.html` with better error handling and UI
- Changed how it loads the VNC interface to prevent redirect loops
- Added logging and error display capabilities

**File location**: `/home/<USER>/dev/10Baht/bahtbrowse/novnc_proxy.html`

**Status**: ✅ This part worked - we were able to create the file

### 2. Tried to update nginx configuration directly

**What we did**:
- Attempted to add a location block to the existing nginx configuration:
```nginx
location = /novnc_proxy.html {
    root /tmp/serve;
    try_files $uri =404;
    
    add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
    add_header Pragma "no-cache";
    add_header Expires "0";
}
```

**Status**: ❌ Failed - We got errors about duplicate upstream "vnc_proxy" definitions

### 3. Created a shell script to modify nginx configuration

**What we did**:
- Created `add_proxy_location.sh` to insert the location block using sed
- Tried to run it inside the container with root privileges

**Status**: ❌ Partially failed - The script ran but we still got the "duplicate upstream" error

### 4. Created a separate server block for port 8083

**What we did**:
- Created a new nginx configuration file `proxy-server.conf`
- Configured it to listen on port 8083 and serve the proxy HTML

**Status**: ❌ Failed - We still encountered the "duplicate upstream" error

### 5. Tried to copy the novnc_proxy.html to the container

**What we did**:
- Copied our improved proxy HTML to the Docker container's serve directory

**Command used**:
```bash
docker exec -u root docker_browser mkdir -p /tmp/serve && chmod 777 /tmp/serve
docker cp novnc_proxy.html docker_browser:/tmp/serve/
```

**Status**: ✅ This part worked - The file was copied successfully

## Root Issue Analysis

The core issue appears to be related to how nginx configurations are loaded in the container. When we tried to add a new configuration or modify the existing one, we encountered a "duplicate upstream" error:

```
2025/04/27 15:23:43 [emerg] 37476#37476: duplicate upstream "vnc_proxy" in /etc/nginx/sites-enabled/default:1
```

This suggests that:
1. There is an `upstream vnc_proxy` block defined in multiple places
2. Nginx is trying to load both definitions, which causes a conflict

## Recommendations

To fix the issue, we should:

1. Extract the existing nginx configuration to understand its structure
2. Make a complete replacement rather than trying to add to it
3. Consider a different approach like:
   - Modifying the application code (`app.py`) to handle the redirection differently
   - Using a different port or path for the proxy page
   - Mounting a volume with our custom configuration during container startup

## What Worked

1. Successfully created an improved `novnc_proxy.html` that should prevent redirect loops
2. Successfully copied this file to the container's serve directory
3. Identified the root cause of the configuration issues

## Next Steps

1. Obtain a full copy of the nginx configuration
2. Create a single, complete replacement configuration
3. Apply the new configuration by replacing the file entirely rather than modifying it
4. Restart the nginx server
5. Test whether the redirect loop is resolved 
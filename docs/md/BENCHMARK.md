# bahtBrowse Browser Benchmark Suite

This benchmark suite allows you to compare the performance of Firefox and Chromium browsers running on Ubuntu and Alpine Linux containers.

## Overview

The benchmark suite includes:

1. Four browser containers:
   - Firefox on Ubuntu
   - Firefox on Alpine
   - Chromium on Ubuntu
   - Chromium on Alpine

2. A load testing framework that simulates user sessions and collects performance metrics

3. Elasticsearch and Kibana for metrics visualization

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Python 3.9 or higher
- Playwright

### Building the Containers

Run the build script to create all four browser containers:

```bash
./build-benchmark-containers.sh
```

This will:
1. Build all four browser containers
2. Start the containers
3. Start Elasticsearch and Kibana for metrics collection

### Running Benchmarks

To run benchmarks across all browser-OS combinations:

```bash
python -m tests.load_testing.benchmark_runner --scenario low --all
```

Available options:
- `--scenario [low|medium|high]`: Load testing scenario to run
- `--browser [firefox|chromium]`: Test only specific browser type
- `--os [ubuntu|alpine]`: Test only specific OS type
- `--all`: Test all browser-OS combinations

### Accessing the Containers

Each browser container is accessible at:

- Firefox on Ubuntu: http://localhost:8001
- Firefox on Alpine: http://localhost:8002
- Chromium on Ubuntu: http://localhost:8003
- Chromium on Alpine: http://localhost:8004

Elasticsearch and Kibana are available at:
- Elasticsearch: http://localhost:9200
- Kibana: http://localhost:5601

## Benchmark Metrics

The benchmark suite collects the following metrics:

- Container request time
- Browser launch time
- Navigation time
- DOM complete time
- Memory usage
- CPU usage
- Container size
- Startup time
- Total session duration

## Rich CLI Interface

The benchmark runner features a rich CLI interface with real-time visualization:

- **Header**: Shows the benchmark scenario and elapsed time
- **Browser-OS Panels**: Four panels showing metrics for each browser-OS combination
  - Firefox on Ubuntu
  - Firefox on Alpine
  - Chromium on Ubuntu
  - Chromium on Alpine
- **Progress Bar**: Shows the overall progress of the benchmark
- **Winner Panel**: Shows the current best performing browser-OS combination with detailed metrics

The best performing combination is highlighted with a green border, and the winner panel shows a detailed breakdown of its performance metrics.

![Benchmark CLI Interface](docs/images/benchmark-cli.png)

*Screenshot: The Rich CLI interface showing real-time benchmark results*

## Benchmark Scenarios

### Low Load
- 20 concurrent users (5 per browser-OS combination)
- 30-second ramp-up time
- 5-minute test duration
- 5-15 seconds think time between actions

### Medium Load
- 40 concurrent users (10 per browser-OS combination)
- 60-second ramp-up time
- 10-minute test duration
- 3-10 seconds think time between actions

### High Load
- 100 concurrent users (25 per browser-OS combination)
- 120-second ramp-up time
- 20-minute test duration
- 2-8 seconds think time between actions

## Reports

After running a benchmark, a comprehensive report is generated in the `tests/load_testing/reports` directory. The report includes:

- CSV data
- JSON data
- HTML report with charts
- Performance comparison across browser-OS combinations

## Troubleshooting

If you encounter issues with the benchmark suite:

1. Check that all containers are running:
   ```bash
   docker compose -f docker-compose.benchmark.yml ps
   ```

2. Check container logs:
   ```bash
   docker compose -f docker-compose.benchmark.yml logs browser_firefox_ubuntu
   ```

3. Ensure Elasticsearch is running:
   ```bash
   curl http://localhost:9200
   ```

4. Make sure Python dependencies are installed:
   ```bash
   pip install -r tests/load_testing/requirements.txt
   ```

5. Install Playwright browsers:
   ```bash
   playwright install
   ```

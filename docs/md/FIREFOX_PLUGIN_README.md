# bahtBrowse Firefox Plugin

This directory contains the bahtBrowse Firefox plugin, which allows users to redirect URLs from Firefox to the bahtBrowse service for secure browsing.

## Features

- One-click redirection from Firefox to bahtBrowse
- Popup interface for quick access
- Configurable settings for host, port, and behaviour
- Connection status indicator
- Error handling and notifications

## Installation

### For Development (Temporary Installation)

1. Go to `about:debugging` in Firefox
2. Click "This Firefox"
3. Click "Load Temporary Add-on..."
4. Navigate to and select the `browser_plugins/firefox/build/bahtbrowse_bouncer.xpi` file

### For Regular Use (Permanent Installation)

1. Go to `about:addons` in Firefox
2. Click the gear icon (⚙️)
3. Select "Install Add-on From File..."
4. Navigate to and select the `browser_plugins/firefox/build/bahtbrowse_bouncer.xpi` file

## Building the Plugin

### Standard Build

Run the following command to build the plugin:

```bash
cd browser_plugins/firefox
./build.sh
```

This will create the XPI file in the `browser_plugins/firefox/build` directory.

### Development Build (Without Minification)

For development, you can build the plugin without minification:

```bash
cd browser_plugins/firefox
./rebuild_without_minify.sh
```

## Configuration

After installation, you can configure the plugin by:

1. Clicking on the bahtBrowse icon in the Firefox toolbar
2. Clicking the gear icon (⚙️) in the popup
3. Adjusting the settings as needed
4. Clicking "Save"

Available settings include:

- **Host**: The hostname of the bahtBrowse service (default: "localhost")
- **Port**: The port number of the bahtBrowse service (default: "8082")
- **Open in New Tab**: Whether to open redirected URLs in a new tab
- **Whitelist/Blacklist**: Domain management for automatic redirection

## Troubleshooting

If you encounter issues with the plugin:

1. Check that the bahtBrowse service is running
2. Verify your connection settings in the plugin options
3. Check for error messages in the plugin popup
4. For development issues, see browser console logs

## Directory Structure

- `browser_plugins/firefox/`: Main plugin directory
  - `icons/`: Plugin icons
  - `options/`: Settings page
  - `popup/`: Popup interface
  - `build/`: Compiled plugin files
  - `*.js`: Main plugin code

## Support

For support, please see the main bahtBrowse documentation.

# BahtBrowse Redirect Loop Fix

## Issue Summary

<PERSON><PERSON><PERSON><PERSON><PERSON> was experiencing a redirect loop between the API server and the VNC viewer. When a user requests a URL through the API (`/browse/?url=example.com`), the system correctly redirects to `/novnc_proxy.html?session=<session_id>`, but then additional redirects cause a loop instead of displaying the VNC interface.

## Root Cause

The redirection issue is caused by:

1. The `novnc_proxy.html` page trying to load the VNC viewer using a direct URL that may trigger further redirects
2. Nginx configuration not properly handling the proxy page
3. Multiple redirects occurring without reaching a final stable page

## Solution

### 1. Create an Improved Proxy Page

Replace the `novnc_proxy.html` with an improved version that:
- Uses a direct, relative path to the VNC endpoint
- Includes better error handling
- Uses an iframe to embed the VNC viewer directly

```html
<!-- Key portion of the solution -->
<script>
  function connectToVNC() {
    const sessionId = getURLParameter('session');
    if (!sessionId) return;
    
    const iframe = document.getElementById('vnc-iframe');
    // Use the existing VNC run endpoint in nginx
    iframe.src = `/vnc1/run?session=${sessionId}&_ts=${Date.now()}`;
  }
</script>
```

### 2. Configure Nginx Properly

Update the nginx configuration to properly serve the proxy page and handle the VNC connection:

1. Create a directory to serve the proxy page:
   ```bash
   docker exec -u root docker_browser mkdir -p /tmp/serve
   ```

2. Copy the improved proxy page:
   ```bash
   docker cp novnc_proxy.html docker_browser:/tmp/serve/
   ```

3. Replace the default nginx site configuration (create a complete new file rather than modifying):
   ```nginx
   # Add this location block to the server section
   location = /novnc_proxy.html {
       root /tmp/serve;
       try_files $uri =404;
       add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0";
       add_header Pragma "no-cache";
       add_header Expires "0";
   }
   ```

### 3. Testing the Fix

1. Test the redirect flow using the test script:
   ```bash
   ./test_connection_flow.sh "http://example.com" "docker_browser"
   ```

2. Test for redirect loops with:
   ```bash
   ./test_redirect_loop.sh
   ```

## Implementation Notes

Due to issues with modifying the existing nginx configuration (duplicate upstream errors), the recommended approach is to:

1. Get a full backup of the existing configuration
2. Create a complete replacement with the required changes
3. Replace the entire file at once rather than trying to modify it
4. For a more permanent solution, consider creating a custom Docker image with these fixes pre-applied

## Result

When properly implemented, the browser's URL path flow should be:
1. User requests: `/browse/?url=example.com`
2. API redirects to: `/novnc_proxy.html?session=<session_id>`
3. Proxy page loads VNC viewer via iframe with: `/vnc1/run?session=<session_id>`
4. VNC session displays the requested page without further redirects 
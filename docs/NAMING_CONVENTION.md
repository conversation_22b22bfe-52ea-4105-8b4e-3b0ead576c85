# BahtBrowse Naming Convention

This document outlines the standardized naming conventions for BahtBrowse containers, services, and other components.

## Container Naming Convention

### Service Containers

All service containers should be prefixed with `bahtbrowse-services-`:

```
bahtbrowse-services-<service-name>
```

Examples:
- `bahtbrowse-services-api` - API service
- `bahtbrowse-services-worker-container` - Container management worker
- `bahtbrowse-services-worker-pool` - Pool management worker
- `bahtbrowse-services-worker-monitoring` - Monitoring worker
- `bahtbrowse-services-beat` - Celery beat scheduler
- `bahtbrowse-services-flower` - Celery flower monitoring
- `bahtbrowse-services-redis` - Redis service
- `bahtbrowse-services-postgres` - PostgreSQL database

### Browser Containers

All browser containers should be prefixed with `bahtbrowse-browsers-`:

```
bahtbrowse-browsers-<browser-type>-<os-type>
```

Examples:
- `bahtbrowse-browsers-firefox-ubuntu` - Firefox on Ubuntu
- `bahtbrowse-browsers-firefox-alpine` - Firefox on Alpine
- `bahtbrowse-browsers-chromium-ubuntu` - Chromium on Ubuntu
- `bahtbrowse-browsers-chromium-alpine` - Chromium on Alpine

## Docker Image Naming Convention

Docker images should follow a similar naming convention:

### Service Images

```
bahtbrowse/services-<service-name>:<tag>
```

Examples:
- `bahtbrowse/services-api:latest`
- `bahtbrowse/services-worker:latest`

### Browser Images

```
bahtbrowse/browsers-<browser-type>-<os-type>:<tag>
```

Examples:
- `bahtbrowse/browsers-firefox-ubuntu:latest`
- `bahtbrowse/browsers-chromium-alpine:latest`

## Docker Compose Service Names

In Docker Compose files, service names should follow the same convention:

### Service Components

```yaml
services:
  bahtbrowse-services-api:
    # ...
  bahtbrowse-services-worker-container:
    # ...
  bahtbrowse-services-redis:
    # ...
```

### Browser Components

```yaml
services:
  bahtbrowse-browsers-firefox-ubuntu:
    # ...
  bahtbrowse-browsers-chromium-alpine:
    # ...
```

## Benefits of Standardized Naming

1. **Clarity**: Clear distinction between service containers and browser containers
2. **Consistency**: Consistent naming across all components
3. **Automation**: Easier to automate tasks based on container type
4. **Monitoring**: Improved monitoring and filtering in tools and dashboards
5. **Documentation**: Clearer documentation and communication

## Implementation

When creating new containers or updating existing ones, ensure they follow this naming convention. The BahtBrowse CLI tool and other monitoring utilities are designed to recognize and categorize containers based on this convention.

For existing containers that don't follow this convention, they should be renamed during the next maintenance window or update cycle.

## Example Docker Compose File

An example Docker Compose file that follows this naming convention is provided at `docker-compose.example.yml`. This file can be used as a reference when creating new Docker Compose files or updating existing ones.

```bash
# To use the example Docker Compose file
docker compose -f docker-compose.example.yml up -d
```

# Port Configuration Update

This document provides information about the updated port configuration in bahtBrowse.

## Default Ports

The default ports used by bahtBrowse have been updated to avoid conflicts:

- **Central API**: 5002 (was 5000)
- **Docker Queue API**: 5001 (was 5000)
- **Redis**: 6379 (unchanged)
- **Celery Flower**: 5555 (unchanged)
- **Nginx**: 80 (unchanged)
- **Admin Dashboard**: 9001 (unchanged)
- **Elasticsearch**: 9200 (unchanged)
- **Kibana**: 5601 (unchanged)
- **Logstash**: 9600 (unchanged)

## Port Conflict Detection and Resolution

bahtBrowse includes a port conflict detection and resolution system that automatically detects when default ports are already in use and finds alternative ports. This makes the system more resilient when running in environments where the default ports might already be in use by other applications.

The port conflict detection and resolution system provides the following features:

- **Automatic detection of port conflicts**: The system checks if the default ports are already in use.
- **Dynamic port allocation**: If a default port is in use, the system finds an available port in the appropriate range.
- **Visual indicators**: Services using non-default ports are highlighted in the status table.
- **Configurable port ranges**: Port ranges can be configured for different service categories.
- **Seamless communication**: Services can still communicate with each other when using alternative ports.

## Port Ranges

The port conflict resolution system defines the following port ranges for different service categories:

- **Web Services**: 8000-8100
- **API Services**: 5000-5100
- **Monitoring Services**: 5500-5700
- **Infrastructure Services**: 6300-6400
- **Core Services**: 8000-8100

## Using the Port Conflict Resolution System

The port conflict resolution system is integrated into the bahtBrowse CLI tool and startup scripts. When you start services using these tools, they automatically check if the default ports are available and find alternative ports if needed.

### Using the Start Services Script

To start all services with automatic port conflict resolution:

```bash
./scripts/start_services.sh
```

This script will:
1. Check if the default ports are available
2. Start the services on the default ports if available
3. Find alternative ports if the default ports are in use
4. Display the actual ports being used

### Using the Port Configuration Test Script

To verify that the services are running on the correct ports:

```bash
python scripts/test/test_port_configuration.py
```

This script will:
1. Check if the services are running
2. Verify that they are accessible on the expected ports
3. Display a summary of the test results

### Using the Stop Services Script

To stop all services:

```bash
./scripts/stop_services.sh
```

This script will stop all running services, regardless of which ports they are using.

## Environment Variables for Port Configuration

The following environment variables can be used to configure the ports:

- `API_PORT`: The port for the Central API (default: 5002)
- `FLASK_RUN_PORT`: The port for the Docker Queue API (default: 5001)
- `REDIS_PORT`: The port for Redis (default: 6379)
- `CELERY_FLOWER_PORT`: The port for Celery Flower (default: 5555)

Example:

```bash
API_PORT=5010 FLASK_RUN_PORT=5011 ./scripts/start_services.sh
```

This will start the Central API on port 5010 and the Docker Queue API on port 5011.

## Testing Port Configuration

bahtBrowse includes tests for port configuration and port conflict resolution. These tests verify that the system correctly handles port conflicts and can find alternative ports when needed.

To run the port configuration tests:

```bash
python -m pytest tests/docker_queue/test_port_configuration.py
```

To run the port conflict resolution tests:

```bash
python tests/test_port_conflict_resolution.py
```

## Troubleshooting Port Issues

If you encounter port conflicts or other port-related issues, try the following:

1. Check if the ports are already in use by other applications:
   ```bash
   sudo lsof -i :<port>
   ```

2. Use the port configuration test script to verify that the services are running on the expected ports:
   ```bash
   python scripts/test/test_port_configuration.py
   ```

3. If a service fails to start due to a port conflict, try specifying a different port using the appropriate environment variable.

4. If all else fails, stop all services and start them again:
   ```bash
   ./scripts/stop_services.sh
   ./scripts/start_services.sh
   ```

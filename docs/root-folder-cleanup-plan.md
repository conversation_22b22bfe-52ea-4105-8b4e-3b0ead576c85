# Root Folder Cleanup Plan

This document outlines a comprehensive plan to clean up the root folder of the BahtBrowse project.

## Current State

The root folder currently contains approximately 89 files, many of which should be organized into appropriate directories. This creates several issues:

1. Difficulty finding specific files
2. Confusion about the purpose of each file
3. Challenges in maintaining the codebase
4. Poor first impression for new developers

## Cleanup Plan

### 1. Python Files

Move all Python files to appropriate directories:

| File | Destination |
|------|-------------|
| api_server.py | src/api/ |
| conftest.py | tests/ |
| elk_demo.py | src/utils/ |
| fixed_app.py | src/api/ |
| fix_test_scripts.py | scripts/test/ |
| fix_test_symlink.py | scripts/test/ |
| modified_app.py | src/api/ |
| port_test.py | src/utils/ |
| query_elasticsearch.py | src/utils/ |
| setup.py | / (keep in root) |
| test_container_management.py | tests/unit/ |
| test_docker.py | tests/unit/ |
| test_real_docker.py | tests/unit/ |
| test_redirect.py | tests/unit/ |
| test_vnc.py | tests/unit/ |

### 2. Shell Scripts

Move all shell scripts to appropriate directories:

| File | Destination |
|------|-------------|
| add_proxy_location.sh | scripts/utils/ |
| apply_ui_patch.sh | scripts/utils/ |
| build-benchmark-containers.sh | scripts/build/ |
| build-tor-browser.sh | scripts/build/ |
| build-ungoogled-chromium-prebuilt.sh | scripts/build/ |
| build-ungoogled-chromium.sh | scripts/build/ |
| test_all_variants.sh | scripts/test/ |
| test_connection_flow.sh | scripts/test/ |
| test_connection.sh | scripts/test/ |
| test_redirect_loop.sh | scripts/test/ |
| test_tor_browser.sh | scripts/test/ |

### 3. HTML Files

Move all HTML files to appropriate directories:

| File | Destination |
|------|-------------|
| fixed_vnc.html | web/templates/ |
| improved_vnc.html | web/templates/ |
| novnc_proxy.html | web/templates/ |
| redirect_test_advanced.html | web/templates/ |
| redirect_test.html | web/templates/ |
| test_connection.html | web/templates/ |
| test_redirect.html | web/templates/ |
| vnc_redirect.html | web/templates/ |

### 4. JavaScript Files

Move all JavaScript files to appropriate directories:

| File | Destination |
|------|-------------|
| example.spec.js | tests/js/ |
| playwright.config.js | tests/ |
| simple-test.js | tests/js/ |
| test_redirect_cli.js | tests/js/ |
| test_redirect.js | tests/js/ |
| test.spec.js | tests/js/ |
| ui_js_patch.js | web/static/ |

### 5. Configuration Files

Move all configuration files to appropriate directories:

| File | Destination |
|------|-------------|
| nginx.conf | nginx/conf.d/ |
| nginx_fix.conf | nginx/conf.d/ |
| nginx-site-default.conf | nginx/conf.d/ |
| proxy-server.conf | nginx/conf.d/ |

### 6. Docker Compose Files

Move all Docker Compose files to appropriate directories:

| File | Destination |
|------|-------------|
| docker-compose.bahtbrowse-elk.yml | docker/compose/ |
| docker-compose.benchmark.yml | docker/compose/ |
| docker-compose.chromium-alpine.yml | docker/compose/ |
| docker-compose.chromium-ubuntu.yml | docker/compose/ |
| docker-compose.elk-simple.yml | docker/compose/ |
| docker-compose.elk.yml | docker/compose/ |
| docker-compose.firefox-alpine.yml | docker/compose/ |
| docker-compose.firefox-ubuntu.yml | docker/compose/ |
| docker-compose.playwright.yml | docker/compose/ |
| docker-compose.tor-browser-alpine.yml | docker/compose/ |
| docker-compose.tor-browser.yml | docker/compose/ |
| docker-compose.ungoogled-chromium-prebuilt.yml | docker/compose/ |
| docker-compose.ungoogled-chromium.yml | docker/compose/ |
| docker-compose.yml | docker/compose/ |

### 7. Other Files

Organize other files as appropriate:

| File | Destination |
|------|-------------|
| .pre-commit-config.yaml | / (keep in root) |
| .gitignore | / (keep in root) |
| CHANGELOG.md | / (keep in root) |
| README.md | / (keep in root) |
| shell.nix | / (keep in root) |
| pyproject.toml | / (keep in root) |
| ui.js.original | web/static/ |
| ui.js.patched | web/static/ |

## Implementation Steps

1. Create any missing directories
2. Move files to their appropriate directories
3. Update references to moved files
4. Test functionality to ensure everything works
5. Update documentation to reflect the new structure

## Files to Keep in Root

The following files should remain in the root directory:

1. README.md - Main project documentation
2. CHANGELOG.md - Project changelog
3. LICENSE - Project license
4. setup.py - Python package setup
5. pyproject.toml - Python project configuration
6. shell.nix - Nix development environment
7. .gitignore - Git ignore file
8. .pre-commit-config.yaml - Pre-commit hooks configuration

## Expected Result

After cleanup, the root directory should contain only essential files, with all other files organized into appropriate directories. This will make the project more maintainable, easier to navigate, and more professional in appearance.

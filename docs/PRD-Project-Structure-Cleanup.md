# Product Requirements Document: Project Structure Cleanup

## Overview

This document outlines the requirements and implementation plan for cleaning up the bahtBrowse project structure. The goal is to create a more organized, maintainable, and standardized codebase that follows best practices for Python projects and provides clear documentation for developers.

## Goals

1. Standardize folder structure according to Python best practices
2. Consolidate duplicate code and eliminate redundant files
3. Ensure all documentation follows UK English standards
4. Update README and CHANGELOG to reflect the new structure
5. Create a comprehensive test suite checklist for verifying changes
6. Improve developer experience through better organization

## Current Structure Issues

The current project structure has several issues that need to be addressed:

1. **Inconsistent folder naming and organization**: Multiple similar folders with overlapping functionality (e.g., `firefox_plugin` and `browser_plugins/firefox`)
2. **Root directory clutter**: Too many files in the root directory, including test files that should be in the `tests` directory
3. **Inconsistent documentation**: Mix of documentation styles and languages
4. **Duplicate code**: Similar functionality implemented in multiple places
5. **Unclear separation of concerns**: Modules with overlapping responsibilities
6. **Inconsistent testing approach**: Tests scattered across different directories

## Proposed Structure

```
bahtbrowse/
├── browser_plugins/          # All browser extension code
│   ├── firefox/              # Firefox plugin files
│   └── chrome/               # Future Chrome plugin files (placeholder)
├── core/                     # Core application code
│   ├── api/                  # API server code
│   ├── browser/              # Browser management code
│   ├── container/            # Container management code
│   └── utils/                # Utility functions
├── docker_queue/             # Queue management system
│   ├── api/                  # API endpoints
│   ├── config/               # Configuration
│   ├── dashboard/            # Dashboard UI
│   ├── performance/          # Performance tools
│   └── tasks/                # Celery tasks
├── docs/                     # Documentation
│   ├── architecture/         # Architecture diagrams and docs
│   ├── development/          # Development guides
│   ├── images/               # Images for documentation
│   └── user/                 # User guides
├── elk/                      # ELK stack configuration
├── scripts/                  # Utility scripts
├── tests/                    # All test files
│   ├── core/                 # Tests for core functionality
│   ├── docker_queue/         # Tests for docker queue
│   ├── integration/          # Integration tests
│   ├── load_testing/         # Load testing scripts
│   └── unit/                 # Unit tests
├── .github/                  # GitHub workflows and templates
├── .gitignore                # Git ignore file
├── CHANGELOG.md              # Changelog
├── docker-compose.yml        # Main Docker Compose file
├── Dockerfile                # Main Dockerfile
├── LICENSE                   # License file
├── pyproject.toml            # Python project configuration
├── README.md                 # Main README
└── requirements.txt          # Dependencies
```

## Implementation Plan

### 1. Folder Restructuring

1. **Create core module**:
   - Move core functionality from root directory into appropriate submodules
   - Organize by functionality (api, browser, container, utils)

2. **Consolidate browser plugins**:
   - Ensure all browser plugin code is in the `browser_plugins` directory
   - Remove duplicate code in `firefox_plugin` directory

3. **Organize tests**:
   - Move all test files from root into the `tests` directory
   - Organize by test type (unit, integration, load)

4. **Clean up root directory**:
   - Move scripts to the `scripts` directory
   - Consolidate Docker files
   - Ensure only essential files remain in root

### 2. Documentation Updates

1. **Standardize README**:
   - Update to reflect new structure
   - Ensure UK English spelling throughout
   - Improve installation and usage instructions

2. **Update CHANGELOG**:
   - Add entry for structure cleanup
   - Follow Keep a Changelog format
   - Ensure UK English spelling

3. **Create development guides**:
   - Document folder structure
   - Provide contribution guidelines
   - Include testing procedures

### 3. Testing Strategy

1. **Create test checklist**:
   - Comprehensive list of tests to run after structure changes
   - Include unit, integration, and load tests
   - Document expected outcomes

2. **Update test scripts**:
   - Ensure all test scripts work with new structure
   - Update paths in test files
   - Create new test runner if needed

## Detailed Changes

### File Moves and Consolidations

| Current Location | New Location | Notes |
|------------------|--------------|-------|
| `firefox_plugin/*` | `browser_plugins/firefox/*` | Consolidate with existing files |
| `*.py` (root) | `core/` | Move Python modules to core |
| `test_*.py` (root) | `tests/` | Move test files to tests directory |
| `*.sh` (root) | `scripts/` | Move shell scripts to scripts directory |
| Documentation files | `docs/` | Organize documentation |

### Documentation Updates

1. **README.md**:
   - Update project structure section
   - Update installation instructions
   - Update development workflow
   - Standardize to UK English

2. **CHANGELOG.md**:
   - Add entry for structure cleanup
   - Document all moved files
   - Standardize to UK English

## Test Checklist

To ensure the restructuring doesn't break functionality, the following tests should be run:

1. **Build Tests**:
   - [ ] Build Firefox plugin
   - [ ] Build Docker container
   - [ ] Build ELK stack

2. **Functional Tests**:
   - [ ] Firefox plugin functionality
   - [ ] Container creation and management
   - [ ] API endpoints
   - [ ] Queue management

3. **Integration Tests**:
   - [ ] End-to-end workflow
   - [ ] Browser session creation
   - [ ] Container lifecycle

4. **Performance Tests**:
   - [ ] Load testing
   - [ ] Resource usage

## Success Criteria

The project structure cleanup will be considered successful when:

1. All files are organized according to the proposed structure
2. All tests pass with the new structure
3. Documentation is updated and standardized to UK English
4. No functionality is lost or broken
5. Developer experience is improved through better organization

## Timeline

1. **Phase 1: Planning and Documentation** (Current)
   - Create PRD
   - Document current structure
   - Create detailed migration plan

2. **Phase 2: Core Restructuring**
   - Create new directory structure
   - Move files to appropriate locations
   - Update import statements

3. **Phase 3: Testing and Validation**
   - Run test checklist
   - Fix any issues
   - Validate functionality

4. **Phase 4: Documentation Updates**
   - Update README
   - Update CHANGELOG
   - Create additional documentation

5. **Phase 5: Final Review and Merge**
   - Code review
   - Final testing
   - Merge to main branch

## Conclusion

This project structure cleanup will significantly improve the maintainability and organization of the bahtBrowse codebase. By following Python best practices and providing clear documentation, we will make it easier for developers to understand and contribute to the project.

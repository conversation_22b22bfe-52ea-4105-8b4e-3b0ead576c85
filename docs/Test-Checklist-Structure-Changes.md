# Test Checklist for Project Structure Changes

This checklist should be used to verify that all functionality remains intact after the project structure cleanup. Run these tests in order and document any issues encountered.

## Prerequisites

- [ ] Ensure all dependencies are installed: `pip install -r requirements.txt -r requirements-dev.txt`
- [ ] Ensure Docker is running
- [ ] Ensure Redis is running (if used for tests)

## Build Tests

### Firefox Plugin Build
- [ ] Navigate to browser_plugins/firefox
- [ ] Run `./build.sh`
- [ ] Verify that the plugin builds successfully
- [ ] Check that the XPI file is created in the build directory

### Docker Container Build
- [ ] Run `./scripts/build.sh` from the project root
- [ ] Verify that the container builds successfully
- [ ] Check that the image is available with `docker images`

### ELK Stack Build
- [ ] Run `docker-compose -f docker-compose.elk.yml build`
- [ ] Verify that all ELK components build successfully

## Unit Tests

### Core Functionality Tests
- [ ] Run `pytest tests/core/`
- [ ] Verify all tests pass
- [ ] Check code coverage with `pytest --cov=core tests/core/`

### Docker Queue Tests
- [ ] Run `pytest tests/docker_queue/`
- [ ] Verify all tests pass
- [ ] Check code coverage with `pytest --cov=docker_queue tests/docker_queue/`

### Browser Plugin Tests
- [ ] Run browser plugin tests (if available)
- [ ] Verify all tests pass

## Integration Tests

### API Integration Tests
- [ ] Run `pytest tests/integration/test_api_integration.py`
- [ ] Verify all API endpoints respond correctly
- [ ] Check that API can communicate with other components

### Container Lifecycle Tests
- [ ] Run `pytest tests/integration/test_container_lifecycle.py`
- [ ] Verify containers can be created, accessed, and destroyed
- [ ] Check that browser sessions work correctly

### End-to-End Workflow Tests
- [ ] Run `pytest tests/integration/test_end_to_end.py`
- [ ] Verify the complete workflow from plugin to browser session
- [ ] Check that all components interact correctly

## Performance Tests

### Load Testing
- [ ] Run `python tests/load_testing/load_test_runner.py`
- [ ] Verify the system can handle multiple concurrent sessions
- [ ] Check resource usage during load testing

### Resource Usage Tests
- [ ] Monitor CPU and memory usage during tests
- [ ] Verify that resource usage is within acceptable limits
- [ ] Check for memory leaks or resource exhaustion

## Security Tests

### Container Isolation Tests
- [ ] Run `pytest tests/security/test_container_isolation.py`
- [ ] Verify that containers are properly isolated
- [ ] Check that browser sessions cannot access host resources

### Plugin Security Tests
- [ ] Run security tests for the browser plugin
- [ ] Verify that the plugin follows security best practices
- [ ] Check for potential vulnerabilities

## Documentation Tests

### README Verification
- [ ] Verify that the README accurately reflects the new structure
- [ ] Check that all commands and paths in the README work
- [ ] Ensure all documentation uses UK English spelling

### API Documentation
- [ ] Verify that API documentation is up-to-date
- [ ] Check that all endpoints are documented
- [ ] Ensure examples use the correct paths

## Troubleshooting

If any tests fail, follow these steps:

1. Check that all files were moved to the correct locations
2. Verify that import statements were updated correctly
3. Check for path references in configuration files
4. Look for hardcoded paths that may need to be updated
5. Check for environment variables that may need to be set

## Sign-off

Once all tests have passed, the structure changes can be considered complete. Document any issues encountered and their resolutions for future reference.

- [ ] All tests passed
- [ ] Issues documented
- [ ] Changes committed to version control

**Tested by:** _________________
**Date:** _________________

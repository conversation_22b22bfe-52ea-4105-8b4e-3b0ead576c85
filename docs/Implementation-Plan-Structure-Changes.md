# Implementation Plan for Project Structure Changes

This document outlines the step-by-step process for implementing the project structure changes described in the PRD. Each step includes specific commands and actions to take, along with verification steps to ensure nothing breaks during the transition.

## Phase 1: Setup and Preparation

### 1.1 Create New Directory Structure

```bash
# Create core directory and subdirectories
mkdir -p core/api
mkdir -p core/browser
mkdir -p core/container
mkdir -p core/utils

# Create test subdirectories
mkdir -p tests/core
mkdir -p tests/integration
mkdir -p tests/unit

# Create documentation subdirectories
mkdir -p docs/architecture
mkdir -p docs/development
mkdir -p docs/user
```

### 1.2 Create Backup

Before making any changes, create a backup of the current state:

```bash
# Create a backup branch
git checkout -b backup/pre-structure-cleanup
git add .
git commit -m "Backup before structure cleanup"
git checkout feature/cw17-cleanup
```

## Phase 2: Move Files to New Structure

### 2.1 Move Core Python Files

```bash
# Move API-related files
git mv api_server.py core/api/server.py
# Create __init__.py files
touch core/__init__.py
touch core/api/__init__.py
touch core/browser/__init__.py
touch core/container/__init__.py
touch core/utils/__init__.py
```

### 2.2 Consolidate Browser Plugins

```bash
# If there are duplicate files, merge them
# Otherwise, just ensure all plugin code is in browser_plugins
git mv firefox_plugin/* browser_plugins/firefox/
```

### 2.3 Move Test Files

```bash
# Move test files from root to tests directory
git mv test_*.py tests/
# Organize tests by type
git mv tests/test_app.py tests/core/
git mv tests/test_integration.py tests/integration/
```

### 2.4 Move Scripts

```bash
# Move shell scripts to scripts directory
git mv *.sh scripts/
# Exclude build.sh and other essential scripts that should stay in root
git mv scripts/build.sh ./
git mv scripts/rebuild.sh ./
```

## Phase 3: Update Import Statements and References

### 3.1 Update Import Statements

For each moved Python file, update import statements to reflect the new structure:

```python
# Old import
from files.app import SomeClass

# New import
from core.browser.app import SomeClass
```

### 3.2 Update Script References

Update any scripts that reference moved files:

```bash
# Old reference
python api_server.py

# New reference
python -m core.api.server
```

### 3.3 Update Documentation References

Update paths in documentation to reflect the new structure.

## Phase 4: Update Configuration Files

### 4.1 Update pyproject.toml

Update the project configuration to reflect the new structure:

```toml
[tool.pytest]
testpaths = ["tests"]

[tool.coverage.run]
source = ["core", "docker_queue", "browser_plugins"]
```

### 4.2 Update Docker Configurations

Update Docker configurations to use the new file paths.

## Phase 5: Testing and Validation

### 5.1 Run Unit Tests

```bash
pytest tests/unit
```

### 5.2 Run Integration Tests

```bash
pytest tests/integration
```

### 5.3 Build and Test Firefox Plugin

```bash
cd browser_plugins/firefox
./build.sh
# Test the plugin
```

### 5.4 Build and Test Docker Container

```bash
./build.sh
# Test the container
```

## Phase 6: Documentation Updates

### 6.1 Update README.md

Update the README to reflect the new structure and ensure all instructions use the new paths.

### 6.2 Update CHANGELOG.md

Add an entry for the structure cleanup in the CHANGELOG.

### 6.3 Create Additional Documentation

Create any additional documentation needed to explain the new structure.

## Phase 7: Final Review and Commit

### 7.1 Review Changes

```bash
git status
git diff --stat
```

### 7.2 Commit Changes

```bash
git add .
git commit -m "Implement project structure cleanup"
```

### 7.3 Create Pull Request

Create a pull request for the changes to be reviewed before merging to main.

## Rollback Plan

If issues are encountered that cannot be resolved quickly, use the backup branch to restore the previous state:

```bash
git checkout backup/pre-structure-cleanup
git checkout -b feature/cw17-cleanup-retry
```

## Conclusion

This implementation plan provides a structured approach to reorganizing the project. By following these steps and carefully testing at each stage, we can ensure a smooth transition to the new structure without breaking existing functionality.

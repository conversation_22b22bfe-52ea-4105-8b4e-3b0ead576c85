# BahtBrowse Documentation

Welcome to the BahtBrowse documentation. This document serves as a central index for all BahtBrowse documentation.

## Project Overview

BahtBrowse is a containerized browser environment that allows users to browse the web securely. It provides isolation from the host system, protecting against malicious websites and potential security threats.

## Project Structure

The BahtBrowse project is organized into the following directories:

- `browser_plugins/`: Browser extension code
  - `firefox/`: Firefox plugin
- `docker/`: Docker-related files
  - `compose/`: Docker Compose files
  - `config/`: Dockerfile configurations
- `docs/`: Documentation
  - `index.md`: Central documentation index
  - `PRD-*.md`: Product requirement documents
- `nginx/`: Nginx configuration
  - `conf.d/`: Nginx configuration files
  - `templates/`: Nginx configuration templates
- `scripts/`: Scripts for building and testing
  - `build/`: Build scripts
  - `test/`: Test scripts
  - `utils/`: Utility scripts
- `src/`: Source code
  - `api/`: API code
  - `core/`: Core functionality
  - `utils/`: Utility functions
- `tests/`: Test suite
  - `unit/`: Unit tests
  - `js/`: JavaScript tests
- `web/`: Web interface
  - `static/`: Static assets
  - `templates/`: HTML templates

## Component Documentation

### Browser Plugins

- [Browser Plugins Overview](../browser_plugins/README.md): Overview of browser plugins for BahtBrowse
- [Firefox Plugin Installation](../browser_plugins/firefox/INSTALLATION.md): Installation instructions for the Firefox plugin
- [Firefox Plugin README](../browser_plugins/firefox/README.md): Detailed information about the Firefox plugin

### Docker Configuration

- [Docker Configuration Overview](../docker/README.md): Overview of Docker configuration for BahtBrowse

### Nginx Configuration

- [Nginx Configuration Overview](../nginx/README.md): Overview of nginx configuration for BahtBrowse

### Scripts

- [Scripts Overview](../scripts/README.md): Overview of scripts for building, testing, and managing BahtBrowse

### Web Interface

- [Web Interface Overview](../web/README.md): Overview of the BahtBrowse web interface

### Source Code

- [Source Code Overview](../src/README.md): Overview of the BahtBrowse source code

## Product Requirements Documents (PRDs)

- [Main Folder Cleanup PRD](PRD-main-folder-cleanup.md): Requirements for cleaning up the main folder structure
- [Main Branch Integration PRD](PRD-main-branch-integration.md): Requirements for integrating the main branch
- [Docker Queue Management PRD](PRD-Docker-Queue-Management.md): Requirements for Docker queue management
- [Container Pool Management and Dark Mode PRD](PRD-container-pool-management-and-dark-mode.md): Requirements for container pool management and dark mode
- [ELK Stack Monitoring PRD](PRD-elk-stack-monitoring.md): Requirements for ELK stack monitoring
- [Celery Container Logging ELK PRD](PRD-celery-container-logging-elk.md): Requirements for Celery container logging with ELK
- [Sphinx Documentation PRD](sphinx-prd/PRD-Sphinx-Documentation.md): Requirements for Sphinx documentation
- [Next Steps PRD](PRD-Next-Steps.md): Requirements for next steps

## Implementation Plans

- [Main Branch Integration Implementation Plan](Implementation-Plan-Main-Branch-Integration.md): Implementation plan for main branch integration
- [Celery Queue Implementation Plan](Implementation-Plan-Celery-Queue.md): Implementation plan for Celery queue

## Checklists

- [Main Branch Integration Checklist](Main-Branch-Integration-Checklist.md): Checklist for main branch integration

## Tracking Documents

- [Main Folder Cleanup Tracking](main-folder-cleanup-tracking.md): Tracking document for main folder cleanup

## Other Documentation

- [Data Storage](data_storage.md): Information about data storage in BahtBrowse
- [Load Testing Suite Summary](load-testing-suite-summary.md): Summary of the load testing suite
- [Firefox Plugin PRD](firefox_plugin_prd.md): Product requirements document for the Firefox plugin

## External Resources

- [GitHub Repository](https://github.com/tenbahtsecurity/bahtbrowse): BahtBrowse GitHub repository
- [Issue Tracker](https://github.com/tenbahtsecurity/bahtbrowse/issues): BahtBrowse issue tracker

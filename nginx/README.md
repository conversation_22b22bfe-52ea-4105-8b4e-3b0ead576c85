# BahtBrowse Nginx Configuration

This directory contains the nginx configuration files for BahtBrowse.

## Directory Structure

- `conf.d/`: Contains the main nginx configuration files
- `templates/`: Contains nginx configuration templates

## Main Configuration Files

- `bahtbrowse.conf`: The main nginx configuration file for BahtBrowse
- `nginx.conf`: The original nginx configuration file
- `nginx_fix.conf`: A fixed version of the nginx configuration
- `nginx-site-default.conf`: The default site configuration
- `proxy-server.conf`: Configuration for the proxy server

## Redirection Loop Fix

The `bahtbrowse.conf` file includes a fix for the redirection loop issue that was previously encountered. The fix involves:

1. Using a separate server block for port 8083 to serve the proxy HTML
2. Ensuring proper proxy_pass directives for the VNC interface
3. Adding appropriate cache control headers to prevent stale redirects

## Usage

To use these configuration files:

1. Copy the appropriate configuration file to `/etc/nginx/sites-available/`
2. Create a symlink in `/etc/nginx/sites-enabled/`
3. Restart nginx

Example:

```bash
sudo cp nginx/conf.d/bahtbrowse.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/bahtbrowse.conf /etc/nginx/sites-enabled/
sudo systemctl restart nginx
```

## Troubleshooting

If you encounter issues with the nginx configuration:

1. Check the nginx error logs: `sudo tail -f /var/log/nginx/error.log`
2. Verify the configuration syntax: `sudo nginx -t`
3. Ensure there are no duplicate upstream definitions
4. Check that the proxy HTML file exists in the specified location

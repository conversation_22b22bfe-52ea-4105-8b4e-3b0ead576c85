<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <script>
        function connectWebSocket() {
            const ws = new WebSocket("ws://localhost:6080/websockify");
            
            ws.onopen = function() {
                document.getElementById("status").textContent = "Connected";
                document.getElementById("status").style.color = "green";
                console.log("WebSocket connection established");
            };
            
            ws.onclose = function() {
                document.getElementById("status").textContent = "Disconnected";
                document.getElementById("status").style.color = "red";
                console.log("WebSocket connection closed");
            };
            
            ws.onerror = function(error) {
                document.getElementById("status").textContent = "Error";
                document.getElementById("status").style.color = "red";
                console.error("WebSocket error:", error);
            };
        }
    </script>
</head>
<body onload="connectWebSocket()">
    <h1>WebSocket Test</h1>
    <p>Status: <span id="status">Connecting...</span></p>
</body>
</html>
